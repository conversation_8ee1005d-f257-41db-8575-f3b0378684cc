<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
</head>
<body >
<h1>一、业务DB多分区差异检查(杭州对比上海)</h1>
<div style="margin: 0; padding: 0;width: 100%; border: 1px solid #000;">
        <div style="display: flex;justify-content: space-between;margin-bottom: -1px;margin-top: -1px;">
            <div style="width: 10%;display: flex;justify-content: space-between;margin-bottom: -1px;margin-top: -1px;">对比的分区</div>
            <div style="width: 10%;display: flex;justify-content: space-between;margin-bottom: -1px;margin-top: -1px;">数据库</div>
            <div style="width: 15%;display: flex;justify-content: space-between;margin-bottom: -1px;margin-top: -1px;">数据表</div>
            <div style="width: 15%;display: flex;justify-content: space-between;margin-bottom: -1px;margin-top: -1px;">错误类型</div>
           <div style="width: 50%;display: flex;justify-content: space-between;margin-bottom: -1px;margin-top: -1px; word-wrap: break-word;"> 差异详细原因(杭州)</div>
        </div>
        {% for item in errMsgs %}
        <div style="display: flex;justify-content: space-between;margin-bottom: -1px;margin-top: -1px;background-color:lightgray;">
                <div style="width: 10%; padding: -1px;border:1px;" >{{ item.regionName|escape }}</div>
                <div style="width: 10%; padding: -1px;border:1px;color:dodgerblue;" >{{ item.dbName|escape }}</div>
                <div style="width: 15%;padding: -1px;border:1px; color:greenyellow;" >{{ item.tableName|escape }}</div>
                <div style="width: 15%; padding: -1px;border:1px; color:red;">{{ item.errType|escape }}</div>
                <div style="width: 50%; padding: 10px;border:1px; ">{{ item.errMsg|escape }}</div>
        </div>
        {% endfor %}
</div>
<h1>二、ODS-DB多分区差异检查</h1>
<h1>三、ES-多分区差异检查</h1>
</body>
</html>
