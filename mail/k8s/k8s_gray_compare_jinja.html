<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
</head>
<body >
{% for regionRoot in regionList %}
<h1>{{regionRoot.regionName}}(活跃更新日期:{{regionRoot.date}})</h1>
<div style="margin: 0; padding: 0;width: 100%; border: 1px solid #000;">
        <div style="display: flex;justify-content: space-between;margin-bottom: -1px;margin-top: -1px;">
            <div style="width: 30%;display: flex;justify-content: space-between;margin-bottom: -1px;margin-top: -1px;"></div>
            <div style="width: 20%;display: flex;justify-content: space-between;margin-bottom: -1px;margin-top: -1px;">灰度活跃</div>
            <div style="width: 20%;display: flex;justify-content: space-between;margin-bottom: -1px;margin-top: -1px;">正式活跃</div>
            <div style="width: 20%;display: flex;justify-content: space-between;margin-bottom: -1px;margin-top: -1px;">灰度总量</div>
            <div style="width: 20%;display: flex;justify-content: space-between;margin-bottom: -1px;margin-top: -1px;">正式总量</div>
        </div>
    <div style="display: flex;justify-content: space-between;margin-bottom: -1px;margin-top: -1px;">
        <div style="width: 30%; padding: -1px;border:1px;" >连锁数量</div>
        <div style="width: 20%; padding: -1px;border:1px;color:blue;font-size: xx-large" >{{ regionRoot.grayActiveRealChainCount |escape }}</div>
        <div style="width: 20%;padding: -1px;border:1px; color:red;font-size: xx-large" >{{ regionRoot.prodActiveRealChainCount |escape }}</div>
        <div style="width: 20%; padding: -1px;border:1px;color:blue;font-size: xx-large" >{{ regionRoot.grayChainCount |escape }}</div>
        <div style="width: 20%;padding: -1px;border:1px; color:red;font-size: xx-large" >{{ regionRoot.prodChainCount |escape }}</div>
    </div>
    <div style="display: flex;justify-content: space-between;margin-bottom: -1px;margin-top: -1px;">
        <div style="width: 30%; padding: -1px;border:1px;" >诊所管家</div>
        <div style="width: 20%; padding: -1px;border:1px;color:blue;font-size: xx-large" >{{ regionRoot.grayActiveClinicChainCount |escape }}</div>
        <div style="width: 20%;padding: -1px;border:1px; color:red;font-size: xx-large" >{{ regionRoot.prodActiveClinicChainCount |escape }}</div>
        <div style="width: 20%; padding: -1px;border:1px;color:blue;font-size: xx-large" >{{ regionRoot.grayClinicChainCount |escape }}</div>
        <div style="width: 20%;padding: -1px;border:1px; color:red;font-size: xx-large" >{{ regionRoot.prodClinicChainCount |escape }}</div>
    </div>
    <div style="display: flex;justify-content: space-between;margin-bottom: -1px;margin-top: -1px;">
        <div style="width: 30%; padding: -1px;border:1px;" >口腔管家</div>
        <div style="width: 20%; padding: -1px;border:1px;color:blue;font-size: xx-large" >{{ regionRoot.grayActiveOralChainCount |escape }}</div>
        <div style="width: 20%;padding: -1px;border:1px; color:red;font-size: xx-large" >{{ regionRoot.prodActiveOralChainCount |escape }}</div>
        <div style="width: 20%; padding: -1px;border:1px;color:blue;font-size: xx-large" >{{ regionRoot.grayOralChainCount |escape }}</div>
        <div style="width: 20%;padding: -1px;border:1px; color:red;font-size: xx-large" >{{ regionRoot.prodOralChainCount |escape }}</div>
    </div>
    <div style="display: flex;justify-content: space-between;margin-bottom: -1px;margin-top: -1px;">
        <div style="width: 30%; padding: -1px;border:1px;" >眼科管家</div>
        <div style="width: 20%; padding: -1px;border:1px;color:blue;font-size: xx-large" >{{ regionRoot.grayActiveEyeChainCount |escape }}</div>
        <div style="width: 20%;padding: -1px;border:1px; color:red;font-size: xx-large" >{{ regionRoot.prodActiveEyeChainCount |escape }}</div>
        <div style="width: 20%; padding: -1px;border:1px;color:blue;font-size: xx-large" >{{ regionRoot.grayEyeChainCount |escape }}</div>
        <div style="width: 20%;padding: -1px;border:1px; color:red;font-size: xx-large" >{{ regionRoot.prodEyeChainCount |escape }}</div>
    </div>
    <div style="display: flex;justify-content: space-between;margin-bottom: -1px;margin-top: -1px;">
        <div style="width: 30%; padding: -1px;border:1px;" >药店管家</div>
        <div style="width: 20%; padding: -1px;border:1px;color:blue;font-size: xx-large" >{{ regionRoot.grayActivePharmacyChainCount |escape }}</div>
        <div style="width: 20%;padding: -1px;border:1px; color:red;font-size: xx-large" >{{ regionRoot.prodActivePharmacyChainCount |escape }}</div>
        <div style="width: 20%; padding: -1px;border:1px;color:blue;font-size: xx-large" >{{ regionRoot.grayPharmacyChainCount |escape }}</div>
        <div style="width: 20%;padding: -1px;border:1px; color:red;font-size: xx-large" >{{ regionRoot.prodPharmacyChainCount |escape }}</div>
    </div>
    <div style="display: flex;justify-content: space-between;margin-bottom: -1px;margin-top: -1px;">
        <div style="width: 30%; padding: -1px;border:1px;" >医院管家</div>
        <div style="width: 20%; padding: -1px;border:1px;color:blue;font-size: xx-large" >{{ regionRoot.grayActiveHospitalChainCount |escape }}</div>
        <div style="width: 20%;padding: -1px;border:1px; color:red;font-size: xx-large" >{{ regionRoot.prodActiveHospitalChainCount |escape }}</div>
        <div style="width: 20%; padding: -1px;border:1px;color:blue;font-size: xx-large" >{{ regionRoot.grayHospitalChainCount |escape }}</div>
        <div style="width: 20%;padding: -1px;border:1px; color:red;font-size: xx-large" >{{ regionRoot.prodHospitalChainCount |escape }}</div>
    </div>
    <hr style="border: none; border-top: 1px solid black; width: 100%;">
    <div style="display: flex;justify-content: space-between;margin-bottom: -1px;margin-top: -1px;">
        <div style="width: 30%;display: flex;justify-content: space-between;margin-bottom: -1px;margin-top: -1px;">服务名称</div>
        <div style="width: 20%;display: flex;justify-content: space-between;margin-bottom: -1px;margin-top: -1px;">灰度节点数量</div>
        <div style="width: 20%;display: flex;justify-content: space-between;margin-bottom: -1px;margin-top: -1px;">正式节点数量</div>
    </div>
        {% for item in regionRoot.serviceList %}
        <div style="display: flex;justify-content: space-between;margin-bottom: -1px;margin-top: -1px;">
                <div style="width: 30%; padding: -1px;border:1px;" >{{ item.name|escape }}</div>
            <div style="width: 20%;padding: -1px;border:1px;font-size: x-large" ><span style="font-size: x-large;color: greenyellow">{{ item.grayCount|escape }}({{ item.graySuggestCount|escape }})</span><br><span style="font-size: small ;color:cornflowerblue">{{ item.grayTag|escape }}</span></div>
            <div style="width: 20%;padding: -1px;border:1px;font-size: x-large" ><span style="font-size: x-large;color: greenyellow">{{ item.prodCount|escape }}({{ item.prodSuggestCount|escape }})</span><br><span style="font-size: small ;color:cornflowerblue">{{ item.prodTag|escape }}</span></div>
        </div>
        {% endfor %}
</div>
{% endfor %}
</body>
</html>
