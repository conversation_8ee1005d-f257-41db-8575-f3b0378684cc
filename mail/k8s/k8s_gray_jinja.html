<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
</head>
<body >
{% for regionRoot in regionList %}
<h1>{{regionRoot.regionName}}({{regionRoot.date}})</h1>
<div style="margin: 0; padding: 0;width: 100%; border: 1px solid #000;">
        <div style="display: flex;justify-content: space-between;margin-bottom: -1px;margin-top: -1px;">
            <div style="width: 20%;display: flex;justify-content: space-between;margin-bottom: -1px;margin-top: -1px;"></div>
            <div style="width: 40%;display: flex;justify-content: space-between;margin-bottom: -1px;margin-top: -1px;">灰度活跃(灰度总量)</div>
            <div style="width: 40%;display: flex;justify-content: space-between;margin-bottom: -1px;margin-top: -1px;">正式活跃(正式总量)</div>
        </div>
    <div style="display: flex;justify-content: space-between;margin-bottom: -1px;margin-top: -1px;">
        <div style="width: 20%; padding: -1px;border:1px;" >连锁数量</div>
        <div style="display: flex;width: 40%;" >
            <span style=" padding: -1px;border:1px;color:red;font-size: xx-large">{{ regionRoot.grayActiveRealChainCount |escape }}</span>
            <span style="font-size: x-large;color: greenyellow">({{ regionRoot.grayChainCount|escape }})</span>
        </div>
        <div style="display: flex;width: 40%;" >
            <span style=" padding: -1px;border:1px;color:red;font-size: xx-large">{{ regionRoot.prodActiveRealChainCount |escape }}</span>
            <span style="font-size: x-large;color: greenyellow">({{ regionRoot.prodChainCount|escape }})</span>
        </div>
    </div>
    <div style="display: flex;justify-content: space-between;margin-bottom: -1px;margin-top: -1px;">
        <div style="width: 20%; padding: -1px;border:1px;" >诊所管家</div>
            <div style="display: flex;width: 40%;" >
            <span style=" padding: -1px;border:1px;color:red;font-size: xx-large">{{ regionRoot.grayActiveClinicChainCount |escape }}</span>
            <span style="font-size: x-large;color: greenyellow">({{ regionRoot.grayClinicChainCount|escape }})</span>
        </div>
            <div style="display: flex;width: 40%;" >
            <span style="padding: -1px;border:1px;color:red;font-size: xx-large">{{ regionRoot.prodActiveClinicChainCount |escape }}</span>
            <span style="font-size: x-large;color: greenyellow">({{ regionRoot.prodClinicChainCount|escape }})</span>
        </div>
    </div>
    <div style="display: flex;justify-content: space-between;margin-bottom: -1px;margin-top: -1px;">
        <div style="width: 20%; padding: -1px;border:1px;" >口腔管家</div>
            <div style="display: flex;width: 40%;" >
            <span style=" padding: -1px;border:1px;color:red;font-size: xx-large">{{ regionRoot.grayActiveOralChainCount |escape }}</span>
            <span style="font-size: x-large;color: greenyellow">({{ regionRoot.grayOralChainCount|escape }})</span>
        </div>
            <div style="display: flex;width: 40%;" >
            <span style="padding: -1px;border:1px;color:red;font-size: xx-large">{{ regionRoot.prodActiveOralChainCount |escape }}</span>
            <span style="font-size: x-large;color: greenyellow">({{ regionRoot.prodOralChainCount|escape }})</span>
        </div>
    </div>
    <div style="display: flex;justify-content: space-between;margin-bottom: -1px;margin-top: -1px;">
        <div style="width: 20%; padding: -1px;border:1px;" >眼科管家</div>
            <div style="display: flex;width: 40%;" >
            <span style=" padding: -1px;border:1px;color:red;font-size: xx-large">{{ regionRoot.grayActiveEyeChainCount |escape }}</span>
            <span style="font-size: x-large;color: greenyellow">({{ regionRoot.grayEyeChainCount|escape }})</span>
        </div>
            <div style="display: flex;width: 40%;" >
            <span style=" padding: -1px;border:1px;color:red;font-size: xx-large">{{ regionRoot.prodActiveEyeChainCount |escape }}</span>
            <span style="font-size: x-large;color: greenyellow">({{ regionRoot.prodEyeChainCount|escape }})</span>
        </div>
    </div>
    <div style="display: flex;justify-content: space-between;margin-bottom: -1px;margin-top: -1px;">
        <div style="width: 20%; padding: -1px;border:1px;" >药店管家</div>
            <div style="display: flex;width: 40%;" >
            <span style=" padding: -1px;border:1px;color:red;font-size: xx-large">{{ regionRoot.grayActivePharmacyChainCount |escape }}</span>
            <span style="font-size: x-large;color: greenyellow">({{ regionRoot.grayPharmacyChainCount|escape }})</span>
        </div>
            <div style="display: flex;width: 40%;" >
            <span style="padding: -1px;border:1px;color:red;font-size: xx-large">{{ regionRoot.prodActivePharmacyChainCount |escape }}</span>
            <span style="font-size: x-large;color: greenyellow">({{ regionRoot.prodPharmacyChainCount|escape }})</span>
        </div>
    </div>
    <div style="display: flex;justify-content: space-between;margin-bottom: -1px;margin-top: -1px;">
        <div style="width: 20%; padding: -1px;border:1px;" >医院管家</div>
            <div style="display: flex;width: 40%;" >
            <span style="padding: -1px;border:1px;color:red;font-size: xx-large">{{ regionRoot.grayActiveHospitalChainCount |escape }}</span>
            <span style="font-size: x-large;color: greenyellow">({{ regionRoot.grayHospitalChainCount|escape }})</span>
        </div>
            <div style="display: flex;width: 40%;" >
            <span style="padding: -1px;border:1px;color:red;font-size: xx-large">{{ regionRoot.prodActiveHospitalChainCount |escape }}</span>
            <span style="font-size: x-large;color: greenyellow">({{ regionRoot.prodHospitalChainCount|escape }})</span>
        </div>
    </div>
</div>
{% endfor %}
<div style=" padding: -1px;border:1px;color:red;font-size: xx-large">备注：活跃计算方式最近一个月有收费单的连锁</div>
</body>
</html>
