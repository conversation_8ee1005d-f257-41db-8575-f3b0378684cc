# PreGrayDataMigrate 

## 项目简介

用于执行需求上PGV时刷数据的脚本管理

1. 涉及线上数据更新，必须慎重操作，所有提交都需要发送合并请求给分支管理员（李龙彬、杨俊勇）
2. 每一个完整全量算一个周期，这期间的刷数据都放在一个文件夹中管理。比如`2022-03-10`发了一次全量，则新建文件夹`20220311`，期间新增需求都往`20220311`中添加，直到`2022-04-18`又一次全量后，`20220311`文件夹关闭，新建文件夹`20220419`
3. 每个需求一个脚本，每个脚本要在`v2_data_migrate_script`有相应记录和执行环境才会生效
4. `scrirps/common/deploy_weapp.py` 用于重新发布一次诊所小程序，每一个周期，都应该添加一下这个脚步到pre和gray，在`v2_data_migrate_script`这张表中体现为两行数据
5. 每个连锁执行了某个脚本后，都会在`v2_data_migrate_script_organ_execute`中产生一条记录，以防止重复执行

## 项目结构

PreGrayDataMigrate/
├── cron/              # 定时任务相关脚本
├── dcm4chee/          # DICOM 相关脚本
├── es/                # Elasticsearch 相关脚本
├── idwork/            # ID 生成器相关脚本
├── mail/              # 邮件相关脚本
├── main.py            # 主程序入口
├── manage/            # 管理脚本
├── multizone/         # 多区域数据处理脚本
├── scripts/           # 主要业务脚本目录
│   ├── common/        # 通用脚本
│   └── {YYYYMMDD}/    # 按全量发布周期组织的脚本目录
└── yinxy/             # yinxiaoyang 个人目录，存放个人脚本

### 目录说明

1. `scripts/`: 存放所有业务相关的数据迁移脚本
   - `common/`: 存放通用脚本，如小程序重新发布脚本
   - `{YYYYMMDD}/`: 按全量发布周期组织的脚本目录，如 `20220311/`

2. `multizone/`: 多区域数据处理相关脚本，用于处理跨区域数据迁移

3. `main.py`: 项目主程序入口，包含核心逻辑和公共函数

4. 其他目录均按功能模块进行组织，如 `cron/` 存放定时任务脚本，`es/` 存放 Elasticsearch 相关脚本等

## 开发规范

### 数据库连接

1. 数据库连接使用 `multizone.db.DBClient` 类，主要参数如下：
   ```python
   DBClient(zone,        # 分区名称：ShangHai/HangZhou
           cluster,      # 数据库集群：abc_cis_mixed/abc_cis_basic
           database,     # 数据库名称
           env='prod',   # 环境：prod/pre/gray/dev/test
           no_tunnel=False)  # 是否不使用跳板机（如果是 prod，则使用跳板机 ）
   ```

2. 分区配置在 `zone_shanghai.yaml` 和 `zone_hangzhou.yaml` 中定义，包含：
   - 数据库集群配置
   - 跳板机配置
   - 数据库列表

### 脚本开发规范

1. 主方法规范：
   ```python
   def main():
       parser = argparse.ArgumentParser(description='脚本说明')
       parser.add_argument('--env', help='环境[dev/test/prod]')
       parser.add_argument('--chain_id', help='连锁id')
       parser.add_argument('--region_name', help='分区名称[ShangHai/HangZhou/...]')
   ```

2. 参数说明：
   - `env`: 运行环境
   - `chain_id`: 连锁ID
   - `region_name`: 运行分区