#!/usr/bin/env python
# -*- coding: utf-8 -*-

import re
import sys

def fix_fstring_in_file(filename):
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Pattern to match f-strings
    pattern = r'f"([^"]*)"'
    
    def replace_fstring(match):
        fstring_content = match.group(1)
        # Replace {variable} with {variable} for .format()
        # This is a simple replacement - might need more complex logic for some cases
        return '"{}"'.format(fstring_content)
    
    # Replace f-strings with .format() strings
    new_content = re.sub(pattern, replace_fstring, content)
    
    # Now we need to convert the format placeholders
    # This is a more complex task, let's do it manually for critical ones
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(new_content)

if __name__ == '__main__':
    if len(sys.argv) != 2:
        print("Usage: python fix_fstring.py <filename>")
        sys.exit(1)
    
    fix_fstring_in_file(sys.argv[1])
    print("F-strings replaced successfully")
