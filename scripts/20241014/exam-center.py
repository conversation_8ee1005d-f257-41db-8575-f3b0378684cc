"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
import argparse

def run(region_name, chain_id, env):
    goods_client = DBClient(region_name, 'abc_cis_stock', 'abc_cis_goods', env, True)
    goods_client.execute(f"""
    insert into v2_goods_price_opt(id, goods_id, chain_id, clinic_id, piece_price, package_price, package_cost_price,
                               created_user_id, created_date, last_modified_user_id, last_modified_date, sub_price_flag,
                               process_price, price_type, price_makeup_percent, individual_pricing_type, pharmacy_type,
                               pharmacy_no, is_deleted)
    SELECT uuid_short(),
           goods_id,
           chain_id,
           organ_id,
           piece_price,
           package_price,
           package_cost_price,
           created_user_id,
           created_date,
           last_modified_user_id,
           last_modified_date,
           sub_price_flag,
           process_price,
           price_type,
           price_makeup_percent,
           individual_pricing_type,
           null,
           null,
           0
    from v2_goods_price where chain_id = '{chain_id}'
    """)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    run(args.region_name, args.chain_id, args.env)

if __name__ == '__main__':
    main()
