"""
刷药店的 v2_goods_stock_log 表的 source_order_id 和 source_order_detail_id 字段
"""
import argparse
import os
import sys
from datetime import datetime, timedelta

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.db import DBClient

from scripts.common.utils.lists import ListUtils
from scripts.common.utils.sqls import SqlUtils


def execute_sql(client, sql):
    client.execute(sql)
    print(sql)
    pass


class UpdateData:
    chain_id = None
    goods_log_client = None
    basic_ob_client = None
    dispensing_ob_client = None
    goods_log_ob_client = None
    goods_ob_client = None
    env = None

    def __init__(self, env, region_name, chain_id):
        self.env = env
        self.chain_id = chain_id
        self.region_name = region_name
        goods_log_database = 'abc_cis_goods_log'
        if env == 'dev' or env == 'test':
            goods_log_database = 'abc_cis_goods'
        self.goods_log_client = DBClient(self.region_name, 'abc_cis_stock_zip', goods_log_database, self.env, True)
        self.basic_ob_client = DBClient(self.region_name, 'ob', 'abc_cis_basic', self.env, True)
        self.dispensing_ob_client = DBClient(self.region_name, 'ob', 'abc_cis_dispensing', self.env, True)
        self.goods_log_ob_client = DBClient(self.region_name, 'ob', goods_log_database, self.env, True)
        self.goods_ob_client = DBClient(self.region_name, 'ob', 'abc_cis_goods', self.env, True)

    def run(self):
        # 查询当前门店的 hisType 是否 10，如果不是 10 则不执行
        organs = self.basic_ob_client.fetchall("""
            select id
            from organ
            where parent_id = '{chainId}' and his_type = 10 and id != parent_id
        """.format(chainId=self.chain_id))
        if not organs:
            return

        clinic_ids = ListUtils.dist_mapping(organs, lambda organ: organ['id'])

        for clinic_id in clinic_ids:
            self.flush_clinic_goods_stock_log(clinic_id)

    def flush_clinic_goods_stock_log(self, clinic_id):
        # 先查询最早的一条发药单的创建时间
        sheet = self.dispensing_ob_client.fetchone("""
            select min(created) as created
            from v2_dispensing_sheet
            where clinic_id = '{clinicId}';
        """.format(clinicId=clinic_id))
        if not sheet or not sheet['created']:
            return

        min_created = sheet['created']
        begin_date = min_created
        # 结束日期为当前时间
        now = datetime.now()
        end_date = datetime(now.year, now.month, now.day, 23, 59, 59)

        while begin_date < end_date:
            # 每次处理 30 天的数据
            current_end_date = begin_date + timedelta(days=15)

            offset = 0
            limit = 100
            while True:
                # 分页查询 100 条发药单
                dispense_items = self.dispensing_ob_client.fetchall("""
                    select b.id, b.patient_order_id, b.source_form_item_id, a.source_sheet_id
                    from v2_dispensing_sheet a
                             inner join v2_dispensing_form_item b on (a.id = b.dispensing_sheet_id)
                    where a.clinic_id = '{clinicId}'
                      and a.created between '{beginDate}' and '{endDate}'
                    and b.status != 2
                    order by a.created
                    limit {offset}, {limit}
                """.format(clinicId=clinic_id, beginDate=begin_date, endDate=current_end_date, offset=offset, limit=limit))
                offset = offset + limit

                if not dispense_items:
                    break

                # 查询发药单的所有进销存记录
                patient_order_ids = ListUtils.dist_mapping(dispense_items, lambda dispense_item: dispense_item['patient_order_id'])
                source_order_detail_ids = ListUtils.dist_mapping(dispense_items, lambda dispense_item: dispense_item['id'])
                goods_logs = self.goods_log_ob_client.fetchall("""
                    select id, order_detail_id, source_order_id, source_order_detail_id, stock_id, batch_id
                    from v2_goods_stock_log
                    where patient_order_id in ({patient_order_ids}) and order_detail_id in ({source_order_detail_ids})
                """.format(patient_order_ids=SqlUtils.to_in_value(patient_order_ids), source_order_detail_ids=SqlUtils.to_in_value(source_order_detail_ids)))

                if not goods_logs:
                    print(f"clinic {clinic_id} goods log not found, begin_date: {begin_date}, end_date: {current_end_date}")
                    break

                stock_ids = ListUtils.dist_mapping(goods_logs, lambda goods_log: goods_log['stock_id'])
                goods_stocks = self.goods_ob_client.fetchall("""
                    select id, batch_id
                    from v2_goods_stock
                    where id in ({stock_ids})
                """.format(stock_ids=SqlUtils.to_in_value(stock_ids)))
                stock_id_to_goods_stock = ListUtils.to_map(goods_stocks, lambda goods_stock: goods_stock['id'])

                order_detail_id_to_dispense_item = ListUtils.to_map(dispense_items, lambda dispense_item: dispense_item['id'])
                update_goods_logs = []
                for goods_log in goods_logs:
                    order_detail_id = goods_log['order_detail_id']
                    dispense_item = order_detail_id_to_dispense_item.get(order_detail_id)
                    if not dispense_item:
                        print(f"clinic {clinic_id} dispense item not found, order_detail_id: {order_detail_id}")
                        continue

                    batch_id = None
                    stock_id = goods_log['stock_id']
                    goods_stock = stock_id_to_goods_stock.get(stock_id)
                    if goods_stock and goods_stock['batch_id']:
                        batch_id = goods_stock['batch_id']
                    else:
                        print(f"clinic {clinic_id} goods stock not found, stock_id: {stock_id}")
                        batch_id = stock_id

                    update_goods_logs.append({
                        'id': goods_log['id'],
                        'sourceOrderId': dispense_item['source_sheet_id'],
                        'sourceOrderDetailId': dispense_item['source_form_item_id'],
                        'batchId': batch_id
                    })

                if not update_goods_logs:
                    continue

                # 批量更新
                source_order_case_when = ""
                source_order_detail_case_when = ""
                batch_case_when = ""
                for update_goods_log in update_goods_logs:
                    source_order_case_when += f" when {update_goods_log['id']} then '{update_goods_log['sourceOrderId']}' "
                    source_order_detail_case_when += f" when {update_goods_log['id']} then '{update_goods_log['sourceOrderDetailId']}' "
                    batch_case_when += f" when {update_goods_log['id']} then '{update_goods_log['batchId']}' "

                update_sql = """
                    update v2_goods_stock_log
                    set source_order_id = case id {sourceOrderCaseWhen} end,
                        source_order_detail_id = case id {sourceOrderDetailCaseWhen} end,
                        batch_id = case id {batchCaseWhen} end
                    where id in ({ids});
                """.format(ids=SqlUtils.to_in_value(ListUtils.dist_mapping(update_goods_logs, lambda e: e['id'])),
                           sourceOrderCaseWhen=source_order_case_when,
                           sourceOrderDetailCaseWhen=source_order_detail_case_when,
                           batchCaseWhen=batch_case_when)
                self.goods_log_client.execute(update_sql)

            begin_date = current_end_date


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    updateData = UpdateData(args.env, args.region_name, args.chain_id)
    updateData.run()


if __name__ == '__main__':
    main()
