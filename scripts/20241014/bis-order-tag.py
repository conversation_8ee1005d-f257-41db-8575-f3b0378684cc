"""
    运营中心订单数据打标签
"""

import argparse
import copy
import os
import sys

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient

DEFAULT_OPERATOR_ID = "00000000000000000000000000000000"

def run(env):
    bis_db_client = DBClient('ShangHai', 'abc_cis_mixed', 'abc_bis', env, True)

    # 当前正式环境的门店4859
    clinicIdList = bis_db_client.fetchall("""
        select distinct clinic_id from v1_mall_order s where s.pay_time is not null and s.is_deleted = 0 and s.status in (10, 20, 30, 42, 43)
    """)

    bis_db_client.execute("""
        update v1_mall_order set order_tag = 0 where is_deleted = 0
    """)

    bis_db_client.execute("""
        delete from v1_mall_order_clinic_tag where is_deleted = 0
    """)

    if len(clinicIdList) > 0:
        chunkedList = split_list(clinicIdList, 200)
        for index, chunk in enumerate(chunkedList):
            # 查询该集合门店的订单数据
            clinicShortId = [str(d['clinic_id']) for d in chunk]
            clinicOrderList = bis_db_client.fetchall("""
                select
                    a.id,
                    a.clinic_id as clinicId,
                    a.pay_time as payTime,
                    a.status,
                    a.order_tag as orderTag,
                    GROUP_CONCAT(distinct k.category_id SEPARATOR ',') AS categoryId
                from v1_mall_order a
                inner join v1_mall_order_item k on a.id = k.order_id
                where a.is_deleted = 0 and k.is_deleted = 0 and a.pay_time is not null
                    and a.status in (10, 20, 30, 42, 43) and a.is_only_inner = 0 and a.clinic_id in ({inStr})
                group by a.id
            """.format(inStr = ("'" + "','".join(clinicShortId)) + "'"))
            # 对订单数据按照门店分组
            groupedOrderList = groupByField(clinicOrderList, 'clinicId')
            # 一个批次中记录需要更新的Order集合
            updateOrderSqlList = []
            insertOrderTagSqlList = []
            if len(groupedOrderList) == 0:
                continue
            for clinicId, orderList in groupedOrderList.items():
                if len(orderList) == 0:
                    continue
                # 对集合订单按照时间排序
                orderSortList = sorted(orderList, key=lambda x: x['payTime'])
                firstOrderId = firstOrderTag(orderSortList)
                drugFirstOrderId = drugFirstOrderTag(orderSortList)
                instrumentFirstOrderId = instrumentFirstOrderTag(orderSortList)

                tagList = lossOrderTag(orderSortList)
                lossWarnTagList = tagList[0]
                lossTagList = tagList[1]
                lastDrugPayOrder = tagList[2]

                lastPaidOrderId = None
                lastPayTime = None
                if lastDrugPayOrder is not None:
                    # 最后一个支付订单
                    lastPaidOrderId = lastDrugPayOrder['id']
                    lastPayTime = lastDrugPayOrder['payTime']

                # 组装批量更新SQL
                for order in orderList:
                    if (order['id'] in lossWarnTagList or order['id'] in lossTagList) and order['id'] != firstOrderId and order['id'] != drugFirstOrderId and order['id'] != instrumentFirstOrderId:
                        # 初始时未定义任何标签
                        order['orderTag'] = 0
                        if order['id'] in lossWarnTagList:
                            order['orderTag'] = 1
                        if order['id'] in lossTagList:
                            order['orderTag'] = 2
                        # 更新SQL语句加到需要更新的集合中
                        updateOrderSqlList.append("""
                            UPDATE V1_MALL_ORDER SET order_tag = '{orderTag}' WHERE ID = '{orderId}'
                        """.format(orderTag = order['orderTag'], orderId = order['id']))

                # 新增表(v1_mall_order_clinic_tag)中的数据
                if lastPayTime is not None:
                    insertOrderTagSqlList.append("""
                        insert into v1_mall_order_clinic_tag(id, clinic_id, first_order_id, drug_first_order_id, instrument_first_order_id, last_paid_order_id, last_pay_time, created, created_by, last_modified, last_modified_by)
                        VALUES (uuid_short(), {clinicId}, {firstOrderId}, {drugId}, {instrumentId}, {lastPaidOrderId}, '{lastPayTime}', CURRENT_TIMESTAMP, '{createdBy}', CURRENT_TIMESTAMP, '{lastModifiedBy}')
                    """.format(clinicId = clinicId, firstOrderId = firstOrderId, drugId = drugFirstOrderId or 'NULL', instrumentId = instrumentFirstOrderId or 'NULL', lastPaidOrderId = lastPaidOrderId, lastPayTime = lastPayTime, createdBy = DEFAULT_OPERATOR_ID, lastModifiedBy = DEFAULT_OPERATOR_ID))
                else:
                    insertOrderTagSqlList.append("""
                        insert into v1_mall_order_clinic_tag(id, clinic_id, first_order_id, drug_first_order_id, instrument_first_order_id, created, created_by, last_modified, last_modified_by)
                        VALUES (uuid_short(), {clinicId}, {firstOrderId}, {drugId}, {instrumentId}, CURRENT_TIMESTAMP, '{createdBy}', CURRENT_TIMESTAMP, '{lastModifiedBy}')
                    """.format(clinicId = clinicId, firstOrderId = firstOrderId, drugId = drugFirstOrderId or 'NULL', instrumentId = instrumentFirstOrderId or 'NULL', createdBy = DEFAULT_OPERATOR_ID, lastModifiedBy = DEFAULT_OPERATOR_ID))

            # 门店纬度批量更新数据
            if len(updateOrderSqlList) > 0:
                bis_db_client.executemany(updateOrderSqlList)
            if len(insertOrderTagSqlList) > 0:
                bis_db_client.executemany(insertOrderTagSqlList)
"""
    首单标签
"""
def firstOrderTag(orderSortList):
    return orderSortList[0]['id']

"""
    药品首单标签
    return 返回首单id
"""
def drugFirstOrderTag(orderSortList):
    # 根据10001或者10004是否在catrgoryId字符串中来判断是否是药品首单
    for order in orderSortList:
        categoryId = order['categoryId']
        if categoryId is not None:
            if '10001' in categoryId or '10004' in categoryId:
                return order['id']

"""
    器械首单标签
    return 返回首单id
"""
def instrumentFirstOrderTag(orderSortList):
    # 根据10005、10006、10010、100011、10012、10013是否在catrgoryId字符串中来判断是否是药品首单
    for order in orderSortList:
        categoryId = order['categoryId']
        if categoryId is not None:
            if '10005' in categoryId or '10006' in categoryId or '10010' in categoryId or '10011' in categoryId or '10012' in categoryId or '10013' in categoryId:
                return order['id']

"""
    流失预警召回标签 和 流失召回标签
    return [流失预警召回集合, 流失召回集合]
"""
def lossOrderTag(orderSortList):
    # 根据payTime计算相邻两个数据之间的间隔天数
    lossWarnTagList = []
    lossTagList = []
    # 此处数据需要深拷贝
    deepCopyOrderSortList = copy.deepcopy(orderSortList)
    # 过滤出药品数据
    copyOrderList = [copyOrder for copyOrder in deepCopyOrderSortList if '10001' in copyOrder["categoryId"] or '10004' in copyOrder["categoryId"]]
    # 按照时间排序
    copyOrderSortList = sorted(copyOrderList, key=lambda x: x['payTime'])
    for index, order in enumerate(copyOrderSortList):
        if index == 0:
            continue
        else:
            # 计算时间间隔
            days = (order['payTime'] - copyOrderSortList[index - 1]['payTime']).days
            if 30 < days <= 60:
                # 如果超出30天而不超过60天则添加订单id到流失预警召回标签集合中
                lossWarnTagList.append(order['id'])
            elif days > 60:
                # 如果超出60天则添加订单id到流失召回标签集合中
                lossTagList.append(order['id'])
    tagList = [lossWarnTagList, lossTagList, copyOrderSortList[-1] if len(copyOrderSortList) > 0 else None]
    return tagList

"""
    按照指定字段进行分组
    data: 数据列表
    field: 分组字段
    return: 分组后的数据
"""
def groupByField(data, field):
    grouped = {}
    for item in data:
        key = item[field]
        if key not in grouped:
            grouped[key] = []
        grouped[key].append(item)
    return grouped

"""
    拆分列表为多个小列表,每个小列表最多包含chunk_size个元素
    param source_list: 需要拆分的源列表
    param chunk_size: 每个小列表最多包含的元素个数
    return: 拆分后的小列表
"""
def split_list(source_list, chunk_size):
    return [source_list[i:i + chunk_size] for i in range(0, len(source_list), chunk_size)]

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.env:
        parser.print_help()
        sys.exit(-1)
    run(args.env)
    # run("test")

if __name__ == '__main__':
    main()
