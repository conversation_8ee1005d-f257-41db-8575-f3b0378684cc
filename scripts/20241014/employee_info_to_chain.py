"""
成员信息下沉到连锁
"""
import argparse
import os
import sys

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.db import DBClient


def execute_sql(client, sql):
    client.execute(sql)
    print(sql)
    pass


class UpdateData:
    chain_id = None
    basic_wdb_client = None
    env = None

    def __init__(self, env, region_name, chain_id):
        self.env = env
        self.chain_id = chain_id
        self.region_name = region_name
        self.basic_wdb_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', self.env, True)

    def run(self):
        # 将employee的信息同步到 v2_clinic_chain_employee
        sql = """
            update v2_clinic_chain_employee a
                inner join employee e on a.employee_id = e.id
            set a.name          = e.name,
                a.name_py       = e.name_py,
                a.name_py_first = e.name_py_first,
                a.hand_sign_type = e.hand_sign_type,
                a.hand_sign = e.hand_sign,
                a.hand_sign_pic_url = e.hand_sign_pic_url
            where a.chain_id = '{chainId}';
        """.format(chainId=self.chain_id)
        execute_sql(self.basic_wdb_client, sql)

        # v2_clinic_chain_employee_snap 数据初始化
        # 不可重入
        sql = """
            insert into v2_clinic_chain_employee_snap(id, chain_id, clinic_id, employee_id, name, name_py, name_py_first, hand_sign,
                                          hand_sign_pic_url, hand_sign_type, created, effective_time, expiry_time)
            SELECT substr(uuid_short(),4),
                   a.chain_id,
                   a.chain_id,
                   a.employee_id,
                   e.name,
                   e.name_py,
                   e.name_py_first,
                   e.hand_sign,
                   e.hand_sign_pic_url,
                   e.hand_sign_type,
                   '1970-01-01 00:00:00',
                   '1970-01-01 00:00:00',
                   '9999-12-31 00:00:00'
            from v2_clinic_chain_employee a inner join employee e on a.employee_id = e.id
            where a.chain_id = '{chainId}';
        """.format(chainId=self.chain_id)
        execute_sql(self.basic_wdb_client, sql)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    updateData = UpdateData(args.env, args.region_name, args.chain_id)
    updateData.run()


if __name__ == '__main__':
    main()
