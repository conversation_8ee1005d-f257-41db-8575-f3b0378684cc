"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
import arg<PERSON><PERSON>

def run(region_name, chain_id, env):
    exam_client = DBClient(region_name, 'abc_cis_outpatient', 'abc_cis_examination', env, True)
    ob_client = DBClient(region_name, 'ob', 'abc_cis_examination', env, True)
    rows1 = ob_client.fetchall(f"""
    select concat('update v2_examination_sheet set extended_data = \\'{{"inspectInstitutionId": "', e.extend_info ->> '$.supplierIds[0]', '"}}\\' where id = \\'', s.id, '\\';') as stmt
    from v2_examination_sheet s
             inner join abc_cis_goods.v2_goods_extend e
                        on s.chain_id = e.chain_id and s.clinic_id = e.organ_id and s.examination_id = e.goods_id and s.type = 1
    where s.chain_id = '{chain_id}' and s.extended_data is null and e.extend_info is not null and e.extend_info ->> '$.supplierIds[0]' is not null and e.extend_info ->> '$.supplierIds[0]' != '';
    """)
    for row in rows1:
        # print(row['stmt'])
        exam_client.execute(row['stmt'])

    rows2 = ob_client.fetchall(f"""
    select concat('update v2_examination_sheet set extended_data = json_set(extended_data, \\'$.inspectInstitutionId\\', \\'', e.extend_info ->> '$.supplierIds[0]' ,'\\') where id = \\'', s.id, '\\';') as stmt
    from v2_examination_sheet s
             inner join abc_cis_goods.v2_goods_extend e
                        on s.chain_id = e.chain_id and s.clinic_id = e.organ_id and s.examination_id = e.goods_id and s.type = 1
    where s.chain_id = '{chain_id}' and s.extended_data is not null and s.extended_data ->> '$.inspectInstitutionId' is null and e.extend_info is not null and e.extend_info ->> '$.supplierIds[0]' is not null and e.extend_info ->> '$.supplierIds[0]' != '';
    """)
    for row in rows2:
        # print(row['stmt'])
        exam_client.execute(row['stmt'])



def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    run(args.region_name, args.chain_id, args.env)

if __name__ == '__main__':
    main()
