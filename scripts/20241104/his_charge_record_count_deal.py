import argparse
import os
import sys

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.db import DBClient


class UpdateData:
    chain_id = None
    his_charge_wdb_client = None
    basic_db_client = None
    env = None

    def __init__(self, env, region_name, chain_id):
        self.env = env
        self.chain_id = chain_id
        self.region_name = region_name
        self.his_charge_wdb_client = DBClient(self.region_name, 'scrm_hospital', 'abc_his_charge', 'prod', True)
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', 'prod', True)
        # self.his_charge_wdb_client = DBClient(self.region_name, 'test', 'abc_his_charge', 'test', True)
        # self.basic_db_client = DBClient(self.region_name, 'test', 'abc_cis_basic', 'test', True)

    def run(self):

        organHisType = self.basic_db_client.fetchone(
            ''' select his_type from organ where id = '{chainId}' '''.format(chainId=self.chain_id))
        if organHisType:
            if organHisType['his_type'] != 100:
                print("organHisType is not 100")
                return

        patient_order_ids = self.his_charge_wdb_client.fetchall("""
        select distinct patient_order_id from v2_his_charge_settle_transaction_record  where chain_id = '{chainId}'
        """.format(chainId=self.chain_id))

        for patient_order_id in patient_order_ids:
            patient_order_id = patient_order_id['patient_order_id']
            item_records = self.his_charge_wdb_client.fetchall("""
                    select id,charge_type,created from v2_his_charge_settle_transaction_record  where patient_order_id = '{patientOrderId}' order by created
                    """.format(patientOrderId=patient_order_id))

            # item_batch_records = self.his_charge_wdb_client.fetchall("""
            #         select id,transaction_record_id from v2_his_charge_settle_transaction_record_batch_info  where patient_order_id = '{patientOrderId}'
            #         """.format(patientOrderId=self.patient_order_id))

            last_charge_type = None
            last_created = 0
            toZero = False
            list_A = []
            for record in item_records:
                if last_charge_type is None:
                    last_charge_type = record['charge_type']
                    last_created = record['created']
                    continue

                # if record['charge_type'] == last_charge_type & record['created'] == last_created:
                #
                #     continue

                if record['charge_type'] == last_charge_type and record['created'] != last_created:
                    toZero = True

                if record['charge_type'] != last_charge_type:
                    toZero = False

                if record['charge_type'] != last_charge_type and last_charge_type == 0 and record['charge_type'] == 1:
                    toZero = True

                if toZero:
                    list_A.append(record["id"])

            for i in range(0, len(list_A), 100):
                batch = list_A[i:i + 100]

                # 构建用于批量更新的SQL语句
                ids_str = ', '.join(map(str, batch))

                # 执行第一个表的批量更新
                sql1 = f"""
                                    UPDATE v2_his_charge_settle_transaction_record
                                    SET product_unit_count = 0
                                    WHERE id IN ({ids_str});
                                """
                self.his_charge_wdb_client.execute(sql1)
                print(sql1 + "\n")
                # 执行第二个表的批量更新
                sql2 = f"""
                                    UPDATE v2_his_charge_settle_transaction_record_batch_info
                                    SET product_unit_count = 0
                                    WHERE transaction_record_id IN ({ids_str});
                                """
                self.his_charge_wdb_client.execute(sql2)



def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    updateData = UpdateData(args.env, args.region_name, args.chain_id)
    # updateData = UpdateData("args.env", "ShangHai", "ffffffff00000000347ca195a4618000")
    updateData.run()


if __name__ == '__main__':
    main()
