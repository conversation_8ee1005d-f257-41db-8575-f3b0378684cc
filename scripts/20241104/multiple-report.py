"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
import arg<PERSON><PERSON>

def run(region_name, chain_id, env):
    exam_client = DBClient(region_name, 'abc_cis_outpatient', 'abc_cis_examination', env, True)
    exam_client.execute(f"""
    update v2_examination_sheet_report sr inner join v2_examination_sheet s on sr.id = s.id
    set sr.examination_sheet_id = s.id, sr.merge_sheet_id = s.merge_sheet_id
    where sr.chain_id = '{chain_id}' and sr.examination_sheet_id is null and sr.merge_sheet_id is null;
    """)

    ob_client = DBClient(region_name, 'ob', 'abc_cis_examination', env, True)
    rows1 = ob_client.fetchall(f"""
    select concat('update v2_examination_sheet set device_id = ', s.device_model_id, ', device_model_id = ', d.device_model_id, ' where id = \\'', s.id, '\\';') as `stmt`
    from abc_cis_examination.v2_examination_sheet s
             left join abc_cis_goods.v2_goods_examination_device d on s.device_model_id = d.id
    where s.type = 2
      and s.sub_type = 10
      and s.device_model_id is not null and d.id is not null and s.chain_id = '{chain_id}';
    """)
    for row in rows1:
        # print(row['stmt'])
        exam_client.execute(row['stmt'])

    rows2 = ob_client.fetchall(f"""
    select concat('update v2_examination_merge_sheet set device_id = ', s.device_model_id, ', device_model_id = ', d.device_model_id, ' where id = \\'', s.id, '\\';') as `stmt`
    from abc_cis_examination.v2_examination_merge_sheet s
             left join abc_cis_goods.v2_goods_examination_device d on s.device_model_id = d.id
    where s.type = 2
      and s.sub_type = 10
      and s.device_model_id is not null and d.id is not null and s.chain_id = '{chain_id}';
    """)
    for row in rows2:
        # print(row['stmt'])
        exam_client.execute(row['stmt'])


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    run(args.region_name, args.chain_id, args.env)

if __name__ == '__main__':
    main()
