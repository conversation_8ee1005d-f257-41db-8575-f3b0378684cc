#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.rpc import regionRpcHost
import json
import argparse
import requests

default_id = '00000000000000000000000000000000'


# 多库房把以前老的药房类型刷到新的标上
def flushRule(abcRegion, chain_id):
    goods_db_client = DBClient(abcRegion, 'abc_cis_stock', 'abc_cis_goods', 'prod', True)
    basic_db_client = DBClient(abcRegion, 'abc_cis_mixed', 'abc_cis_property', 'prod', True)
    basic_db_client.execute("""update abc_cis_property.v2_property_config_item v2pci inner join abc_cis_basic.organ o on v2pci.v2_scope_id = o.id and o.parent_id = '{chainId}'
    set value = replace(value, '1', '1,16')
    where v2pci.`key` in ('clinicNurseSettings.enterListSettings.itemType', 'clinicNurseSettings.enableExecuteItemType');""".format(
        chainId=chain_id))

    organs = basic_db_client.fetchall(
        ''' select id as clinicId from abc_cis_basic.organ where parent_id = '{chainId}' '''.format(chainId=chain_id))
    for organ in organs:
        clinicId = organ['clinicId']
        pharmacy_list = goods_db_client.fetchall(
            """select a.chain_id ,a.clinic_id,a.no,a.type,a.default_goods_types ,c.bus_support_flag  from v2_goods_pharmacy as a inner join v2_goods_clinic_config as c on a.clinic_id = c.clinic_id where a.chain_id ='{chainId}'  and a.clinic_id = '{clinicId}' and a.is_deleted = 0 ; """.format(
                chainId=chain_id, clinicId=clinicId))
        typeIdToNo = {}
        for pharmacy in pharmacy_list:
            if pharmacy['default_goods_types'] is None:
                continue
            pConfigList = json.loads(pharmacy['default_goods_types'])
            for pCfg in pConfigList['typeList']:
                typeIdToNo[pCfg['typeId']] = pharmacy['no']
            sql = """
                        insert into v2_goods_pharmacy_extend(id, chain_id, clinic_id, goods_id, pharmacy_no, pharmacy_type, position,
                                                     is_deleted, created, created_by, last_modified, last_modified_by)
                select substr(uuid_short(), 5),
                       chain_id,
                       organ_id,
                       goods_id,
                       {pharmacyNo},
                       {pharmacyType}, 
                       position,
                       0,
                       now(),
                       '00000000000000000000000000000000',
                       now(),
                       '00000000000000000000000000000000'
                from v2_goods_extend
                where chain_id = '{chainId}' and organ_id ='{clinicId}' 
                  and position != '';
            """.format(chainId=chain_id, clinicId=pharmacy['clinic_id'], pharmacyNo=pharmacy['no'],
                       pharmacyType=pharmacy['type'])
            goods_db_client.execute(sql)

        clinic_list = goods_db_client.fetchall(
            """select  chain_id,clinic_id,bus_support_flag,his_type from v2_goods_clinic_config where chain_id ='{chainId}' and clinic_id ='{clinicId}'; """.format(
                chainId=chain_id, clinicId=clinicId))
        for clinic in clinic_list:
            sceneType = 1
            newGoodsType = {"typeIdList": [{"typeId": 12}, {"typeId": 16}]}
            pharmacyNo = 0
            if 12 in typeIdToNo and typeIdToNo[12] is not None:
                pharmacyNo = typeIdToNo[12]
            goods_db_client.execute(
                """INSERT INTO v2_goods_pharmacy_rule (id,chain_id,clinic_id, scene_type, goods_type, department, ward_area, pharmacy_type, pharmacy_no,sort,status,is_deleted,inner_flag) values (substr(uuid_short(), 5),'{chainId}','{clinicId}',{sceneType},'{goodsType}','[]',null,{pharmacyType},{pharmacyNo},0,1,0,1)"""
                .format(chainId=clinic['chain_id'], clinicId=clinic['clinic_id'],
                        goodsType=json.dumps(newGoodsType),
                        pharmacyType=0,
                        sceneType=sceneType,
                        pharmacyNo=pharmacyNo));
            newGoodsType = {"typeIdList": [{"typeId": 14}]}
            pharmacyNo = 0
            if 14 in typeIdToNo and typeIdToNo[14] is not None:
                pharmacyNo = typeIdToNo[14]
            goods_db_client.execute(
                """INSERT INTO v2_goods_pharmacy_rule (id,chain_id,clinic_id, scene_type, goods_type, department, ward_area, pharmacy_type, pharmacy_no,sort,status,is_deleted,inner_flag) values (substr(uuid_short(), 5),'{chainId}','{clinicId}',{sceneType},'{goodsType}','[]',null,{pharmacyType},{pharmacyNo},0,1,0,1)"""
                .format(chainId=clinic['chain_id'], clinicId=clinic['clinic_id'],
                        goodsType=json.dumps(newGoodsType),
                        pharmacyType=0,
                        sceneType=sceneType,
                        pharmacyNo=pharmacyNo));
            newGoodsType = {"typeIdList": [{"typeId": 15}]}
            pharmacyNo = 0
            if 15 in typeIdToNo and typeIdToNo[15] is not None:
                pharmacyNo = typeIdToNo[15]
            goods_db_client.execute(
                """INSERT INTO v2_goods_pharmacy_rule (id,chain_id,clinic_id, scene_type, goods_type, department, ward_area, pharmacy_type, pharmacy_no,sort,status,is_deleted,inner_flag) values (substr(uuid_short(), 5),'{chainId}','{clinicId}',{sceneType},'{goodsType}','[]',null,{pharmacyType},{pharmacyNo},0,1,0,1)"""
                .format(chainId=clinic['chain_id'], clinicId=clinic['clinic_id'],
                        goodsType=json.dumps(newGoodsType),
                        pharmacyType=0,
                        sceneType=sceneType,
                        pharmacyNo=pharmacyNo));
            newGoodsType = {"typeIdList": [{"typeId": 17}, {"typeId": 18}, {"typeId": 19}]}
            pharmacyNo = 0
            if 17 in typeIdToNo and typeIdToNo[17] is not None:
                pharmacyNo = typeIdToNo[17]
            goods_db_client.execute(
                """INSERT INTO v2_goods_pharmacy_rule (id,chain_id,clinic_id, scene_type, goods_type, department, ward_area, pharmacy_type, pharmacy_no,sort,status,is_deleted,inner_flag) values (substr(uuid_short(), 5),'{chainId}','{clinicId}',{sceneType},'{goodsType}','[]',null,{pharmacyType},{pharmacyNo},0,1,0,1)"""
                .format(chainId=clinic['chain_id'], clinicId=clinic['clinic_id'],
                        goodsType=json.dumps(newGoodsType),
                        pharmacyType=0,
                        sceneType=sceneType,
                        pharmacyNo=pharmacyNo));
            newGoodsType = {"typeIdList": [{"typeId": 25}, {"typeId": 26}, {"typeId": 27}, {"typeId": 28}]}
            pharmacyNo = 0
            if 25 in typeIdToNo and typeIdToNo[25] is not None:
                pharmacyNo = typeIdToNo[25]
            goods_db_client.execute(
                """INSERT INTO v2_goods_pharmacy_rule (id,chain_id,clinic_id, scene_type, goods_type, department, ward_area, pharmacy_type, pharmacy_no,sort,status,is_deleted,inner_flag) values (substr(uuid_short(), 5),'{chainId}','{clinicId}',{sceneType},'{goodsType}','[]',null,{pharmacyType},{pharmacyNo},0,1,0,1)"""
                .format(chainId=clinic['chain_id'], clinicId=clinic['clinic_id'],
                        goodsType=json.dumps(newGoodsType),
                        pharmacyType=0,
                        sceneType=sceneType,
                        pharmacyNo=pharmacyNo))
            if clinic['bus_support_flag'] & 0x04 != 0:
                newGoodsType = {
                    "typeIdList": [{"typeId": 64}, {"typeId": 65}, {"typeId": 66}, {"typeId": 67}, {"typeId": 68},
                                   {"typeId": 69}]}
                sceneType = 3 if clinic['his_type'] == 100 else 1
                pharmacyNo = 0
                if 64 in typeIdToNo and typeIdToNo[64] is not None:
                    pharmacyNo = typeIdToNo[64]
                if clinic['his_type'] == 100:
                    goods_db_client.execute(
                        """INSERT INTO v2_goods_pharmacy_rule (id,chain_id,clinic_id, scene_type, goods_type, department, ward_area, pharmacy_type, pharmacy_no,sort,status,is_deleted,inner_flag) values (substr(uuid_short(), 5),'{chainId}','{clinicId}',{sceneType},'{goodsType}','[]','[]' ,{pharmacyType},{pharmacyNo},0,1,0,1)"""
                        .format(chainId=clinic['chain_id'], clinicId=clinic['clinic_id'],
                                goodsType=json.dumps(newGoodsType),
                                pharmacyType=0,
                                sceneType=sceneType,
                                pharmacyNo=pharmacyNo))
                else:
                    goods_db_client.execute(
                        """INSERT INTO v2_goods_pharmacy_rule (id,chain_id,clinic_id, scene_type, goods_type, department, ward_area, pharmacy_type, pharmacy_no,sort,status,is_deleted,inner_flag) values (substr(uuid_short(), 5),'{chainId}','{clinicId}',{sceneType},'{goodsType}',null,'[]' ,{pharmacyType},{pharmacyNo},0,1,0,1)"""
                        .format(chainId=clinic['chain_id'], clinicId=clinic['clinic_id'],
                                goodsType=json.dumps(newGoodsType),
                                pharmacyType=0,
                                sceneType=sceneType,
                                pharmacyNo=pharmacyNo))

            if clinic['his_type'] == 100:
                sceneType = 2
                newGoodsType = {"typeIdList": [{"typeId": 12}, {"typeId": 16}]}
                pharmacyNo = 0
                if 12 in typeIdToNo and typeIdToNo[12] is not None:
                    pharmacyNo = typeIdToNo[12]
                goods_db_client.execute(
                    """INSERT INTO v2_goods_pharmacy_rule (id,chain_id,clinic_id, scene_type, goods_type, department, ward_area, pharmacy_type, pharmacy_no,sort,status,is_deleted,inner_flag) values (substr(uuid_short(), 5),'{chainId}','{clinicId}',{sceneType},'{goodsType}',null,'[]',{pharmacyType},{pharmacyNo},0,1,0,1)"""
                    .format(chainId=clinic['chain_id'], clinicId=clinic['clinic_id'],
                            goodsType=json.dumps(newGoodsType),
                            pharmacyType=0,
                            sceneType=sceneType,
                            pharmacyNo=pharmacyNo));
                newGoodsType = {"typeIdList": [{"typeId": 14}]}
                pharmacyNo = 0
                if 14 in typeIdToNo and typeIdToNo[14] is not None:
                    pharmacyNo = typeIdToNo[14]
                goods_db_client.execute(
                    """INSERT INTO v2_goods_pharmacy_rule (id,chain_id,clinic_id, scene_type, goods_type, department, ward_area, pharmacy_type, pharmacy_no,sort,status,is_deleted,inner_flag) values (substr(uuid_short(), 5),'{chainId}','{clinicId}',{sceneType},'{goodsType}',null,'[]',{pharmacyType},{pharmacyNo},0,1,0,1)"""
                    .format(chainId=clinic['chain_id'], clinicId=clinic['clinic_id'],
                            goodsType=json.dumps(newGoodsType),
                            pharmacyType=0,
                            sceneType=sceneType,
                            pharmacyNo=pharmacyNo));
                newGoodsType = {"typeIdList": [{"typeId": 15}]}
                pharmacyNo = 0
                if 15 in typeIdToNo and typeIdToNo[15] is not None:
                    pharmacyNo = typeIdToNo[15]
                goods_db_client.execute(
                    """INSERT INTO v2_goods_pharmacy_rule (id,chain_id,clinic_id, scene_type, goods_type, department, ward_area, pharmacy_type, pharmacy_no,sort,status,is_deleted,inner_flag) values (substr(uuid_short(), 5),'{chainId}','{clinicId}',{sceneType},'{goodsType}',null,'[]',{pharmacyType},{pharmacyNo},0,1,0,1)"""
                    .format(chainId=clinic['chain_id'], clinicId=clinic['clinic_id'],
                            goodsType=json.dumps(newGoodsType),
                            pharmacyType=0,
                            sceneType=sceneType,
                            pharmacyNo=pharmacyNo));
                newGoodsType = {"typeIdList": [{"typeId": 17}, {"typeId": 18}, {"typeId": 19}]}
                pharmacyNo = 0
                if 17 in typeIdToNo and typeIdToNo[17] is not None:
                    pharmacyNo = typeIdToNo[17]
                goods_db_client.execute(
                    """INSERT INTO v2_goods_pharmacy_rule (id,chain_id,clinic_id, scene_type, goods_type, department, ward_area, pharmacy_type, pharmacy_no,sort,status,is_deleted,inner_flag) values (substr(uuid_short(), 5),'{chainId}','{clinicId}',{sceneType},'{goodsType}',null,'[]',{pharmacyType},{pharmacyNo},0,1,0,1)"""
                    .format(chainId=clinic['chain_id'], clinicId=clinic['clinic_id'],
                            goodsType=json.dumps(newGoodsType),
                            pharmacyType=0,
                            sceneType=sceneType,
                            pharmacyNo=pharmacyNo));
                newGoodsType = {"typeIdList": [{"typeId": 25}, {"typeId": 26}, {"typeId": 27}, {"typeId": 28}]}
                pharmacyNo = 0
                if 25 in typeIdToNo and typeIdToNo[25] is not None:
                    pharmacyNo = typeIdToNo[25]
                    goods_db_client.execute(
                        """INSERT INTO v2_goods_pharmacy_rule (id,chain_id,clinic_id, scene_type, goods_type, department, ward_area, pharmacy_type, pharmacy_no,sort,status,is_deleted,inner_flag) values (substr(uuid_short(), 5),'{chainId}','{clinicId}',{sceneType},'{goodsType}',null,'[]',{pharmacyType},{pharmacyNo},0,1,0,1)"""
                        .format(chainId=clinic['chain_id'], clinicId=pharmacy['clinic_id'],
                                goodsType=json.dumps(newGoodsType),
                                pharmacyType=0,
                                sceneType=sceneType,
                                pharmacyNo=pharmacyNo))


def flushPhamacyConfig(abcRegion, chain_id):
    goods_db_client = DBClient(abcRegion, 'abc_cis_stock', 'abc_cis_goods', 'prod', True)
    sql = """
        INSERT INTO v2_goods_pharmacy (id, chain_id, clinic_id, no, name, type, stock_cut_type, sort, status, is_deleted, created_by, created, last_modified_by, last_modified, goods_type, goods_sub_type, goods_cmspec, support_goods_types, default_goods_types, extend_info, external_pharmacy_config, inner_flag, dispense_flag, can_out_default_goods_in_pharmacy, can_out_goods_in_pharmacy)
        select substr(uuid_short(), 5),cfg.chain_id,cfg.clinic_id,0,'本地药房',0,0, 0, 1, 0, 'robins',now(),'robins',now(),
              null, null, null, null,
                '{{"typeList": [{{"name": "西药", "type": 1, "cMSpec": "", "typeId": 12, "subType": 1, "parentId": 1, "isSupportLocal": 1, "isSupportVirtual": 0, "isSupportAirPharmacy": 0}}, {{"name": "中成药", "type": 1, "cMSpec": "", "typeId": 16, "subType": 3, "parentId": 1, "isSupportLocal": 1, "isSupportVirtual": 0, "isSupportAirPharmacy": 0}}, {{"name": "医用材料", "type": 2, "cMSpec": "", "typeId": 17, "subType": 1, "parentId": 2, "isSupportLocal": 1, "isSupportVirtual": 0, "isSupportAirPharmacy": 0}}, {{"name": "后勤材料", "type": 2, "cMSpec": "", "typeId": 18, "subType": 2, "parentId": 2, "isSupportLocal": 1, "isSupportVirtual": 0, "isSupportAirPharmacy": 0}}, {{"name": "固定资产", "type": 2, "cMSpec": "", "typeId": 19, "subType": 3, "parentId": 2, "isSupportLocal": 1, "isSupportVirtual": 0, "isSupportAirPharmacy": 0}}, {{"name": "自制成品", "type": 7, "cMSpec": "", "typeId": 25, "subType": 1, "parentId": 7, "isSupportLocal": 1, "isSupportVirtual": 0, "isSupportAirPharmacy": 0}}, {{"name": "保健药品", "type": 7, "cMSpec": "", "typeId": 26, "subType": 2, "parentId": 7, "isSupportLocal": 1, "isSupportVirtual": 0, "isSupportAirPharmacy": 0}}, {{"name": "保健食品", "type": 7, "cMSpec": "", "typeId": 27, "subType": 3, "parentId": 7, "isSupportLocal": 1, "isSupportVirtual": 0, "isSupportAirPharmacy": 0}}, {{"name": "其他商品", "type": 7, "cMSpec": "", "typeId": 28, "subType": 4, "parentId": 7, "isSupportLocal": 1, "isSupportVirtual": 0, "isSupportAirPharmacy": 0}}]}}',
            null, null, 1, 0, 1, 0
               from v2_goods_clinic_config as cfg left join  v2_goods_pharmacy v2gp on cfg.chain_id = v2gp.chain_id and cfg.clinic_id = v2gp.clinic_id
            where v2gp.id is null and cfg.chain_id ='{chainId}'
    """
    goods_db_client.execute(sql.format(chainId=chain_id))


# def flushReturnOutOrderSupplier(abcRegion, chain_id):
#     goods_db_client = DBClient(abcRegion, 'abc_cis_stock', 'abc_cis_goods', 'prod', True)
#     sql = """
#         update v2_goods_stock_in_order as a inner join v2_goods_stock_in_order as b
#         on a.id = b.in_order_id and a.chain_id = b.chain_id and a.chain_id = '{chainId}' and
#         a.type != 10 and b.type = 10
#         set b.supplier_id = a.supplier_id
#         where a.chain_id = '{chainId}'
#         and b.supplier_id is  null
#     """
#     goods_db_client.execute(sql.format(chainId=chain_id))
#     sql = """
#        update v2_goods_chain_config set stock_reception_chain_review = 1 where chain_id = '{chainId}' and stock_out_chain_review = 1;
#     """
#     goods_db_client.execute(sql.format(chainId=chain_id))
#     sql = """
#         update v2_goods_stock_out_order set status = 5 where type = 1 and status in (0, 1) and chain_id = '{chainId}';
#     """
#     goods_db_client.execute(sql.format(chainId=chain_id))
#     sql = """
#         update v2_goods_stock_in_order as a inner join v2_goods_stock_in_order as b
#         on a.id = b.in_order_id and a.chain_id = b.chain_id and a.chain_id = '{chainId}' and
#         a.type != 10 and b.type = 10
#         set b.supplier_id = a.supplier_id
#         where a.chain_id = '{chainId}'
#         and b.supplier_id is  null
#     """
#     goods_db_client.execute(sql.format(chainId=chain_id))
#     sql = """
#         update v2_goods as g inner join v2_goods_stock as gs on
#             g.id = gs.goods_id and g.organ_id = gs.chain_id and g.type = 24
#             set gs.spu_goods_id = g.spu_goods_id
#             where g.organ_id = '{chainId}'
#     """
#     goods_db_client.execute(sql.format(chainId=chain_id))
#     goods_db_client.execute(
#         """update v2_goods_stock_out set application_package_count = package_count, application_piece_count = piece_count where type = 1 and  chain_id ='{chainId}';""".format(
#             chainId=chain_id))


def flushClinicConfig(abcRegion, chain_id):
    goods_db_client = DBClient(abcRegion, 'abc_cis_stock', 'abc_cis_goods', 'prod', True)
    adb_db_client = DBClient(abcRegion, 'ods', 'abc_cis_goods', 'prod', True)

    # 检验单-跟设备型号的device_type绑定
    exam_sql = """
        SELECT CONCAT('insert into v2_goods_clinic_config(id, chain_id, clinic_id, goods_purchase_cycle_days, stock_days_of_day_avg_sell, disable_expired_goods, disable_no_stock_goods, stock_warn_goods_turnover_days, stock_warn_goods_will_expired_month, created,created_by, last_modified, last_modified_by, 
        node_type, view_mode, his_type, need_summery_goods_stat, open_pharmacy_flag, virtual_open_pharmacy_flag, lock_configs, bus_support_flag) values(substr(uuid_short(), 5),' ,
            '\''',o.parent_id,'\'',',
                      '\''',o.id,'\'',',
                        IF(o.bus_support_flag & 0x04,
                           '\''[{{"days": 15, "name": "西药", "typeId": 12}}, {{"days": 15, "name": "中成药", "typeId": 16}}, {{"days": 15, "name": "中药饮片", "typeId": 14}}, {{"days": 15, "name": "中药颗粒", "typeId": 15}}, {{"days": 15, "name": "医用材料", "typeId": 17}}, {{"days": 15, "name": "后勤材料", "typeId": 18}}, {{"days": 15, "name": "固定资产", "typeId": 19}}, {{"days": 15, "name": "自制成品", "typeId": 25}}, {{"days": 15, "name": "保健药品", "typeId": 26}}, {{"days": 15, "name": "保健食品", "typeId": 27}}, {{"days": 15, "name": "其他商品", "typeId": 28}}]\'',',
                           '\''[{{"days": 15, "name": "西药", "typeId": 12}}, {{"days": 15, "name": "中成药", "typeId": 16}}, {{"days": 15, "name": "中药饮片", "typeId": 14}}, {{"days": 15, "name": "中药颗粒", "typeId": 15}}, {{"days": 15, "name": "医用材料", "typeId": 17}}, {{"days": 15, "name": "后勤材料", "typeId": 18}}, {{"days": 15, "name": "固定资产", "typeId": 19}}, {{"days": 15, "name": "自制成品", "typeId": 25}}, {{"days": 15, "name": "保健药品", "typeId": 26}}, {{"days": 15, "name": "保健食品", "typeId": 27}}, {{"days": 15, "name": "其他商品", "typeId": 28}},{{ "days": 15, "name": "镜片", "typeId": 64 }}, {{ "days": 15, "name": "镜架", "typeId": 65 }}, {{ "days": 15, "name": "角膜塑形镜", "typeId": 66 }}, {{ "days": 15, "name": "软性亲水镜", "typeId": 67 }}, {{ "days": 15, "name": "硬性透氧镜", "typeId": 68 }}, {{ "days": 15, "name": "太阳镜", "typeId": 69 }}]\'','
                            ),
            '30, 0, 0, 7, 3,',
                      'now(),\''00000000000000000000000000000000\'',now(),\''00000000000000000000000000000000\'',',
                          '\''',o.node_type,'\'',',
                          '\''',o.view_mode,'\''',',
                          '\'',o.his_type,'\'',',
                              '0,10,0,\''[{{"lockFlag": 0, "sceneType": 0}}]\'',',
                          '\''',o.bus_support_flag,'\'')'
         ) as sqlItem
         from abc_cis_basic.organ as o left join abc_cis_goods.v2_goods_clinic_config as c on o.id = c.clinic_id
         where  c.id is null and o.status = 1 and o.parent_id='{chainId}';
    """.format(chainId=chain_id)
    exam_update_sql_res = adb_db_client.fetchall(exam_sql)
    for stmt in exam_update_sql_res:
        sql = stmt['sqlItem']
        if sql is None:
            continue
        goods_db_client.execute(sql)


def flushGoodsHospitalFeeType(abcRegion, chain_id):
    goods_db_client = DBClient(abcRegion, 'abc_cis_stock', 'abc_cis_goods', 'prod', True)
    # 刷费用类型和费用项id
    # 5 挂号，8 套餐，9 在线问诊，12 西药，14/15 中药 16 中成药 17 医用材料 18 后勤材料  20 检验 21 检查 22 治疗 23 理疗  25 自制成品 26 保健药品 27 保健食品 28 其他商品 56 护理 58 床位费 59 诊疗费用
    # sql = """
    #     update v2_goods
    #     set v2_goods.fee_compose_type = 0,
    #         v2_goods.fee_type_id      = type_id
    #     where v2_goods.type_id in (5, 9, 12,  16, 20, 21,  25, 26, 27, 28, 56, 58, 59) and v2_goods.organ_id = '{chainId}';
    # """.format(chainId=chain_id)
    # goods_db_client.execute(sql)
    # sql = """
    #     update v2_goods
    #     set v2_goods.fee_compose_type = 0,
    #         v2_goods.fee_type_id      = 22
    #     where v2_goods.type_id in (22, 23) and v2_goods.organ_id = '{chainId}';
    # """.format(chainId=chain_id)
    # goods_db_client.execute(sql)
    # sql = """
    #     update v2_goods
    #     set v2_goods.fee_compose_type = 0,
    #         v2_goods.fee_type_id      = 13
    #     where v2_goods.type_id in (14, 15) and v2_goods.organ_id = '{chainId}';
    # """.format(chainId=chain_id)
    # goods_db_client.execute(sql)
    # sql = """
    #     update v2_goods
    #     set v2_goods.fee_compose_type = 0,
    #         v2_goods.fee_type_id      = 1003
    #     where v2_goods.type in (19) and v2_goods.organ_id = '{chainId}';
    # """.format(chainId=chain_id)
    # goods_db_client.execute(sql)
    # sql = """
    #     update v2_goods
    #     set v2_goods.fee_compose_type = 0,
    #         v2_goods.fee_type_id      = 1004
    #     where v2_goods.type_id in (17, 18,64,65,66,67,68,69) and v2_goods.organ_id = '{chainId}';
    # """.format(chainId=chain_id)
    # goods_db_client.execute(sql)
    sql = """
        update  v2_goods_custom_type set type_id = 33 where type_id = 59 and chain_id = '{chainId}';
    """.format(chainId=chain_id)
    goods_db_client.execute(sql)
    sql = """
        update v2_goods set type = 19 ,sub_type = 0 ,type_id =33,fee_type_id =1003 where type = 22 and organ_id = '{chainId}';
    """.format(chainId=chain_id)
    goods_db_client.execute(sql)
    # sql = """
    #     update v2_goods
    #     set v2_goods.fee_compose_type = 10,
    #         v2_goods.fee_type_id      = type_id
    #     where type_id in (8) and v2_goods.organ_id ='{chainId}';
    # """.format(chainId=chain_id)
    # goods_db_client.execute(sql)
    # sql = """
    #     update v2_goods_stock_locking
    #     set lock_id = id
    #     where chain_id = '{chainId}';
    # """.format(chainId=chain_id)
    # goods_db_client.execute(sql)
    sql = """
        
        insert into v2_goods_compose_price_opt(id, chain_id,organ_id,goods_id,
                                               parent_goods_id,
                                               compose_price,
                                               compose_package_price,
                                               compose_piece_price,
                                               created_by,
                                               created,
                                               last_modified_by,
                                               last_modified,
                                               compose_fraction_price)
        select substr(uuid_short(), 5),
               '{chainId}',
               organ_id,
               goods_id,
               parent_goods_id,
               compose_price,
               compose_package_price,
               compose_piece_price,
               created_user_id,
               created_date,
               last_modified_user_id,
               now(),
               compose_fraction_price
        from v2_goods_compose_price where parent_goods_id in (select id from v2_goods where type in(11,3) and v2_goods.organ_id = '{chainId}');
    """.format(chainId=chain_id)
    goods_db_client.execute(sql)
    goods_db_client.execute(
        """update v2_goods set fee_type_id = 58  where custom_type_id = 200 and  organ_id ='{chainId}';""".format(
            chainId=chain_id))
    goods_db_client.execute(
        """update v2_goods set fee_type_id = 56  where custom_type_id = 100 and  organ_id ='{chainId}';""".format(
            chainId=chain_id))


def flushGoodsClinicFeeType(abcRegion, chain_id):
    goods_db_client = DBClient(abcRegion, 'abc_cis_stock', 'abc_cis_goods', 'prod', True)
    # 刷费用类型和费用项id
    # 5 挂号，8 套餐，9 在线问诊，12 西药，14/15 中药 16 中成药 17 医用材料 18 后勤材料  20 检验 21 检查 22 治疗 23 理疗  25 自制成品 26 保健药品 27 保健食品 28 其他商品 56 护理 58 床位费 59 诊疗费用
    # sql = """
    #     update v2_goods
    #     set v2_goods.fee_compose_type = 0,
    #         v2_goods.fee_type_id      = type_id
    #     where v2_goods.type_id in (5, 9, 12,  16, 20, 21,  25, 26, 27, 28, 56, 58, 59) and v2_goods.organ_id = '{chainId}';
    # """.format(chainId=chain_id)
    # goods_db_client.execute(sql)
    # sql = """
    #     update v2_goods
    #     set v2_goods.fee_compose_type = 0,
    #         v2_goods.fee_type_id      = 22
    #     where v2_goods.type_id in (22, 23) and v2_goods.organ_id = '{chainId}';
    # """.format(chainId=chain_id)
    # goods_db_client.execute(sql)
    # sql = """
    #     update v2_goods
    #     set v2_goods.fee_compose_type = 0,
    #         v2_goods.fee_type_id      = 13
    #     where v2_goods.type_id in (14, 15) and v2_goods.organ_id = '{chainId}';
    # """.format(chainId=chain_id)
    # goods_db_client.execute(sql)
    # sql = """
    #     update v2_goods
    #     set v2_goods.fee_compose_type = 0,
    #         v2_goods.fee_type_id      = 1003
    #     where v2_goods.type in (19) and v2_goods.organ_id = '{chainId}';
    # """.format(chainId=chain_id)
    # goods_db_client.execute(sql)
    # sql = """
    #     update v2_goods
    #     set v2_goods.fee_compose_type = 0,
    #         v2_goods.fee_type_id      = 1004
    #     where v2_goods.type_id in (17, 18,64,65,66,67,68,69) and v2_goods.organ_id = '{chainId}';
    # """.format(chainId=chain_id)
    # goods_db_client.execute(sql)
    # sql = """
    #     update v2_goods
    #     set v2_goods.fee_compose_type = 10,
    #         v2_goods.fee_type_id      = type_id
    #     where type_id in (8) and v2_goods.organ_id ='{chainId}';
    # """.format(chainId=chain_id)
    # goods_db_client.execute(sql)
    # sql = """
    #     update v2_goods_stock_locking
    #     set lock_id = id
    #     where chain_id = '{chainId}';
    # """.format(chainId=chain_id)
    # goods_db_client.execute(sql)
    sql = """

        insert into v2_goods_compose_price_opt(id, chain_id,organ_id,goods_id,
                                               parent_goods_id,
                                               compose_price,
                                               compose_package_price,
                                               compose_piece_price,
                                               created_by,
                                               created,
                                               last_modified_by,
                                               last_modified,
                                               compose_fraction_price)
        select substr(uuid_short(), 5),
               '{chainId}',
               organ_id,
               goods_id,
               parent_goods_id,
               compose_price,
               compose_package_price,
               compose_piece_price,
               created_user_id,
               created_date,
               last_modified_user_id,
               now(),
               compose_fraction_price
        from v2_goods_compose_price where parent_goods_id in (select id from v2_goods where type in(11,3) and v2_goods.organ_id = '{chainId}');
    """.format(chainId=chain_id)
    goods_db_client.execute(sql)


# def flushMessage(abcRegion, chain_id):
#     message_client = DBClient(abcRegion, 'abc_cis_account_base', 'abc_cis_message', 'prod', True)
#     message_client.execute("""update abc_cis_message.v2_message_sms_sign a
#     inner join abc_cis_message.v2_message_sms_sign b on a.chain_id = b.chain_id and b.service_provider = 1 and b.status = 1
#         set a.xw_accounts = b.xw_accounts
#         where a.service_provider = 0
#           and a.status = 1
#         and a.chain_id ='{chainId}'; """.format(chainId=chain_id))
#
#     message_client.execute("""
#         update abc_cis_message.v2_message_sms_sign
#         set status = 99
#         where service_provider = 1
#           and status = 1
#         and chain_id ='{chainId}';""".format(chainId=chain_id))


def updateReg(abcRegion, chain_id):
    reg_db_client = DBClient(abcRegion, 'abc_cis_outpatient', 'abc_cis_registration', 'prod', True)
    ods_db_client = DBClient(abcRegion, 'ods', 'abc_cis_basic', 'prod', True)
    sql_query = """select concat(
               'INSERT IGNORE INTO v2_registration_fee_product (id, chain_id, clinic_id, name, fee_type_id,reg_unit_price, reg_cost_unit_price, sort,disable_delete, is_deleted, deleted_millis, created,created_by, last_modified, last_modified_by) values (substr(uuid_short(), 5),\''',
               parent_id,
               '\'',\''',
               id,
               '\'',\''普通门诊挂号费\'',5,0,0,0,1,0,0,now(),\''00000000000000000000000000000000\'', now(),\''00000000000000000000000000000000\'');'
           ) as sqlItem
        from abc_cis_basic.organ
        where his_type = 100
          and node_type = 2
          and parent_id = '{chainId}';""".format(chainId=chain_id)

    exam_update_sql_res = ods_db_client.fetchall(sql_query)
    for stmt in exam_update_sql_res:
        sql = stmt['sqlItem']
        if sql is None:
            continue
        reg_db_client.execute(sql)

    # reg_db_client.execute("""update v2_registration_employee_registration_fee v2rerf inner join v2_registration_fee_product v2rfp
    #         on v2rerf.clinic_id = v2rfp.clinic_id and v2rfp.disable_delete = 1
    #     set v2rerf.fee_product_id           = ifnull(v2rerf.fee_product_id, v2rfp.id),
    #         v2rerf.revisited_fee_product_id =ifnull(v2rerf.revisited_fee_product_id, v2rfp.id)
    #     where v2rerf.chain_id = '{chainId}' and  (v2rerf.fee_product_id is null
    #        or v2rerf.revisited_fee_product_id is null);""".format(chainId=chain_id))


def updateAdvice(abcRegion, chain_id):
    advice_db_client = DBClient(abcRegion, 'scrm_hospital', 'abc_his_advice', 'prod', True)
    sql = """update v1_advice_rule
            set print_content_summary = case
                                            when type in (0, 10, 20)
                                                then '[{{"type":0,"typeDisplayName":"执行单","isPrinted":0}}, {{"type":3,"typeDisplayName":"口服药单","isPrinted":0}}]'
                                            when type in (50, 60, 70)
                                                then '[{{"type":0,"typeDisplayName":"执行单","isPrinted":0}}, {{"type":2,"typeDisplayName":"治疗护理单","isPrinted":0}}]'
                                            when type = 40
                                                then '[{{"type":0,"typeDisplayName":"执行单","isPrinted":0}}, {{"type":6,"typeDisplayName":"检验申请单","isPrinted":0}}, {{"type":7,"typeDisplayName":"样本条码","isPrinted":0}}]'
                                            when type = 30
                                                then '[{{"type":0,"typeDisplayName":"执行单","isPrinted":0}}, {{"type":5,"typeDisplayName":"检查申请单","isPrinted":0}}]'
                end
            where chain_id = '{chainId}'
              and print_content_summary is null;
            """.format(chainId=chain_id)
    advice_db_client.execute(sql)

    # advice_db_client.execute("""update v1_advice_execute v1ae inner join v1_advice_rule v1ar on v1ae.chain_id = v1ar.chain_id and v1ae.advice_rule_id = v1ar.id
    #     set v1ae.print_content_summary = v1ar.print_content_summary,
    #         v1ae.print_status          = if(v1ar.print_content_summary is null, -1, 0)
    #     where v1ae.chain_id = '{chainId}'
    #       and v1ae.print_status is null;""".format(chainId=chain_id))


# def updateCharge(abcRegion, chain_id):
#     try:
#         charge_dbcli = DBClient(abcRegion, 'abc_cis_charge', 'abc_cis_charge', 'prod', True)
#         charge_dbcli.execute("""update abc_cis_charge.v2_charge_medicare_limit_price_type a
#             set limit_detail = '[
#               {{
#                 "minPriceLimit": 0,
#                 "maxPriceLimit": null,
#                 "limitRate": ""
#               }}
#             ]',
#                 limit_detail = JSON_SET(limit_detail, '$[0].limitRate', a.limit_rate) where chain_id ='{}' ;
#                 """.format(chainId=chain_id))
#     except Exception as e:
#         print(e)
#         return


def rpc_for_hospital(abcRegion,chain_id):
    requests.get(
        """http://{rpcHost}/rpc/v3/goods/jenkins/internal-install/fee-types?chainId={chainId}""".format(chainId = chain_id,rpcHost=regionRpcHost(abcRegion)))


def run(abcRegion, chain_id):
    flushClinicConfig(abcRegion, chain_id)
    flushPhamacyConfig(abcRegion, chain_id)
    flushRule(abcRegion, chain_id)
    basic_db_client = DBClient(abcRegion, 'abc_cis_mixed', 'abc_cis_basic', 'prod', True)
    organHisType = basic_db_client.fetchone(
        ''' select his_type from organ where id = '{chainId}' '''.format(chainId=chain_id))
    if organHisType:
        if organHisType['his_type'] == 100:
            flushGoodsHospitalFeeType(abcRegion,chain_id)
            rpc_for_hospital(abcRegion,chain_id)
        else:
            flushGoodsClinicFeeType(abcRegion,chain_id)
    requests.put("""http://{rpcHost}/rpc/v3/goods/jenkins/stock-goods/out-order-to-in-order?chainId={chainId}""".format(
        chainId = chain_id,rpcHost=regionRpcHost(abcRegion)))
    # flushReturnOutOrderSupplier(abcRegion, chain_id)
    # flushMessage(abcRegion,chain_id)
    # requests.get("""http://{rpcHost}/rpc/v3/goods/jenkins/clean-chain-redis-cache?chainId={chainId}""".format(chainId = chain_id,rpcHost=regionRpcHost(abcRegion)))

    updateReg(abcRegion, chain_id)
    updateAdvice(abcRegion, chain_id)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.region_name, args.chain_id)


if __name__ == '__main__':
    main()
