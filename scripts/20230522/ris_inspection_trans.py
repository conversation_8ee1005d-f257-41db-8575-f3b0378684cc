#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
import argparse

default_id = '00000000000000000000000000000000'


def ris_inspection_trans(abcRegion,chain_id):
    basic_db_client = DBClient(abcRegion,'abc_cis_mixed', 'abc_cis_basic', 'prod', True)
    exam_db_client = DBClient(abcRegion,'abc_cis_outpatient', 'abc_cis_examination', 'prod', True)
    goods_db_client = DBClient(abcRegion,'abc_cis_stock', 'abc_cis_goods', 'prod', True)

    organHisType = basic_db_client.fetchone(
        ''' select his_type from organ where id = '{chainId}' '''.format(chainId=chain_id))
    if organHisType:
        if organHisType['his_type'] == 2 or organHisType['his_type'] == 100:
            print("organHisType is 2 or 100")
            return

    # 普通检查刷成ris检查、device_type、biz_extensions、extend_spec、清空绑定的设备id、未知、CR、骨密度、视光类都刷到其他上
    goods_db_client.execute("""
    update v2_goods g left join v2_goods_examination_device d on g.biz_relevant_id = d.id
    set g.biz_extensions = json_set(g.biz_extensions, '$.itemCategory', cast(if(d.device_type is null or d.device_type = 0 or d.device_type = 3 or d.device_type = 6 or d.device_type = 7, 13, d.device_type) as char)),
        g.device_type    = if(d.device_type is null or d.device_type = 0 or d.device_type = 3 or d.device_type = 6 or d.device_type = 7, 13, d.device_type),
        g.extend_spec     = '10',
        g.biz_relevant_id = null,
        g.last_modified_user_id = '{defaultId}',
        g.last_modified_date = now()
    where g.organ_id = '{chainId}'
      and g.type = 3
      and g.sub_type = 2
      and (g.extend_spec is null or g.extend_spec = '0' or g.extend_spec = '')
      and g.biz_extensions is not null
      and json_type(g.biz_extensions) != 'NULL'
      and g.status = 1;
    """.format(chainId=chain_id, defaultId=default_id))

    goods_db_client.execute("""
    update v2_goods g left join v2_goods_examination_device d on g.biz_relevant_id = d.id
    set g.biz_extensions = json_object('itemCategory', cast(if(d.device_type is null or d.device_type = 0 or d.device_type = 3 or d.device_type = 7, 13, d.device_type) as char)),
        g.device_type    = if(d.device_type is null or d.device_type = 0 or d.device_type = 3 or d.device_type = 6 or d.device_type = 7, 13, d.device_type),
        g.extend_spec     = '10',
        g.biz_relevant_id = null,
        g.last_modified_user_id = '{defaultId}',
        g.last_modified_date = now()
    where g.organ_id = '{chainId}'
      and g.type = 3
      and g.sub_type = 2
      and (g.extend_spec is null or g.extend_spec = '0' or g.extend_spec = '')
      and (g.biz_extensions is null or json_type(g.biz_extensions) = 'NULL')
      and g.status = 1;
    """.format(chainId=chain_id, defaultId=default_id))

    # 删除老设备
    goods_db_client.execute("""
    update v2_goods_examination_device
    set status = 99, last_modified_by = '{defaultId}', last_modified = now()
    where chain_id = '{chainId}'
      and goods_type = 3
      and goods_sub_type = 2
      and status = 10
      and device_model_id is null
      and inner_flag = 0;
    """.format(chainId=chain_id, defaultId=default_id))

    # 检查报告sub_type、device_type 刷成ris
    exam_db_client.execute("""
    update v2_examination_sheet
    set sub_type = 10, device_type = if(device_type is null or device_type = 0 or device_type = 3 or device_type = 6 or device_type = 7, 13, device_type), device_model_id = null, last_modified_by = '{defaultId}', last_modified = now()
    where chain_id = '{chainId}' 
      and type = 2
      and sub_type = 0;
    """.format(chainId=chain_id, defaultId=default_id))

    exam_db_client.execute("""
    update v2_examination_merge_sheet
    set sub_type = 10,  device_type = if(device_type is null or device_type = 0 or device_type = 3 or device_type = 6 or device_type = 7, 13, device_type), device_model_id = null, last_modified_by = '{defaultId}', last_modified = now()
    where chain_id = '{chainId}' 
      and type = 2
      and sub_type = 0;
    """.format(chainId=chain_id, defaultId=default_id))


def run(abcRegion,chain_id):
    ris_inspection_trans(abcRegion,chain_id)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.region_name, args.chain_id)


if __name__ == '__main__':
    main()
