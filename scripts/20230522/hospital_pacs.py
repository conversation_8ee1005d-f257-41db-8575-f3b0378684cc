#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db
import argparse

default_id = '00000000000000000000000000000000'


def updateDeviceType(chain_id):
    exam_db_client = db.DBClient('abc_cis_outpatient', 'abc_cis_examination')
    goods_db_client = db.DBClient('abc_cis_stock', 'abc_cis_goods')
    adb_db_client = db.DBClient('adb', 'abc_cis_examination')

    # 检验单-跟设备型号的device_type绑定
    exam_sql = """
    select CONCAT('update v2_examination_sheet set device_type = ', dm.device_type, ' where device_model_id = ', '"', device_model_id, '"', ' and type = 1 and chain_id = ', '"', s.chain_id, '"', ';') as `sql`
    from v2_examination_sheet s
             inner join abc_cis_goods.v2_goods_examination_device_model dm on s.device_model_id = dm.id
    where s.type = 1 and s.chain_id = '{}'
    group by device_model_id;
    """.format(chain_id)
    exam_update_sql_res = adb_db_client.fetchall(exam_sql)
    for stmt in exam_update_sql_res:
        sql = stmt['sql']
        if sql is None:
            continue
        exam_db_client.execute(sql)

    # 检验单合并-跟设备型号的device_type绑定
    exam_merge_sql = """
    select CONCAT('update v2_examination_merge_sheet set device_type = ', dm.device_type, ' where device_model_id = ', '"', device_model_id, '"', ' and type = 1 and chain_id = ', '"', ms.chain_id, '"', ';') as `sql`
    from v2_examination_merge_sheet ms
             inner join abc_cis_goods.v2_goods_examination_device_model dm on ms.device_model_id = dm.id
    where ms.type = 1 and ms.chain_id = '{}'
    group by device_model_id;
    """.format(chain_id)
    exam_merge_update_sql_res = adb_db_client.fetchall(exam_merge_sql)
    for stmt in exam_merge_update_sql_res:
        sql = stmt['sql']
        if sql is None:
            continue
        exam_db_client.execute(sql)

    # 普通检查-跟设备的device_type绑定
    normal_inspect_sql = """
    select CONCAT('update v2_examination_sheet set device_type = ', d.device_type, ' where device_model_id = ', '"', s.device_model_id, '"', ' and type = 2 and sub_type = 0 and chain_id = ', '"', s.chain_id, '"', ';') as `sql`
    from v2_examination_sheet s
             inner join abc_cis_goods.v2_goods_examination_device d on s.device_model_id = d.id
    where s.type = 2 and s.chain_id = '{}'
      and sub_type = 0
    group by s.device_model_id;
    """.format(chain_id)
    inspect_update_sql_res = adb_db_client.fetchall(normal_inspect_sql)
    for stmt in inspect_update_sql_res:
        sql = stmt['sql']
        if sql is None:
            continue
        exam_db_client.execute(sql)

    # ris检查-跟goods绑定
    ris_inspect_sql = """
    select CONCAT('update v2_examination_sheet set device_type = ', g.device_type, ' where examination_id = ', '"', s.examination_id, '"', ' and type = 2 and sub_type = 10 and chain_id = ', '"', s.chain_id, '"', ';') as `sql`
    from v2_examination_sheet s
             inner join abc_cis_goods.v2_goods g on s.examination_id = g.id
    where s.type = 2 and s.chain_id = '{}'
      and s.sub_type = 10
    group by s.examination_id;
    """.format(chain_id)
    ris_inspect_update_sql_res = adb_db_client.fetchall(ris_inspect_sql)
    for stmt in ris_inspect_update_sql_res:
        sql = stmt['sql']
        if sql is None:
            continue
        exam_db_client.execute(sql)

    # 眼科检查-默认为未知设备
    exam_db_client.execute("""
        update v2_examination_sheet set device_type = 0 where type = 2 and sub_type = 20 and chain_id = '{}';
        """.format(chain_id))
    # goods上的device_type修正
    goods_db_client.execute("""
    update v2_goods set device_type = 0 where type = 3 and sub_type = 2 and extend_spec = '20' and organ_id = '{}';
    """.format(chain_id))


def run(chain_id):
    updateDeviceType(chain_id)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)


if __name__ == '__main__':
    main()
