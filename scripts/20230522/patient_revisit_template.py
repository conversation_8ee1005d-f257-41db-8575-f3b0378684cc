#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db
import argparse

default_id = '00000000000000000000000000000000'


def updateInVoiceType(chain_id):
    patient_db_client = db.DBClient('abc_cis_account_base', 'abc_cis_patient')
    patient_db_client.execute("""update abc_cis_patient.v2_patient_revisit_template
set tasks = JSON_ARRAY(JSON_OBJECT('target', content))
where content != ''
  and content is not null
  and tasks is null
and chain_id ='{chainId}';""".format(chainId=chain_id))


def run(chain_id):
    updateInVoiceType(chain_id)
    # rsp = requests.post('http://pre.rpc.abczs.cn/rpc/charges/jenkins/owe/refresh-parted-paid-owe-sheet-record-detail',
    #                     json={'chainIds': chain_id})
    # print rsp.content


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)


if __name__ == '__main__':
    main()
