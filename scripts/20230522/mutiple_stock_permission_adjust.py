#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
import argparse

default_id = '00000000000000000000000000000000'


def update_permission(abcRegion, chain_id):
    basic_db_client = DBClient(abcRegion, 'abc_cis_mixed', 'abc_cis_basic', 'prod', True)

    # 【医院管家】有管理采购权限：赋予供应商采购和线下采集的权限
    basic_db_client.execute("""
    update clinic_employee a
    inner join organ b on a.clinic_id = b.id and b.his_type = 100
    set a.module_ids = concat(a.module_ids, ',', '51902,51903'), a.last_modified_by = '{defaultId}', a.last_modified = now()
    where find_in_set(102, module_ids) # 采购管理
      and !find_in_set(0, module_ids)  # 本身不是超管;
      and length(a.module_ids) > 0     # 权限不为空的
      and b.parent_id = '{chainId}';
    """.format(chainId=chain_id, defaultId=default_id))

    # 【医院管家有库存权限具有品种档案的权限】
    basic_db_client.execute("""
    update clinic_employee a
        inner join organ b on a.clinic_id = b.id and b.his_type = 100
    set a.module_ids = concat(a.module_ids, ',', 51901), a.last_modified_by = '{defaultId}', a.last_modified = now()
    where find_in_set(8, module_ids)
      and !find_in_set(51901, module_ids) # 本身没有品种档案权限
      and !find_in_set(0, module_ids) # 本身不是超管;
      and length(a.module_ids) > 0    # 权限不为空的
      and b.parent_id = '{chainId}';
    """.format(chainId=chain_id, defaultId=default_id))

    # 【医院管家有库存权限具有供应商】
    basic_db_client.execute("""
    update clinic_employee a
    inner join organ b on a.clinic_id = b.id and b.his_type = 100
set a.module_ids = concat(a.module_ids, ',', 107), a.last_modified_by = '{defaultId}', a.last_modified = now()
where find_in_set(8, module_ids)
  and !find_in_set(107, module_ids) # 本身没有供应商权限
  and !find_in_set(0, module_ids) # 本身不是超管;
  and length(a.module_ids) > 0    # 权限不为空的
  and b.parent_id = '{chainId}';

    """.format(chainId=chain_id, defaultId=default_id))

    basic_db_client.execute("""
    update clinic_employee a
    inner join organ b on a.clinic_id = b.id and b.his_type = 100
set a.module_ids = concat(a.module_ids, ',', 108), a.last_modified_by = '{defaultId}', a.last_modified = now()
where find_in_set(8, module_ids)
  and !find_in_set(108, module_ids) # 本身没有结算申请权限
  and !find_in_set(0, module_ids) # 本身不是超管;
  and length(a.module_ids) > 0    # 权限不为空的
  and b.parent_id = '{chainId}';

    """.format(chainId=chain_id, defaultId=default_id))

    basic_db_client.execute("""
    update clinic_employee a
    inner join organ b on a.clinic_id = b.id and b.his_type = 100
set a.module_ids = concat(a.module_ids, ',', 109), a.last_modified_by = '{defaultId}', a.last_modified = now()
where find_in_set(8, module_ids)
  and !find_in_set(109, module_ids) # 本身没有结算审核权限
  and !find_in_set(0, module_ids) # 本身不是超管;
  and length(a.module_ids) > 0    # 权限不为空的
  and b.parent_id = '{chainId}';
    """.format(chainId=chain_id, defaultId=default_id))

    # 【医院管家】有出库权限的人有领用、科室消耗、报损、其他出库4个权限
    basic_db_client.execute("""
    update clinic_employee a
    inner join organ b on a.clinic_id = b.id and b.his_type = 100
    set a.module_ids = concat(a.module_ids, ',', '51211,51212,51213,51214'), a.last_modified_by = '{defaultId}', a.last_modified = now()
    where find_in_set(104, module_ids)
      and !find_in_set(8, module_ids) # 本身没有库存管理权限
      and !find_in_set(0, module_ids) # 本身不是超管;
      and length(a.module_ids) > 0    # 权限不为空的
      and b.parent_id = '{chainId}';
    """.format(chainId=chain_id, defaultId=default_id))

    # 【医院管家】有耗材/药品的人员：有库存和品种档案的权限
    basic_db_client.execute("""
    update clinic_employee a
    inner join organ b on a.clinic_id = b.id and b.his_type = 100
    set a.module_ids = concat(a.module_ids, ',', '51209,51901'), a.last_modified_by = '{defaultId}', a.last_modified = now()
    where find_in_set(101, module_ids)
      and !find_in_set(8, module_ids) # 本身没有库存管理权限
      and !find_in_set(0, module_ids) # 本身不是超管;
      and length(a.module_ids) > 0    # 权限不为空的
      and b.parent_id = '{chainId}';
    """.format(chainId=chain_id, defaultId=default_id))

    # 【医院管家+诊所管家】有入库权限的人：赋予采购入库权限
    basic_db_client.execute("""
    update clinic_employee a
    inner join organ b on a.clinic_id = b.id
    set a.module_ids = concat(a.module_ids, ',', '51210'), a.last_modified_by = '{defaultId}', a.last_modified = now()
    where find_in_set(103, module_ids) # 入库权限
      and !find_in_set(8, module_ids)  # 本身没有库存管理权限
      and !find_in_set(0, module_ids)  # 本身不是超管;
      and length(a.module_ids) > 0     # 权限不为空的
      and b.parent_id = '{chainId}';
    """.format(chainId=chain_id, defaultId=default_id))

    # 【诊所管家】有出库权限的人有领用、报损、2个权限
    basic_db_client.execute("""
    update clinic_employee a
    inner join organ b on a.clinic_id = b.id and b.his_type != 100
set a.module_ids = concat(a.module_ids, ',', '51211,51213'), a.last_modified_by = '{defaultId}', a.last_modified = now()
where find_in_set(104, module_ids)
  and !find_in_set(8, module_ids) # 本身没有库存管理权限
  and !find_in_set(0, module_ids) # 本身不是超管;
  and length(a.module_ids) > 0    # 权限不为空的
  and b.parent_id = '{chainId}';
    """.format(chainId=chain_id, defaultId=default_id))

    # 【诊所管家】有耗材/药品的人员：有库存的权限
    basic_db_client.execute("""
        update clinic_employee a
    inner join organ b on a.clinic_id = b.id and b.his_type != 100
set a.module_ids = concat(a.module_ids, ',', '51209'), a.last_modified_by = '{defaultId}', a.last_modified = now()
where find_in_set(101, module_ids)
  and !find_in_set(8, module_ids) # 本身没有库存管理权限
  and !find_in_set(0, module_ids) # 本身不是超管;
  and length(a.module_ids) > 0    # 权限不为空的
  and b.parent_id = '{chainId}';
        """.format(chainId=chain_id, defaultId=default_id))

    # 【医院管家】，具有【门诊】【通用】【住院】权限的人员赋予系统设置权限 517
    basic_db_client.execute("""
            update clinic_employee a
    inner join organ b on a.clinic_id = b.id and b.his_type = 100
set a.module_ids = concat(a.module_ids, ',', 7), a.last_modified_by = '{defaultId}', a.last_modified = now()
where (find_in_set(51701, module_ids) or find_in_set(51702, module_ids) or find_in_set(51703, module_ids))
  and !find_in_set(7, module_ids) # 本身没有设置权限
  and !find_in_set(0, module_ids) # 本身不是超管;
  and length(a.module_ids) > 0    # 权限不为空的
  and b.parent_id = '{chainId}';
            """.format(chainId=chain_id, defaultId=default_id))

    # 【全部】删除出库权限
    basic_db_client.execute("""
    update clinic_employee a
    inner join organ b on a.clinic_id = b.id
set a.module_ids = replace(a.module_ids, '104,', ''), a.last_modified_by = '{defaultId}', a.last_modified = now()
where find_in_set(104, a.module_ids)
  and b.parent_id = '{chainId}';
    """.format(chainId=chain_id, defaultId=default_id))

    # 【全部】删除耗材/药品权限
    basic_db_client.execute("""
    update clinic_employee a
    inner join organ b on a.clinic_id = b.id
set a.module_ids = replace(a.module_ids, '101,', ''), a.last_modified_by = '{defaultId}', a.last_modified = now()
where find_in_set(101, a.module_ids)
  and b.parent_id = '{chainId}';
    """.format(chainId=chain_id, defaultId=default_id))

    # 【全部】删除入库权限
    basic_db_client.execute("""
    update clinic_employee a
    inner join organ b on a.clinic_id = b.id
set a.module_ids = replace(a.module_ids, '103,', ''), a.last_modified_by = '{defaultId}', a.last_modified = now()
where find_in_set(103, a.module_ids)
  and b.parent_id = '{chainId}';
    """.format(chainId=chain_id, defaultId=default_id))

    # 【医院管家】删除【门诊】【通用】【住院】权限
    basic_db_client.execute("""
    update clinic_employee a
    inner join organ b on a.clinic_id = b.id and b.his_type = 100
set a.module_ids = replace(a.module_ids, '51701,', ''), a.last_modified_by = '{defaultId}', a.last_modified = now()
where find_in_set(51701, a.module_ids)
  and b.parent_id = '{chainId}';
    """.format(chainId=chain_id, defaultId=default_id))

    basic_db_client.execute("""
    update clinic_employee a
    inner join organ b on a.clinic_id = b.id and b.his_type = 100
set a.module_ids = replace(a.module_ids, '51702,', ''), a.last_modified_by = '{defaultId}', a.last_modified = now()
where find_in_set(51702, a.module_ids)
  and b.parent_id = '{chainId}';
    """.format(chainId=chain_id, defaultId=default_id))

    basic_db_client.execute("""
    update clinic_employee a
    inner join organ b on a.clinic_id = b.id and b.his_type = 100
set a.module_ids = replace(a.module_ids, '51703,', ''), a.last_modified_by = '{defaultId}', a.last_modified = now()
where find_in_set(51703, a.module_ids)
  and b.parent_id = '{chainId}';
    """.format(chainId=chain_id, defaultId=default_id))

    # 【医院管家】删除管理采购权限
    basic_db_client.execute("""
    update clinic_employee a
    inner join organ b on a.clinic_id = b.id and b.his_type = 100
set a.module_ids = replace(a.module_ids, '102,', ''), a.last_modified_by = '{defaultId}', a.last_modified = now()
where find_in_set(102, a.module_ids)
  and b.parent_id = '{chainId}';
    """.format(chainId=chain_id, defaultId=default_id))


def run(abcRegion, chain_id):
    update_permission(abcRegion, chain_id)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.region_name, args.chain_id)


if __name__ == '__main__':
    main()
