#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db
import argparse


def updatePatientTagStyle(chain_id):
    patient_db_client = db.DBClient('abc_cis_account_base', 'abc_cis_patient')

    sqls = [
        # #ed2323 -> #ff1111
        """
        update v2_patient_tag_type
        set style = replace(style, '#ed2323', '#ff1111')
        where style like '%#ed2323%' and chain_id = '{chainId}';
        """,
        # #00c1cf -> #01bac7
        """
        update v2_patient_tag_type
        set style = replace(style, '#00c1cf', '#01bac7')
        where style like '%#00c1cf%' and chain_id = '{chainId}';
        """,
        # /img/patient-label/xxx.png   ->      /img/patient-label/v2/xxx.png
        """
        update v2_patient_tag_type
        set style = replace(style, '/img/patient-label/', '/img/patient-label/v2/')
        where style like '%/img/patient-label/%' and style not like '%/img/patient-label/v2/%' and chain_id = '{chainId}' 
        """
    ]

    for sql in sqls:
        patient_db_client.execute(sql.format(chainId=chain_id))

def run(chain_id):
    updatePatientTagStyle(chain_id)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)


if __name__ == '__main__':
    main()
