#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db
import argparse

default_id = '00000000000000000000000000000000'


def updateInVoiceType(chain_id):
    patient_db_client = db.DBClient('abc_cis_mixed', 'abc_cis_property')
    patient_db_client.execute("""insert into abc_cis_property.v2_property_config_item(id, `key`, value, scope, scope_id, is_deleted, created_by, created,
                                    last_modified_by, last_modified, key_first, key_second, key_third, key_fourth,
                                    key_fifth, v2_scope_id)
select substr(uuid_short(), 5),
       'outpatient.diagnosisTreatment.inspection',
       value,
       scope,
       scope_id,
       is_deleted,
       v2pci.created_by,
       now(),
       v2pci.last_modified_by,
       now(),
       key_first,
       key_second,
       'inspection',
       key_fourth,
       key_fifth,
       v2_scope_id
from  abc_cis_basic.v2_clinic_chain_employee v2cce
         inner join abc_cis_property.v2_property_config_item v2pci on v2pci.v2_scope_id collate utf8mb4_0900_ai_ci = v2cce.employee_id
where v2cce.chain_id ='{chainId}'
  and v2pci.`key` = 'outpatient.diagnosisTreatment.examination'
on duplicate key update value = values(value);""".format(chainId=chain_id))
    patient_db_client.execute("""insert into v2_property_config_item(id, `key`, value, scope, scope_id, is_deleted, created_by, created,
                                    last_modified_by, last_modified, key_first, key_second, key_third, key_fourth,
                                    key_fifth, v2_scope_id)
select substr(uuid_short(), 5),
       'outpatient.diagnosisTreatment.material',
       value,
       scope,
       scope_id,
       is_deleted,
       v2pci.created_by,
       now(),
       v2pci.last_modified_by,
       now(),
       key_first,
       key_second,
       'material',
       key_fourth,
       key_fifth,
       v2_scope_id
from  abc_cis_basic.v2_clinic_chain_employee v2cce
         inner join v2_property_config_item v2pci on v2pci.v2_scope_id  = v2cce.employee_id
where v2cce.chain_id ='{chainId}'
  and v2pci.`key` = 'outpatient.diagnosisTreatment.materialGoods'
on duplicate key update value = values(value);""".format(chainId=chain_id))


def run(chain_id):
    updateInVoiceType(chain_id)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)


if __name__ == '__main__':
    main()
