#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db
import argparse
import requests

default_id = '00000000000000000000000000000000'


#刷BusFlag
def updateClinicBusFlag(chain_id):
    goods_db_client = db.DBClient('abc_cis_stock', 'abc_cis_goods')
    ods_client = db.DBClient('ods', 'abc_cis_goods')
    goods_sql = """
            select CONCAT('update v2_goods_clinic_config set bus_support_flag=',o.bus_support_flag ,' where  id = ',c.id,';')  as exeSql 
            from abc_cis_goods.v2_goods_clinic_config as c 
                inner join abc_cis_basic.organ as o on c.clinic_id = o.id 
                where c.bus_support_flag != o.bus_support_flag 
                    and chain_id = '{chainId}' """.format(chainId= chain_id)
    goods_sql_res = ods_client.fetchall(goods_sql)
    for stmt in goods_sql_res:
        sql = stmt['exeSql']
        if sql is None:
            continue
        goods_db_client.execute(sql)
#刷采购天数
def addEyePurchaseConfig(chain_id):
    goods_db_client = db.DBClient('abc_cis_stock', 'abc_cis_goods')
    goods_sql = """
    update v2_goods_clinic_config set goods_purchase_cycle_days = json_merge(goods_purchase_cycle_days, '[
          {{
            "days": 15,
            "name": "镜片",
            "typeId": 64
          }},
          {{
            "days": 15,
            "name": "镜架",
            "typeId": 65
          }},
          {{
            "days": 15,
            "name": "角膜塑形镜",
            "typeId": 66
          }},
          {{
            "days": 15,
            "name": "软性亲水镜",
            "typeId": 67
          }},
          {{
            "days": 15,
            "name": "硬性透氧镜",
            "typeId": 68
          }},
          {{
            "days": 15,
            "name": "太阳镜",
            "typeId": 69
          }}
        ]')
        where goods_purchase_cycle_days is not null  and bus_support_flag &0x04 and chain_id = '{chainId}'
    """.format(chainId= chain_id)
    goods_db_client.execute(goods_sql)
#迁移老的GoodsCompose
def moveGoodsCompose(chain_id):
    goods_db_client = db.DBClient('abc_cis_stock', 'abc_cis_goods')
    goods_sql = """
            insert into v2_goods_compose_opt(id, chain_id,goods_id,
                                             parent_goods_id,
                                            compose_price,
                                             compose_package_count,
                                             compose_piece_count,
                                             compose_package_price,
                                             compose_piece_price,
                                             created_user_id,
                                             created_date,
                                             last_modified_user_id,
                                             last_modified_date,
                                             compose_use_dismounting,
                                             compose_sort,
                                             compose_fraction_price)
            select substr(uuid_short(), 5),
                   '{chainId}',
                   c.goods_id,
                   c.parent_goods_id,
                   c.compose_price,
                   c.compose_package_count,
                   c.compose_piece_count,
                   c.compose_package_price,
                   c.compose_piece_price,
                   c.created_user_id,
                   c.created_date,
                   c.last_modified_user_id,
                   c.last_modified_date,
                   c.compose_use_dismounting,
                   c.compose_sort,
                   c.compose_fraction_price
            from v2_goods_compose as c inner join v2_goods as g on c.goods_id = g.id
            where  g.organ_id ='{chainId}';
    """.format(chainId= chain_id)
    goods_db_client.execute(goods_sql)

#更新GoodsComposeFlag
def updateGoodsComposeFlag(chain_id):
    goods_db_client = db.DBClient('abc_cis_stock', 'abc_cis_goods')
    sqls =[
           """
                update v2_goods as g inner join v2_goods_compose_opt v2gc on g.id = v2gc.goods_id
                set g.compose_flag = IFNULL(g.compose_flag, 0) | 1
                where g.organ_id ='{chainId}';
           """,
           """
               update v2_goods as g inner join v2_goods_association_goods v2gc on g.id = v2gc.goods_id
                set g.compose_flag = IFNULL(g.compose_flag, 0) | 2,
                    v2gc.chain_id  = g.organ_id
                where v2gc.type = 1 and v2gc.chain_id='{chainId}';
           """,
           """
                update v2_goods as g inner join v2_goods_association_goods v2gc on g.id = v2gc.goods_id
                set g.compose_flag = IFNULL(g.compose_flag, 0) | 4,
                    v2gc.chain_id  = g.organ_id
                where v2gc.type = 0  and v2gc.chain_id='{chainId}';
           """
    ]
    for sql in sqls:
        sql_f = sql.format(chainId=chain_id)
        goods_db_client.execute(sql_f)

#插入默认药房
def appendDefaultPharmacy(chain_id):
    goods_db_client = db.DBClient('abc_cis_stock', 'abc_cis_goods')
    sqls =[
        """
             update   v2_goods_pharmacy as p inner join  v2_goods_clinic_config v2gcc on p.chain_id = v2gcc.chain_id and p.clinic_id = v2gcc.clinic_id
             set p.default_goods_types = JSON_MERGE_PATCH(p.default_goods_types, JSON_OBJECT('typeList',JSON_ARRAY_APPEND(p.default_goods_types -> '$.typeList', '$',
                JSON_OBJECT( 'name', '镜片', 'type', 24, 'subType', 1, 'typeId', 90, 'parentId', 63, 'isSupportLocal', 1, 'isSupportVirtual', 0, 'isSupportAirPharmacy', 0)
                )))
            WHERE p.no = 0 and v2gcc.bus_support_flag & 4 != 0 and v2gcc.chain_id ='{chainId}';
        """,
        """
             update   v2_goods_pharmacy as p inner join  v2_goods_clinic_config v2gcc on p.chain_id = v2gcc.chain_id and p.clinic_id = v2gcc.clinic_id
             set p.default_goods_types = JSON_MERGE_PATCH(p.default_goods_types, JSON_OBJECT('typeList',JSON_ARRAY_APPEND(p.default_goods_types -> '$.typeList', '$',
                JSON_OBJECT('name', '镜架', 'type', 24, 'subType', 2, 'typeId', 65, 'parentId', 63, 'isSupportLocal', 1, 'isSupportVirtual', 0, 'isSupportAirPharmacy', 0)
                )))
            WHERE p.no = 0 and v2gcc.bus_support_flag & 4 != 0 and v2gcc.chain_id ='{chainId}';
        """,
        """
             update   v2_goods_pharmacy as p inner join  v2_goods_clinic_config v2gcc on p.chain_id = v2gcc.chain_id and p.clinic_id = v2gcc.clinic_id
             set p.default_goods_types = JSON_MERGE_PATCH(p.default_goods_types, JSON_OBJECT('typeList',JSON_ARRAY_APPEND(p.default_goods_types -> '$.typeList', '$',
                    JSON_OBJECT('name', '角膜塑形镜', 'type', 24, 'subType', 3, 'typeId', 66, 'parentId', 63, 'isSupportLocal', 1, 'isSupportVirtual', 0, 'isSupportAirPharmacy', 0)
                )))
            WHERE p.no = 0 and v2gcc.bus_support_flag & 4 != 0 and v2gcc.chain_id ='{chainId}';
        """,
        """
             update   v2_goods_pharmacy as p inner join  v2_goods_clinic_config v2gcc on p.chain_id = v2gcc.chain_id and p.clinic_id = v2gcc.clinic_id
             set p.default_goods_types = JSON_MERGE_PATCH(p.default_goods_types, JSON_OBJECT('typeList',JSON_ARRAY_APPEND(p.default_goods_types -> '$.typeList', '$',
                    JSON_OBJECT('name', '软性亲水镜', 'type', 24, 'subType', 4, 'typeId', 67, 'parentId', 63, 'isSupportLocal', 1, 'isSupportVirtual', 0, 'isSupportAirPharmacy', 0)
                )))
            WHERE p.no = 0 and v2gcc.bus_support_flag & 4 != 0 and v2gcc.chain_id ='{chainId}';
        """,
        """
             update   v2_goods_pharmacy as p inner join  v2_goods_clinic_config v2gcc on p.chain_id = v2gcc.chain_id and p.clinic_id = v2gcc.clinic_id
             set p.default_goods_types = JSON_MERGE_PATCH(p.default_goods_types, JSON_OBJECT('typeList',JSON_ARRAY_APPEND(p.default_goods_types -> '$.typeList', '$',
                    JSON_OBJECT('name', '硬性透氧镜', 'type', 24, 'subType', 5, 'typeId', 68, 'parentId', 63, 'isSupportLocal', 1, 'isSupportVirtual', 0, 'isSupportAirPharmacy', 0)
                )))
            WHERE p.no = 0 and v2gcc.bus_support_flag & 4 != 0 and v2gcc.chain_id ='{chainId}';
        """,
        """
             update   v2_goods_pharmacy as p inner join  v2_goods_clinic_config v2gcc on p.chain_id = v2gcc.chain_id and p.clinic_id = v2gcc.clinic_id
             set p.default_goods_types = JSON_MERGE_PATCH(p.default_goods_types, JSON_OBJECT('typeList',JSON_ARRAY_APPEND(p.default_goods_types -> '$.typeList', '$',
                    JSON_OBJECT('name', '太阳镜', 'type', 24, 'subType', 6, 'typeId', 69, 'parentId', 63, 'isSupportLocal', 1, 'isSupportVirtual', 0, 'isSupportAirPharmacy', 0)
                )))
            WHERE p.no = 0 and v2gcc.bus_support_flag & 4 != 0 and v2gcc.chain_id ='{chainId}';
        """
    ]
    for sql in sqls:
        sql_f = sql.format(chainId=chain_id)
        goods_db_client.execute(sql_f)


def closeUnClearExtendFlag(chain_id):
    goods_db_client = db.DBClient('abc_cis_stock', 'abc_cis_goods')
    sqls =[
        #  stock_warn_customize 不在控制 purchase_cycle_days 采购周期
        """
        update v2_goods_extend
            set purchase_cycle_days = null
        where stock_warn_customize = 0
              and chain_id ='{chainId}'
              and purchase_cycle_days is not null;

        """,
        # stock_warn_customize 不再控制 expired_warn_months 有效期
        """
       update v2_goods_extend
            set expired_warn_months=null
        where stock_warn_customize = 0
              and chain_id ='{chainId}'
              and expired_warn_months is not null ;

        """,
        """
            update v2_goods_extend e inner  join  v2_goods_clinic_config v2gcc on e.chain_id = v2gcc.chain_id and e.organ_id = v2gcc.clinic_id
            set e.shortage_warn_count=null,
                e.shortage_warn_count_small_unit = null,
                e.turnover_days = IFNULL(e.turnover_days,v2gcc.stock_warn_goods_turnover_days),
                e.days_of_day_avg_sell = IFNULL(e.days_of_day_avg_sell,v2gcc.stock_days_of_day_avg_sell)
            where e.stock_warn_customize = 0
            and e.shortage_warn_enable = 0
              and e.chain_id ='{chainId}';
        """,
        """
       update v2_goods_extend
           set turnover_days=null,days_of_day_avg_sell = null,stock_warn_customize = 1
           where 
            shortage_warn_enable = 1
             and chain_id ='{chainId}'
       """
    ]
    for sql in sqls:
        sql_f = sql.format(chainId=chain_id)
        goods_db_client.execute(sql_f)

def run(chain_id):
    updateClinicBusFlag(chain_id)
    addEyePurchaseConfig(chain_id)
    # moveGoodsCompose(chain_id)
    # updateGoodsComposeFlag(chain_id)
    appendDefaultPharmacy(chain_id)
    # closeUnClearExtendFlag(chain_id)
    requests.get("""http://prod.rpc.abczs.cn/rpc/v3/goods/jenkins/clean-chain-redis-cache?chainId={}""".format(chain_id))



def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)


if __name__ == '__main__':
    main()
