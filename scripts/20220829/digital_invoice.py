#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys 
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db 
import argparse
import logging
logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)


class DigitalInvoice(object):

    def __init__(self):
        self._db_client_cache = {}


    def _get_db_client(self, cluster, database):
        k = '{0}-{1}'.format(cluster, database)
        if k in self._db_client_cache.keys():
            return self._db_client_cache[k]
        
        db_client = db.DBClient(cluster, database)
        self._db_client_cache[k] = db_client
        return db_client

    def update_digital(self, chain_id):
        sqls = [
            #  挂号费
            '''
            update v2_invoice_record
            set invoice_category    = 1,
                invoice_supplier_id = 1,
                invoice_type        = 11
            where invoice_type = 1
            and chain_id = '{0}';
            ''',
            '''
            update v2_invoice_config
            set invoice_supplier_id = 1
            where type = 1
            and chain_id ='{0}';
            ''',

            '''
            update v2_invoice_sheet
            set invoice_supplier_id = 1
            where chain_id = '{0}';
            '''
        ]

        invoice_db_client = self._get_db_client('abc_cis_bill', 'abc_cis_invoice')

        for sql in sqls:
            sql_f = sql.format(chain_id)
            print sql_f
            invoice_db_client.execute(sql_f)


    def execute_chain(self, chain_id):
        self.update_digital(chain_id)


def run(chain_id):
    #ABC系统目前就几家诊所在用发票，通过写死chainId的方式来刷会快点
    openInvoiceChainIdList = ["e040fead29154787b1ee3c88de2be5fa" ,"efa2923ba6174f69948447a10ff1cf8b" ,"ffffffff000000000bc1d7c003be2000" ,"ffffffff000000001488991807758000" ,"ffffffff0000000012c7302006d6c000" ,"ffffffff000000001e0415d00a08c000" ,"a0e540bdf2674bcfa29489b9db659539" ,"ffffffff00000000182afac8087b6000" ,"ffffffff000000001f09e9f80a1e6000" ,"ffffffff000000001cd3b96809ed2000" ,"ffffffff0000000022e23d880a562001" ]
    if chain_id in openInvoiceChainIdList:
        rgt = DigitalInvoice()
        rgt.execute_chain(chain_id)
    else:
       print "not open chainId"


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)

if __name__ == '__main__':
    main()
