#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import json
import db
import argparse
import logging

logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)


class InvoiceUpdate(object):

    def __init__(self):
        self._db_client_cache = {}

    def _get_db_client(self, cluster, database):
        k = '{0}-{1}'.format(cluster, database)
        if k in self._db_client_cache.keys():
            return self._db_client_cache[k]

        db_client = db.DBClient(cluster, database)
        self._db_client_cache[k] = db_client
        return db_client




    def update_invoice(self,chain_id):
        # 从ods里面把chainId很clinicId拿到
        sql = '''
            select * from v2_invoice_record where detail is not null and chain_id = '{chainId}'
        '''.format(chainId=chain_id)

        print sql
        invoice_client = db.DBClient('abc_cis_bill', 'abc_cis_invoice')
        row_list = invoice_client.fetchall(sql)
        for row in row_list:
            id = row['id']
            detailCol = row['detail']
            detailJson = json.loads(detailCol)
            if detailJson == None:
                print("id -> {id}, detail is json null".format(id=id))
                continue
            if 'chargeForms' in detailJson:
                chargeForms = detailJson['chargeForms']
                if len(chargeForms) > 0:
                    goodsNum = 0
                    for chargeForm in chargeForms:
                        chargeFormItems = chargeForm['chargeFormItems']
                        if len(chargeFormItems) > 0:
                            goodsNum = goodsNum + len(chargeFormItems)
                    sql = """
                        update v2_invoice_record set goods_num = '{goodsNum}' where id = '{id}';
                    """.format(goodsNum=goodsNum,id=id)
                    invoice_client.execute(sql)
            if 'detailItems' in detailJson:
                sql = """
                update v2_invoice_record set goods_num = '{goodsNum}' where id = '{id}';
                """.format(goodsNum=len(detailJson['detailItems']),id=id)
                invoice_client.execute(sql)


    def execute_chain(self, chain_id):
        self.update_invoice(chain_id)


def run(chain_id):
    rgt = InvoiceUpdate()
    rgt.execute_chain(chain_id)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)


if __name__ == '__main__':
    main()
