#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys 
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db 
import argparse
import logging
logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)


class GoodsUpdate(object):

    def __init__(self):
        self._db_client_cache = {}


    def _get_db_client(self, cluster, database):
        k = '{0}-{1}'.format(cluster, database)
        if k in self._db_client_cache.keys():
            return self._db_client_cache[k]
        
        db_client = db.DBClient(cluster, database)
        self._db_client_cache[k] = db_client
        return db_client

    def update_goods(self, chain_id):
        #处理DB数据
        sqls = [
            #子店对码 ---子店自己未设置过支付方式  把总部设置的支付方式刷到门店上
            '''
            update  v2_goods as g inner  join  v2_goods_extend v2ge on g.id = v2ge.goods_id and v2ge.chain_id = g.organ_id and g.organ_id =  '{0}'
                set v2ge.shebao_pay_mode = g.shebao_pay_mode
            where v2ge.shebao_code_national_matched_status  = 1 and v2ge.shebao_pay_mode is null  and g.organ_id =  '{0}';
            ''',
            #子店未对码/不容许医保支付 ---不管子店是否设置过支付方式，都把对码子店支付方式设置成null
            '''
            update  v2_goods as g inner  join  v2_goods_extend v2ge on g.id = v2ge.goods_id and v2ge.chain_id = g.organ_id  and g.organ_id =  '{0}'
                set v2ge.shebao_pay_mode = null
            where v2ge.shebao_code_national_matched_status in(0,-1)  and g.organ_id =  '{0}';

            '''
        ]
        goods_db_client = self._get_db_client('abc_cis_stock', 'abc_cis_goods')

        #清理Redis缓存
        for sql in sqls:
            sql_f = sql.format(chain_id)
            goods_db_client.execute(sql_f)
        command = '''
        redis-cli -h r-uf6cx731ddew2rqe88.redis.rds.aliyuncs.com -n 0 -p 6379 keys '_scgoods:g:info:{0}:*' | xargs redis-cli -h r-uf6cx731ddew2rqe88.redis.rds.aliyuncs.com -n 0 -p 6379 del
        '''.format(chain_id )
        os.system(command)


    def execute_chain(self, chain_id):
        self.update_goods(chain_id)


def run(chain_id):
   rgt = GoodsUpdate()
   rgt.execute_chain(chain_id)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)

if __name__ == '__main__':
    main()
