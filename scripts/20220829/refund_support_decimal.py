#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys 
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db 
import argparse
import logging
logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)


class ChargeUpdate(object):

    def __init__(self):
        self._db_client_cache = {}


    def _get_db_client(self, cluster, database):
        k = '{0}-{1}'.format(cluster, database)
        if k in self._db_client_cache.keys():
            return self._db_client_cache[k]
        
        db_client = db.DBClient(cluster, database)
        self._db_client_cache[k] = db_client
        return db_client

    def update_charge(self, chain_id):
        sqls = [
            #  挂号费
            '''
             update v2_charge_sheet set transaction_record_handle_mode = 1 where status in (1,3) and chain_id = '{0}';
            '''
        ]

        charge_db_client = self._get_db_client('abc_cis_charge', 'abc_cis_charge')

        for sql in sqls:
            sql_f = sql.format(chain_id)
            charge_db_client.execute(sql_f)


    def execute_chain(self, chain_id):
        self.update_charge(chain_id)


def run(chain_id):
   rgt = ChargeUpdate()
   rgt.execute_chain(chain_id)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)

if __name__ == '__main__':
    main()
