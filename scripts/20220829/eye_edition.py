#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys 
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db 
import argparse
import logging
logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)


class OrganUpdate(object):

    def __init__(self):
        self._db_client_cache = {}


    def _get_db_client(self, cluster, database):
        k = '{0}-{1}'.format(cluster, database)
        if k in self._db_client_cache.keys():
            return self._db_client_cache[k]
        
        db_client = db.DBClient(cluster, database)
        self._db_client_cache[k] = db_client
        return db_client

    def update_organ(self, chain_id):
        #处理DB数据
        sqls = [
            '''
update v2_clinic_current_edition as e inner join  organ as o on e.clinic_id = o.id
set e.edition_id = e.edition_id + 200
where o.his_type = 2 and chain_id ='{0}';
            ''',
            '''
update v2_clinic_edition_order as e inner join  organ as o on e.bind_clinic_id = o.id
set e.edition_id = e.edition_id + 200
where o.his_type = 2 and bind_chain_id= '{0}';
            '''
        ]
        goods_db_client = self._get_db_client('abc_cis_mixed', 'abc_cis_basic')

        #清理Redis缓存
        for sql in sqls:
            sql_f = sql.format(chain_id)
            goods_db_client.execute(sql_f)


    def execute_chain(self, chain_id):
        self.update_organ(chain_id)


def run(chain_id):
   rgt = OrganUpdate()
   rgt.execute_chain(chain_id)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)

if __name__ == '__main__':
    main()
