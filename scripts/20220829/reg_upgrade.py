#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON>yong <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import requests
import db
import argparse
import logging

logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)


class RegUpdate(object):

    def __init__(self):
        self._db_client_cache = {}

    def _get_db_client(self, cluster, database):
        k = '{0}-{1}'.format(cluster, database)
        if k in self._db_client_cache.keys():
            return self._db_client_cache[k]

        db_client = db.DBClient(cluster, database)
        self._db_client_cache[k] = db_client
        return db_client

    # 刷数据前进行一些必要数据的清理，其实这个不带chaid的不应该放在这里 目前没好的位置放，就放这里了
    #大量拉门店的时候这三个sql可以copy出来手动执行
    def update_default_config(self):
        sqls = [
            # 将 v2_registration_appointment_config 中 reservation_time 为null 的设置为默认值
            '''
            update v2_registration_appointment_config
            set reservation_time = '{ "day": 0, "week": 2, "month": 0 }'
            where reservation_time is null;''',
            # 将 v2_registration_appointment_config 中 register_start_time 为空的 的设置为默认值
            '''
            update v2_registration_appointment_config
            set register_start_time = '08:00'
            where register_start_time is null or register_start_time = '';
            ''',
            #将 v2_registration_appointment_config 中 ahead_of_cancel_time 为空的 的设置为默认值
            '''
            update v2_registration_appointment_config
            set ahead_of_cancel_time = '{ "min": 15, "hour": 0 }'
            where ahead_of_cancel_time is null;
            '''
        ]
        outpatient_db_client = self._get_db_client('abc_cis_outpatient', 'abc_cis_registration')
        for sql in sqls:
            print sql
            outpatient_db_client.execute(sql)

    def update_reg(self, chain_id):
        sqls = [
            # 将老数据的预约设置迁移到新表中
            '''
                insert ignore into v2_registration_config(id, chain_id, clinic_id, type, is_open, display_name, available_role_ids,
                                          mode_type,
                                          service_type, custom_period, service_duration, not_must_reserve_employee,
                                          show_reserve_product, reservation_time_range, enable_ahead_close_reserve,
                                          ahead_close_reserve_time, enable_leave_for_pc, enable_leave_for_member,
                                          enable_wechat_add_epidemiological, disable_epidemiological_registration,
                                          wechat_reserve_need_apply, need_sign_in, need_scan_sign_in,
                                          enable_wechat_reserve_limit_count, wechat_reserve_limit_count_rule,
                                          enable_wechat_break_appointment_punish, wechat_break_appointment_punish_rule,
                                          retreat_notice, wechat_reserve_must_pay, hide_fee_we_clinic_before_paid,
                                          wechat_enable_member_card_pay, enable_wechat_reserve_refund,
                                          wechat_reserve_refund_rule, show_none_schedule_doctor,
                                          enable_choose_consulting_room,
                                          is_deleted, deleted_millis, created_by, created, last_modified_by,
                                          last_modified)
select id,
       chain_id,
       v2rac.clinic_id,
       0,
       1,
       '门诊预约',
       '[1,9]',
       0,
       service_type,
       custom_period,
       null,
       1 - must_reserve_doctor,
       0,
       json_set('{{}}', '$.endTime', reservation_time, '$.registerStartTime', register_start_time),
       enable_ahead_of_close_reserve,
       ahead_of_close_reserve_time,
       enable_leave_for_pc,
       enable_leave_for_member,
       enable_wechat_add_epidemiological,
       disable_epidemiological_registration,
       0,
       need_sign_in,
       need_scan_sign_in,
       enable_wechat_reserve_limit_count,
       if(enable_wechat_reserve_limit_count = 1,
          json_set('{{}}', '$.limitCount', wechat_reserve_limit_count, '$.limitCountPeriod',
                   wechat_reserve_limit_count_period), null),
       enable_wechat_break_appointment_punish,
       wechat_break_appointment_punish_rule,
       retreat_notice,
       must_pay,
       hide_fee_we_clinic_before_paid,
       enable_paid_by_member_card,
       enable_wechat_reserve_refund,
       json_set('{{}}', '$.type', wechat_reserve_refund_type, '$.refundRates',
                if(wechat_reserve_refund_type = 1, wechat_reserve_stepped_refund_rates, json_array(
                        json_set('{{}}', '$.minAheadTime', ahead_of_cancel_time, '$.refundRate',
                                 wechat_reserve_refund_rate)))),
       show_none_schedule_doctor,
       consulting_room_setting,
       is_deleted,
       0,
       created_by,
       created,
       last_modified_by,
       v2rac.last_modified
from v2_registration_appointment_config v2rac
         inner join (select clinic_id, max(last_modified) as last_modified
                     from v2_registration_appointment_config
                     where is_deleted = 0
                     group by clinic_id) t1 on v2rac.clinic_id = t1.clinic_id and v2rac.last_modified = t1.last_modified
where is_deleted = 0
      and v2rac.clinic_id in ({clinicIdList});
            ''',


            # 将老数据的自定义预约设置迁移到新表中
            '''
insert ignore into v2_registration_custom_config(id, registration_type, chain_id, clinic_id, department_id, employee_id,
                                                 is_use_custom, reservation_time_range, is_deleted, created, created_by,
                                                 last_modified, last_modified_by)
select id,
       0,
       chain_id,
       clinic_id,
       department_id,
       employee_id,
       is_use_custom,
       json_set('{{}}', '$.endTime', reservation_time, '$.registerStartTime', register_start_time),
       is_deleted,
       created,
       created_by,
       last_modified,
       last_modified_by
from v2_registration_custom_appointment_config
where is_deleted = 0
      and clinic_id in ({clinicIdList});
            ''',
            # 将老数据的排班迁移到新表中
            '''
insert ignore into v2_registration_schedule(id, chain_id, registration_type, clinic_id, department_id, employee_id,
                                     consulting_room_id, shift_id, consulting_room_name, working_date, service_num,
                                     service_time_rule, is_deleted, created_by, created, last_modified_by,
                                     last_modified)
select id,
       '',
       if(s.role='医生',0,-1),
       clinic_id,
       department_id,
       employee_id,
       consulting_room_id,
       shift_id,
       consulting_room_name,
       working_date,
       service_num,
       null,
       is_deleted,
       created_by,
       created,
       last_modified_by,
       last_modified
from schedule s
where is_deleted = 0
      and clinic_id in ({clinicIdList});
            ''',
            '''
insert into v2_registration_schedule(chain_id, registration_type, clinic_id, department_id, employee_id,
                                     consulting_room_id, shift_id, consulting_room_name, working_date,
                                     service_num,
                                     service_time_rule, is_deleted, created_by, created, last_modified_by,
                                     last_modified)
select '',
       if(s.role='医生',0,-1),
       s.clinic_id,
       s.department_id,
       s.employee_id,
       s.consulting_room_id,
       s.shift_id,
       s.consulting_room_name,
       s.working_date,
       s.service_num,
       null,
       s.is_deleted,
       s.created_by,
       s.created,
       s.last_modified_by,
       s.last_modified
from schedule s
where s.clinic_id in ({clinicIdList})
  and id not in (select id
                 from v2_registration_schedule v2rs
                 where v2rs.clinic_id in ({clinicIdList})
)
  and s.is_deleted = 0;
            '''
        ]

        basic_db_client = self._get_db_client('abc_cis_mixed', 'abc_cis_basic')

        #拼clinicIdList
        organs = basic_db_client.fetchall(''' select id from organ where parent_id = '{chainId}' '''.format(chainId=chain_id))
        clinic_ids = ','.join([''' '{clinicId}' '''.format(clinicId=organ['id']) for organ in organs])

        outpatient_db_client = self._get_db_client('abc_cis_outpatient', 'abc_cis_registration')
        for sql in sqls:
            sql_f = sql.format(clinicIdList=clinic_ids)
            print sql_f
            outpatient_db_client.execute(sql_f)


    def update_v2_registration_schedule_chain_id(self,chain_id):
        # 从ods里面把chainId很clinicId拿到
        sql = '''
            select distinct o.parent_id as chainId, v2rs.clinic_id as clinicId 
            from abc_cis_registration.v2_registration_schedule v2rs 
            inner join abc_cis_basic.organ o on v2rs.clinic_id = o.id   COLLATE utf8mb4_unicode_ci
            where v2rs.chain_id = '{chainId}'
        '''.format(chainId=chain_id)

        print sql
        ods_read_only_client = db.DBClient('ods', 'abc_cis_registration')
        clinic_list = ods_read_only_client.fetchall(sql)

        outpatient_db_client = self._get_db_client('abc_cis_outpatient', 'abc_cis_registration')

        if clinic_list and len(clinic_list) > 0:
            for clinic_config in clinic_list:
                update_sql = '''
                   update v2_registration_schedule set chain_id = '{chainId}' where clinic_id= '{clinicId}' 
                '''.format(chainId=clinic_config['chainId'], clinicId=clinic_config['clinicId'])
                print update_sql
                outpatient_db_client.execute(update_sql)

    def rpc_for_oral(self,chain_id):
        basic_db_client = self._get_db_client('abc_cis_mixed', 'abc_cis_basic')

        organHisType = basic_db_client.fetchone(''' select his_type from organ where id = '{chainId}' '''.format(chainId=chain_id))
        print organHisType
        if organHisType:
            if organHisType['his_type'] == 1:
                reqUrl = 'http://pre.rpc.abczs.cn/rpc/registrations/products/jenkins/init/chain-product-type'
                rsp = requests.put(reqUrl, json={
                    'chainIdsStr': chain_id
                })
                print rsp
    def execute_chain(self, chain_id):
        self.update_default_config()
        self.update_reg(chain_id)
        self.update_v2_registration_schedule_chain_id(chain_id)
        self.rpc_for_oral(chain_id)




def run(chain_id):
    rgt = RegUpdate()
    rgt.execute_chain(chain_id)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)


if __name__ == '__main__':
    main()
