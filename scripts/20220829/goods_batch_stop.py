#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys 
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db 
import argparse
import logging
logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)


class GoodsUpdate(object):

    def __init__(self):
        self._db_client_cache = {}


    def _get_db_client(self, cluster, database):
        k = '{0}-{1}'.format(cluster, database)
        if k in self._db_client_cache.keys():
            return self._db_client_cache[k]
        
        db_client = db.DBClient(cluster, database)
        self._db_client_cache[k] = db_client
        return db_client

    def update_goods(self, chain_id):
        #处理DB数据
        sqls = [
            '''
           update v2_goods_stock set status = 2 where piece_count =0 and package_count =0 and chain_id = '{chainId}'; 
            '''
        ]
        goods_db_client = self._get_db_client('abc_cis_stock', 'abc_cis_goods')

        #清理Redis缓存
        for sql in sqls:
            sql_f = sql.format(chainId=chain_id)
            goods_db_client.execute(sql_f)


    def execute_chain(self, chain_id):
        self.update_goods(chain_id)


def run(chain_id):
   rgt = GoodsUpdate()
   rgt.execute_chain(chain_id)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)

if __name__ == '__main__':
    main()
