#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys 
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db 
import argparse
import logging
logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)


class ChargeUpdate(object):

    def __init__(self):
        self._db_client_cache = {}


    def _get_db_client(self, cluster, database):
        k = '{0}-{1}'.format(cluster, database)
        if k in self._db_client_cache.keys():
            return self._db_client_cache[k]
        
        db_client = db.DBClient(cluster, database)
        self._db_client_cache[k] = db_client
        return db_client

    def update_charge(self, chain_id):
        sqls = [
            #  挂号费
            '''
            update v2_charge_hospital_sheet a left join (
    select b.business_id,
           b.created,
           b.created_by
    from (select a.business_id,
                 a.created,
                 a.created_by,
                 row_number() over (partition by business_id order by created asc ) rn
          from v2_charge_owe_combine_transaction a where a.business_id is not null and a.source = 1 and a.is_paid_back = 0 and a.chain_id = '{chainId}') b
    where b.rn = 1) c on a.id = c.business_id
set a.first_charged_time = c.created where a.status != 0 AND a.chain_id = '{chainId}';
            ''',
            '''
           update v2_charge_hospital_sheet a left join (
    select b.business_id,
           b.created,
           b.created_by
    from (select a.business_id,
                 a.created,
                 a.created_by,
                 row_number() over (partition by business_id order by created desc ) rn
          from v2_charge_owe_combine_transaction a where a.business_id is not null and a.source = 1 and a.is_paid_back = 0 and a.chain_id ='{chainId}') b
    where b.rn = 1) c on a.id = c.business_id
set a.charged_time = c.created , a.charged_by = c.created_by where a.status != 0 AND a.chain_id = '{chainId}';   
            '''

        ]

        charge_db_client = self._get_db_client('abc_cis_charge', 'abc_cis_charge')

        for sql in sqls:
            sql_f = sql.format(chainId=chain_id)
            charge_db_client.execute(sql_f)


    def execute_chain(self, chain_id):
        self.update_charge(chain_id)


def run(chain_id):
   rgt = ChargeUpdate()
   rgt.execute_chain(chain_id)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)

if __name__ == '__main__':
    main()
