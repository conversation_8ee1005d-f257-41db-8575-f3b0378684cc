#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import logging
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
import argparse

default_id = '00000000000000000000000000000000'

standard_device_id = '2807277108149698562'
standard_chain_id = 'ffffffff0000000026f573b00acb4000'


def run(region_name, chain_id):
    goodsdbclient = DBClient(region_name, 'abc_cis_stock', 'abc_cis_goods', 'prod', True)
    # examdbclient = DBClient(region_name, 'abc_cis_outpatient', 'abc_cis_examination', 'prod', True)
    # 检查设备型号的goods_extend_spec 改成 10
    # goodsdbclient.execute(f"""update v2_goods_examination_device
    #                         set goods_extend_spec = '10'
    #                         where goods_type = 3
    #                           and goods_sub_type = 2
    #                           and inner_flag = 0
    #                           and goods_extend_spec is null and chain_id = '{chain_id}';""")

    # 眼科检查关联的检查检验单
    # examdbclient.execute(f"""update v2_examination_sheet set device_model_id = '10000', last_modified = now(), last_modified_by = '{default_id}' where type = 2 and sub_type = 20 and chain_id = '{chain_id}';""")
    # examdbclient.execute(f"""update v2_examination_merge_sheet set device_model_id = '10000', last_modified = now(), last_modified_by = '{default_id}' where type = 2 and sub_type = 20 and chain_id = '{chain_id}';""")


    # if chain_id == standard_chain_id:
    #     logging.warning("standard chain, skip")
    #     return

    # 眼科组合项不关联设备型号
    # goodsdbclient.execute(f"""update v2_goods
    #                         set biz_relevant_id = null
    #                         where type = 3
    #                           and sub_type = 2
    #                           and extend_spec = '20'
    #                           and combine_type = 1
    #                           and organ_id = '{chain_id}';""")
    #
    # # 眼科单项要改成关联设备型号
    # goodsdbclient.execute(f"""update v2_goods
    #                         set biz_relevant_id = '10000'
    #                         where type = 3
    #                           and sub_type = 2
    #                           and extend_spec = '20'
    #                           and combine_type = 0
    #                           and organ_id = '{chain_id}'""")

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.region_name, args.chain_id)


if __name__ == '__main__':
    main()
