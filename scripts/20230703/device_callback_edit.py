#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
import argparse

default_id = '00000000000000000000000000000000'


def updateDeviceCallbackData(abcRegion,chain_id):
    db_client = DBClient(abcRegion,'abc_cis_outpatient', 'abc_cis_examination', 'prod', True)
    db_client.execute("""update v2_examination_device_callback_data
                                set status             = 20,
                                    callback_data_time = created,
                                    last_modified_by = '{defaultId}',
                                    last_modified = now()
                                where device_id is not null and callback_data_time is null and status != 20 and chain_id = '{chainId}'""".format(chainId=chain_id, defaultId=default_id))

def run(abcRegion,chain_id):
    updateDeviceCallbackData(abcRegion,chain_id)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.region_name,args.chain_id)


if __name__ == '__main__':
    main()
