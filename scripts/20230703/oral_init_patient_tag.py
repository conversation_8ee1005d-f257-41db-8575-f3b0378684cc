#! /usr/bin/env python3
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import copy
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import argparse
from idwork import IdWork
from multizone.db import DBClient

default_id = '00000000000000000000000000000000'


class Utils(object):
    @staticmethod
    def orElse(value, defualt):
        if value is None:
            return defualt
        else:
            return value

    @staticmethod
    def wrap(value, token='\''):
        if value is None:
            return value
        else:
            return token + value + token


class OralInit:
    chain_id = None
    patient_db_client = None
    basic_db_client = None
    id_work = None
    patient_tag_template_chain_id = None

    def __init__(self,region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.patient_db_client = DBClient(self.region_name,'abc_cis_account_base', 'abc_cis_patient', 'prod', True)
        self.im_db_client = DBClient(self.region_name,'abc_cis_account_base', 'abc_cis_im', 'prod', True)
        self.basic_db_client = DBClient(self.region_name,'abc_cis_mixed', 'abc_cis_basic', 'prod', True)
        self.patient_tag_template_chain_id = 'ffffffff0000000034909b8ced558000'
        self.id_work = IdWork(self.patient_db_client, False)

    def run(self):
        # 患者标签初始化
        # self.patient_tag_type_init()
        self.flush_im_conversion()

    def flush_im_conversion(self):
        # 口腔数据初始化-患者标签初始化
        self.im_db_client.execute("""
                    update abc_cis_im.v2_im_conversation a
    inner join (SELECT p.conversation_id as conversation_id, max(m.created) as max_msg_time
                FROM abc_cis_im.v2_im_group_participant p
                         left JOIN abc_cis_im.v2_im_message m ON p.conversation_id = m.conversation_id
                WHERE p.is_deleted = 0
                  and m.is_deleted = 0
                  and m.notify_flag = 0
                  and p.chain_id  = '{chainId}'
                  and m.chain_id = '{chainId}'
                group by p.conversation_id) as b on a.id = b.conversation_id
set a.max_msg_time = b.max_msg_time
where a.chain_id = '{chainId}';
                """.format(chainId=self.chain_id))


    def patient_tag_type_init(self):
        # 口腔数据初始化-患者标签初始化
        if self.chain_id:
            organ = self.basic_db_client.fetchone("""
                    select o.id, o.his_type
                    from organ o
                    where id = '{chainId}' and his_type = 1
                """.format(chainId=self.chain_id))
            if not organ:
                return
            self.patient_tag_type_init_chain(self.chain_id)

    def patient_tag_type_init_chain(self, chain_id):
        # 当前连锁就是模板连锁，直接跳过
        if chain_id == self.patient_tag_template_chain_id:
            return

        # 新建或修改过标签的连锁，不做任何处理
        has_create_or_update_patient_tag_type = has_create_or_update_patient_tag_type_by_chain_id(
            self.patient_db_client, chain_id)
        if has_create_or_update_patient_tag_type['count'] > 0:
            return

        # 配置 id_work
        self.id_work.config()

        # 查询标签模板
        patient_tag_type_templates = query_patient_tag_type_by_chain_id(self.patient_db_client,
                                                                        self.patient_tag_template_chain_id,
                                                                        True)
        if not patient_tag_type_templates or len(patient_tag_type_templates) == 0:
            return

        # 未使用过标签的用户，直接覆盖
        used_patient_tags = used_patient_tag_by_chain_id(self.patient_db_client, chain_id)
        if len(used_patient_tags) == 0:
            # 先删除连锁之前所有的标签
            delete_patient_tag_type_by_chain_id(self.patient_db_client, chain_id)
            # 根据模板构建患者标签
            patient_tag_types = build_patient_tag_type_by_template(patient_tag_type_templates, chain_id, self.id_work)
            # 插入患者标签
            insert_patient_tag_type(self.patient_db_client, patient_tag_types)
            return

        # 未新建过标签，且仅使用过"复购高"、"客单高"的用户，则仅保留这两项
        used_patient_tag_ids = set([tag['tag_id'] for tag in used_patient_tags])
        chain_custom_patient_tag_type_count = count_chain_custom_patient_tag_type(self.patient_db_client, chain_id)
        if chain_custom_patient_tag_type_count == 0:
            # 诊所没有新建或修改过标签
            tag_types = query_patient_tag_type_tree_by_chain_id(self.patient_db_client, chain_id)
            if len(used_patient_tag_ids) <= 2:
                remove_except_tag_type_ids = set()
                remove_other_tag_type = True
                for tag_type_tree in tag_types:
                    is_consume_tag = tag_type_tree['parent_name'] == '消费标签' and (
                            tag_type_tree['name'] == '复购高' or tag_type_tree['name'] == '客单高')
                    # 如果使用了非复购高和客单高以外的标签，则不删除其他的标签
                    if not is_consume_tag and tag_type_tree['id'] in used_patient_tag_ids:
                        remove_other_tag_type = False
                        break
                if remove_other_tag_type:
                    for tag_type_tree in tag_types:
                        if tag_type_tree['parent_name'] == '消费标签' and (
                                tag_type_tree['name'] == '复购高' or tag_type_tree['name'] == '客单高'):
                            remove_except_tag_type_ids.add(tag_type_tree['id'])
                            remove_except_tag_type_ids.add(tag_type_tree['parent_id'])
                    if len(remove_except_tag_type_ids) > 0:
                        delete_patient_tag_type_by_exclude_id(self.patient_db_client, chain_id,
                                                              remove_except_tag_type_ids)

        # 追加模板
        # 查询连锁下所有的标签
        patient_tag_types = query_patient_tag_type_by_chain_id(self.patient_db_client, chain_id)
        tag_type_name_to_tag_type = {tag_type['name']: tag_type for tag_type in patient_tag_types}
        insert_tag_types = []
        insert_tag_rules = []
        for tag_type_template in patient_tag_type_templates:
            parent_tag_type_name = tag_type_template['name']
            tag_type_tree = tag_type_name_to_tag_type.get(parent_tag_type_name)
            parent_tag_type_id = None
            if tag_type_tree is None:
                parent_tag_type_id = self.id_work.getUUID()
                insert_tag_types.append({"id": parent_tag_type_id,
                                         "name": parent_tag_type_name,
                                         "tagType": tag_type_template['tag_type'],
                                         "genMode": tag_type_template['gen_mode'],
                                         "style": tag_type_template['style'],
                                         "baseTagId": tag_type_template['id'],
                                         "viewMode": tag_type_template['view_mode'],
                                         "sort": tag_type_template['sort']})
            else:
                parent_tag_type_id = tag_type_tree['id']

            children_tag_tag_temples = tag_type_template['children']
            if children_tag_tag_temples is None or len(children_tag_tag_temples) == 0:
                continue

            tag_type_name_to_children_tag_type = {}
            if tag_type_tree is not None and tag_type_tree['children'] is not None \
                    and len(tag_type_tree['children']) > 0:
                tag_type_name_to_children_tag_type = {tag_type['name']: tag_type
                                                      for tag_type in tag_type_tree['children']}

            for children_tag_tag_temple in children_tag_tag_temples:
                children_tag_tag_temple_name = children_tag_tag_temple['name']
                children_tag_tag = tag_type_name_to_children_tag_type.get(children_tag_tag_temple_name)
                if children_tag_tag is not None:
                    continue

                children_tag_tag_id = self.id_work.getUUID()
                insert_tag_types.append({"id": children_tag_tag_id,
                                         "name": children_tag_tag_temple_name,
                                         "tagType": children_tag_tag_temple['tag_type'],
                                         "genMode": children_tag_tag_temple['gen_mode'],
                                         "style": children_tag_tag_temple['style'],
                                         "baseTagId": children_tag_tag_temple['id'],
                                         "viewMode": children_tag_tag_temple['view_mode'],
                                         "sort": tag_type_template['sort'],
                                         "parentId": parent_tag_type_id})

                rules = children_tag_tag_temple['rules']
                if not rules:
                    continue

                for rule in rules:
                    rule_id = self.id_work.getUID()
                    insert_tag_rules.append({
                        "id": rule_id,
                        "tagId": children_tag_tag_id,
                        "type": rule['type'],
                        "minValue": rule['min_value'],
                        "maxValue": rule['max_value'],
                        "keywords": rule['keywords'],
                        "dateType": rule['date_type'],
                        "dateValue": rule['date_value'],
                        "status": rule['status'],
                        "params": rule['params'],
                        "matchType": rule['match_type']
                    })

        # 查询模板
        if len(insert_tag_types) == 0:
            return

        insert_tag_type_by_tag_type_model(self.patient_db_client, chain_id, insert_tag_types, insert_tag_rules)


def insert_patient_tag_type(client, patient_tag_types):
    if not patient_tag_types or len(patient_tag_types) == 0:
        return

    tag_type_values = []
    tag_rule_values = []
    for parent_patient_tag_type in patient_tag_types:
        tag_type_values.append(u"""('{id}', '{chainId}', '{name}', {status}, '00000000000000000000000000000000', current_timestamp(),
                      '00000000000000000000000000000000', current_timestamp(), {tagType}, null, {genMode},
                      {style}, '{baseTagId}', {viewMode}, {sort})""".format(
            id=parent_patient_tag_type['id'],
            chainId=parent_patient_tag_type['chain_id'],
            name=parent_patient_tag_type['name'],
            status=parent_patient_tag_type['status'],
            tagType=Utils.orElse(parent_patient_tag_type['tag_type'], 'NULL'),
            genMode=Utils.orElse(parent_patient_tag_type['gen_mode'], 'NULL'),
            style=Utils.orElse(Utils.wrap(parent_patient_tag_type['style']), 'NULL'),
            baseTagId=Utils.orElse(parent_patient_tag_type['base_tag_id'], 'NULL'),
            viewMode=Utils.orElse(parent_patient_tag_type['view_mode'], 'NULL'),
            sort=parent_patient_tag_type['sort']))

        children_patient_tag_types = parent_patient_tag_type['children']
        if children_patient_tag_types is None:
            continue

        for children_patient_tag_type in children_patient_tag_types:
            tag_type_values.append(
                u"""('{id}', '{chainId}', '{name}', {status}, '00000000000000000000000000000000', current_timestamp(),
                '00000000000000000000000000000000', current_timestamp(), {tagType}, '{parentId}', {genMode},
                {style}, '{baseTagId}', {viewMode}, {sort})""".format(
                    id=children_patient_tag_type['id'],
                    chainId=children_patient_tag_type['chain_id'],
                    name=children_patient_tag_type['name'],
                    status=children_patient_tag_type['status'],
                    tagType=Utils.orElse(children_patient_tag_type['tag_type'], 'NULL'),
                    parentId=Utils.orElse(children_patient_tag_type['parent_id'], 'NULL'),
                    genMode=Utils.orElse(children_patient_tag_type['gen_mode'], 'NULL'),
                    style=Utils.orElse(Utils.wrap(children_patient_tag_type['style']), 'NULL'),
                    baseTagId=Utils.orElse(children_patient_tag_type['base_tag_id'], 'NULL'),
                    viewMode=Utils.orElse(children_patient_tag_type['view_mode'], 'NULL'),
                    sort=children_patient_tag_type['sort']))

            rules = children_patient_tag_type['rules']
            if not rules:
                continue

            for rule in rules:
                tag_rule_values.append("""
                ({id}, '{chainId}', '{tagId}', '{type}', '{minValue}', '{maxValue}', '{keywords}', '{dateType}', 
                '{dateValue}', '{status}', current_timestamp(), '00000000000000000000000000000000', current_timestamp(), 
                '00000000000000000000000000000000', '{params}', '{matchType}')
                """.format(
                    id=rule['id'],
                    chainId=rule['chain_id'],
                    tagId=rule['tag_id'],
                    type=rule['type'],
                    minValue=rule['min_value'],
                    maxValue=rule['max_value'],
                    keywords=Utils.wrap(rule['keywords']),
                    dateType=rule['date_type'],
                    dateValue=rule['date_value'],
                    status=rule['status'],
                    params=rule['params'],
                    matchType=rule['match_type']
                ).replace("'None'", "NULL"))

    client.execute(u"""
        INSERT INTO v2_patient_tag_type (id, chain_id, name, status, created_by, created, last_modified_by,
                                                     last_modified, tag_type, parent_id, gen_mode, style, base_tag_id,
                                                     view_mode, sort)
        VALUES {values}
    """.format(values=','.join(tag_type_values)))

    if tag_rule_values:
        client.execute("""
        INSERT INTO v2_patient_tag_rule (id, chain_id, tag_id, type, min_value, max_value, keywords,
                                                      date_type, date_value, status, created, created_by, last_modified,
                                                      last_modified_by, params, match_type)
        VALUES {values}
        """.format(values=','.join(tag_rule_values)))


def build_patient_tag_type_by_template(patient_tag_type_templates, chain_id, id_work):
    if not patient_tag_type_templates or len(patient_tag_type_templates) == 0:
        return []

    patient_tag_types = copy.deepcopy(patient_tag_type_templates)
    for parent_patient_tag_type in patient_tag_types:
        parent_patient_tag_type['base_tag_id'] = parent_patient_tag_type['id']
        parent_patient_tag_type['id'] = id_work.getUUID()
        parent_patient_tag_type['chain_id'] = chain_id

        children_patient_tag_types = parent_patient_tag_type['children']
        if children_patient_tag_types is None:
            continue

        for children_patient_tag_type in children_patient_tag_types:
            children_patient_tag_type['base_tag_id'] = children_patient_tag_type['id']
            children_patient_tag_type['id'] = id_work.getUUID()
            children_patient_tag_type['chain_id'] = chain_id
            children_patient_tag_type['parent_id'] = parent_patient_tag_type['id']

            rules = children_patient_tag_type['rules']
            if not rules:
                continue

            for rule in rules:
                rule['id'] = id_work.getUID()
                rule['chain_id'] = chain_id
                rule['tag_id'] = children_patient_tag_type['id']

    return patient_tag_types


def query_patient_tag_type_by_chain_id(client, chain_id, with_tag_rule=False):
    # 查询所有的患者标签类型
    all_patient_tag_types = client.fetchall("""
        select *
        from v2_patient_tag_type
        where chain_id = '{chainId}' and status <> 99
    """.format(chainId=chain_id))

    # 查询标签规则
    tag_type_rules = []
    if with_tag_rule:
        tag_ids = [tag_type['id'] for tag_type in all_patient_tag_types]
        tag_type_rules = query_patient_tag_rule_by_tag_id(client, chain_id, tag_ids)

    tag_id_to_tag_rules = {}
    for rule in tag_type_rules:
        tag_id = rule['tag_id']
        if not tag_id_to_tag_rules.get(tag_id):
            tag_id_to_tag_rules[tag_id] = []

        tag_id_to_tag_rules[tag_id].append(rule)

    # 构建成树形
    tag_type_id_to_tag_type = {patient_tag_type['id']: patient_tag_type for patient_tag_type in all_patient_tag_types}
    tree = []
    for patient_tag_type in all_patient_tag_types:
        patient_tag_type['rules'] = tag_id_to_tag_rules.get(patient_tag_type['id'])
        parent_id = patient_tag_type['parent_id']
        if parent_id is None:
            tree.append(patient_tag_type)
        else:
            parent = tag_type_id_to_tag_type[parent_id]
            if parent is not None:
                children = parent.setdefault('children', [])
                children.append(patient_tag_type)

    return tree


def query_patient_tag_rule_by_tag_id(client, chain_id, tag_ids):
    if not tag_ids:
        return {}

    tag_id_str = ", ".join("'" + tag_id + "'" for tag_id in tag_ids)
    return client.fetchall("""
        select *
        from v2_patient_tag_rule
        where chain_id = '{chainId}' and tag_id in ({tagId})
    """.format(chainId=chain_id, tagId=tag_id_str))


def delete_patient_tag_type_by_chain_id(client, chain_id):
    return client.execute("""
        update v2_patient_tag_type
        set status = 99
        where chain_id = '{chainId}'
    """.format(chainId=chain_id))


def has_create_or_update_patient_tag_type_by_chain_id(client, chain_id):
    return client.fetchone("""
        select count(1) as count
        from v2_patient_tag_type
        where chain_id = '{chainId}' and last_modified_by <> '00000000000000000000000000000000'
    """.format(chainId=chain_id))


def used_patient_tag_by_chain_id(client, chain_id):
    return client.fetchall("""
        select tag_id
        from v2_patient_tag
        where chain_id = '{chainId}'
    """.format(chainId=chain_id))


def query_patient_tag_type_tree_by_chain_id(client, chain_id):
    return client.fetchall("""
        select c.id, c.name, p.id as parent_id, p.name as parent_name
        from v2_patient_tag_type c
                 left join v2_patient_tag_type p on (c.parent_id = p.id)
        where c.chain_id = '{chainId}'
          and c.status = 1
          and p.status = 1
          and c.parent_id is not null
    """.format(chainId=chain_id))


def delete_patient_tag_type_by_exclude_id(client, chain_id, exclude_tag_ids):
    return client.execute("""
        update v2_patient_tag_type
        set status = 99
        where chain_id = '{chainId}'
          and id not in ({tagIds}); 
    """.format(chainId=chain_id, tagIds=','.join({Utils.wrap(exclude_tag_id) for exclude_tag_id in exclude_tag_ids})))


def insert_tag_type_by_tag_type_model(patient_db_client, chain_id, insert_tag_types, insert_tag_rules):
    if not insert_tag_types or len(insert_tag_types) == 0:
        return

    tag_type_values = []
    for tag_type in insert_tag_types:
        tag_type_values.append(
            """('{id}', '{chainId}', '{name}', '{tagType}', {status}, '{createdBy}', current_timestamp(), 
            '{lastModifiedBy}', current_timestamp(), '{parentId}', {genMode}, {viewMode}, '{style}', 
            '{baseTagId}')""".format(
                id=tag_type['id'],
                chainId=chain_id,
                name=tag_type['name'],
                tagType=tag_type['tagType'],
                status=1,
                createdBy=default_id,
                lastModifiedBy=default_id,
                parentId=tag_type.get('parentId'),
                genMode=tag_type['genMode'],
                viewMode=tag_type['viewMode'],
                style=tag_type['style'],
                baseTagId=tag_type['baseTagId']).replace("'None'", "NULL"))

    tag_rule_values = []
    for rule in insert_tag_rules:
        tag_rule_values.append("""
                        ({id}, '{chainId}', '{tagId}', '{type}', '{minValue}', '{maxValue}', '{keywords}', '{dateType}', 
                        '{dateValue}', '{status}', current_timestamp(), '00000000000000000000000000000000', 
                        current_timestamp(), '00000000000000000000000000000000', '{params}', '{matchType}')
                        """.format(
            id=rule['id'],
            chainId=chain_id,
            tagId=rule['tagId'],
            type=rule['type'],
            minValue=rule['minValue'],
            maxValue=rule['maxValue'],
            keywords=rule['keywords'],
            dateType=rule['dateType'],
            dateValue=rule['dateValue'],
            status=rule['status'],
            params=rule['params'],
            matchType=rule['matchType']
        ).replace("'None'", "NULL"))

    sql = """
    insert into v2_patient_tag_type (id, chain_id, name, tag_type, status, created_by, created, last_modified_by,
                                 last_modified, parent_id, gen_mode, view_mode, style, base_tag_id)
    values {values}
    """.format(values=','.join(tag_type_values))
    patient_db_client.execute(sql)

    if tag_rule_values:
        patient_db_client.execute("""
        INSERT INTO v2_patient_tag_rule (id, chain_id, tag_id, type, min_value, max_value, keywords,
                                                      date_type, date_value, status, created, created_by, last_modified,
                                                      last_modified_by, params, match_type)
        VALUES {values}
        """.format(values=','.join(tag_rule_values)))


def count_chain_custom_patient_tag_type(client, chain_id):
    # 查询连锁自定义的标签类型数量
    return client.fetchone("""
        select count(*) as count
        from v2_patient_tag_type
        where chain_id = '{chainId}'
          and (created_by != '00000000000000000000000000000000' or last_modified_by != '00000000000000000000000000000000');
    """.format(chainId=chain_id))['count']


def main():
    print(sys.path)
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    oral_init = OralInit(args.region_name,args.chain_id)
    oral_init.run()


if __name__ == '__main__':
    main()
