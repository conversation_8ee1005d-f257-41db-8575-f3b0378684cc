#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
import argparse

default_id = '00000000000000000000000000000000'


def updateTreatmentExecuteSmsSwitch(abcRegion,chain_id):
    db_client = DBClient(abcRegion,'abc_cis_account_base', 'abc_cis_message', 'prod', True)
    db_client.execute("""update v2_message_switch_config
                        set sms_template = '{{"types":[100030,100031],"templates":[{{"name":"执行划扣","content":"【ABC数字医疗云】您在体验诊所完成了针灸1穴位，剩余1穴位。"}}, {{"name":"撤销执行","content":"【ABC数字医疗云】您在体验诊所撤销了针灸1穴位，剩余2穴位。"}}]}}'
                        where `key` = 'others.zl-execute' and chain_id = '{chainId}';""".format(chainId = chain_id))

def run(abcRegion,chain_id):
    updateTreatmentExecuteSmsSwitch(abcRegion,chain_id)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.region_name, args.chain_id)


if __name__ == '__main__':
    main()
