#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
import time
import sys
import argparse
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from scripts.common.utils.lists import ListUtils
from scripts.common.utils.sqls import SqlUtils


from multizone.db import DBClient


class DispensePriceCheck:
    chain_id = None
    basic_adb_client = None
    charge_ods_client = None
    dispensing_adb_client = None
    goods_log_db_client = None
    id_work = None

    def __init__(self, abcRegion, chain_id):
        self.chain_id = chain_id
        self.basic_adb_client = DBClient(abcRegion, 'adb', 'abc_cis_basic', 'prod', True)
        self.charge_ods_client = DBClient(abcRegion, 'ods', 'abc_cis_charge', 'prod', True)
        self.dispensing_adb_client = DBClient(abcRegion, 'adb', 'abc_cis_dispensing', 'prod', True)
        self.goods_log_adb_client = DBClient(abcRegion, 'adb', 'abc_cis_goods_log', 'prod', True)
        self.goods_log_db_client = DBClient(abcRegion, 'abc_cis_stock_zip', 'abc_cis_goods_log', 'prod', True)

    def chain_dispense_price_check(self):
        # 连锁下的所有门店
        organs = self.basic_adb_client.fetchall("""
                    select o.id, cgo.created
                    from organ o inner join
                        v2_clinic_gray_organ cgo on (o.parent_id = cgo.chain_id)
                    where o.parent_id = '{chainId}' and o.status < 90
                """.format(chainId=self.chain_id))
        chain_error_count = 0
        for organ in organs:
            start_time = time.time() * 1000
            clinic_error_count = self.clinic_dispense_price_check(organ['id'], organ['created'])
            print(f"clinic {organ['id']} error count: {clinic_error_count}, cost:{time.time() * 1000 - start_time}ms")
            chain_error_count += clinic_error_count

        print(f"chainid {self.chain_id} error count: {chain_error_count}")

    def clinic_dispense_price_check(self, clinic_id, start_time):
        error_count = 0
        cursor = None
        while True:
            # 查询收费单
            charge_sheet_ids = page_query_charge_sheet_ids(self.charge_ods_client, clinic_id, start_time, 50, cursor)
            if not charge_sheet_ids:
                break
            cursor = charge_sheet_ids[-1]

            # 查询收费项
            charge_form_items = list_charge_form_items_by_charge_sheet_ids(self.charge_ods_client, charge_sheet_ids)
            if not charge_form_items:
                continue

            # 查询发药项
            charge_form_item_ids = ListUtils.dist_mapping(charge_form_items, lambda _charge_form_item: _charge_form_item['id'])
            dispense_items = list_dispense_item_by_source_form_item_id(self.dispensing_adb_client,
                                                                       charge_form_item_ids)
            if not dispense_items:
                continue

            # 查询goods_stock_log
            dispense_item_ids = ListUtils.dist_mapping(dispense_items, lambda _dispense_item: _dispense_item['id'])
            goods_stock_logs = list_goods_stock_log_by_order_detail_id(self.goods_log_adb_client, dispense_item_ids)
            if not goods_stock_logs:
                continue

            source_form_item_id_to_dispense_item = ListUtils.to_map(dispense_items, lambda _dispense_item: _dispense_item['source_form_item_id'])
            order_detail_id_to_goods_stock_logs = ListUtils.group_by(goods_stock_logs, lambda _goods_stock_log: _goods_stock_log['order_detail_id'])
            for charge_form_item in charge_form_items:
                charge_form_item_id = charge_form_item['id']
                dispense_item = source_form_item_id_to_dispense_item.get(charge_form_item_id)
                if not dispense_item:
                    continue
                dispense_item_id = dispense_item['id']
                goods_stock_logs = order_detail_id_to_goods_stock_logs.get(dispense_item_id)
                if not goods_stock_logs:
                    continue
                error_count += self.dispense_price_check(charge_form_item, goods_stock_logs)
        return error_count

    def dispense_price_check(self, charge_form_item, goods_stock_logs):
        if not charge_form_item or not goods_stock_logs:
            return 0

        error_count = 0
        for goods_stock_log in goods_stock_logs:
            total_price = charge_form_item['total_price']
            discount_price = charge_form_item['discount_price']
            origin_total_price = goods_stock_log['origin_total_price']
            # discount_price 是负数，所以 - 实际上是增加了发药金额
            # 如果刚好出现了这种情况的，说明发药金额计算错误了
            # 实际值应该是 total_price + discount_price
            if origin_total_price == total_price - discount_price:
                new_origin_total_price = total_price + discount_price
                sql = """
                update v2_goods_stock_log 
                    set origin_total_price = {originTotalPrice}
                where id = '{id}'
                """.format(originTotalPrice=new_origin_total_price, id=goods_stock_log['id'])
                # print(sql)
                self.goods_log_db_client.execute(sql)
                sql = """
                update v2_goods_stock_log_bat 
                    set total_origin_price = {totalOriginPrice}
                where id = '{id}'
                """.format(totalOriginPrice=new_origin_total_price, id=goods_stock_log['bat_id'])
                # print(sql)
                self.goods_log_db_client.execute(sql)
                error_count += 1

        return error_count

    def run(self):
        self.chain_dispense_price_check()


def list_goods_stock_log_by_order_detail_id(client, order_detail_ids):
    if not order_detail_ids:
        return

    order_detail_id_str = SqlUtils.to_in_value(order_detail_ids)
    return client.fetchall("""
        select *
        from v2_goods_stock_log
        where order_detail_id in ({orderDetailIds}) and action = '发药'
    """.format(orderDetailIds=order_detail_id_str))


def list_dispense_item_by_source_form_item_id(client, source_form_ids):
    if not source_form_ids:
        return []

    source_form_id_str = SqlUtils.to_in_value(source_form_ids)
    return client.fetchall("""
        select id, source_form_item_id
        from v2_dispensing_form_item
        where source_form_item_id in ({sourceFormIds}) and associate_form_item_id is null
    """.format(sourceFormIds=source_form_id_str))


def list_charge_form_items_by_charge_sheet_ids(client, charge_sheet_ids):
    if not charge_sheet_ids:
        return []

    charge_sheet_id_str = SqlUtils.to_in_value(charge_sheet_ids)
    return client.fetchall("""
        select *
        from v2_charge_form_item
        where charge_sheet_id in ({ids}) and discount_price < 0
    """.format(ids=charge_sheet_id_str))


def page_query_charge_sheet_ids(client, clinic_id, start_time, limit, cursor=None):
    charge_sheets = None
    if not cursor:
        sql = """
            select id
            from v2_charge_sheet cs
            where cs.clinic_id = '{clinicId}'
              and cs.created between '{startTime}' and '2023-09-11 00:00:00'
              and cs.discount_fee < 0
            order by cs.id
            limit {limit}
        """.format(clinicId=clinic_id, startTime=start_time, limit=limit)
        charge_sheets = client.fetchall(sql)
    else:
        sql = """
                    select id
                    from v2_charge_sheet cs
                    where cs.clinic_id = '{clinicId}'
                      and cs.created between '{startTime}' and '2023-09-11 00:00:00'
                      and cs.discount_fee < 0
                      and cs.id > '{cursor}'
                    order by cs.id
                    limit {limit}
                """.format(clinicId=clinic_id, startTime=start_time, limit=limit, cursor=cursor)
        charge_sheets = client.fetchall(sql)

    return ListUtils.dist_mapping(charge_sheets, lambda charge_sheet: charge_sheet['id'])


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    check = DispensePriceCheck(args.region_name, args.chain_id)
    check.run()


if __name__ == '__main__':
    main()
