#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os


CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient,escape_string
import argparse
from idwork import IdWork

default_id = '00000000000000000000000000000000'


class OralInit:
    chain_id = None
    patient_db_client = None
    basic_db_client = None
    shorturl_db_client = None
    id_work = None
    abcRegion = None

    def __init__(self, abcRegion, chain_id):
        self.chain_id = chain_id
        self.abcRegion = abcRegion
        self.patient_db_client = DBClient(abcRegion, 'abc_cis_account_base', 'abc_cis_patient', 'prod', True)
        self.basic_db_client = DBClient(abcRegion, 'abc_cis_mixed', 'abc_cis_basic', 'prod', True)
        self.message_db_client = DBClient(abcRegion, 'abc_cis_account_base', 'abc_cis_message', 'prod', True)
        self.shorturl_db_client = DBClient(abcRegion, 'abc_cis_mixed', 'abc_cis_shorturl', 'prod', True)
        self.outpatient_db_client = DBClient(abcRegion, 'abc_cis_outpatient', 'abc_cis_outpatient', 'prod', True)
        self.id_work = IdWork(self.shorturl_db_client, False)
        self.id_work.config()

    def revisit_init_chain(self, chain_id, his_type):
        # 连锁下的所有门店
        organs = self.basic_db_client.fetchall("""
            select id
            from organ
            where parent_id = '{chainId}' and status < 90
        """.format(chainId=chain_id))
        for organ in organs:
            self.revisit_init_clinic(chain_id, organ['id'], his_type)

    def revisit_init_clinic(self, chain_id, clinic_id, his_type):
        """
        将门店自己创建的模板刷到自定义目录下
        将系统默认的模板刷到常规目录下
        口腔门店增加 牙体、牙周、外科、正畸、修复、种植目录
        """
        # 查询随访目标模板
        all_revisit_target_temples = self.patient_db_client.fetchall("""
            select *
            from v2_patient_revisit_template
            where clinic_id = '00000000000000000000000000000000' or (clinic_id = '{clinicId}' and created_by != '00000000000000000000000000000000')
              and type = 0
              and is_deleted = 0
            order by created
        """.format(clinicId=clinic_id))
        if len(all_revisit_target_temples) == 0:
            return

        # 根据 clinic_id 分组
        clinic_id_to_revisit_target_temples = {}
        for revisit_target_temple in all_revisit_target_temples:
            if revisit_target_temple['clinic_id'] not in clinic_id_to_revisit_target_temples:
                clinic_id_to_revisit_target_temples[revisit_target_temple['clinic_id']] = []
            clinic_id_to_revisit_target_temples[revisit_target_temple['clinic_id']].append(revisit_target_temple)

        insert_revisit_catalogue_dir_sqls = []
        insert_revisit_catalogue_file_sqls = []
        insert_revisit_template_sqls = []

        # 处理用户自定义模板（就算用户没有自定义过都要先创建一个自定义目录）
        self.revisit_init_clinic_build_sql(chain_id,
                                           clinic_id,
                                           insert_revisit_catalogue_dir_sqls,
                                           insert_revisit_catalogue_file_sqls,
                                           insert_revisit_template_sqls,
                                           clinic_id_to_revisit_target_temples.get(clinic_id),
                                           '自定义')
        # 处理常规模板
        if default_id in clinic_id_to_revisit_target_temples:
            self.revisit_init_clinic_build_sql(chain_id,
                                               clinic_id,
                                               insert_revisit_catalogue_dir_sqls,
                                               insert_revisit_catalogue_file_sqls,
                                               insert_revisit_template_sqls,
                                               clinic_id_to_revisit_target_temples[default_id],
                                               '常规')

        # 如果是口腔门店，还需要生成内置的目录 牙体、牙周、外科、正畸、修复、种植目录
        if his_type == 1:
            # 牙体
            revisit_target_temples = [{"clinic_id": default_id, "name": "根管治疗", "content": "",
                                       "tasks": """[{"target":"1. 询问是否有不适症状，如疼痛<br>2. 询问是否有咬合痛、咬合高点<br>3. 告知患者暂封材料存在脱落的可能。如有脱落，及时与医生联系","executeMode":0,"executorId":"","scheduleDate":"2023-07-20T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":1,"notifyEmployeeList":[]},{"target":"1. 询问牙齿是否有松动、疼痛，补牙材料是否有松动、脱落<br>2. 建议修复。根管治疗后，应及时对患牙做冠修复，防止牙折断。因去除牙髓的患牙脆性较大，在咬硬物时，容易发生牙折断。","executeMode":0,"executorId":"","scheduleDate":"2023-07-26T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":7,"notifyEmployeeList":[]},{"target":"1. 询问牙齿是否有松动、疼痛，补牙材料是否有松动、脱落<br>2. 询问患者是否已做全冠修复（若无，则建议修复，并详细说明修复的必要性）<br>3. 建议复查","executeMode":0,"executorId":"","scheduleDate":"2023-10-17T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":90,"notifyEmployeeList":[]},{"target":"1. 询问牙齿是否有松动、疼痛，补牙材料是否有松动、脱落<br>2. 询问患者是否已做全冠修复（若无，则建议修复，并详细说明修复的必要性）<br>3. 预约复诊检查","executeMode":0,"executorId":"","scheduleDate":"2024-01-15T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":180,"notifyEmployeeList":[]},{"target":"1. 询问牙齿是否有松动、疼痛，补牙材料是否有松动、脱落<br>2. 询问患者是否已做全冠修复（若无，则建议修复，并详细说明修复的必要性）<br>3. 预约复诊检查","executeMode":0,"executorId":"","scheduleDate":"2024-07-13T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":360,"notifyEmployeeList":[]}]"""},
                                      {"clinic_id": default_id, "name": "补牙", "content": "",
                                       "tasks": """[{"target":"1. 询问咬合是否有高点<br>2. 询问牙齿是否酸软、咀嚼是否有力<br>3. 询问牙齿有无冷热刺激痛<br>4. 若有不适，暂不用该牙咀嚼，且及时与医生联系","executeMode":0,"executorId":"","scheduleDate":"2023-07-20T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":1,"notifyEmployeeList":[]},{"target":"1. 询问牙齿不适症状是否缓解、咀嚼是否有力 <br>2. 若无不适，请定期检查        <br>3. 若仍有不适，约复诊调改或重补，并申明重补或调改免费<br>4. 满意度调查","executeMode":0,"executorId":"","scheduleDate":"2023-07-26T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":7,"notifyEmployeeList":[]},{"target":"1. 牙齿充填物是否松动或脱落        <br>2. 牙齿咀嚼是否习惯 <br>3. 告知患者定期复查","executeMode":0,"executorId":"","scheduleDate":"2023-10-17T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":90,"notifyEmployeeList":[]},{"target":"1. 牙齿充填物是否松动或脱落        <br>2. 牙齿咀嚼是否习惯 <br>3. 预约复查，定期洁牙","executeMode":0,"executorId":"","scheduleDate":"2024-01-15T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":180,"notifyEmployeeList":[]},{"target":"1. 建议每年定期检查<br>2. 定期洁牙","executeMode":0,"executorId":"","scheduleDate":"2024-07-13T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":360,"notifyEmployeeList":[]}]"""}]
            self.revisit_init_clinic_build_sql(chain_id,
                                               clinic_id,
                                               insert_revisit_catalogue_dir_sqls,
                                               insert_revisit_catalogue_file_sqls,
                                               insert_revisit_template_sqls,
                                               revisit_target_temples,
                                               '牙体')

            # 牙周
            revisit_target_temples = [{"clinic_id": default_id, "name": "牙周治疗", "content": "",
                                       "tasks": """[{"target":"1. 询问是否有牙齿冷热酸甜刺激现象、出血、牙周红肿等<br>2. 告知患者注意口腔卫生，可使用抗过敏牙膏，每天坚持2次刷牙<br>3. 告知患者饮食宜清淡，勿食辛辣刺激食物<br>4. 预约复诊检查","executeMode":0,"executorId":"","scheduleDate":"2023-07-19T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":0,"notifyEmployeeList":[]},{"target":"1. 询问是否有牙齿冷热酸甜刺激现象、出血、牙周红肿等<br>2. 告知患者注意口腔卫生，可使用抗过敏牙膏，每天坚持2次刷牙<br>3. 告知患者饮食宜清淡，勿食辛辣刺激食物<br>4. 预约复诊检查","executeMode":0,"executorId":"","scheduleDate":"2023-07-26T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":7,"notifyEmployeeList":[]},{"target":"1. 询问是否有刷牙出血、吮吸出血现象<br>2. 告知患者注意口腔卫生，可使用抗过敏牙膏，每天坚持2次刷牙<br>3. 预约复诊检查","executeMode":0,"executorId":"","scheduleDate":"2023-08-18T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":30,"notifyEmployeeList":[]},{"target":"1. 询问是否有刷牙出血、吮吸出血现象<br>2. 告知患者注意口腔卫生，可使用抗过敏牙膏，每天坚持2次刷牙<br>3. 预约复诊检查","executeMode":0,"executorId":"","scheduleDate":"2023-10-17T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":90,"notifyEmployeeList":[]},{"target":"1. 询问是否有刷牙出血、吮吸出血现象<br>2. 告知患者注意口腔卫生，可使用抗过敏牙膏，每天坚持2次刷牙","executeMode":0,"executorId":"","scheduleDate":"2024-07-13T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":360,"notifyEmployeeList":[]}]"""},
                                      {"clinic_id": default_id, "name": "洁牙", "content": "",
                                       "tasks": """[{"target":"1. 询问洗牙后，牙齿有何不适<br>2. 告知患者刚洁完牙后会有些不习惯，如牙齿酸软，并解释牙缝变宽的原因<br>3. 告知患者洁牙后24小时内不宜吃过冷、过热食物","executeMode":0,"executorId":"","scheduleDate":"2023-07-20T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":1,"notifyEmployeeList":[]},{"target":"1. 询问牙齿有无酸软，牙龈有无肿痛出血<br>2. 询问有无按照正确的刷牙方法刷牙","executeMode":0,"executorId":"","scheduleDate":"2023-07-26T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":7,"notifyEmployeeList":[]},{"target":"1. 询问口腔卫生情况<br>2. 询问牙齿有没有牙结石及色渍<br>3. 询问牙齿有无松动，刷牙或咬硬物有无出血等<br>4. 建议复诊检查，有问题及时与医生联系","executeMode":0,"executorId":"","scheduleDate":"2023-10-17T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":90,"notifyEmployeeList":[]},{"target":"1. 询问口腔卫生情况<br>2. 询问牙齿有没有牙结石及色渍<br>3. 询问牙齿有无松动，刷牙或咬硬物有无出血等<br>4. 建议复诊检查，有问题及时与医生联系","executeMode":0,"executorId":"","scheduleDate":"2024-01-15T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":180,"notifyEmployeeList":[]},{"target":"1. 询问口腔卫生情况<br>2. 预约洁牙，告知患者每年洁牙一次的必要性","executeMode":0,"executorId":"","scheduleDate":"2024-07-13T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":360,"notifyEmployeeList":[]}]"""}]
            self.revisit_init_clinic_build_sql(chain_id,
                                               clinic_id,
                                               insert_revisit_catalogue_dir_sqls,
                                               insert_revisit_catalogue_file_sqls,
                                               insert_revisit_template_sqls,
                                               revisit_target_temples,
                                               '牙周')

            # 外科
            revisit_target_temples = [{"clinic_id": default_id, "name": "普通牙拔除", "content": "",
                                       "tasks": """[{"target":"1. 询问拔牙处有无疼痛、肿胀、出血<br>2. 告知患者，如果疼痛较甚，可适当服用止痛药<br>3. 告知患者拔牙后注意事项，如勿用舌头舔拔牙处、勿食热饮、勿食辛辣刺激食物等","executeMode":0,"executorId":"","scheduleDate":"2023-07-19T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":0,"notifyEmployeeList":[]},{"target":"1. 询问拔牙处有无疼痛、肿胀、出血<br>2. 常规进行卫生宣教","executeMode":0,"executorId":"","scheduleDate":"2023-07-22T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":3,"notifyEmployeeList":[]},{"target":"1. 询问拔牙创口是否已经恢复<br>2. 告知患者2-3个月为修复的最佳时机，并讲解及时修复的必要性","executeMode":0,"executorId":"","scheduleDate":"2023-09-17T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":60,"notifyEmployeeList":[]},{"target":"1. 询问是否已经镶牙<br>2. 预约复诊检查","executeMode":0,"executorId":"","scheduleDate":"2024-01-15T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":180,"notifyEmployeeList":[]},{"target":"1. 询问是否已经镶牙<br>2. 预约复诊检查","executeMode":0,"executorId":"","scheduleDate":"2024-07-13T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":360,"notifyEmployeeList":[]}]"""},
                                      {"clinic_id": default_id, "name": "阻生牙拔除", "content": "",
                                       "tasks": """[{"target":"1. 询问拔牙创口有无疼痛、出血和肿胀，是否有麻木，身体有无发烧          <br>2. 嘱咐饮食宜软、清淡，保持口腔卫生，注意休息，不要熬夜和做剧烈运动            <br>3. 告知患者，如果疼痛较甚，可适当服用止痛药","executeMode":0,"executorId":"","scheduleDate":"2023-07-19T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":0,"notifyEmployeeList":[]},{"target":"1. 询问拔牙处有无疼痛、肿胀、出血<br>2. 常规进行卫生宣教","executeMode":0,"executorId":"","scheduleDate":"2023-07-29T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":10,"notifyEmployeeList":[]},{"target":"1. 询问近来口腔卫生情况        <br>2. 告知定期复查","executeMode":0,"executorId":"","scheduleDate":"2023-10-17T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":90,"notifyEmployeeList":[]},{"target":"1. 询问牙齿情况，牙龈有无出血（刷牙或咬硬物时）等<br>2. 预约到院进行口腔检查及洁牙，告知洁牙的重要性","executeMode":0,"executorId":"","scheduleDate":"2024-01-15T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":180,"notifyEmployeeList":[]},{"target":"1. 询问牙齿情况，牙龈有无出血（刷牙或咬硬物时）等<br>2. 预约到院进行口腔检查及洁牙，告知洁牙的重要性","executeMode":0,"executorId":"","scheduleDate":"2024-07-13T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":360,"notifyEmployeeList":[]}]"""}]
            self.revisit_init_clinic_build_sql(chain_id,
                                               clinic_id,
                                               insert_revisit_catalogue_dir_sqls,
                                               insert_revisit_catalogue_file_sqls,
                                               insert_revisit_template_sqls,
                                               revisit_target_temples,
                                               '外科')
            # 正畸
            revisit_target_temples = [{"clinic_id": default_id, "name": "正畸(保持器佩戴)", "content": "",
                                       "tasks": """[{"target":"1. 告知患者白天、晚上都需佩带保持器，吃饭的时候需取下，饭后需清洗<br>2. 预约下次复查时间","executeMode":0,"executorId":"","scheduleDate":"2023-07-19T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":0,"notifyEmployeeList":[]},{"target":"1. 询问保持器佩戴的情况，是否适应，有无不适症状 <br>2. 提醒饭后需清洗保持器<br>3. 注意口腔卫生，正确刷牙","executeMode":0,"executorId":"","scheduleDate":"2023-07-26T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":7,"notifyEmployeeList":[]},{"target":"1. 询问保持器佩戴的情况，是否适应，有无不适症状 <br>2. 提醒饭后需清洗保持器<br>3. 注意口腔卫生，正确刷牙","executeMode":0,"executorId":"","scheduleDate":"2023-08-18T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":30,"notifyEmployeeList":[]},{"target":"1. 询问保持器佩戴的情况，是否适应，有无不适症状 <br>2. 提醒饭后需清洗保持器<br>3. 注意口腔卫生，正确刷牙<br>4. 预约下次复查时间","executeMode":0,"executorId":"","scheduleDate":"2023-10-17T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":90,"notifyEmployeeList":[]},{"target":"1. 询问保持器佩戴的情况，是否适应，有无不适症状 <br>2. 提醒饭后需清洗保持器<br>3. 注意口腔卫生，正确刷牙<br>4. 预约下次复查时间","executeMode":0,"executorId":"","scheduleDate":"2024-01-15T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":180,"notifyEmployeeList":[]},{"target":"1. 询问保持器佩戴的情况，是否适应，有无不适症状 <br>2. 提醒饭后需清洗保持器<br>3. 注意口腔卫生，正确刷牙<br>4. 预约下次复查时间","executeMode":0,"executorId":"","scheduleDate":"2024-07-13T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":360,"notifyEmployeeList":[]}]"""},
                                      {"clinic_id": default_id, "name": "正畸(矫治器佩戴)", "content": "",
                                       "tasks": """[{"target":"1. 询问是否有不适，刚佩戴需有一定的适应过程，牙齿在缓慢移动的过程中会出现酸软无力，胀痛，属于正常现象 <br>2. 询问托槽是否刮嘴，如果刮嘴，用正畸蜡涂抹刮嘴处<br>3. 注意刷牙，吃软且小块的食物，不要吃较粘、较硬食物，避免托槽脱落<br>4. 告知患者，如有不适症状，及时随诊","executeMode":0,"executorId":"","scheduleDate":"2023-07-19T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":0,"notifyEmployeeList":[]},{"target":"1. 询问是否有牙齿酸软无力、胀痛、口腔溃疡 <br>2. 询问托槽是否刮嘴，如果刮嘴，用正畸蜡涂抹刮嘴处<br>3. 注意刷牙，吃软且小块的食物，不要吃较粘、较硬食物，避免托槽脱落<br>4. 告知患者，如有不适症状，及时随诊","executeMode":0,"executorId":"","scheduleDate":"2023-07-26T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":7,"notifyEmployeeList":[]},{"target":"1. 询问对托槽的适应情况     <br>2. 注意口腔保健方法 <br>3. 提醒定期复诊","executeMode":0,"executorId":"","scheduleDate":"2023-10-17T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":90,"notifyEmployeeList":[]}]"""}]
            self.revisit_init_clinic_build_sql(chain_id,
                                               clinic_id,
                                               insert_revisit_catalogue_dir_sqls,
                                               insert_revisit_catalogue_file_sqls,
                                               insert_revisit_template_sqls,
                                               revisit_target_temples,
                                               '正畸')

            # 修复
            revisit_target_temples = [{"clinic_id": default_id, "name": "戴牙", "content": "",
                                       "tasks": """[{"target":"1. 询问患者戴牙后是否舒适，有无食物嵌塞、邻牙酸胀、上下咬合不适 <br>2. 告知患者保持良好的口腔卫生 <br>3. 告知患者，如有不适症状，及时随诊<br>4. 预约复诊检查","executeMode":0,"executorId":"","scheduleDate":"2023-07-20T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":1,"notifyEmployeeList":[]},{"target":"1. 询问患者戴牙后是否舒适，有无食物嵌塞、邻牙酸胀、上下咬合不适 <br>2. 告知患者保持良好的口腔卫生 <br>3. 告知患者，如有不适症状，及时随诊<br>4. 预约复诊检查","executeMode":0,"executorId":"","scheduleDate":"2023-08-18T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":30,"notifyEmployeeList":[]},{"target":"1. 查看就诊记录，近日是否就诊 <br>2. 预约免费复查时间 <br>3. 视情况告知如有崩瓷、食物嵌塞等情况，在保修期内可免费重做<br>4. 告知患者，如有不适症状，及时随诊","executeMode":0,"executorId":"","scheduleDate":"2024-01-15T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":180,"notifyEmployeeList":[]},{"target":"1. 查看就诊记录，近日是否就诊 <br>2. 预约免费复查时间 <br>3. 视情况告知如有崩瓷、食物嵌塞等情况，在保修期内可免费重做<br>4. 告知患者，如有不适症状，及时随诊","executeMode":0,"executorId":"","scheduleDate":"2024-07-13T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":360,"notifyEmployeeList":[]}]"""},
                                      {"clinic_id": default_id, "name": "活动义齿", "content": "",
                                       "tasks": """[{"target":"1. 告知活动牙佩戴初期可能出现的不适症状<br>2. 指导摘戴方式、提醒清洗和使用方法（睡前要取下并放入冷水中清洗）<br>3. 告知患者若有不适，及时与医生联系","executeMode":0,"executorId":"","scheduleDate":"2023-07-20T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":1,"notifyEmployeeList":[]},{"target":"1. 询问活动牙有无压痛，佩戴是否服帖 <br>2. 指导摘戴方式、提醒清洗和使用方法（睡前要取下并放入冷水中清洗）<br>3. 告知患者若有不适，及时与医生联系","executeMode":0,"executorId":"","scheduleDate":"2023-07-26T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":7,"notifyEmployeeList":[]},{"target":"1. 询问活动牙坚持佩戴情况<br>2. 询问活动牙有无压痛，佩戴是否服帖<br>3. 询问清洗保养情况如何<br>4. 告知随着牙龈的萎缩，活动牙会出现不服帖的情况，需要定期半年复查<br>5. 告知半年后复查时间","executeMode":0,"executorId":"","scheduleDate":"2023-08-18T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":30,"notifyEmployeeList":[]},{"target":"1. 查看就诊记录，近日是否就诊 <br>2. 预约免费复查时间 <br>3. 告知患者，如有不适症状，及时随诊","executeMode":0,"executorId":"","scheduleDate":"2024-01-15T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":180,"notifyEmployeeList":[]},{"target":"1. 查看就诊记录，近日是否就诊 <br>2. 预约免费复查时间 <br>3. 告知患者，如有不适症状，及时随诊","executeMode":0,"executorId":"","scheduleDate":"2024-07-13T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":360,"notifyEmployeeList":[]}]"""}]
            self.revisit_init_clinic_build_sql(chain_id,
                                               clinic_id,
                                               insert_revisit_catalogue_dir_sqls,
                                               insert_revisit_catalogue_file_sqls,
                                               insert_revisit_template_sqls,
                                               revisit_target_temples,
                                               '修复')

            # 种植
            revisit_target_temples = [{"clinic_id": default_id, "name": "种植I期(植入种植体)", "content": "",
                                       "tasks": """[{"target":"1. 询问麻药是否消退<br>2. 询问种植区是否渗血<br>3. 嘱顾客种植术后注意事项，如勿舔伤口，勿食辛辣刺激食物等<br>4. 嘱顾客按医嘱服用消炎药，必要时服用止痛药。如有任何疑问或不适，需及时联系医生<br>5. 嘱顾客10日后复诊拆线","executeMode":0,"executorId":"","scheduleDate":"2023-07-19T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":0,"notifyEmployeeList":[]},{"target":"1. 询问种植区伤口愈合情况，是否渗血、疼痛，或有其他不适症状<br>2. 嘱顾客3个月后复诊，检查是否能进行II期手术 <br>3. 顾客满意度调查","executeMode":0,"executorId":"","scheduleDate":"2023-07-22T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":3,"notifyEmployeeList":[]}]"""},
                                      {"clinic_id": default_id, "name": "种植II期(安装愈合基台)", "content": "",
                                       "tasks": """[{"target":"1. 询问种植区是否疼痛、出血明显，及其它不适症状。若症状轻微，则属于正常现象，若症状严重，请及时联系医生<br>2. 嘱顾客种植术后注意事项，如勿舔伤口，勿食辛辣刺激食物等","executeMode":0,"executorId":"","scheduleDate":"2023-07-19T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":0,"notifyEmployeeList":[]},{"target":"1. 询问种植区伤口愈合情况，是否渗血、疼痛，或有其他不适症状<br>2. 预约戴牙时间<br>3. 顾客满意度调查","executeMode":0,"executorId":"","scheduleDate":"2023-07-22T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":3,"notifyEmployeeList":[]}]"""},
                                      {"clinic_id": default_id, "name": "种植III期(戴牙)", "content": "",
                                       "tasks": """[{"target":"1. 询问顾客戴牙冠后的咀嚼情况，是否出现咀嚼不适、疼痛<br>2. 告知顾客后期可能出现塞牙，教导如何正确使用牙线维护口腔清洁<br>3. 告知顾客后期可能出现牙冠松动现象，不用过于担心，可预约时间联系医生处理<br>4. 嘱顾客定期进行种植体牙周护理，建议每3个月至半年洁牙及护理1次","executeMode":0,"executorId":"","scheduleDate":"2023-07-20T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":1,"notifyEmployeeList":[]},{"target":"1. 询问顾客戴牙冠后是否出现咀嚼不适、疼痛、食物嵌塞<br>2. 嘱顾客半年后复诊，检查修复体<br>3. 若有任何疑问或不适，及时联系医生<br>4. 客户满意度调查","executeMode":0,"executorId":"","scheduleDate":"2023-10-17T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":90,"notifyEmployeeList":[]},{"target":"1. 询问顾客修复体情况，是否出现咀嚼不适、疼痛、牙龈出血、食物嵌塞<br>2. 预约顾客半年修复体复诊<br>3. 定期洁治及牙周护理","executeMode":0,"executorId":"","scheduleDate":"2024-01-15T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":180,"notifyEmployeeList":[]},{"target":"1. 询问顾客修复体使用情况<br>2. 建议每年定期检查，定期洁牙","executeMode":0,"executorId":"","scheduleDate":"2024-07-13T00:00:00Z","afterOutpatientDays":0,"scheduleLocalTime":"08:00:00","scheduleAfterDays":360,"notifyEmployeeList":[]}]"""}]
            self.revisit_init_clinic_build_sql(chain_id,
                                               clinic_id,
                                               insert_revisit_catalogue_dir_sqls,
                                               insert_revisit_catalogue_file_sqls,
                                               insert_revisit_template_sqls,
                                               revisit_target_temples,
                                               '种植')

        # 插入数据
        insert_revisit_catalogue_sqls = insert_revisit_catalogue_dir_sqls + insert_revisit_catalogue_file_sqls
        if len(insert_revisit_catalogue_sqls) > 0:
            sql = """
                INSERT INTO v2_short_url_catalogue (id, parent_id, chain_id, clinic_id, owner_id, owner_type,
                                                      is_folder, type, category, name, sort, is_deleted, created,
                                                      created_by, last_modified, last_modified_by, children_count,
                                                      level, source_id)
                VALUES {values}
            """.format(values=','.join(insert_revisit_catalogue_sqls))
            self.shorturl_db_client.execute(sql)

        if len(insert_revisit_template_sqls) > 0:
            sql = """
                INSERT INTO v2_patient_revisit_template (id, chain_id, clinic_id, name, content, is_deleted,
                                                         created, created_by, last_modified_by, last_modified, type,
                                                         tasks)
                VALUES {values}
            """.format(values=','.join(insert_revisit_template_sqls))
            self.patient_db_client.execute(sql)

    def revisit_init_clinic_build_sql(self,
                                      chain_id,
                                      clinic_id,
                                      insert_revisit_catalogue_dir_sqls,
                                      insert_revisit_catalogue_file_sqls,
                                      insert_revisit_template_sqls,
                                      revisit_target_temples,
                                      catalog_dir_name):
        # 插入文件夹目录
        revisit_template_dir_catalogs = query_revisit_template_catalogue_by_name(self.shorturl_db_client, chain_id,
                                                                                 clinic_id, None, 1, [catalog_dir_name])
        if len(revisit_template_dir_catalogs) > 1:
            return

        revisit_catalogue_dir_id = None
        if len(revisit_template_dir_catalogs) == 1:
            revisit_catalogue_dir_id = revisit_template_dir_catalogs[0]['id']
        else:
            revisit_catalogue_dir_id = self.id_work.getUIDLong()
            children_count = 0 if not revisit_target_temples else len(revisit_target_temples)
            insert_revisit_catalogue_dir_sqls.append(u"""
                    ('{id}', null, '{chainId}', '{clinicId}',
                            '{clinicId}', 1, 1, 23, 1, '{catalogDirName}', '{sort}', 0, current_timestamp(),
                            '00000000000000000000000000000000', current_timestamp(), '00000000000000000000000000000000', {childrenCount}, 0, null)
                    """.format(id=revisit_catalogue_dir_id, chainId=chain_id, clinicId=clinic_id,
                               catalogDirName=escape_string(catalog_dir_name),
                               sort=len(insert_revisit_catalogue_dir_sqls),
                               childrenCount=children_count))

        if not revisit_target_temples or len(revisit_target_temples) == 0:
            return

        for revisit_target_temple in revisit_target_temples:
            has_file = revisit_target_temple['clinic_id'] == clinic_id

            if has_file:
                revisit_default_catalogue_file_id = revisit_target_temple['id']
                created_by = revisit_target_temple['created_by']
                last_modified_by = revisit_target_temple['last_modified_by']
                if not created_by:
                    created_by = '00000000000000000000000000000000'
                if not last_modified_by:
                    last_modified_by = '00000000000000000000000000000000'
            else:
                revisit_default_catalogue_file_id = self.id_work.getUIDLong()
                created_by = '00000000000000000000000000000000'
                last_modified_by = '00000000000000000000000000000000'

            # 插入文件目录
            # 如果已经有文件了，则只需要插入文件目录，并且文件目录的ID等于已有的文件ID
            insert_revisit_catalogue_file_sqls.append(u"""
                    ('{id}', '{parentId}', '{chainId}', '{clinicId}',
                        '{clinicId}', 1, 0, 23, 1, '{name}', 0, 0, current_timestamp(),
                        '{createdBy}', current_timestamp(), '{lastModifiedBy}', 0, 0, null)
                    """.format(id=revisit_default_catalogue_file_id, parentId=revisit_catalogue_dir_id,
                               chainId=chain_id, sort=len(insert_revisit_catalogue_file_sqls),
                               clinicId=clinic_id, name=escape_string(revisit_target_temple['name']),
                               createdBy=created_by, lastModifiedBy=last_modified_by))
            if not has_file:
                # 插入文件
                insert_revisit_template_sqls.append(u"""
                    ('{id}', '{chainId}', '{clinicId}', '{name}',
                            '{content}', 0, current_timestamp(),
                            '{createdBy}', '{lastModifiedBy}', current_timestamp(), 0, '{tasks}')
                    """.format(id=revisit_default_catalogue_file_id, chainId=chain_id, clinicId=clinic_id,
                               name=escape_string(revisit_target_temple['name']),
                               content=revisit_target_temple['content'], tasks=revisit_target_temple['tasks'],
                               createdBy=created_by, lastModifiedBy=last_modified_by))

    def run(self ):
        # 口腔数据初始化-随访
        self.revisit_init()
        # 病历模板初始化
        self.medical_record_init()

        self.message()

    def message(self):
        self.message_db_client.execute("""update abc_cis_message.v2_message_switch_config
                set `group` = '营销',
                    `key`   = replace(`key`, 'others.', 'marketing.')
                where `key` in ('others.promotion-patient-card', 'others.promotion-patient-coupon', 'others.member-balance')
                  and chain_id = '{chainId}';""".format(chainId=self.chain_id))
        self.message_db_client.execute("""update abc_cis_message.v2_message_switch_config set notify_desc = '患者预约修改成功后，立即通知患者'
                where `key` = 'appointment.update' and chain_id = '{chainId}';""".format(chainId=self.chain_id))
        self.message_db_client.execute("""insert into abc_cis_message.v2_message_switch_config (id, chain_id, `group`, `key`, name, notify_desc,
                                                      notify_minutes_before, sms_template, wx_template, weapp_template,
                                                      sms_switch, wx_switch, weapp_switch, is_deleted,
                                                      created, last_modified)
        select UUID_SHORT(),
               chain_id,
               '营销',
               'marketing.patient-points',
               '积分变更通知',
               '积分变更时，立即通知患者',
               null,
               '{{"types": [1700001, 1700002, 1700003], "templates": [{{"name": "发放提醒", "content": "【ABC数字医疗云】恭喜您已获得x积分，当前累计积分xxx，请查收！"}}, {{"name": "使用提醒", "content": "【ABC数字医疗云】您在2023-07-01 12:33成功使用x积分，当前剩余积分xxx，请核实！"}}, {{"name": "返还提醒", "content": "【ABC数字医疗云】您有x积分于2023-07-01 12:33成功返还至您的积分账户，当前积分：xxx，请查收！"}}]}}',
               null,
               null,
               0,
               0,
               0,
               0,
               now(),
               now()
        from abc_cis_message.v2_message_switch_config
        where chain_id  ='{chainId}'
        group by chain_id;""".format(chainId=self.chain_id))
        self.message_db_client.execute("""insert into abc_cis_message.v2_message_switch_config (id, chain_id, `group`, `key`, name, notify_desc,
                                                      notify_minutes_before, sms_template, wx_template, weapp_template,
                                                      sms_switch, wx_switch, weapp_switch, is_deleted,
                                                      created, last_modified)
select UUID_SHORT(),
       chain_id,
       '营销',
       'marketing.patient-points-expire',
       '积分过期提醒',
       '积分到期前%s自动提醒患者使用',
       0,
       '{{"types": [1700004], "templates": [{{"name": "过期提醒", "content": "【ABC数字医疗云】你现有XXX积分，将于2023-07-01过期清零，请尽快使用哦！"}}]}}',
       null,
       null,
       0,
       0,
       0,
       0,
       now(),
       now()
from abc_cis_message.v2_message_switch_config
where chain_id ='{chainId}'
group by chain_id;""".format(chainId=self.chain_id))

    def revisit_init(self):
        # 口腔数据初始化-随访
        if self.chain_id:
            organ = self.basic_db_client.fetchone("""
                    select id, his_type
                    from organ
                    where id = '{chainId}'
                """.format(chainId=self.chain_id))
            if not organ:
                return

            self.revisit_init_chain(self.chain_id, organ['his_type'])
        # else:
        #     organs = self.basic_db_client.fetchall("""
        #             select id, his_type
        #             from organ
        #             where status = 1 and node_type in (0, 1)
        #         """)
        #
        #     for organ in organs:
        #         self.revisit_init_chain(organ['id'], organ['his_type'])
        pass

    def medical_record_init(self):
        # 口腔数据初始化-患者标签初始化
        if self.chain_id:
            organ = self.basic_db_client.fetchone("""
                    select o.id, o.his_type
                    from organ o
                    where id = '{chainId}' and his_type = 1
                """.format(chainId=self.chain_id))
            if not organ:
                return

            self.medical_record_init_chain(self.chain_id)
        # else:
        #     organs = self.basic_db_client.fetchall("""
        #             select id
        #             from organ
        #             where status = 1 and node_type in (0, 1) and his_type = 1
        #         """)
        #     for organ in organs:
        #         self.medical_record_init_chain(organ['id'])
        pass

    def medical_record_init_chain(self, chain_id):
        # 连锁下的所有门店
        organs = self.basic_db_client.fetchall("""
                select id
                from organ
                where parent_id = '{chainId}' and status = 1
            """.format(chainId=chain_id))
        for organ in organs:
            self.medical_record_init_clinic(chain_id, organ['id'])
        pass

    def medical_record_init_clinic(self, chain_id, clinic_id):
        own_type = None
        if chain_id == clinic_id:
            own_type = 1
        else:
            own_type = 2

        catalogue_folders = query_medical_record_catalogue_by_name(self.outpatient_db_client, clinic_id, own_type,
                                                                   ['\'牙龈病\'', '\'非龋性疾病\''])
        if catalogue_folders is None or len(catalogue_folders) == 0:
            return

        # 在牙龈病下增加牙结石 (洁牙)病历模板
        # 统计 catalogue_folders 中 name 为 '牙龈病' 的目录数量
        folder_count = 0
        folder_id = None
        for catalogue_folder in catalogue_folders:
            if catalogue_folder['name'] == '牙龈病':
                folder_count += 1
                folder_id = catalogue_folder['id']
        sqls = []
        # 只有精准找到了该目录时才初始化
        medical_record_catalogue = query_medical_record_catalogue_by_parent_id_and_name(self.outpatient_db_client,
                                                                                        clinic_id, own_type, folder_id,
                                                                                        '\'牙结石 (洁牙)\'')
        if folder_count == 1 and len(medical_record_catalogue) == 0:
            template_id = self.id_work.getUID()
            # 目录文件
            sqls.append("""
                    INSERT INTO v2_outpatient_template_catalogue (id, parent_id, chain_id, clinic_id, owner_id,
                                                                  owner_type, is_folder, type, category, name, sort,
                                                                  used_count, is_deleted, created, created_by,
                                                                  last_modified, last_modified_by, children_count)
                    VALUES ({id}, {folderId}, '{chainId}', '{clinicId}',
                            '{clinicId}', {ownType}, 0, 0, 1, '牙结石 (洁牙)', 0, 0, 0, current_timestamp(),
                            '00000000000000000000000000000000', current_timestamp(), '00000000000000000000000000000000', 0)
                """.format(id=template_id, folderId=folder_id, chainId=chain_id, clinicId=clinic_id, ownType=own_type))
            # 文件
            sqls.append("""
                    INSERT INTO v2_outpatient_template_medical_record (id, chain_id, clinic_id, owner_id, owner_type,
                                                                          category, name, used_count, chief_complaint,
                                                                          present_history, past_history,
                                                                          physical_examination, diagnosis, is_deleted,
                                                                          created, created_by, last_modified,
                                                                          last_modified_by, syndrome, therapy,
                                                                          chinese_examination, oral_examination,
                                                                          family_history, auxiliary_examination,
                                                                          epidemiological_history, obstetrical_history,
                                                                          chinese_prescription, diagnosis_infos,
                                                                          dentistry_extend, doctor_advice,
                                                                          extend_diagnosis_infos,
                                                                          auxiliary_examinations, personal_history,
                                                                          wear_glasses_history, eye_examination, type,
                                                                          target, prognosis, allergic_history)
                    VALUES ({id}, '{chainId}', '{clinicId}', '{clinicId}', {ownType}, 1, '牙结石（洁牙）', 0, '刷牙出血、口臭半年',
                            '患者半年前无明显诱因下，时常出现刷牙出血，且食用苹果等较硬水果时，也可出现牙齿出血症状。口中常有异味，影响社交。患者此前未行治疗。今为寻求治疗，遂来我处门诊就诊。',
                            '否认既往洁牙史；否认高血压、糖尿病、冠心病病史；否认食物药物过敏史。', '', '牙结石', 0, current_timestamp(),
                            '00000000000000000000000000000000', current_timestamp(), '00000000000000000000000000000000', null, null, null,
                            null, '', null, '', '', null, '[{{"code": "", "name": "牙结石", "diseaseType": null}}]',
                            '{{"disposals": [{{"value": "1. 口腔卫生宣教：引导患者正确刷牙，告知患者牙线、牙签及牙间隙刷的正确操作方法。<br>2. 向患者告知病情、治疗方案、费用、风险、预后等情况，患者及家属知情并同意治疗。<br>3. 全口超声波洁治，喷砂，抛光，上药。", "toothNos": []}}], "treatmentPlans": [{{"value": "洁治术", "toothNos": []}}], "dentistryExaminations": [{{"value": "全口卫生状况较差，牙面大量色素附着，牙结石(+++)，菌斑指数(PI):2。牙龈充血，色红，边缘厚钝，质地松软脆弱。龈沟深度3mm。探诊后出血，未及牙周袋。松动度(Ⅰ)/(Ⅱ)。牙表面粗糙，黑褐色污渍较多。全口龈缘大量软垢。", "toothNos": []}}]}}',
                            '1. 一周内禁食酸性食物，避冷热刺激。<br>2. 漱口水含漱。<br>3. 牙龈少量出血属正常现象，如有大量出血或不适，及时随诊。',
                            '[{{"value": [{{"code": "", "name": "牙结石", "diseaseType": null}}], "toothNos": []}}]',
                            '[{{"value": "暂无。", "toothNos": []}}]', '', null, null, 0, null, null, null);
                """.format(id=template_id, chainId=chain_id, clinicId=clinic_id, ownType=own_type))

        # 在非龋性疾病下增加牙齿变色 (家庭美白)模板，修改修改牙齿变色模板名称为牙齿变色 (常规美白)
        # 统计 catalogue_folders 中 name 为 '非龋性疾病' 的目录数量
        folder_count = 0
        folder_id = None
        for catalogue_folder in catalogue_folders:
            if catalogue_folder['name'] == '非龋性疾病':
                folder_count += 1
                folder_id = catalogue_folder['id']
        medical_record_catalogue = query_medical_record_catalogue_by_parent_id_and_name(self.outpatient_db_client,
                                                                                        clinic_id, own_type, folder_id,
                                                                                        '\'牙齿变色 (家庭美白)\'')
        if folder_count == 1 and len(medical_record_catalogue) == 0:
            template_id = self.id_work.getUID()
            # 目录文件
            sqls.append("""
                    INSERT INTO v2_outpatient_template_catalogue (id, parent_id, chain_id, clinic_id, owner_id,
                                                                  owner_type, is_folder, type, category, name, sort,
                                                                  used_count, is_deleted, created, created_by,
                                                                  last_modified, last_modified_by, children_count)
                    VALUES ({id}, {folderId}, '{chainId}', '{clinicId}',
                            '{clinicId}', {ownType}, 0, 0, 1, '牙齿变色 (家庭美白)', 0, 0, 0, current_timestamp(),
                            '00000000000000000000000000000000', current_timestamp(), '00000000000000000000000000000000', 0)
                """.format(id=template_id, folderId=folder_id, chainId=chain_id, clinicId=clinic_id, ownType=own_type))
            # 文件
            sqls.append("""
                    INSERT INTO v2_outpatient_template_medical_record (id, chain_id, clinic_id, owner_id, owner_type,
                                                                                              category, name, used_count, chief_complaint,
                                                                                              present_history, past_history,
                                                                                              physical_examination, diagnosis, is_deleted,
                                                                                              created, created_by, last_modified,
                                                                                              last_modified_by, syndrome, therapy,
                                                                                              chinese_examination, oral_examination,
                                                                                              family_history, auxiliary_examination,
                                                                                              epidemiological_history, obstetrical_history,
                                                                                              chinese_prescription, diagnosis_infos,
                                                                                              dentistry_extend, doctor_advice,
                                                                                              extend_diagnosis_infos,
                                                                                              auxiliary_examinations, personal_history,
                                                                                              wear_glasses_history, eye_examination, type,
                                                                                              target, prognosis, allergic_history)
                    VALUES ({id}, '{chainId}', '{clinicId}',
                            '{clinicId}', {ownType}, 1, '牙齿变色 (家庭美白)', 0, '牙齿颜色变黄数年。',
                            '患者自述长期吸烟、喝浓茶，导致牙齿颜色慢慢变黄。因影响美观，遂来要求诊治。',
                            '患者自述无高血压、糖尿病、冠心病等慢性疾病病史；自述无肝炎、结核、艾滋病等传染病史及其密切接触史；自述无重大外伤及手术史；自述无食物药物过敏史，无输血史。余系统回顾未见明显异常。',
                            '', '牙齿变色', 0, current_timestamp(), '00000000000000000000000000000000', current_timestamp(),
                            '00000000000000000000000000000000', null, null, null, null, '', null, '', '', null,
                            '[{{"code": "", "name": "牙齿变色", "diseaseType": null}}]',
                            '{{"disposals": [{{"value": "1. 睡前刷牙，后用牙线清洁，充分漱口。<br>2. 取出牙套，在需要美白的牙齿位置放3滴左右的美白剂。<br>3. 将牙套戴到牙齿上，去除多余凝胶，每晚都需带牙套睡觉。<br>4. 晨起取出牙套，清洗，刷牙。建议使用漱口水。", "toothNos": []}}], "treatmentPlans": [{{"value": "牙齿家庭美白", "toothNos": []}}], "dentistryExaminations": [{{"value": "全口卫生状况较差，牙面大量色素附着。牙冠颜色偏黄，VITA比色板C2。牙体轻度釉质发育不全。冷热诊正常。探诊 (-)，叩诊 (-)，松动度(-)。余牙未见异常。", "toothNos": []}}]}}',
                            '1. 牙齿家庭美白需在夜间使用。<br>2. 勿食过冷、过热及含有色素的食物，如咖啡、浓茶、可乐等。<br>3. 若牙齿出现不适，需立即联系医生。',
                            '[{{"value": [{{"code": "", "name": "牙齿变色", "diseaseType": null}}], "toothNos": []}}]',
                            '[{{"value": "暂无。", "toothNos": []}}]', '', null, null, 0, null, null, null);
                """.format(id=template_id, chainId=chain_id, clinicId=clinic_id, ownType=own_type))
            medical_records = query_medical_record_catalogue_by_parent_id_and_name(self.outpatient_db_client, clinic_id,
                                                                                   own_type, folder_id, '\'牙齿变色\'')
            if len(medical_records) == 1:
                template_id = medical_records[0]['id']
                sqls.append("""
                        update v2_outpatient_template_catalogue
                        set name = '牙齿变色（常规美白）'
                        where id = '{id}';
                    """.format(id=template_id))
                sqls.append("""
                        update v2_outpatient_template_medical_record
                        set name = '牙齿变色（常规美白）'
                        where id = '{id}';
                    """.format(id=template_id))

        if len(sqls) == 0:
            return
        for sql in sqls:
            self.outpatient_db_client.execute(sql)


def query_medical_record_catalogue_by_parent_id_and_name(client, clinic_id, own_type, folder_id, name):
    return client.fetchall("""
       select id
       from v2_outpatient_template_catalogue
       where clinic_id = '{clinicId}'
         and category = 1
         and is_folder = 0
         and parent_id = '{folderId}'
         and type = 0
         and owner_type = {ownType}
         and name = {name}
       """.format(clinicId=clinic_id, ownType=own_type, folderId=folder_id, name=name))


def query_medical_record_catalogue_by_name(client, clinic_id, own_type, names):
    return client.fetchall("""
       select id, name
       from v2_outpatient_template_catalogue
       where clinic_id = '{clinicId}'
         and category = 1
         and is_folder = 1
         and type = 0
         and owner_type = {ownType}
         and name in ({name})
       """.format(clinicId=clinic_id, ownType=own_type, name=','.join(names)))


def query_revisit_template_catalogue_by_name(client, chain_id, clinic_id, parent_id, is_folder, names):
    sql = None
    # 用逗号拼接 names 并且两边带上 ' 号
    name_str = ", ".join("'" + name + "'" for name in names)
    if parent_id is None:
        sql = """
            select id, name
            from v2_short_url_catalogue
            where chain_id = '{chainId}'
              and clinic_id = '{clinicId}'
              and owner_id = '{clinicId}'
              and owner_type = 1
              and parent_id is null
              and is_folder = {isFolder}
              and name in ({name})
              and type = 23
              and category = 1
        """.format(chainId=chain_id, clinicId=clinic_id, isFolder=is_folder, name=name_str)
    else:
        sql = """
            select id, name
            from v2_short_url_catalogue
            where chain_id = '{chainId}'
              and clinic_id = '{clinicId}'
              and owner_id = '{clinicId}'
              and owner_type = 1
              and parent_id = '{parentId}'
              and is_folder = {isFolder}
              and name in ({name})
              and type = 23
              and category = 1
        """.format(chainId=chain_id, clinicId=clinic_id, parentId=parent_id, isFolder=is_folder, name=name_str)
    return client.fetchall(sql)


def main():
    print(sys.path)
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    oral_init = OralInit(args.region_name, args.chain_id)
    oral_init.run()


if __name__ == '__main__':
    main()
