#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
import argparse

default_id = '00000000000000000000000000000000'


def updateTreatmentExecuteSmsSwitch(abcRegion, chain_id):
    db_client = DBClient(abcRegion, 'abc_cis_mixed', 'abc_cis_basic', 'prod', True)
    goods_db_client = DBClient(abcRegion, 'abc_cis_stock', 'abc_cis_goods', 'prod', True)
    sql_query = """
        select CONCAT('update v2_goods_compose_opt set clinic_id =\''', id, '\'' where chain_id =\''', parent_id, '\''; ') as sqlItem 
        from abc_cis_basic.organ
        where parent_id = '{chainId}'
            and his_type != 100
            and view_mode = 1
            and node_type = 2 ;""".format(chainId=chain_id)

    exam_update_sql_res = db_client.fetchall(sql_query)
    for stmt in exam_update_sql_res:
        sql = stmt['sqlItem']
        if sql is None:
            continue
        print(sql)
        goods_db_client.execute(sql)


def run(abcRegion, chain_id):
    updateTreatmentExecuteSmsSwitch(abcRegion, chain_id)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.region_name, args.chain_id)


if __name__ == '__main__':
    main()
