#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import argparse
import logging

import requests
import logging
import json
from elasticsearch import Elasticsearch

logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)
SEARCH_NORMAL= 'abc-search-prod-normal'
SEARCH_HA= 'abc-search-prod'
RG_SH='sh_'
RG_HZ= 'hz_'
ES_INFO = {
    RG_SH + 'dev-search': {
        'url': 'http://dev-search.es.abczs.cn:9200'
    },
    RG_SH + 'prod-search': {
        'url': 'http://prod-search.es.abczs.cn:9200'
    },
    RG_SH + 'abc-search-dev': {
        'url': 'http://elastic:<EMAIL>:9200',
        'username': 'elastic',
        'password': '379f2d6ce1a1a52bc6804ac65d77D'
    },
    RG_SH + 'abc-search-prod': {
        'url': 'http://elastic:<EMAIL>:9200',
        'username': 'elastic',
        'password': '379f2d6ce1a1a52bc6804ac65d77D'
    },
    RG_SH + 'abc-search-prod-normal': {
        'url': 'http://elastic:<EMAIL>:9200',
        'username': 'elastic',
        'password': '379f2d6ce1a1a52bc6804ac65d77E'
    },
    RG_HZ + 'abc-search-prod': {
        'url': 'http://elastic:<EMAIL>:9200',
        'username': 'elastic',
        'password': '379f2d6ce1a1a52bc6804ac65d77D'
    },
    RG_HZ + 'abc-search-prod-normal': {
        'url': 'http://elastic:<EMAIL>:9200',
        'username': 'elastic',
        'password': '379f2d6ce1a1a52bc6804ac65d77D'
    }
}


#通过es api直接操作索引
class ESClient(object):

    def __init__(self, es_name):
        if es_name not in ES_INFO.keys():
            raise Exception('es:{0} not existed'.format(es_name))
        es_info = ES_INFO[es_name]
        self.es_url = es_info['url']
        self.headers = {}
        if 'username' in es_info.keys() and 'password' in es_info.keys():
            authorization = 'Basic ' + '{0}:{1}'.format(es_info['username'], es_info['password']).encode('base64').replace('\n', '')
            self.headers['Authorization'] = authorization
        self.elasticsearch = Elasticsearch(hosts = es_info['url'],headers=self.headers)
        print(self.elasticsearch)
        logging.info("success construt")

    def _es_get(self, path):
        url = '{0}{1}'.format(self.es_url, path)
        requests.get(url,)
        rsp = requests.get(url, headers=self.headers)
        return rsp

    def _es_post(self, path, data=None):
        url = '{0}{1}'.format(self.es_url, path)
        rsp = requests.post(url, json=data, headers=self.headers)
        return rsp

    def _es_put(self, path, data=None):
        url = '{0}{1}'.format(self.es_url, path)
        rsp = requests.put(url, json=data, headers=self.headers)
        return rsp

    def _es_delete(self, path):
        url = '{0}{1}'.format(self.es_url, path)
        rsp = requests.delete(url, headers=self.headers)
        return rsp

    def get_index_info(self, index_name):
        path = '/{0}'.format(index_name)
	print(path)
        rsp = self._es_get(path)
        index_info = None
        if rsp.status_code == 200:
            index_info = rsp.json()
        elif rsp.status_code == 404:
            raise Exception('get_index_info not found index:{0}'.format(index_name))
        else:
            raise Exception('get_index_info error, name:{0}, rsp:{1}'.format(index_name, rsp.content))
        return index_info

    def create_index(self, index_name, index_info):
        logging.info('create index:{0}, body:{1}'.format(index_name, json.dumps(index_info, indent=4)))
        path = '/{0}'.format(index_name)
        rsp = self._es_put(path, index_info)
        if rsp.status_code != 200:
            raise Exception('create_index error, name:{0}, rsp:{1}'.format(index_name, rsp.content))
        return True

    def add_alias_for_index(self, index_name, alias):
        path = '/{0}/_alias/{1}'.format(index_name, alias)
        rsp = self._es_put(path)
        if rsp.status_code != 200:
            raise Exception(
                'add_alias_for_index error, name:{0}, alias:{1}, rsp:{2}'.format(index_name, alias, rsp.content))
        return True

    def delete_alias_for_index(self, index_name, alias):
        path = '/{0}/_alias/{1}'.format(index_name, alias)
        rsp = self._es_delete(path)
        if rsp.status_code != 200:
            raise Exception(
                'delete_alias_for_index error, name:{0}, alias:{1}, rsp:{2}'.format(index_name, alias, rsp.content))
        return True

    def reindex(self, data):
        path = '/_reindex?wait_for_completion=false'
        rsp = self._es_post(path, data)
        if rsp.status_code != 200:
            raise Exception('reindex error, data:{0}, rsp:{1}'.format(data, rsp.content))
        return rsp.json()
    def bulkInsert(self, index_name,write_bulk):
        rsp = self.elasticsearch.bulk(index=index_name, body=write_bulk, timeout='120s', request_timeout=120)
        if rsp['errors']:
            raise Exception('bulkInsert errors, rsp:{}'.format(rsp))
        return rsp

    def search(self, index_name, dsl):
        path = '/{0}/_search'.format(index_name)
        rsp = self._es_post(path, data=dsl)
        if rsp.status_code != 200:
            raise Exception('search error, data:{0}, rsp:{1}'.format(dsl, rsp.content))
        return rsp.json()

    def count(self, index_name,dsl):
        rsp = self.elasticsearch.count(index=[index_name],query=dsl)
        if rsp.status_code != 200:
            logging.error("path:{2},Req:{0}:,rsp:={1}".format(path,dsl,rsp))
            raise Exception('count error, rsp:{0}'.format(rsp.content))
        svr_rsp = rsp.json()
        logging.info("Req:{0}\n,Rsp:{1}\n".format(dsl,svr_rsp))
        return svr_rsp

    def update_by_query(self, index_name, queryDsl,runScript):
        logging.info("DSL:{0}".format(queryDsl))
        rsp = self.elasticsearch.update_by_query(index=index_name,query=queryDsl,script=runScript)
        if rsp.status_code != 200:
            raise Exception('update_by_query error, data:{0}, rsp:{1}'.format(queryDsl, rsp.content))
        svr_rsp = rsp.json()
        logging.info("Req:{0}\n,Rsp:{1}\n".format(queryDsl,svr_rsp))
        return svr_rsp

class ReIndex(object):
    def create_mapping_all(self,es,from_region,to_region, from_index):

	    print('hello =')
	    print(from_index)
        es_client_from = ESClient(from_region+es)
        es_client_to = ESClient(to_region+es)



        from_index_info = es_client_from.get_index_info(from_index)
	    from_index = str(from_index_info.keys()[0])
        from_index_info = from_index_info[from_index]
        from_index_info_settings = from_index_info['settings']
        from_index_info_settings_index = from_index_info_settings['index']
	    print(str(from_index_info_settings))
        to_index = from_index_info_settings_index['provided_name']
	    print(str(to_index))
        to_index_info_settings_index = {
            'number_of_shards': from_index_info_settings_index['number_of_shards'],
            'number_of_replicas': from_index_info_settings_index['number_of_replicas'],
        }

        if 'analysis' in from_index_info_settings_index.keys():
            to_index_info_settings_index['analysis'] = from_index_info_settings_index['analysis']

	    print(str(to_index_info_settings_index))
        if 'max_ngram_diff' in from_index_info_settings_index.keys():
            to_index_info_settings_index['max_ngram_diff'] = from_index_info_settings_index['max_ngram_diff']

        alias = from_index_info['aliases'].keys()[0] if len(from_index_info['aliases'].keys()) > 0 else None
        to_index_info_mappings = from_index_info['mappings']

        to_index_info = {
            'aliases': from_index_info['aliases'],
            'mappings': to_index_info_mappings,
            'settings': {
                'index': to_index_info_settings_index,
            }
        }
        # 创建新索引
        es_client_to.create_index(to_index, to_index_info)

class DuoFenQu(object):

    def __init__(self):
        self._db_client_cache = {}

    def _get_db_client(self, cluster, database):
        k = '{0}-{1}'.format(cluster, database)
        if k in self._db_client_cache.keys():
            return self._db_client_cache[k]

        db_client = db.DBClient(cluster, database)
        self._db_client_cache[k] = db_client
        return db_client

    def logStashFlushToEs(self ):
        try:
            normal_es_indexes = endpoints = [
                                  "prod-processing-denture-sheet",
                                  "prod-examination-merge-sheet",
                                  "prod-helper-questions",
                                  "suggest-patients-speed",
                                  "v3-cis-patient-prod",
                                  "prod-cis-goods-suppliers",
                                  "prod-goods-alias",
                                  "prod-v3-domain-medicine",
                                  "prod-cis-search-goods-nested",
                                  "prod-cis-recommend-goods-nested",
                                  "v3-cis-cdss-registration-form",
                                  "prod-outpatient-template-diagnosis-treatment",
                                  "prod-outpatient-template-medical-record",
                                  "prod-outpatient-template-prescription",
                                  "prod-therapy-registration",
                                  "prod-abc-cdss-patient",
                                  "v3-cis-crm-patient-prod",
                                  "prod-patient-revisit-record",
                                  "prod-domain-department",
                                  "prod-suggest-usages",
                                  "prod-domain-medicine-instruction",
                                  "prod-domain-chinese-medicine-zysj",
                                  "prod-domain-acupuncture-point",
                                  "prod-tcm-model-sickness",
                                  "prod-tcm-model-therapy",
                                  "prod-promotion",
                                  "prod-bis-order",
                                  "prod-domain-material",
                                  "prod-mall-product",
                                  "prod-abc-mall-product",
                                  "prod-mall-goods-sku-promotion",
                                  "prod-invoice-record",
                                  "prod-abc-scrm-customer",
                                  "prod-abc-scrm-customer-sell-opportunity",
                                  "prod-domain-medicine-manual",
                                  "v3-prod-shebao-default-dict-disease",
                                  "v3-prod-shebao-default-dict-disease-v2",
                                  "v3-prod-shebao-shanxi-dict-disease-v2",
                                  "v3-prod-shebao-national-dict-drug-tcm",
                                  "v3-prod-shebao-national-dict-drug",
                                  "v3-prod-shebao-national-dict-material",
                                  "v3-prod-shebao-national-dict-treatment",
                                  "v3-domain-medicine-manual-prod",
                                  "v3-cis-cdss-charge-sheet",
                                  "v3-examination-sheet-prod",
                                  "v3-examination-apply-sheet-prod",
                                  "v3-his-advice-execute-prod",
                                  "v3-his-patient-order-hospital-prod",
                                  "v3-abc-cdss-dispense-sheet-prod",
                                  "v3-his-charge-settle-prod",
                                  "v3-cis-invoice-record-prod",
                                  "v3-cis-nurse-therapy-registration-prod"
                              ]

            for idx in normal_es_indexes:
                ReIndex().create_mapping_all(SEARCH_NORMAL,RG_SH,RG_HZ,idx)
        except Exception as e:
            print(e)



    def execute_chain(self ):
        #先刷Goods数据
        self.logStashFlushToEs()

def run():
    rgt = DuoFenQu()
    rgt.execute_chain()


def main():
    parser = argparse.ArgumentParser()

    run()


if __name__ == '__main__':
    main()