# -*- coding: utf-8 -*-
"""
@name: flush_pharmacy_charge_print_promotion_property_config.py
@author: <PERSON><PERSON><PERSON>
@email: <EMAIL>
@date: 2025-04-01 13:46:43
"""
import json
import os
import sys
import argparse
from datetime import datetime

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient

env = 'prod'
default_id = '00000000000000000000000000000000'

class UpdateData:
    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', env, True)
        self.property_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_property' if env == 'prod' else 'abc_cis_basic', env, True)

    def run(self):
        organ_list = self.basic_db_client.fetchall("""
        select * from organ where parent_id = '{chainId}' and node_type = 2 and status = 1 and his_type = 10;
        """.format(chainId=self.chain_id))
        if organ_list is None or len(organ_list) == 0:
            return
        for organ in organ_list:
            print(organ['id'])
            config_item_list = self.property_db_client.fetchall("""
            select * from v2_property_config_item where v2_scope_id = '{clinicId}' and scope = 'clinic' and `key` = 'print.cashier.cashierInfo.discountFee' and value = '1'
            """.format(clinicId=organ['id']))
            if config_item_list is None or len(config_item_list) == 0:
                print('no v2_property_config_item')
                continue
            single_short_id = self.property_db_client.fetchone("""select substr(uuid_short(), 5) as uuid""")['uuid']
            single_insert_sql = """
            insert into v2_property_config_item(id, `key`, value, scope, is_deleted, created_by, created, last_modified_by, last_modified, key_first, key_second, key_third, key_fourth, v2_scope_id) values ('{id}', 'print.cashier.cashierInfo.singlePromotionFee', '1', 'clinic', 0, '{createdBy}', now(),
            '{lastModifiedBy}', now(), 'print', 'cashier', 'cashierInfo', 'singlePromotionFee', '{clinicId}');
            """.format(id=str(single_short_id), createdBy=default_id, lastModifiedBy=default_id, clinicId=organ['id'])
            package_short_id = self.property_db_client.fetchone("""select substr(uuid_short(), 5) as uuid""")['uuid']
            package_insert_sql = """
            insert into v2_property_config_item(id, `key`, value, scope, is_deleted, created_by, created, last_modified_by, last_modified, key_first, key_second, key_third, key_fourth, v2_scope_id) values ('{id}', 'print.cashier.cashierInfo.packagePromotionFee', '1', 'clinic', 0, '{createdBy}', now(),
            '{lastModifiedBy}', now(), 'print', 'cashier', 'cashierInfo', 'packagePromotionFee', '{clinicId}');
            """.format(id=str(package_short_id), createdBy=default_id, lastModifiedBy=default_id, clinicId=organ['id'])
            # print(single_insert_sql)
            # print(package_insert_sql)
            try:
                self.property_db_client.execute(single_insert_sql)
            except Exception as e:
                print(e)
            try:
                self.property_db_client.execute(package_insert_sql)
            except Exception as e:
                print(e)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    update_data = UpdateData(args.region_name, args.chain_id)
    update_data.run()


if __name__ == '__main__':
    main()