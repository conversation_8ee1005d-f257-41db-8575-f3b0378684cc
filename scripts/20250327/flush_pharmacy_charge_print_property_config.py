# -*- coding: utf-8 -*-
"""
@name: flush_pharmacy_charge_print_property_config.py
@author: <PERSON><PERSON><PERSON>
@email: <EMAIL>
@date: 2025-03-24 10:46:36
"""
import json
import os
import sys
import argparse
from datetime import datetime

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient

env = 'prod'
default_id = '00000000000000000000000000000000'

class UpdateData:
    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', env, True)
        self.property_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_property' if env == 'prod' else 'abc_cis_basic', env, True)

    def run(self):
        organ_list = self.basic_db_client.fetchall("""
        select * from organ where parent_id = '{chainId}' and node_type = 2 and status = 1 and his_type = 10;
        """.format(chainId=self.chain_id))
        if organ_list is None or len(organ_list) == 0:
            return
        for organ in organ_list:
            print(organ['id'])
            config_item_list = self.property_db_client.fetchall("""
            select * from v2_property_config_item where v2_scope_id = '{clinicId}' and scope = 'clinic' and `key` like 'print.cashier%';
            """.format(clinicId=organ['id']))
            settlementInfo = None
            settlementInfoZero = None
            medicalFeeGrade = None
            ownExpenseRatio = None
            west_item_list = []
            chinese_item_list = []
            material_item_list = []
            if config_item_list is None or len(config_item_list) == 0:
                continue
            for config_item in config_item_list:
                print(config_item)
                id = str(config_item['id'])
                key = config_item['key']
                value = config_item['value']
                if key is None:
                    continue
                if key == 'print.cashier.healthCardInfo.medicalFeeGrade':
                    medicalFeeGrade = config_item
                if key == 'print.cashier.healthCardInfo.ownExpenseRatio':
                    ownExpenseRatio = config_item
                # 结算信息
                if key == 'print.cashier.healthCardInfo.settlementInfo' and value == '1':
                    settlementInfo = config_item
                if key == 'print.cashier.healthCardInfo.zeroSettlementInfo':
                    settlementInfoZero = config_item
                # 西药
                if key.startswith('print.cashier.westernMedicine'):
                    west_item_list.append(config_item)
                # 中药
                if key.startswith('print.cashier.chinese'):
                    chinese_item_list.append(config_item)
                # 材料商品
                if key.startswith('print.cashier.materialGoods'):
                    material_item_list.append(config_item)

            update_sql_list = []

            if settlementInfo:
                if settlementInfo['value'] == '1':
                    # 全部打印
                    value = '1'
                    if settlementInfoZero and settlementInfoZero['value'] == '0':
                        # 打印金额为0的
                        value = '2'
                    update_sql = """
                    update v2_property_config_item set value = '{value}' where id = '{id}';
                    """.format(value=value, id=settlementInfo['id'])
                    update_sql_list.append(update_sql)
                    # print(update_sql)
            has_set_product = False
            medicalFeeGrade_value = 0
            ownExpenseRatio_value = 0
            if medicalFeeGrade and medicalFeeGrade['value'] == '1':
                has_set_product = True
                medicalFeeGrade_value = 1
            if ownExpenseRatio and ownExpenseRatio['value'] == '1':
                has_set_product = True
                ownExpenseRatio_value = 1
            item_value_json = [
                {
                    "type": "westernMedicine",
                    "items": [
                        {"key": "spec", "value": 0}, {"key": "dosageForm", "value": 0},
                        {"key": "manufacturer", "value": 0}, {"key": "mha", "value": 0},
                        {"key": "position", "value": 0}, {"key": "batchNumber", "value": 0},
                        {"key": "validityDate", "value": 0}, {"key": "medicalFeeGrade", "value": medicalFeeGrade_value},
                        {"key": "ownExpenseRatio", "value": ownExpenseRatio_value}
                    ]
                },
                {
                    "type": "chinese",
                    "items": [
                        {"key": "spec", "value": 0},
                        {"key": "manufacturer", "value": 0}, {"key": "mha", "value": 0},
                        {"key": "position", "value": 0}, {"key": "batchNumber", "value": 0},
                        {"key": "validityDate", "value": 0}, {"key": "medicalFeeGrade", "value": medicalFeeGrade_value},
                        {"key": "ownExpenseRatio", "value": ownExpenseRatio_value}
                    ]
                },
                {
                    "type": "materialGoods",
                    "items": [
                        {"key": "spec", "value": 0},
                        {"key": "manufacturer", "value": 0}, {"key": "mha", "value": 0},
                        {"key": "position", "value": 0}, {"key": "batchNumber", "value": 0},
                        {"key": "validityDate", "value": 0}, {"key": "medicalFeeGrade", "value": medicalFeeGrade_value},
                        {"key": "ownExpenseRatio", "value": ownExpenseRatio_value}
                    ]
                },
                {
                    "type": "productGoods",
                    "items": [
                        {"key": "spec", "value": 0}, {"key": "manufacturer", "value": 0},
                        {"key": "position", "value": 0}, {"key": "batchNumber", "value": 0},
                        {"key": "validityDate", "value": 0}
                    ]
                }
            ]
            if len(west_item_list) > 0:
                west_value_list = None
                for value_json in item_value_json:
                    if value_json['type'] == 'westernMedicine':
                        west_value_list = value_json['items']
                        break
                if west_value_list:
                    for west_item in west_item_list:
                        key = west_item['key'].split('.')[-1]
                        if west_item['value'] == '1':
                            for key_value in west_value_list:
                                if key == key_value['key']:
                                    key_value['value'] = 1
                            has_set_product = True

            if len(chinese_item_list) > 0:
                chinese_value_list = None
                for value_json in item_value_json:
                    if value_json['type'] == 'chinese':
                        chinese_value_list = value_json['items']
                        break
                if chinese_value_list:
                    for west_item in chinese_item_list:
                        key = west_item['key'].split('.')[-1]
                        if west_item['value'] == '1':
                            for key_value in chinese_value_list:
                                if key == key_value['key']:
                                    key_value['value'] = 1
                            has_set_product = True
            if len(material_item_list) > 0:
                material_value_list = None
                for value_json in item_value_json:
                    if value_json['type'] == 'materialGoods':
                        material_value_list = value_json['items']
                        break
                if material_value_list:
                    for west_item in material_item_list:
                        key = west_item['key'].split('.')[-1]
                        if west_item['value'] == '1':
                            for key_value in material_value_list:
                                if key == key_value['key']:
                                    key_value['value'] = 1
                            has_set_product = True
                product_value_list = None
                for value_json in item_value_json:
                    if value_json['type'] == 'productGoods':
                        product_value_list = value_json['items']
                        break
                if product_value_list:
                    for west_item in material_item_list:
                        key = west_item['key'].split('.')[-1]
                        if west_item['value'] == '1':
                            for key_value in product_value_list:
                                if key == key_value['key']:
                                    key_value['value'] = 1
                            has_set_product = True

            if has_set_product:
                # print(json.dumps(item_value_json))
                short_id = self.property_db_client.fetchone("""select substr(uuid_short(), 5) as uuid""")['uuid']
                insert_sql = """
                insert into v2_property_config_item(id, `key`, value, scope, is_deleted, created_by, created, last_modified_by, last_modified, key_first, key_second, key_third, key_fourth, v2_scope_id) values ('{id}', 'print.cashier.feeInfo.itemDetail', '{value}', 'clinic', 0, '{createdBy}', now(),
                '{lastModifiedBy}', now(), 'print', 'cashier', 'feeInfo', 'itemDetail', '{clinicId}') ON DUPLICATE KEY UPDATE value = values(value);
                """.format(id=str(short_id), value=json.dumps(item_value_json), createdBy=default_id, lastModifiedBy=default_id, clinicId=organ['id'])
                update_sql_list.append(insert_sql)

            if len(update_sql_list) == 0:
                return
            for update_sql in update_sql_list:
                print(update_sql)
                try:
                    self.property_db_client.execute(update_sql)
                except Exception as e:
                    print(e)

        print()


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    update_data = UpdateData(args.region_name, args.chain_id)
    update_data.run()


if __name__ == '__main__':
    main()