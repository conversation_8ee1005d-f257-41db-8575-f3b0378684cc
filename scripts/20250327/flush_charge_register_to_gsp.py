# -*- coding: utf-8 -*-
"""
@name: flush_charge_register_to_gsp.py
@author: <PERSON><PERSON><PERSON>
@email: <EMAIL>
@date: 2025-03-13 10:03:46
"""
import os
import sys
import argparse
import datetime

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient

DEFAULT_ID = 'sys'
execute_count = 200

env = 'prod'


class UpdateData:
    chain_id = None
    basic_db_client = None
    charge_db_client = None
    pha_db_client = None

    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', env, True)
        self.charge_db_client = DBClient(self.region_name, 'abc_cis_charge', 'abc_cis_charge', env, True)
        # self.pha_db_client = DBClient(self.region_name, 'scrm_hospital', 'abc_cis_gsp', env, True)
        self.pha_db_client = DBClient(self.region_name, 'scrm_hospital', 'abc_pha_gsp', env, True)

    def run(self):
        organ = self.basic_db_client.fetchone("""
        select * from organ where id = '{chainId}' and status = 1 and his_type = 10;
        """.format(chainId=self.chain_id))
        if organ is None:
            return
        if organ['his_type'] != 10:
            return
        print(self.chain_id)
        self.flush_charge_register_info_to_gsp()
        self.flush_charge_register_identity_info_to_gsp()
        self.flush_charge_register_prescription_info_to_gsp()
        return

    def flush_charge_register_info_to_gsp(self):
        offset = 0
        limit = 200
        sql_temple = f"""
                insert into v2_gsp_register_info(id, chain_id, clinic_id, charge_sheet_id, source_sheet_id, identity_id, prescription_id, source_type, is_deleted, created, created_by, last_modified, last_modified_by)
                values (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s);
        """

        insert_list = []
        while True:
            register_info_list = self.charge_db_client.fetchall("""
            select * from v2_charge_sheet_register_info where chain_id = '{chainId}' and is_deleted = 0
            limit {offset}, {limit}
            """.format(chainId=self.chain_id, offset=offset, limit=limit))
            if register_info_list is None or len(register_info_list) == 0:
                break
            for register_info in register_info_list:
                params = (str(register_info['id']), register_info['chain_id'], register_info['clinic_id'],
                          register_info['charge_sheet_id'] if register_info['charge_sheet_id'] else None,
                          register_info['charge_sheet_id'] if register_info['charge_sheet_id'] else None,
                          str(register_info['identity_id']) if register_info['identity_id'] else None,
                          str(register_info['prescription_id']) if register_info['prescription_id'] else None, '0', '0',
                          register_info['created'], register_info['created_by'],
                          register_info['last_modified'], register_info['last_modified_by'])
                insert_list.append(params)
            offset += limit

        if len(insert_list) == 0:
            return
        print("register_info count", len(insert_list))
        for i in range(0, len(insert_list), execute_count):
            self.pha_db_client.executemanyParam(sql_temple, insert_list[i:i + execute_count])
        return

    def flush_charge_register_identity_info_to_gsp(self):
        offset = 0
        limit = 200
        insert_list = []

        insert_sql = """
            insert into v2_gsp_register_identity(id, chain_id, clinic_id, name, birthday, age, sex, id_card, mobile, mobile_last4, version, id_card_type, is_deleted, created, created_by, last_modified, last_modified_by)
            values (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s);
        """
        while True:
            identity_info_list = self.charge_db_client.fetchall("""
            select * from v2_charge_sheet_register_identity where chain_id = '{chainId}' and is_deleted = 0
            limit {offset}, {limit}
            """.format(chainId=self.chain_id, offset=offset, limit=limit))
            if identity_info_list is None or len(identity_info_list) == 0:
                break

            for identity_info in identity_info_list:
                params = (str(identity_info['id']), identity_info['chain_id'], identity_info['clinic_id'],
                          identity_info['name'] if identity_info['name'] else '',
                          identity_info['birthday'] if identity_info['birthday'] else None,
                          identity_info['age'] if identity_info['age'] else None,
                          identity_info['sex'] if identity_info['sex'] else None,
                          identity_info['id_card'] if identity_info['id_card'] else None,
                          identity_info['mobile'] if identity_info['mobile'] else None,
                          identity_info['mobile_last4'] if identity_info['mobile_last4'] else None,
                          identity_info['version'] if identity_info['version'] else '',
                          identity_info['id_card_type'] if identity_info['id_card_type'] else None, '0',
                          identity_info['created'],
                          identity_info['created_by'], identity_info['last_modified'],
                          identity_info['last_modified_by'])
                insert_list.append(params)

            offset += limit
        if len(insert_list) == 0:
            return
        print("identity_info count", len(insert_list))
        for i in range(0, len(insert_list), execute_count):
            self.pha_db_client.executemanyParam(insert_sql, insert_list[i:i + execute_count])

        return

    def flush_charge_register_prescription_info_to_gsp(self):
        offset = 0
        limit = 200
        insert_list = []

        insert_sql = """
        insert into v2_gsp_register_prescription(id, chain_id, clinic_id, no, register_identity_id, prescription_physician,
            reviewer_physician, reviewer, dispatcher, remark, prescription_urls, version, diagnosis,
            is_deleted, created, created_by, last_modified, last_modified_by)
            values (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s);
        """
        while True:
            prescription_info_list = self.charge_db_client.fetchall("""
            select * from v2_charge_sheet_register_prescription where chain_id = '{chainId}' and is_deleted = 0
            limit {offset}, {limit}
            """.format(chainId=self.chain_id, offset=offset, limit=limit))
            if prescription_info_list is None or len(prescription_info_list) == 0:
                break

            for prescription_info in prescription_info_list:
                params = (
                str(prescription_info['id']), prescription_info['chain_id'], prescription_info['clinic_id'],
                prescription_info['no'] if prescription_info['no'] else None,
                prescription_info['register_identity_id'] if prescription_info['register_identity_id'] else None,
                prescription_info['prescription_physician'] if prescription_info['prescription_physician'] else None,
                prescription_info['reviewer_physician'] if prescription_info['reviewer_physician'] else None,
                prescription_info['reviewer'] if prescription_info['reviewer'] else None,
                prescription_info['dispatcher'] if prescription_info['dispatcher'] else None,
                prescription_info['remark'] if prescription_info['remark'] else None,
                prescription_info['prescription_urls'] if prescription_info['prescription_urls'] else None,
                prescription_info['version'] if prescription_info['version'] else None,
                prescription_info['diagnosis'] if prescription_info['diagnosis'] else None, '0',
                prescription_info['created'], prescription_info['created_by'],
                prescription_info['created'],
                prescription_info['created_by'])
                insert_list.append(params)
            offset += limit
        if len(insert_list) == 0:
            return
        print("prescription_info count", len(insert_list))
        for i in range(0, len(insert_list), execute_count):
            self.pha_db_client.executemanyParam(insert_sql, insert_list[i:i + execute_count])

        return


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    update_data = UpdateData(args.region_name, args.chain_id)
    update_data.run()


if __name__ == '__main__':
    main()