# -*- coding: utf-8 -*-
import argparse
import os
import sys

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from idwork import IdWork
import json

from scripts.common.utils.lists import ListUtils
from scripts.common.utils.organ_util import OrganUtils
from scripts.common.utils.sqls import SqlUtils

DEFAULT_ID = '00000000000000000000000000000000'


class UpdateData:
    env = None
    chain_id = None
    basic_db_client = None
    goods_wdb_client = None
    goods_ob_client = None
    promotion_db_client = None
    db_suffix = None
    organ = None

    def __init__(self, region_name, chain_id, env):
        self.env = env
        self.chain_id = chain_id
        self.region_name = region_name
        self.basic_db_client = DBClient(self.region_name, 'ob', 'abc_cis_basic', env, True)
        self.goods_wdb_client = DBClient(self.region_name, 'abc_cis_stock', 'abc_cis_goods', self.env, True)
        if env == 'dev' or env == 'test':
            self.db_suffix = f'_{env}'
        else:
            self.db_suffix = ''

    def run(self):
        self.update_subSetPriceClinics()
    def update_subSetPriceClinics(self):
        select_sql = """
            select view_mode,sub_set_price,sub_set_price_all_clinics from v2_goods_chain_config where  chain_id = '{chainId}'; 
        """.format(chainId=self.chain_id)
        print(select_sql)
        result = self.goods_wdb_client.fetchone(select_sql)
        if result is None:
            return
        view_mode = result['view_mode']
        if view_mode == 1:
            return
        sub_set_price = result['sub_set_price']
        sub_set_price_all_clinics = result['sub_set_price_all_clinics']
        if sub_set_price == 0 and sub_set_price_all_clinics == 0:
            return
        if sub_set_price_all_clinics == 1 and sub_set_price == 0:
            update_sql = """
                update v2_goods_chain_config set  sub_set_price_all_clinics =0 ,sub_set_price_clinics = null
                where chain_id = '{chainId}' ;
            """.format(chainId=self.chain_id)
            print(update_sql)
            self.goods_wdb_client.execute(update_sql)
        if sub_set_price_all_clinics == 1 and sub_set_price == 1:
            # 查询连锁下所有门店
            clinics_sql = """
                select id as clinicId, node_type as nodeType, name as clinicName 
                from organ 
                where parent_id = '{chainId}' and node_type = 2
            """.format(db_suffix=self.db_suffix, chainId=self.chain_id)
            
            print(clinics_sql)
            clinics = self.basic_db_client.fetchall(clinics_sql)
            
            if clinics and len(clinics) > 0:
                # 将门店列表转换为JSON格式字符串
                clinic_json = json.dumps([{
                    "clinicId": clinic["clinicId"],
                    "nodeType": clinic["nodeType"],
                    "clinicName": clinic["clinicName"]
                } for clinic in clinics])
                
                # 更新商品价格配置
                update_sql = """
                    update v2_goods_chain_config set sub_set_price_all_clinics = 0, sub_set_price_clinics = '{clinic_json}'
                    where chain_id = '{chainId}' and sub_set_price = 1;
                """.format(chainId=self.chain_id, clinic_json=clinic_json)
                
                print(update_sql)
                self.goods_wdb_client.execute(update_sql)


def main():
    parser = argparse.ArgumentParser(description='药店会员价刷数据脚本')
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名称[ShangHai/HangZhou/...]')
    parser.add_argument('--env', help='环境[dev/test/prod]')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    update_data = UpdateData(args.region_name, args.chain_id, args.env)
    update_data.run()


if __name__ == '__main__':
    main()
