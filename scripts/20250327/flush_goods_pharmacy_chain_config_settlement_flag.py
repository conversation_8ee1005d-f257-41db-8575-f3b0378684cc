# -*- coding: utf-8 -*-
"""
采购单结算审批标识，默认开启
@name: flush_pharmacy_charge_print_property_config.py
@author: <PERSON><PERSON><PERSON>
@email: <EMAIL>
@date: 2025-03-24 10:46:36
"""
import json
import os
import sys
import argparse
from datetime import datetime
import requests

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.rpc import regionRpcHost

env = 'prod'
default_id = '00000000000000000000000000000000'

class UpdateData:
    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', env, True)
        self.goods_db_client = DBClient(self.region_name, 'abc_cis_stock', 'abc_cis_goods', env, True)

    def run(self):
        chain_organ = self.basic_db_client.fetchone("""
        select * from organ where id = '{chainId}' and node_type = 1 and status = 1 and his_type = 10;
        """.format(chainId=self.chain_id))
        if chain_organ is None:
            return
        if chain_organ['his_type'] != 10:
            return
        chain_config = self.goods_db_client.fetchone("""
        select * from v2_goods_chain_config where chain_id = '{chainId}'
        """.format(chainId=self.chain_id))
        if chain_config is None:
            return
        update_sql = """
        update v2_goods_chain_config set chain_external_flag = chain_external_flag|0x10 where id = {id}
        """.format(id=chain_config['id'])
        print(update_sql)
        self.goods_db_client.execute(update_sql)
        requests.get("""http://{rpcHost}/rpc/v3/goods/jenkins/clean-chain-redis-cache?chainId={chainId}"""
                     .format(chainId = self.chain_id,rpcHost=regionRpcHost(self.region_name, env=env)))


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    update_data = UpdateData(args.region_name, args.chain_id)
    update_data.run()


if __name__ == '__main__':
    main()