import logging
import time

import requests
import json

def run():
    while True:
        t = int(time.time() * 1000)
        rsp = requests.post(
            url=f'https://gateway-p-region2.abcyun.cn/api/v2/ai/ping/webflux?{t}',
            headers={
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            json={
                "medicalRecord": {
                    "id": "ffffffff0000000034ef37b10472c001",
                    "chiefComplaint": "咳嗽",
                    "type": 0
                },
                "diagnosedDate": "2025-02-28T08:28:46Z",
                "patient": {
                    "name": "",
                    "sex": "男",
                    "age": {
                        "year": 44
                    },
                    "isMember": 0,
                    "importFlag": 0,
                    "blockFlag": 0,
                    "isWxMainPatient": 0,
                    "wxBindStatus": 0
                }
            }
        )
        logging.info(f'timestamp = {t}, status_code = {rsp.status_code}, rsp = {rsp.content.decode("utf-8")}')
        if rsp.status_code == 504:
            raise Exception('504错误，我等得可是望眼欲穿啊！！！')
        # time.sleep(0.5)


if __name__ == '__main__':
    run()
