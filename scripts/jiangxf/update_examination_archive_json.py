import argparse
import json
import logging
import os
import time

import requests

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.config import region_name
from multizone.rpc import regionRpcHost
from scripts.common.utils.lists import ListUtils as list_utils

default_id = '00000000000000000000000000000000'


def get_archive_json_data(region_name, env, id, headers):
    ts = int(time.time() * 1000)
    logging.info(f'ts: {ts}')
    return json.loads(
        requests.get(
            url=f'http://{regionRpcHost(region_name, env)}/rpc/examinations/external/examination-sheet-detail/{id}',
            params={'chainId': headers['cis-chain-id']},
            headers=headers
        ).content.decode('utf-8')
    )


def run(region_name, env, ids):
    logging.info(f'ids: {ids}')
    if len(ids) <= 0:
        raise Exception('ids is required')
    # region_name = 'HangZhou'
    # ids = {3811293728502595644}
    ob_client = DBClient(region_name, 'ob', 'abc_cis_examination', env, True)
    # rocketmq_client = RocketMqClient(region_name, 'gray', False)
    # rocketmq_client.producer_start()
    long_ids = [id for id in ids if id.isdigit()]
    str_ids = [id for id in ids if not id.isdigit()]
    rows = []
    if len(long_ids) > 0:
        fetch_sql = f"""select * from v2_examination_merge_sheet where id in ({', '.join([f"'{id}'" for id in long_ids])})"""
        logging.info(fetch_sql)
        rows.extend(ob_client.fetchall(fetch_sql))
    if len(str_ids) > 0:
        fetch_sql = f"""select * from v2_examination_sheet where id in ({', '.join([f"'{id}'" for id in str_ids])})"""
        logging.info(fetch_sql)
        rows.extend(ob_client.fetchall(fetch_sql))
    clinic_id_to_rows = list_utils.group_by(rows, lambda x: x['clinic_id'])
    clinic_rows = ob_client.fetchall(
        f"""select * from abc_cis_basic.organ where id in ({', '.join([f"'{clinic_id}'" for clinic_id in clinic_id_to_rows.keys()])})""")
    clinic_id_clinic = list_utils.to_map(clinic_rows, lambda x: x['id'])
    for clinic_id, rows in clinic_id_to_rows.items():
        for row in rows:
            clinic = clinic_id_clinic[clinic_id]
            headers = {
                'cis-chain-id': f'{clinic["parent_id"]}',
                'cis-clinic-id': f'{clinic["id"]}',
                'cis-employee-id': default_id,
                'cis-clinic-type': f'{clinic["node_type"]}',
                'cis-view-mode': f'{clinic["view_mode"]}',
                'cis-his-type': f'{clinic["his_type"]}'
            }
            if region_name == 'HangZhou':
                headers['__region_id__'] = '2'
            data = get_archive_json_data(region_name, env, row['id'], headers)
            message_body = {
                'chainId': clinic['parent_id'],
                'clinicId': clinic['id'],
                'operatorId': default_id,
                'type': 60002,
                'isInvalid': 0,
                'examinationSheetView': data['data']
            }
            logging.info(message_body)
            # rocketmq_client.send_message('CisExamination', 'ExaminationSheetChange', message_body)
            ts = int(time.time() * 1000)
            logging.info(f'ts = {ts}')
            logging.info(
                json.loads(
                    requests.post(
                        url=f'http://{regionRpcHost(region_name, env)}/rpc/examinations/notify-amqp?{ts}',
                        headers={
                          '__region_id__': '2' if region_name == 'HangZhou' else '1'
                        },
                        params={
                            'topicName': 'CisExamination',
                            'tagName': 'ExaminationSheetChange'
                        },
                        json=message_body
                    ).content.decode('utf-8')
                )
            )
        continue


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--region-id', help='分区id', required=False)
    parser.add_argument('--ids', help='ids[,]分割')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.region_id or not args.ids:
        parser.print_help()
        sys.exit(-1)
    run(region_name(args.region_id), args.env, args.ids.split(','))
