import json
import urllib.parse
import webbrowser
from http.server import BaseHTTPRequestHand<PERSON>, HTTPServer

import requests


# 启动本地HTTP服务器来捕获重定向
class OAuth2RedirectHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        parsed_path = urllib.parse.urlparse(self.path)
        if parsed_path.path != '/auth/wx/open/userInfo':
            self.send_response(404)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            self.wfile.write(b'Not found.')
            return
        query_params = urllib.parse.parse_qs(parsed_path.query)
        self.server.code = query_params.get('code')[0]
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(b'Authorization code received. You can close this window.')


def handler_oauth2_redirect_uri(server_class=HTTPServer, handler_class=OAuth2RedirectHandler, port=8160):
    server_address = ('localhost', port)
    httpd = server_class(server_address, handler_class)
    httpd.handle_request()

    # 获取access_token
    access_token = get_access_token(httpd.code)
    print(f'access_token: {access_token}')
    # 获取用户信息
    user_info = get_user_info(access_token['access_token'], access_token['openid'])
    print(f'user_info: {user_info}')


def qrcode_authorize():
    appid = 'wxed9954c01bb89b47'
    redirect_uri = 'http://localhost:8160/auth/wx/open/userInfo'  # 重定向url（需要在微信开放平台配置）
    scope = 'snsapi_login'
    state = 'state'
    url = f"https://open.weixin.qq.com/connect/qrconnect?appid={appid}&redirect_uri={urllib.parse.quote(string=redirect_uri, encoding='utf-8')}&response_type=code&scope={scope}&state={state}#wechat_redirect"
    webbrowser.open(url)


def get_access_token(code):
    appid = 'wxed9954c01bb89b47'
    appsecret = 'a7482517235173ddb4083788de60b90e'
    access_token = json.loads(
        requests.get(
            f'https://api.weixin.qq.com/sns/oauth2/access_token?appid={appid}&secret={appsecret}&code={code}&grant_type=authorization_code'
        ).content.decode('utf-8')
    )
    return access_token


def get_user_info(access_token, openid):
    lang = 'zh_CN'
    user_info = json.loads(
        requests.get(
            f'https://api.weixin.qq.com/sns/userinfo?access_token={access_token}&openid={openid}&lang={lang}'
        ).content.decode('utf-8')
    )
    return user_info


def h5_authorize():
    appid = 'wxad0b69bcf22772ab'
    appsecret = '9eb6a057df298966a9d39d27174ac07a'
    redirect_uri = 'http://localhost:8160/auth/wx/mp/userInfo '  # 重定向url（需要在微信开放平台配置）
    scope = 'snsapi_userinfo'
    state = 'state'
    url = f"https://open.weixin.qq.com/connect/oauth2/authorize?appid={appid}&redirect_uri={urllib.parse.quote(string=redirect_uri, encoding='utf-8')}&response_type=code&scope={scope}&state={state}&connect_redirect=1#wechat_redirect"
    webbrowser.open(url)


def miniapp_authorize():
    appid = 'wx4fa37501d81fa004'
    appsecret = 'a0e23b931cbe260b9fb855e8c4f2556e'
    code = 'xxx'
    url = f"https://api.weixin.qq.com/sns/jscode2session?appid={appid}&secret={appsecret}&js_code={code}&grant_type=authorization_code"


if __name__ == '__main__':
    # 微信扫码授权
    qrcode_authorize()
    # 微信扫码授权后的回调地址
    qrcode_authorize_redirect_uri = 'http://localhost:8160/auth/wx/open/userInfo?code=011jf1000t0RmS1sgl300Kqtzh1jf10b&state=state'
    handler_oauth2_redirect_uri()
    # 微信网页授权
    # h5_authorize()
    # h5_authorize_redirect_uri = ''
