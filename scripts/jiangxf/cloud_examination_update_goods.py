import json
import logging
import os
import time

import requests

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from scripts.common.utils.lists import ListUtils as list_utils

default_id = '00000000000000000000000000000000'


def export_clinic_list(region_name):
    ob_client = DBClient(region_name, 'ob', 'abc_cis_basic', 'prod', False)
    rows = ob_client.fetchall("""
    select c.parent_id                                                                as chain_id,
       c.id                                                                       as clinic_id,
       c.name                                                                     as clinic_name,
       c.short_name                                                               as clinic_short_name,
       e.name                                                                     as admin_name,
       e.mobile                                                                   as admin_mobile,
       e.id                                                                       as admin_id,
       e.wechat_open_id_mp                                                        as open_id,
       c.node_type,
       c.view_mode,
       c.his_type,
       case env
           when 2 then 'P'
           when 1 then 'G'
           else 'V' end                                                           as `env`,
       d.id                                                                       as `devices.device_id`,
       d.short_id                                                                 as `devices.short_id`,
       d.device_model_id                                                          as `devices.device_model_id`,
       dm.name                                                                    as `devices.name`,
       dm.model                                                                   as `devices.model`,
       dm.device_uuid                                                             as `devices.device_uuid`,
       json_arrayagg(json_object('id', g.id, 'name', g.name))                     as `associationComposeList`,
       d.seller_id                                                                as `devices.seller_id`,
       d.status                                                                   as `devices.status`,
       date_format(d.created, '%Y-%m-%d %T')                                      as `devices.created`
    from abc_cis_goods.v2_goods_examination_device d
             inner join abc_cis_goods.v2_goods_examination_device_model dm
                        on d.device_model_id = dm.id and dm.supplier_id != 0
             inner join abc_cis_goods.v2_goods_medical_stat ms on ms.chain_id = d.chain_id and
                                                                  json_contains(ms.extend_info,
                                                                                json_quote(cast(dm.id as char)),
                                                                                '$.bizRelevantIds')
             inner join abc_cis_goods.v2_goods_compose_opt gco
                        on gco.parent_goods_id = ms.goods_id and gco.compose_type = 30 and gco.chain_id = d.chain_id and gco.is_deleted = 0
             inner join abc_cis_goods.v2_goods g on g.id = gco.goods_id
             inner join abc_cis_basic.organ c on c.id = d.clinic_id
             inner join abc_cis_basic.clinic_employee as b
                        on b.clinic_id = c.id
                            and b.role_id = 1 and b.status = 1
             inner join abc_cis_basic.employee as e
                        on e.id = b.employee_id
             left join abc_cis_basic.v2_clinic_gray_organ go on go.chain_id = d.chain_id
    where d.status != 99
      and d.goods_sub_type = 1
    group by c.id
    order by d.created desc;
    """)
    return rows


if __name__ == '__main__':
    region_name = 'HangZhou'
    env = 'prod'
    rows = export_clinic_list(region_name)
    for row in rows:
        id_to_associationCompose = list_utils.to_map(json.loads(row['associationComposeList'].replace('\"', '"')), lambda x: x['id'])
        if len(id_to_associationCompose.keys()) != 5:
            print(json.dumps(row, ensure_ascii=False))
            print(len(id_to_associationCompose.keys()))
            print(json.dumps(id_to_associationCompose, ensure_ascii=False))
            # 查询设备型号
            device_model_id = row['devices.device_model_id']
            headers = {
                'cis-chain-id': '{}'.format(row['chain_id']),
                'cis-clinic-id': '{}'.format(row['clinic_id']),
                'cis-employee-id': '{}'.format(row['admin_id']),
                'cis-clinic-type': '{}'.format(row['node_type']),
                'cis-view-mode': '{}'.format(row['view_mode']),
                'cis-his-type': '{}'.format(row['his_type'])
            }
            if region_name == 'HangZhou':
                headers['__region_id__'] = '2'
            ts = int(time.time() * 1000)
            print(f'ts = {ts}')
            url = f'http://pre.rpc.abczs.cn/api/v3/goods/exam/assay/device-models/{device_model_id}?{ts}&combineType=&onlyStandardGoods=0'
            logging.info("url = {}".format(url))
            query_device_model_rsp = json.loads(
                requests.get(
                    url=url,
                    headers=headers
                ).content.decode('utf-8')
            )
            # logging.info("query_device_model_rsp = {}".format(json.dumps(query_device_model_rsp, ensure_ascii=False)))
            if query_device_model_rsp is None:
                raise Exception("query_device_model_rsp is None")
            unInstallStandardGoodsList = query_device_model_rsp.get('data', {}).get('unInstallStandardGoodsList', [])
            installStandardGoodsList = query_device_model_rsp.get('data', {}).get('installedLocalGoodsList', [])
            goodsList = []
            if len(unInstallStandardGoodsList) > 0:
                goodsList.extend(unInstallStandardGoodsList)
            if len(installStandardGoodsList) > 0:
                goodsList.extend(installStandardGoodsList)
            if len(goodsList) > 0:
                batch_create_req_body = {
                    "combineItemGoodsIds": [
                        {
                            "children": [child['id'] for child in goods['children']],
                            "id": goods['id']
                        }
                        for goods in goodsList if goods['combineType'] == 1
                    ],
                    "singleItemGoodsIds": [goods['id'] for goods in goodsList if goods['combineType'] == 0]
                }
                logging.info("batch_create_req_body = {}".format(
                    json.dumps(batch_create_req_body, ensure_ascii=False, indent=2)))
                ts2 = int(time.time() * 1000)
                url = f'http://pre.rpc.abczs.cn/api/v2/examination-goods/examinations/batch-create/{device_model_id}?{ts2}'
                logging.info("url = {}".format(url))
                batch_create_rsp = json.loads(
                    requests.post(
                        url=url,
                        json=batch_create_req_body,
                        headers=headers
                    ).content.decode('utf-8')
                )
                logging.info("batch_create_rsp = {}".format(json.dumps(batch_create_rsp, ensure_ascii=False, indent=2)))
                taskId = batch_create_rsp.get('data', {}).get('taskId')
                if taskId is None or taskId == '':
                    raise Exception("taskId is Required")
                # 轮巡联机进度
                while True:
                    async_task_rsp = json.loads(
                        requests.get(
                            url=f'http://pre.rpc.abczs.cn/api/v2/examination-goods/examinations/async-task-progress/batch-create-examination-goods/{taskId}?{int(time.time() * 1000)}',
                            headers=headers
                        ).content.decode('utf-8')
                    )
                    logging.info("async_task_rsp = {}".format(json.dumps(async_task_rsp, ensure_ascii=False, indent=2)))
                    time.sleep(0.5)
                    if async_task_rsp.get('data', {}).get('taskStatus') == 2:
                        break

