"""

"""
import json
import logging
import os
import uuid
from unicodedata import decimal

import requests
from numpy import double

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.es import ESClient
from openpyxl import load_workbook
from scripts.common.utils.excel_helper import ExcelHelper as excel_helper
from scripts.common.utils.lists import ListUtils as list_utils
import argparse

default_id = '00000000000000000000000000000000'

# es 跨实例迁移
# npm install -g elasticdump
# npm list -g --depth=0
# 导出到json文件 elasticdump --input=http://elastic:<EMAIL>:9200/v3-cis-invoice-tax-catalog-dev --output=output.json --type=data
# 直接跨实例迁移 elasticdump --input=http://elastic:<EMAIL>:9200/v3-cis-invoice-tax-catalog-dev --output=http://elastic:<EMAIL>:9200/v3-cis-invoice-tax-catalog-test --type=data

def run(region_name, env):
    es_cli = ESClient(region_name, env, True, False,
                      jumper_user='jiangxf' if region_name == 'HangZhou' else 'jiangxiaofeng')
    workbook = load_workbook(
        '/Users/<USER>/Library/Containers/com.tencent.WeWorkMac/Data/Documents/Profiles/2D48E004E8749B8FC2B16F579FE2F769/Caches/Files/2025-01/2ba9484ed7620100345e8f81f5178db7/税收分类字典0116.xlsx')
    # 获取工作表
    sheet = workbook.worksheets[0]
    list = excel_helper.sheet_to_json(sheet, {
        'catalog_code': 'code',
        'catalog_name': 'name',
        'catalog_name_short': 'shortName',
        'has_sub_catalog': 'hasSub',
        'explain': 'explain',
        'keywords': 'keywords',
        'tax_rate': 'taxRate',
        'vat_special_management': 'vatSpecialManagement',
        'Version': 'version',
        'IsValid': 'isValid',
        'ParentCatalogCode': 'parentCode',
        'SpecialType': 'specialType',
        'RefundUponTaxationType': 'refundUponTaxationType'
    })
    for l in list:
        if 'taxRate' in l:
            if l['taxRate'] == '':
                l['taxRate'] = None
            else:
                l['taxRate'] = double(l['taxRate'].replace('%', '').replace('、', '.')) / 100
            if l['taxRate'] == 0.0:
                l['taxRate'] = 0
        if 'hasSub' in l:
            l['hasSub'] = int(l['hasSub'])
            if l['hasSub'] == 1:
                l['isLeaf'] = 0
            else:
                l['isLeaf'] = 1
            del l['hasSub']
        else:
            l['isLeaf'] = 1
        if 'isValid' in l:
            l['isValid'] = int(l['isValid'])
        if 'specialType' in l:
            l['specialType'] = int(l['specialType'])
        if 'refundUponTaxationType' in l:
            if l['refundUponTaxationType'] == '':
                l['refundUponTaxationType'] = None
        if 'vatSpecialManagement' in l:
            if l['vatSpecialManagement'] == '':
                l['vatSpecialManagement'] = None
    parent_code_to_children = list_utils.group_by(list, lambda x: x['parentCode'])
    code_entity = list_utils.to_map(list, lambda x: x['code'])

    db_code_entity = {}
    values = []
    id_index = {}
    f = open('invoice_tax_catalog.json', 'w')
    for parent_code, children in parent_code_to_children.items():
        parent = db_code_entity.get(parent_code)
        if parent is None:
            # 先创建父级
            if parent_code not in code_entity:
                logging.error(f'parent_code: {parent_code} not found')
            else:
                parent = code_entity[parent_code]
                if 'parentCode' in parent:
                    del parent['parentCode']
                parent['id'] = str(uuid.uuid4()).replace('-', '')
                for k in set(parent.keys()):
                    if parent[k] is None:
                        del parent[k]
                db_code_entity[parent_code] = parent

                values.append({'index': {'_index': 'v3-cis-invoice-tax-catalog-dev', '_id': str(parent['id'])}})
                values.append(json.dumps(parent, ensure_ascii=False))
                id_index[str(parent['id'])] = len(values) - 1

        isValidChild = False
        for child in children:
            child1 = db_code_entity.get(child['code'])
            if child1 is None:
                if 'parentCode' in child:
                    del child['parentCode']
                child['id'] = str(uuid.uuid4()).replace('-', '')
                child['parentId'] = parent['id'] if parent is not None else '0'
                if child['code'] == '1070000000000000000':
                    child['parentId'] = '0'
                for k in set(child.keys()):
                    if child[k] is None:
                        del child[k]
                db_code_entity[child['code']] = child
                child1 = child

                values.append({'index': {'_index': 'v3-cis-invoice-tax-catalog-dev', '_id': str(child1['id'])}})
                values.append(json.dumps(child1, ensure_ascii=False))
                id_index[str(child1['id'])] = len(values) - 1
            if child1['isValid'] == 1:
                isValidChild = True
        if isValidChild and parent is not None:
            pass
            # parent['isValid'] = 1
            # values[id_index[parent['id']]] = json.dumps(parent, ensure_ascii=False)

    for v in values:
        f.write(json.dumps(v, ensure_ascii=False) + os.linesep)

    es_cli.bulkInsert('v3-cis-invoice-tax-catalog-dev', values)


def main():
    run('ShangHai', 'dev')


if __name__ == '__main__':
    main()
