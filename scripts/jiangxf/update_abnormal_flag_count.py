import os
import sys
import json
import datetime

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.db import DBClient


def run(region_name, chain_id):
    ods_client = DBClient(region_name, 'ods', 'abc_cis_examination', 'prod', False)
    table_name = 'v2_examination_sheet'
    start_date = ods_client.fetchone(
        """select date(min(created)) as startDate from {0} where chain_id = '{1}';""".format(table_name, chain_id))[
        'startDate']
    if start_date is None:
        print('start_date is None')
        return
    today = datetime.date.today()
    # 计算时间间隔
    days = (today - start_date).days
    gap = 30
    select_sql = "select * from v2_examination_sheet where abnormal_info is not null"
    for i in range(0, days, gap):
        sql = f"""{select_sql}
                 {'and' if select_sql.__contains__('where') else 'where'} created >= '{start_date + datetime.timedelta(days=i + (0 if i == 0 else 1))} 00:00:00' 
                 and created <= '{start_date + datetime.timedelta(days=i + gap)} 23:59:59' 
                 and chain_id = '{chain_id}';"""
        print(sql)
        rows = ods_client.fetchall(sql)
        if len(rows) == 0:
            print('no data')
            continue
        # rows拆分 一组2000个
        rows2000 = [rows[i:i + 2000] for i in range(0, len(rows), 2000)]
        f = open('./_update_abnormal_flag_count.sql', 'w+')
        for rows2 in rows2000:
            for row in rows2:
                abnormal_info = json.loads(row['abnormal_info'])
                abnormalItems = abnormal_info.get('abnormalItems')
                if abnormalItems is None:
                    continue
                abnormal_name_to_flag = {abnormalItem['name']: abnormalItem['flag'] for abnormalItem in abnormalItems}
                if -1 in abnormal_name_to_flag.values():
                    # 查询结果
                    results = ods_client.fetchall(
                        f"""select * from v2_examination_sheet_result where examination_sheet_id = '{row['id']}' and examination_value is not null""")
                    result_values = [json.loads(result['examination_value']) for result in results]
                    abnormal_name_to_flag_str = {result_value['name']: result_value['abnormalFlag'] for result_value in result_values if "abnormalFlag" in result_value}
                    update_flag = False
                    for abnormal_name, abnormal_flag in abnormal_name_to_flag.items():
                        if abnormal_flag == -1:
                            abnormal_flag_str = abnormal_name_to_flag_str.get(abnormal_name)
                            if abnormal_flag_str is None:
                                continue
                            if abnormal_flag_str == 'N':
                                print(f'abnormal_name: {abnormal_name} abnormal_flag_str is N, but abnormal_flag is -1')
                                # 移除abnormalItems中的结果
                                abnormalItems.remove({'name': abnormal_name, 'flag': -1})
                                update_flag = True
                    # 更新abnormal_info
                    if update_flag:
                        abnormal_info['abnormalItems'] = abnormalItems
                        abnormalCount = len(abnormalItems)
                        abnormal_info['abnormalCount'] = abnormalCount
                        update_sql = f"update v2_examination_sheet set abnormal_info = '{json.dumps(abnormal_info, ensure_ascii=False) if abnormalCount > 0 else None}', is_abnormal = {1 if abnormalCount > 0 else 0} where id = '{row['id']}';".replace("'None'", 'null')
                        # print(update_sql)
                        f.write(update_sql + os.linesep)

if __name__ == '__main__':
    region_name = 'HangZhou'
    chain_id = 'ffffffff0000000034a7aff0c70f8000'
    run(region_name, chain_id)
