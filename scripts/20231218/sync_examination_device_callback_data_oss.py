#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import logging
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.rpc import regionRpcHost
from multizone.db import DBClient
import argparse
import requests

default_id = '00000000000000000000000000000000'


def run(abcRegion, chain_id, env):
    rsp = requests.put('''http://{regionRpcHost}/rpc/examinations/device-callback-data/sync-oss/{chainId}'''.format(
        regionRpcHost=regionRpcHost(abcRegion, env), chainId=chain_id))
    logging.info(f'rsp: {rsp.content.decode("utf-8")}')


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    run(args.region_name, args.chain_id, 'prod')

    # env = 'dev'
    # basic_db_cli = DBClient('ShangHai', 'abc_cis_mixed', 'abc_cis_basic', env, True)
    # chains = basic_db_cli.fetchall("""select * from organ where node_type = 1""")
    # for chain in chains:
    #     run('ShangHai', chain['id'], env)

if __name__ == '__main__':
    main()
