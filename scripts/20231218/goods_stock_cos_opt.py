"""
刷新formIsDeleted 的数据
"""
import os
import sys
import argparse
import json
from datetime import date, datetime, time, timedelta

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient


# 刷进销存数据
class UpdateData:
    chain_id = None
    goods_db_client = None
    goods_log_db_client = None
    adb_client = None


    def __init__(self, region_name, chain_id):
        self.env = 'prod'
        self.chain_id = chain_id
        self.region_name = region_name
        self.date_after = '2024-10-02'
        self.goods_db_client = DBClient(self.region_name, 'abc_cis_stock', 'abc_cis_goods', self.env, True)
        self.goods_log_db_client = DBClient(self.region_name, 'abc_cis_stock_zip', 'abc_cis_goods_log', self.env, True)
        self.adb_client = DBClient(self.region_name, 'adb', 'abc_cis_goods', self.env, True)

    def run(self):
        try:
            self.reflushStockInfoIntoLog()
        except Exception as e:
            print("id:", self.chain_id, ' error:: ', str(e))
            raise e
        finally:
            self.goods_db_client.close()

    def updateGoodsOrder(self):
        try:
            sqls = [
                '''update v2_goods_stock_in set total_cost = use_total_cost_price where type != 10 and total_cost is null and chain_id = '{chainId}';''',
                '''update v2_goods_stock_in set total_cost =  ROUND((piece_count / piece_num + package_count) * package_cost_price, 4) where type = 10 and total_cost is null and chain_id = '{chainId}';''',
                '''update v2_goods_stock_out set total_cost= ROUND((piece_count / piece_num + package_count) * package_cost_price, 4) where chain_id='{chainId}';''',
                '''update v2_goods_stock_out_draft set total_cost= ROUND((piece_count / piece_num + package_count) * package_cost_price, 4) where chain_id='{chainId}';''',
                '''update v2_goods_stock_trans as t inner  join v2_goods_stock_trans_order as o on t.order_id = o.id set t.total_cost= ROUND((t.piece_count / t.piece_num + t.package_count) * t.package_cost_price, 4) where  o.chain_id ='{chainId}';''',
                '''update v2_goods_stock_trans_draft as t inner  join v2_goods_stock_trans_order_draft as o on t.order_id = o.id set t.total_cost= ROUND((t.piece_count / t.piece_num + t.package_count) * t.package_cost_price, 4) where  o.chain_id ='{chainId}';''',
                # '''update v2_goods_stock_check_draft as t inner join v2_goods_stock_check_order_draft as o on t.order_id = o.id set t.total_cost= IFNULL(ROUND((t.piece_count_change / t.piece_num + t.package_count_change) * t.package_cost_price, 4), 0),t.total_sale_price= IFNULL(ROUND((t.piece_count_change / t.piece_num + t.package_count_change) * t.package_price, 4), 0)  where o.chain_id ='{chainId}';''',
                # '''update v2_goods_stock_check_order_draft set package_count_change = after_count - before_count, amount               = after_cost_amount - before_cost_amount, total_sale_price     = after_sale_amount - before_sale_amount where chain_id ='{chainId}' and amount is null;''',
                '''update v2_goods_stock_check as t inner join v2_goods_stock_check_order as o on t.order_id = o.id set t.total_cost= IFNULL(ROUND((t.piece_count_change / t.piece_num + t.package_count_change) * t.package_cost_price, 4), 0),t.total_sale_price= IFNULL(ROUND((t.piece_count_change / t.piece_num + t.package_count_change) * t.package_price, 4), 0)  where  o.chain_id ='{chainId}'; ''',
                '''update v2_goods_stock_check_order set package_count_change = after_count - before_count, amount               = after_cost_amount - before_cost_amount, total_sale_price     = after_sale_amount - before_sale_amount where chain_id ='{chainId}' ;''',
                '''update v2_goods_cowork_stock_check_task set package_count_change = after_count - before_count, amount               = after_cost_amount - before_cost_amount, total_sale_price     = after_sale_amount - before_sale_amount where chain_id ='{chainId}' ;''',
                '''update v2_goods_stock set left_cost= IF( CEIL(POW(10, 4) * IFNULL((piece_count / piece_num + package_count) * package_cost_price, 0)) / POW(10, 4) > 99999999999.9999, 99999999999.9999, CEIL(POW(10, 4) * IFNULL((piece_count / piece_num + package_count) * package_cost_price, 0)) / POW(10, 4) ) where chain_id = '{chainId}';'''
            ]
            for sql in sqls:
                self.goods_db_client.execute(sql.format(chainId=self.chain_id))
        except Exception as e:
            print(e)

    # 刷这个logBat有几条日志
    def updateGoodsStockLog(self):
        sql = """ select  CONCAT('update v2_goods_stock_log set _in_tax_rat= ', count(*), ' where id in (',group_concat(id),') and _in_tax_rat is not null;') as exeSql from abc_cis_goods_log.v2_goods_stock_log where created_date >= '{dateAfter}'  and chain_id = '{chainId}' group by order_id, order_detail_id, action having  count(*) > 1 """.format(
            chainId=self.chain_id, dateAfter=self.date_after)
        goods_sql_res = self.adb_client.fetchall(sql)
        for stmt in goods_sql_res:
            sql = stmt['exeSql']
            if sql is None:
                continue
            print(sql)
            self.goods_log_db_client.execute(sql)
    def updateTransTraceId(self):
        sql ="""select 
                        CONCAT('update v2_goods_stock_log set patient_order_id=\''',a.id,'\'' where id in(',a.id,',',b.id,') and patient_order_id is not null;') as exeSql
                        from abc_cis_goods_log.v2_goods_stock_log as a
                            inner join abc_cis_goods_log.v2_goods_stock_log as b
                                on a.order_id = b.order_id 
                                    and a.order_detail_id = b.order_detail_id 
                                    and a.action = '调拨入库' and b.action = '调拨出库' 
                                    and a.chain_id = b.chain_id and a.goods_id = b.goods_id 
                                    and a.created_date >= '{dateAfter}'  
                                    and a.chain_id = '{chainId}'
                        where a.created_date >= '{dateAfter}'
                            and a.chain_id = '{chainId}'
                            and a.piece_count = abs(b.piece_count) 
                            and a.package_count = abs(b.package_count) 
                            and a.package_cost_price = b.package_cost_price
                            """\
            .format( chainId=self.chain_id, dateAfter=self.date_after)
        goods_sql_res = self.adb_client.fetchall(sql)
        for stmt in goods_sql_res:
            sql = stmt['exeSql']
            if sql is None:
                continue
            print(sql)
            self.goods_log_db_client.execute(sql)
    def updateRecetTraceId(self):
        sql ="""select 
                        CONCAT('update v2_goods_stock_log set patient_order_id=\''',a.id,'\'' where id in(',a.id,',',b.id,') and patient_order_id is not null;') as exeSql
                        from abc_cis_goods_log.v2_goods_stock_log as a
                            inner join abc_cis_goods_log.v2_goods_stock_log as b
                                on a.order_id = b.order_id 
                                    and a.order_detail_id = b.order_detail_id 
                                    and a.action = '领用入库' and b.action = '科室出库' 
                                    and a.chain_id = b.chain_id and a.goods_id = b.goods_id 
                                    and a.created_date >= '{dateAfter}'  
                                    and a.chain_id = '{chainId}'
                        where a.created_date >= '{dateAfter}'
                            and a.chain_id = '{chainId}'
                            and a.piece_count = abs(b.piece_count) 
                            and a.package_count = abs(b.package_count) 
                            and a.package_cost_price = b.package_cost_price
                            """ \
            .format( chainId=self.chain_id, dateAfter=self.date_after)
        goods_sql_res = self.adb_client.fetchall(sql)
        for stmt in goods_sql_res:
            sql = stmt['exeSql']
            if sql is None:
                continue
            print(sql)
            self.goods_log_db_client.execute(sql)


    def reflushStockInfoIntoLog(self):
        sqlFindInOrderLog ="""
            select stock_id as stockId,  goods_id as goodsId, id as logId,piece_num as pieceNum
            from abc_cis_goods_log.v2_goods_stock_log as a
            where a.created_date >= '{dateAfter}'
                and a.chain_id = '{chainId}'
                and a.action = '采购入库';"""\
            .format(chainId=self.chain_id, dateAfter=self.date_after)
        goods_sql_res = self.adb_client.fetchall(sqlFindInOrderLog)
        for stmt in goods_sql_res:
            inOrderStockId = stmt['stockId']
            goodsId = stmt['goodsId']
            logId = stmt['logId']
            replayGoodsStock ={}
            replayGoodsStock['pieceNum'] = stmt['pieceNum']
            replayGoodsStock['replayGoodsStockList'] = []
            sqlFindStock = """select 
                    id as stockId,
                    batch_id as batchId,
                    organ_id as clinicId,
                    pharmacy_no as pharmacyNo,
                    package_cost_price as packageCostPrice, 
                    pharmacy_type as pharmacyType
                from abc_cis_goods.v2_goods_stock
                where chain_id ='{chainId}' and goods_id = '{goodsId}'
                and id < '{stockId}';"""\
                .format(chainId=self.chain_id,goodsId= goodsId, stockId=inOrderStockId)
            stock_sql_res = self.adb_client.fetchall(sqlFindStock)
            for stmtStock in stock_sql_res:
                try:
                    replayGoodsStockItem = {}
                    replayGoodsStockItem['stockId'] = stmtStock['stockId']
                    replayGoodsStockItem['batchId'] = stmtStock['batchId']
                    replayGoodsStockItem['pharmacyNo'] = stmtStock['pharmacyNo']
                    replayGoodsStockItem['pharmacyType'] = stmtStock['pharmacyType']
                    replayGoodsStockItem['packageCostPrice'] = float(stmtStock['packageCostPrice'])
                    sqlStockBefore = """select id, 
                                    stock_package_count as  packageCount, 
                                    stock_piece_count as pieceCount , 
                                    package_cost_price as packageCostPrice, 
                                    stock_id as stockId
                                from abc_cis_goods_log.v2_goods_stock_log as a
                                where chain_id ='{chainId}' and stock_id = '{stockId}'
                                and id < '{logId}'
                                order by id desc
                            limit 1"""\
                        .format(chainId=self.chain_id,logId=logId,stockId=stmtStock['stockId'])
                    before_stock_sql_res = self.adb_client.fetchall(sqlStockBefore)
                    if len(before_stock_sql_res) < 0:
                        continue
                    bfStock = before_stock_sql_res[0]
                    if bfStock['packageCount'] == 0 and bfStock['pieceCount'] == 0:
                        continue
                    replayGoodsStockItem['packageCount'] = float(bfStock['packageCount'])
                    replayGoodsStockItem['pieceCount'] = float(bfStock['pieceCount'])
                    replayGoodsStockItem['leftCost'] =float( (bfStock['packageCount'] + bfStock['pieceCount']/stmt['pieceNum'] ) * bfStock['packageCostPrice'])
                    replayGoodsStock['replayGoodsStockList'].append(replayGoodsStockItem)
                except Exception as e:
                    print("id:", self.chain_id, ' error:: ', str(e))


            if len(replayGoodsStock['replayGoodsStockList']) == 0:
                continue
            sqlAppendStock = """
                            UPDATE v2_goods_stock_log 
                                    SET goods = JSON_SET(goods, '$.replayGoodsStock', JSON_OBJECT('{replayGoodsStock}')) 
                                    WHERE id ={logId};""".format(logId = stmt['logId'],replayGoodsStock =json.dumps(replayGoodsStock, ensure_ascii=False))
            print(sqlAppendStock)
            self.goods_log_db_client.execute(sqlAppendStock)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    updateData = UpdateData(args.region_name, args.chain_id)
    updateData.run()


if __name__ == '__main__':
    main()
