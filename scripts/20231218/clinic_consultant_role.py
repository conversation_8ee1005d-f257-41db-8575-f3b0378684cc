#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# <AUTHOR> <EMAIL>
#
# Distributed under terms of the MIT license.

"""
口腔门诊咨询师-刷员工数据权限
单店：
门诊 -> 患者
患者-会员管理 -> 营销-会员-会员管理
患者-持卡人管理 -> 营销-卡项-持卡人管理
患者-随访 -> 随访

子店：
门诊 -> 患者
患者-会员管理 -> 营销-会员-会员管理
患者-持卡人管理 -> 营销-卡项-持卡人管理
患者-随访 -> 随访

总部：
患者-会员管理 -> 营销-会员-会员管理
患者-持卡人管理 -> 营销-卡项-持卡人管理
患者-随访 -> 随访
"""
import json
import os
from array import array

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
import argparse

default_id = '00000000000000000000000000000000'

# 门诊
MODULE_OUTPATIENT = '2'
# 患者-会员管理
MODULE_PATIENT_MEMBER = '11'
# 患者-持卡人管理
MODULE_PATIENT_CARD = '304'
# 患者-患者档案
MODULE_PATIENT_ARCHIVES = '301'
# 患者-随访
MODULE_PATIENT_REVISIT = '302'
# 口腔患者
MODULE_PATIENT = '5'
# 营销-会员-会员管理
MODULE_ORAL_PROMOTION_MEMBER_MANAGEMENT = '11'
# 营销-卡项-持卡人管理
MODULE_ORAL_PROMOTION_CARD_MANAGEMENT = '304'


class ClinicRoleUpdator:
    chain_id = None
    region_name = None
    basic_db_client = None

    def __init__(self, chain_id, region_name):
        self.chain_id = chain_id
        self.basic_db_client = DBClient(region_name, 'abc_cis_mixed', 'abc_cis_basic', 'prod', True)
        self.region_name = region_name

    def update_chain_roles(self):
        organs = list_organ_by_parent_id(self.basic_db_client, self.chain_id)
        if not organs:
            return

        for organ in organs:
            if not organ:
                continue

            if organ['his_type'] != 1:
                continue

            self.update_clinic_roles(organ)

    def update_single_organ_roles(self, organ):
        """
        修改单店视图角色权限
        """
        if not organ:
            return

        # 1.查询门店下所有的员工权限信息
        organ_id = organ['id']
        clinic_employees = list_clinic_employee_by_organ_id(self.basic_db_client, organ_id)
        if not clinic_employees:
            return

        # 2. 解析门店员工现在的权限
        for clinic_employee in clinic_employees:
            if not clinic_employee:
                continue

            employee_module_ids_str = clinic_employee['module_ids']
            if not employee_module_ids_str:
                continue

            origin_employee_module_ids: array = employee_module_ids_str.split(',')
            if not origin_employee_module_ids:
                continue

            new_employee_module_ids = list(origin_employee_module_ids)

            if MODULE_PATIENT in new_employee_module_ids:
                # 有患者模块权限，但是没有会员管理模块权限，则增加改权限
                if MODULE_PATIENT_MEMBER not in new_employee_module_ids:
                    new_employee_module_ids.append(MODULE_PATIENT_MEMBER)
                # 有患者模块权限，但是没有持卡人管理模块权限，则增加权限
                if MODULE_PATIENT_CARD not in new_employee_module_ids:
                    new_employee_module_ids.append(MODULE_PATIENT_CARD)
                # 有患者模块权限，但是没有随访模块权限，则增加权限
                if MODULE_PATIENT_REVISIT not in new_employee_module_ids:
                    new_employee_module_ids.append(MODULE_PATIENT_REVISIT)

            if MODULE_OUTPATIENT in new_employee_module_ids:
                # 门诊 -> 患者
                new_employee_module_ids = [MODULE_PATIENT if module_id == MODULE_OUTPATIENT else module_id for module_id in new_employee_module_ids]

            if MODULE_PATIENT_ARCHIVES in new_employee_module_ids:
                # 患者档案 -> 患者
                new_employee_module_ids = [MODULE_PATIENT if module_id == MODULE_PATIENT_ARCHIVES else module_id for module_id in new_employee_module_ids]

            origin_employee_module_id_set = set(origin_employee_module_ids)
            if (set(new_employee_module_ids) == origin_employee_module_id_set
                    and len(origin_employee_module_id_set) == len(origin_employee_module_ids)):
                continue

            sql = """
                            update clinic_employee
                            set module_ids = '{moduleIds}'
                            where id = '{clinicEmployeeId}';
                        """.format(moduleIds=','.join(set(new_employee_module_ids)), clinicEmployeeId=clinic_employee['id'])
            # print(sql)
            self.basic_db_client.execute(sql)

    def update_chain_organ_roles(self, organ):
        """
        修改连锁总部角色权限
        """
        if not organ:
            return

        # 1.查询门店下所有的员工权限信息
        organ_id = organ['id']
        clinic_employees = list_clinic_employee_by_organ_id(self.basic_db_client, organ_id)
        if not clinic_employees:
            return

        # 2. 解析门店员工现在的权限
        for clinic_employee in clinic_employees:
            if not clinic_employee:
                continue

            employee_module_ids_str = clinic_employee['module_ids']
            if not employee_module_ids_str:
                continue

            origin_employee_module_ids: array = employee_module_ids_str.split(',')
            if not origin_employee_module_ids:
                continue

            new_employee_module_ids = list(origin_employee_module_ids)

            if MODULE_PATIENT in new_employee_module_ids:
                # 有患者模块权限，但是没有会员管理模块权限，则增加改权限
                if MODULE_PATIENT_MEMBER not in new_employee_module_ids:
                    new_employee_module_ids.append(MODULE_PATIENT_MEMBER)
                # 有患者模块权限，但是没有持卡人管理模块权限，则增加权限
                if MODULE_PATIENT_CARD not in new_employee_module_ids:
                    new_employee_module_ids.append(MODULE_PATIENT_CARD)
                # 有患者模块权限，但是没有随访模块权限，则增加权限
                if MODULE_PATIENT_REVISIT not in new_employee_module_ids:
                    new_employee_module_ids.append(MODULE_PATIENT_REVISIT)

            if MODULE_OUTPATIENT in new_employee_module_ids:
                # 门诊 -> 患者
                new_employee_module_ids = [MODULE_PATIENT if module_id == MODULE_OUTPATIENT else module_id for module_id in new_employee_module_ids]

            if MODULE_PATIENT_ARCHIVES in new_employee_module_ids:
                # 患者档案 -> 患者
                new_employee_module_ids = [MODULE_PATIENT if module_id == MODULE_PATIENT_ARCHIVES else module_id for module_id in new_employee_module_ids]

            origin_employee_module_id_set = set(origin_employee_module_ids)
            if (set(new_employee_module_ids) == origin_employee_module_id_set
                    and len(origin_employee_module_id_set) == len(origin_employee_module_ids)):
                continue

            sql = """
                            update clinic_employee
                            set module_ids = '{moduleIds}'
                            where id = '{clinicEmployeeId}';
                        """.format(moduleIds=','.join(set(new_employee_module_ids)), clinicEmployeeId=clinic_employee['id'])
            # print(sql)
            self.basic_db_client.execute(sql)

    def update_chain_sub_organ_roles(self, organ):
        """
        修改连锁子店视图角色权限
        """
        if not organ:
            return

        # 1.查询门店下所有的员工权限信息
        organ_id = organ['id']
        clinic_employees = list_clinic_employee_by_organ_id(self.basic_db_client, organ_id)
        if not clinic_employees:
            return

        # 2. 解析门店员工现在的权限
        for clinic_employee in clinic_employees:
            if not clinic_employee:
                continue

            employee_module_ids_str = clinic_employee['module_ids']
            if not employee_module_ids_str:
                continue

            origin_employee_module_ids: array = employee_module_ids_str.split(',')
            if not origin_employee_module_ids:
                continue

            new_employee_module_ids = list(origin_employee_module_ids)

            if MODULE_PATIENT in new_employee_module_ids:
                # 有患者模块权限，但是没有会员管理模块权限，则增加改权限
                if MODULE_PATIENT_MEMBER not in new_employee_module_ids:
                    new_employee_module_ids.append(MODULE_PATIENT_MEMBER)
                # 有患者模块权限，但是没有持卡人管理模块权限，则增加权限
                if MODULE_PATIENT_CARD not in new_employee_module_ids:
                    new_employee_module_ids.append(MODULE_PATIENT_CARD)
                # 有患者模块权限，但是没有随访模块权限，则增加权限
                if MODULE_PATIENT_REVISIT not in new_employee_module_ids:
                    new_employee_module_ids.append(MODULE_PATIENT_REVISIT)

            if MODULE_OUTPATIENT in new_employee_module_ids:
                # 门诊 -> 患者
                new_employee_module_ids = [MODULE_PATIENT if module_id == MODULE_OUTPATIENT else module_id for module_id in new_employee_module_ids]

            if MODULE_PATIENT_ARCHIVES in new_employee_module_ids:
                # 患者档案 -> 患者
                new_employee_module_ids = [MODULE_PATIENT if module_id == MODULE_PATIENT_ARCHIVES else module_id for module_id in new_employee_module_ids]

            origin_employee_module_id_set = set(origin_employee_module_ids)
            if (set(new_employee_module_ids) == origin_employee_module_id_set
                    and len(origin_employee_module_id_set) == len(origin_employee_module_ids)):
                continue

            sql = """
                            update clinic_employee
                            set module_ids = '{moduleIds}'
                            where id = '{clinicEmployeeId}';
                        """.format(moduleIds=','.join(set(new_employee_module_ids)), clinicEmployeeId=clinic_employee['id'])
            # print(sql)
            self.basic_db_client.execute(sql)

    def update_clinic_roles(self, organ):
        if not organ:
            return

        view_mode = organ['view_mode']
        if view_mode == 1:
            # 单店视图
            self.update_single_organ_roles(organ)
        elif view_mode == 0:
            # 连锁视图
            node_type = organ['node_type']
            if node_type == 1:
                # 连锁总部
                self.update_chain_organ_roles(organ)
            elif node_type == 2:
                # 连锁子店
                self.update_chain_sub_organ_roles(organ)
            else:
                print(f"未知的 node_type:{node_type}")
        else:
            print(f"未知的 view_mode:{view_mode}")


def list_organ_by_parent_id(client, chain_id):
    return client.fetchall("""
            select *
            from organ
            where parent_id = '{chain_id}'
        """.format(chain_id=chain_id))


def list_clinic_employee_by_organ_id(client, organ_id):
    return client.fetchall("""
        select *
        from clinic_employee
        where clinic_id = '{organ_id}';
    """.format(organ_id=organ_id))


def run(chain_id, region_name):
    updator = ClinicRoleUpdator(chain_id, region_name)
    updator.update_chain_roles()


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id, args.region_name)


if __name__ == '__main__':
    main()
