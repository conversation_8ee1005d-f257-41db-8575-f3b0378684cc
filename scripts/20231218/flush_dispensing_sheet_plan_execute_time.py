# -*- coding: utf-8 -*-
"""
@name: flush_dispensing_sheet_plan_execute_time.py
@author: <PERSON><PERSON><PERSON>
@email: <EMAIL>
@date: 2024-01-21 11:51:03
"""
import os
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))


from multizone.db import DBClient

import argparse
import json
import sys


class UpdateData:
    chain_id = None

    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', 'prod', True)
        # self.adb_db_client = DBClient(self.region_name, 'adb', 'abc_cis_dispensing', 'dev', True)
        self.his_advice_client = DBClient(self.region_name, 'scrm_hospital', 'abc_his_advice', 'prod', True)
        self.dispense_db_client = DBClient(self.region_name, 'abc_cis_stock_zip', 'abc_cis_dispensing', 'prod', True)
        self.adb_db_client =DBClient(self.region_name, 'adb', 'abc_cis_dispensing', 'prod', True)
        self.ods_db_client =DBClient(self.region_name, 'ods', 'abc_cis_dispensing', 'prod', True)

    def run(self):
        organHisType = self.basic_db_client.fetchone(
            ''' select his_type from organ where id = '{chainId}' '''.format(chainId=self.chain_id))
        if organHisType is None:
            return
        if organHisType['his_type'] != 100:
            print("organHisType is not 100")
            return
        self.flush_sheet_plan_execute_time()
        self.flush_sheet_form_process_info()
        pass

    def flush_sheet_plan_execute_time(self):
        exec_sql = """
            select concat('update v2_dispensing_sheet set plan_execute_time = \''', c.plan_execute_time, '\'' where id = ', '\''', a.id, '\'';') as update_sql from abc_cis_dispensing.v2_dispensing_sheet a
                join abc_cis_dispensing.v2_dispensing_form b on a.chain_id = b.chain_id and a.clinic_id = b.clinic_id and a.id = b.dispensing_sheet_id
                join abc_his_advice.v1_advice_execute c on b.chain_id = c.chain_id and b.clinic_id = c.clinic_id and c.id = b.advice_execute_id
                where a.chain_id = '{chainId}'
                and a.plan_execute_time is null and a.is_deleted = 0 and a.type = 10 and b.advice_execute_id is not null
        """.format(chainId=self.chain_id)
        update_sqls = self.adb_db_client.fetchall(exec_sql)
        for stmt in update_sqls:
            sql = stmt['update_sql']
            if sql is None:
                continue
            # print(sql)
            self.dispense_db_client.execute(sql)
        pass

    def flush_sheet_form_process_info(self):
        query_sql = """
            select a.id, a.usage_info, b.process_info from abc_cis_dispensing.v2_dispensing_form a
            join abc_his_advice.v1_advice_rule b on a.chain_id = b.chain_id and a.clinic_id = b.clinic_id and a.advice_rule_id = b.id
            where a.usage_info is not null and a.type = 10 and b.type in (10, 20) and b.process_info->'$.price' >= 0
            and a.chain_id = '{chainId}' and a.is_deleted = 0 and a.type = 10
        """.format(chainId=self.chain_id)
        query_results = self.ods_db_client.fetchall(query_sql)
        update_sql_list = []
        for stmt in query_results:
            # print(stmt)
            form_id = stmt['id']
            # usage_info = stmt['usage_info']
            # usage_info_json = json.loads(usage_info)
            process_info = stmt['process_info']
            process_info_json = json.loads(process_info)
            process_usage = process_info_json['displayName']
            usage_type = process_info_json['usageType']
            usage_sub_type = process_info_json['usageSubType']
            process_remark = process_info_json['remark']
            total_process_count = process_info_json['totalBagCount']
            process_bag_unit_count_decimal = process_info_json['perDosageBagCount']
            total_process_price = process_info_json['price']
            update_sql = """
                update v2_dispensing_form set usage_info = json_set(usage_info, '$.processUsage', '{processUsage}', '$.usageType', {usageType}, '$.usageSubType', {usageSubType}, '$.processRemark', '{processRemark}', '$.totalProcessCount', {totalProcessCount}, '$.processBagUnitCountDecimal', {processBagUnitCountDecimal}, '$.totalProcessPrice', {totalProcessPrice}) where id = '{formId}';
            """.format(processUsage=process_usage, usageType=usage_type, usageSubType=usage_sub_type, processRemark=process_remark,
                       totalProcessCount=total_process_count, processBagUnitCountDecimal=process_bag_unit_count_decimal,
                       totalProcessPrice=total_process_price, formId=form_id)
            # print(update_sql)
            update_sql_list.append(update_sql)
        for exec_sql in update_sql_list:
            self.dispense_db_client.execute(exec_sql)
        pass


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    update_data = UpdateData(args.region_name, args.chain_id)
    update_data.run()


if __name__ == '__main__':
    main()

