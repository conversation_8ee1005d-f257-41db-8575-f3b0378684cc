#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import logging
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.rediscli import RedisClient
import argparse
import requests

default_id = '00000000000000000000000000000000'


def updateDeviceType(region_name,chain_id):
    out_db_client = DBClient(region_name, 'abc_cis_outpatient', 'abc_cis_outpatient', 'prod', True)
    adb_db_client =DBClient(region_name, 'adb', 'abc_cis_outpatient', 'prod', True)

    # 检验单-跟设备型号的device_type绑定
    exam_sql = """
            select concat('update abc_cis_outpatient.v2_outpatient_template_catalogue set is_deleted = 1 where id = ', '\''', a.id,
              '\''', ';') as sql
            from abc_cis_outpatient.v2_outpatient_template_catalogue a
            inner join abc_cis_basic.organ b on a.chain_id = b.id
            left join abc_cis_outpatient.v2_outpatient_template_catalogue c on c.parent_id = a.id
            where a.owner_type in (1, 2)
                    and b.his_type = 1
                    and a.name = '中药处方'
                    and a.category = 1
                    and a.chain_id = '{chainId}'
                    and a.type = 1
                    and a.is_folder = 1
                    and a.parent_id is null
                    and c.id is null
                    and a.children_count = 0;
    """.format(chainId = chain_id)
    exam_update_sql_res = adb_db_client.fetchall(exam_sql)
    for stmt in exam_update_sql_res:
        sql = stmt['sql']
        if sql is None:
            continue
        out_db_client.execute(sql)



def run(region_name,chain_id):
    updateDeviceType(region_name,chain_id)

def main():

    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.region_name,args.chain_id)


if __name__ == '__main__':
    main()
