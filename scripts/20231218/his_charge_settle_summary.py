#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""
住院结算单汇总信息
"""
import json
import os


CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
import argparse
from idwork import IdWork

default_id = '00000000000000000000000000000000'


class HisChargeSettleSummaryGenerator:
    chain_id = None
    region_name = None
    basic_db_client = None
    id_work = None

    def __init__(self, chain_id, region_name):
        self.chain_id = chain_id
        self.basic_db_client = DBClient(region_name, 'abc_cis_mixed', 'abc_cis_basic', 'prod', True)
        self.his_charge_db_client = DBClient(region_name, 'scrm_hospital', 'abc_his_charge', 'prod', True)
        self.region_name = region_name

    def genernate(self):
        organs = list_organ_by_parent_id(self.basic_db_client, self.chain_id)
        if not organs:
            return

        for organ in organs:
            if not organ:
                continue

            if organ['his_type'] != 100:
                continue

            self.generate_chain(organ)

    def generate_chain(self, organ):
        """
        连锁生成结算单汇总数据
        """
        if not organ:
            return

        self.id_work = IdWork(self.his_charge_db_client, False)
        self.id_work.config()
        offset = 0
        limit = 50
        while True:
            # 查询结算单
            charge_settles = list_charge_settle(self.his_charge_db_client, organ['id'], offset, limit)
            if not charge_settles:
                return

            # 查询所有收费项
            for charge_settle in charge_settles:
                self.generate_charge_settle_goods_fee(charge_settle)

            offset += limit

    def generate_charge_settle_goods_fee(self, charge_settle):
        if not charge_settle:
            return

        settle_status = charge_settle['settle_status']
        if settle_status != 200:
            return

        patient_order_id = charge_settle['patient_order_id']
        exist_charge_settle_fee_types = list_charge_settle_fee_type(self.his_charge_db_client, patient_order_id)
        if exist_charge_settle_fee_types:
            return

        charge_form_item_fee_types = build_charge_form_item_fee_types(self.his_charge_db_client, patient_order_id)
        if not charge_form_item_fee_types:
            return

        sqls = []
        for charge_form_item_fee_type in charge_form_item_fee_types:
            if not charge_form_item_fee_type:
                continue
            his_charge_settle_goods_fee_id = self.id_work.getUID()
            his_charge_form_item_id_str = charge_form_item_fee_type['hisChargeFormItemIds']
            his_charge_form_item_ids = his_charge_form_item_id_str.split(",")
            sqls.append("""
                INSERT INTO v2_his_charge_settle_goods_fee (id, chain_id, clinic_id, his_charge_settle_id, patient_order_id, goods_id, goods_type, goods_sub_type, goods_type_id, name, unit,
                                                                               is_dismounting, advice_id, advice_rule_item_id, display_status, his_charge_sheet_type, his_charge_sheet_id, total_count,
                                                                               charged_total_count, uncharged_total_count, refunded_total_count, unit_price, total_price, charged_total_price, uncharged_total_price,
                                                                               refunded_total_price, display_created, source_charge_form_item_ids, fee_type_id, sort, is_deleted, created, created_by, last_modified,
                                                                               last_modified_by)
                VALUES ({id}, '{chainId}', '{clinicId}', {hisChargeSettleId}, '{patientOrderId}', '{goodsId}', {goodsType}, {goodsSubType},
                        {goodsTypeId}, '{name}', '{unit}', {isDismounting}, {adviceId}, {adviceRuleItemId}, {displayStatus}, {hisChargeSheetType}, 
                        {hisChargeSheetId}, {totalCount}, {chargedTotalCount}, {unchargedTotalCount}, {refundedTotalCount}, {unitPrice}, {totalPrice}, 
                        {chargedTotalPrice}, {unchargedTotalPrice}, {refundedTotalPrice}, '{displayCreated}', '{sourceChargeFormItemIds}', {feeTypeId}, 
                        {sort}, 0, current_timestamp(), '00000000000000000000000000000000', current_timestamp(), '00000000000000000000000000000000');
            """.format(
                id=his_charge_settle_goods_fee_id,
                chainId=charge_settle['chain_id'],
                clinicId=charge_settle['clinic_id'],
                hisChargeSettleId=charge_settle['id'],
                patientOrderId=charge_settle['patient_order_id'],
                goodsId=charge_form_item_fee_type['goods_id'],
                goodsType=charge_form_item_fee_type['goods_type'],
                goodsSubType=charge_form_item_fee_type['goods_sub_type'],
                goodsTypeId=charge_form_item_fee_type['goods_type_id'],
                name=charge_form_item_fee_type['name'],
                unit=charge_form_item_fee_type['unit'],
                isDismounting=charge_form_item_fee_type['is_dismounting'],
                adviceId=charge_form_item_fee_type.get('advice_id'),
                adviceRuleItemId=charge_form_item_fee_type['advice_rule_item_id'],
                displayStatus=charge_form_item_fee_type['displayStatus'],
                hisChargeSheetType=charge_form_item_fee_type['hisChargeSheetType'],
                hisChargeSheetId=charge_form_item_fee_type['hisChargeSheetId'],
                totalCount=charge_form_item_fee_type['totalCount'],
                chargedTotalCount=charge_form_item_fee_type['chargedTotalCount'],
                unchargedTotalCount=charge_form_item_fee_type['unchargedTotalCount'],
                refundedTotalCount=charge_form_item_fee_type['refundedTotalCount'],
                unitPrice=charge_form_item_fee_type['unit_price'],
                totalPrice=charge_form_item_fee_type['totalPrice'],
                chargedTotalPrice=charge_form_item_fee_type['chargedTotalPrice'],
                unchargedTotalPrice=charge_form_item_fee_type['unchargedTotalPrice'],
                refundedTotalPrice=charge_form_item_fee_type['refundedTotalPrice'],
                displayCreated=charge_form_item_fee_type['displayCreated'],
                sourceChargeFormItemIds=json.dumps(his_charge_form_item_ids),
                feeTypeId=charge_form_item_fee_type['fee_type_id'],
                sort=charge_form_item_fee_type['maxId']
            ).replace('None', 'NULL'))

            sqls.append("""
                update v2_his_charge_form_item
                set his_charge_settle_goods_fee_id = {his_charge_settle_goods_fee_id}
                where id in ({his_charge_form_item_id_str});
            """.format(his_charge_settle_goods_fee_id=his_charge_settle_goods_fee_id, his_charge_form_item_id_str=his_charge_form_item_id_str))

        for sql in sqls:
            self.his_charge_db_client.execute(sql)


def list_charge_settle_fee_type(client, patient_order_id):
    return client.fetchall("""
        select *
        from v2_his_charge_settle_goods_fee
        where patient_order_id = '{patient_order_id}' and is_deleted = 0
        limit 1;
    """.format(patient_order_id=patient_order_id))


def build_charge_form_item_fee_types(client, patient_order_id):
    charge_form_item_fee_types = []
    charge_form_item_fee_types.extend(client.fetchall("""
        select a.advice_rule_item_id,
               a.goods_id,
               a.goods_type,
               a.goods_sub_type,
               a.goods_type_id,
               a.fee_type_id,
               a.name,
               a.unit_price,
               a.unit,
               a.is_dismounting,
               min(a.status)      as                                                                                                           displayStatus,
               c.advice_id,
               c.type             as                                                                                                           hisChargeSheetType,
               c.id               as                                                                                                           hisChargeSheetId,
               group_concat(a.id) as                                                                                                           hisChargeFormItemIds,
               sum(a.unit_count * a.dose_count - ifnull(a.refund_unit_count, 0) * ifnull(a.refund_dose_count, 0))                              totalCount,
               sum(if(a.status in (10, 20), a.unit_count * a.dose_count - ifnull(a.refund_unit_count, 0) * ifnull(a.refund_dose_count, 0), 0)) chargedTotalCount,
               sum(if(a.status in (10, 20), ifnull(a.refund_unit_count, 0) * ifnull(a.refund_dose_count, 0), 0))                               refundedTotalCount,
               sum(if(a.status = 0, a.unit_count * a.dose_count, 0))                                                                           unchargedTotalCount,
               sum(a.total_price)                                                                                                              totalPrice,
               sum(a.charged_total_price)                                                                                                      chargedTotalPrice,
               sum(a.unit_price * if(a.status in (10, 20), ifnull(a.refund_unit_count, 0) * ifnull(a.refund_dose_count, 0), 0))                refundedTotalPrice,
               sum(if(a.status = 0, a.total_price, 0))                                                                                         unchargedTotalPrice,
               min(a.created)                                                                                                                  displayCreated,
               max(a.id)                                                                                                                       maxId
        from v2_his_charge_form_item a
                 inner join v2_his_charge_sheet c
                            on a.his_charge_sheet_id = c.id and c.type not in (20, 30) and c.advice_id != ''
        where a.is_deleted = 0
          and c.is_deleted = 0
          and a.patient_order_id = '{patient_order_id}'
          and a.goods_fee_type != 1
        group by c.advice_id, a.advice_rule_item_id, a.goods_id, a.unit_price, a.unit, a.is_dismounting, a.fee_type_id
    """.format(patient_order_id=patient_order_id)))

    charge_form_item_fee_types.extend(client.fetchall("""
        select a.advice_rule_item_id,
               a.goods_id,
               a.goods_type,
               a.goods_sub_type,
               a.goods_type_id,
               a.fee_type_id,
               a.name,
               a.unit_price,
               a.unit,
               a.is_dismounting,
               min(a.status)      as                                                                                                           displayStatus,
               c.type             as                                                                                                           hisChargeSheetType,
               c.id               as                                                                                                           hisChargeSheetId,
               group_concat(a.id) as                                                                                                           hisChargeFormItemIds,
               sum(a.unit_count * a.dose_count - ifnull(a.refund_unit_count, 0) * ifnull(a.refund_dose_count, 0))                              totalCount,
               sum(if(a.status in (10, 20), a.unit_count * a.dose_count - ifnull(a.refund_unit_count, 0) * ifnull(a.refund_dose_count, 0), 0)) chargedTotalCount,
               sum(if(a.status in (10, 20), ifnull(a.refund_unit_count, 0) * ifnull(a.refund_dose_count, 0), 0))                               refundedTotalCount,
               sum(if(a.status = 0, a.unit_count * a.dose_count, 0))                                                                           unchargedTotalCount,
               sum(a.total_price)                                                                                                              totalPrice,
               sum(a.charged_total_price)                                                                                                      chargedTotalPrice,
               sum(a.unit_price * if(a.status in (10, 20), ifnull(a.refund_unit_count, 0) * ifnull(a.refund_dose_count, 0), 0))                refundedTotalPrice,
               sum(if(a.status = 0, a.total_price, 0))                                                                                         unchargedTotalPrice,
               min(a.created)                                                                                                                  displayCreated,
               max(a.id)                                                                                                                       maxId
        from v2_his_charge_form_item a
                 inner join v2_his_charge_sheet c
                            on a.his_charge_sheet_id = c.id and c.type in (20, 30) and c.advice_id is null
        where a.is_deleted = 0
          and c.is_deleted = 0
          and a.patient_order_id = '{patient_order_id}'
          and a.goods_fee_type != 1
        group by c.type, a.goods_id, a.unit_price, a.unit, a.is_dismounting, a.fee_type_id
    """.format(patient_order_id=patient_order_id)))

    return charge_form_item_fee_types


def list_charge_settle(client, chain_id, offset, limit):
    return client.fetchall("""
        select *
        from v2_his_charge_settle
        where chain_id = '{chain_id}'
          and is_deleted = 0
          and settle_status = 200
        order by id
        limit {offset}, {limit}
  """.format(chain_id=chain_id, offset=offset, limit=limit))


def list_organ_by_parent_id(client, chain_id):
    return client.fetchall("""
            select *
            from organ
            where parent_id = '{chain_id}'
        """.format(chain_id=chain_id))


def run(chain_id, region_name):
    generator = HisChargeSettleSummaryGenerator(chain_id, region_name)
    generator.genernate()


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id, args.region_name)


if __name__ == '__main__':
    main()
