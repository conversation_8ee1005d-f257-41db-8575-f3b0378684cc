#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import logging
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.rpc import regionRpcHost
from multizone.db import DBClient
import argparse
import requests

default_id = '00000000000000000000000000000000'


def run(region_name, chain_id, env):
    exam_db_cli = DBClient(region_name, 'abc_cis_outpatient', 'abc_cis_examination', env, True)
    # 更新chainId、clinicId、patientOrderId
    exam_db_cli.execute(f"""
        update v2_examination_sheet_result r inner join v2_examination_sheet s on r.examination_sheet_id = s.id
        set r.chain_id         = s.chain_id,
            r.clinic_id        = s.clinic_id,
            r.patient_order_id = s.patient_order_id
        where s.chain_id = '{chain_id}' 
        and (
            r.chain_id = ''
            or r.clinic_id = ''
            or r.patient_order_id = ''
        );
    """)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    run(args.region_name, args.chain_id, 'prod')


if __name__ == '__main__':
    main()
