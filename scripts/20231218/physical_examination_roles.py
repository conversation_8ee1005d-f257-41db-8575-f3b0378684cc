#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
import argparse

default_id = '00000000000000000000000000000000'


def update_employee_roles(chain_id, region_name):
    basic_db_client = DBClient(region_name, 'abc_cis_mixed', 'abc_cis_basic', 'prod', True)


    # 医院管家检验技师加业务范围(118)
    basic_db_client.execute("""
    UPDATE abc_cis_basic.clinic_employee e
    INNER JOIN abc_cis_basic.organ o
        ON e.clinic_id=o.id AND o.his_type=100
    SET e.roles = JSON_ARRAY_APPEND(e.roles, '$', 118),
        e.last_modified=current_timestamp,
        e.last_modified_by='sql'
    WHERE JSON_CONTAINS(e.roles->'$[*]', '3')=1
      AND JSON_CONTAINS(e.roles->'$[*]', '118')=0
      AND e.chain_id='{chainId}'
    """.format(chainId=chain_id))

    # 医院管家检查技师加业务范围(121)
    basic_db_client.execute("""
    UPDATE abc_cis_basic.clinic_employee e
    INNER JOIN abc_cis_basic.organ o
        ON e.clinic_id=o.id AND o.his_type=100
    SET e.roles = JSON_ARRAY_APPEND(e.roles, '$', 121),
        e.last_modified=current_timestamp,
        e.last_modified_by='sql'
    WHERE JSON_CONTAINS(e.roles->'$[*]', '10')=1
      AND JSON_CONTAINS(e.roles->'$[*]', '121')=0
      AND e.chain_id='{chainId}'
    """.format(chainId=chain_id))

    # 医院管家有收费工作站的收费员，加医院收费员角色(124)
    basic_db_client.execute("""
    UPDATE abc_cis_basic.clinic_employee e
    INNER JOIN abc_cis_basic.organ o
        ON e.clinic_id=o.id AND o.his_type=100
    SET e.roles = JSON_ARRAY_APPEND(e.roles, '$', 124),
        e.last_modified=current_timestamp,
        e.last_modified_by='sql'
    WHERE JSON_CONTAINS(e.roles->'$[*]', '110')=1
      AND JSON_CONTAINS(e.roles->'$[*]', '124')=0
      AND FIND_IN_SET('507', e.module_ids) > 0
      AND e.chain_id='{chainId}'
    """.format(chainId=chain_id))

def run(chain_id, region_name):
    update_employee_roles(chain_id, region_name)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id, args.region_name)


if __name__ == '__main__':
    main()
