#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import logging
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.rpc import regionRpcHost
from multizone.db import DBClient
import argparse
import requests

default_id = '00000000000000000000000000000000'


def run(region_name, chain_id, env):
    exam_db_cli = DBClient(region_name, 'abc_cis_outpatient', 'abc_cis_examination', env, True)
    goods_db_cli = DBClient(region_name, 'abc_cis_stock', 'abc_cis_goods', env, True)
    # 更新检验单排序日期日期
    # exam_db_cli.execute(f"""update v2_examination_sheet set order_by_date = created where order_by_date != created and chain_id = '{chain_id}';""")
    # exam_db_cli.execute(f"""update v2_examination_merge_sheet set order_by_date = created where order_by_date != created and chain_id = '{chain_id}';""")
    #
    # # 刷medical_stat
    # goods_db_cli.execute(f"""update v2_goods_medical_stat ms inner join v2_goods g on ms.goods_id = g.id set ms.compose_flag = g.compose_flag where ms.chain_id = '{chain_id}';""")
    # goods_db_cli.execute(f"""
    #     update v2_goods_medical_stat ms inner join v2_goods_extend ge on ms.goods_id = ge.goods_id and ms.clinic_id = ge.organ_id
    #     set ms.supplier_id = json_unquote(json_extract(ge.extend_info, '$.supplierIds[0]'))
    #     where extend_info is not null
    #       and json_extract(extend_info, '$.supplierIds') is not null
    #       and json_length(json_extract(extend_info, '$.supplierIds')) > 0
    #       and json_extract(extend_info, '$.supplierIds[0]') is not null
    #       and json_extract(extend_info, '$.supplierIds[0]') != ''
    #       and ms.chain_id = '{chain_id}';
    # """)
    #
    # # 清空检查设备
    # goods_db_cli.execute(f"""update v2_goods_extend ge inner join v2_goods g on ge.goods_id = g.id set ge.biz_relevant_id = null where ge.biz_relevant_id is not null and ge.biz_relevant_id != '' and g.inner_flag = 0 and ge.chain_id = '{chain_id}';""")
    #
    # 刷内置诊断词条
    rsp = requests.post('''http://{regionRpcHost}/rpc/examinations/diagnosis-entry/built-in/{chainId}'''.format(
        regionRpcHost=regionRpcHost(region_name, env), chainId=chain_id))
    logging.info(f'rsp: {rsp.content.decode("utf-8")}')


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    run(args.region_name, args.chain_id, 'prod')


if __name__ == '__main__':
    main()
