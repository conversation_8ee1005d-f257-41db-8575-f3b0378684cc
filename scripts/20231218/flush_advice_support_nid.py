# -*- coding: utf-8 -*-
"""
@name: flush_advice_support_nid.py
@author: <PERSON><PERSON><PERSON>
@email: <EMAIL>
@date: 2023-11-21 22:41:44
"""
import argparse
import sys
import requests
import os
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.rediscli import RedisClient

from multizone.db import DBClient
from multizone.rpc import regionRpcHost


DEFAULT_ID = '00000000000000000000000000000000'


class UpdateData:
    chain_id = None
    basic_db_client = None
    goods_db_client = None
    his_advice_client = None

    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', 'prod', True)
        self.goods_db_client = DBClient(self.region_name, 'abc_cis_stock', 'abc_cis_goods', 'prod', True)
        self.his_advice_client = DBClient(self.region_name, 'scrm_hospital', 'abc_his_advice', 'prod', True)
        self.his_charge_client = DBClient(self.region_name, 'scrm_hospital', 'abc_his_charge', 'prod', True)
        self.dispense_db_client = DBClient(self.region_name, 'abc_cis_stock_zip', 'abc_cis_dispensing', 'prod', True)
        self.adb_db_client =DBClient(self.region_name, 'adb', 'abc_cis_dispensing', 'prod', True)

    def run(self):
        organHisType = self.basic_db_client.fetchone(
            ''' select his_type from organ where id = '{chainId}' '''.format(chainId=self.chain_id))
        if organHisType:
            if organHisType['his_type'] == 100:
                print("organHisType is  100")
                self.flush_advice_nid_execute_timing()
                self.flush_advice_goods_need_executive()
                requests.get("""http://{rpcHost}/rpc/v3/goods/jenkins/clean-chain-redis-cache?chainId={chainId}"""
                             .format(chainId=self.chain_id, rpcHost=regionRpcHost(self.region_name)))
                self.flush_advice_department_id()
                return

    def flush_advice_nid_execute_timing(self):
        organs = self.basic_db_client.fetchall("""
            select id as clinicId from abc_cis_basic.organ where his_type = 100 and parent_id = '{chainId}' and id != parent_id
        """.format(chainId=self.chain_id))
        if organs is None or len(organs) == 0:
            return
        insert_list = []
        for organ in organs:
            clinic_id = organ['clinicId']
            sql = """
                insert into v1_advice_freq_execute_timing(id, chain_id, clinic_id, code, description, enable_advice_rule_types, execute_timing_rule, sort, is_deleted, deleted_millis, created, created_by, last_modified, last_modified_by, start_execute_timing, end_execute_timing) values
                    (substr(uuid_short(), 6), '{chainId}', '{clinicId}', 'nid', '每天n次', '[0, 30, 40, 50, 60, 70, 80, 90]', null, 12, 0, 0, now(), '{defaultId}', now(), '{defaultId}', '08:00', '22:00');
            """.format(chainId=self.chain_id, clinicId=clinic_id, defaultId=DEFAULT_ID)
            insert_list.append(sql)
        if insert_list is None or len(insert_list) == 0:
            return
        for sql in insert_list:
            # print(sql)
            try:
                self.his_advice_client.execute(sql)
            except Exception as e:
                print(f'插入医嘱执行时间异常：{e}')

    def flush_advice_goods_need_executive(self):
        """
        护理医嘱老数据处理，护理等级、膳食医嘱不需要执行，其他护理医嘱都需要执行
        :return:
        """
        goods_id_list = self.goods_db_client.fetchall("""
            SELECT g.id as id
                FROM v2_goods g
                JOIN v2_goods_clinic_config cfg ON g.organ_id = cfg.clinic_id AND cfg.his_type = 100
                LEFT JOIN v2_goods_custom_type ct ON g.custom_type_id = ct.id AND g.organ_id = ct.chain_id AND ct.is_deleted = 0
                WHERE
                   g.organ_id = '{chainId}'
                   AND g.status != 99
                   AND g.type IN (21)
                   AND g.name NOT IN ('特级护理', '一级护理', '二级护理', '三级护理')
                   AND (ct.id IS NULL OR ct.name != '膳食')
        """.format(chainId=self.chain_id))
        if goods_id_list is None or len(goods_id_list) == 0:
            return
        update_sql_list = []
        for goods_id_dict in goods_id_list:
            goods_id = goods_id_dict['id']
            update_sql = """
            update v2_goods set need_executive = need_executive | 0x2 where id = '{goodsId}'
            """.format(goodsId=goods_id)
            # print(update_sql)
            update_sql_list.append(update_sql)

        for sql in update_sql_list:
            # print(sql)
            self.goods_db_client.execute(sql)


    #手写医嘱下达
    def flush_advice_department_id(self):
        self.his_advice_client.execute( """
                update v1_advice set created_doctor_department_id = department_id where created_doctor_department_id is null and chain_id = '{chainId}';
            """.format(chainId=self.chain_id ))
        self.his_advice_client.execute( """
                update v1_advice_status_log set actual_operate_department_id = department_id where actual_operate_department_id is null and chain_id = '{chainId}'; 
            """.format(chainId=self.chain_id ))
        self.his_charge_client.execute( """
                update abc_his_charge.v2_his_charge_sheet set created_department_id = department_id where created_department_id is null and chain_id = '{chainId}';
            """.format(chainId=self.chain_id ))
        exam_sql = """
        select CONCAT('UPDATE v2_dispensing_sheet set doctor_department_id =\''',c.department_id,'\'' where id = \''',a.id,'\'';') as sql
            from v2_dispensing_sheet a
            join v2_dispensing_order_rel_sheet b on a.clinic_id = b.clinic_id and a.id = b.dispense_sheet_id and a.is_deleted = 0
            join v2_dispensing_order c on b.clinic_id = c.clinic_id and b.order_id = c.id and c.is_deleted = 0
            where a.type = 10 and a.is_deleted = 0 and a.doctor_department_id is null and a.chain_id  ='{chainId}'
        """.format(chainId = self.chain_id)
        exam_update_sql_res = self.adb_db_client.fetchall(exam_sql)
        for stmt in exam_update_sql_res:
            sql = stmt['sql']
            if sql is None:
                continue
            self.dispense_db_client.execute(sql)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    update_data = UpdateData(args.region_name, args.chain_id)
    update_data.run()


if __name__ == '__main__':
    main()

