#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""
医院统计重构
"""
import argparse
import os
import sys

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient

default_id = '00000000000000000000000000000000'


class UpdateData:
    chain_id = None
    basic_db_client = None
    id_work = None
    type = None
    businessType = None

    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', 'prod', True)
        self.his_ward_db_client = DBClient(self.region_name, 'scrm_hospital', 'abc_his_ward', 'prod', True)

    def run(self):
        organ = self.basic_db_client.fetchone("""select his_type from organ where id = '{chainId}' """.format(chainId=self.chain_id))
        if not organ or organ['his_type'] != 100:
            return

        # 床位快照数据
        self.his_ward_db_client.execute("""
            insert into v1_ward_room_bed_snapshot(id, chain_id, clinic_id, ward_area_id, ward_room_id, bed_total,
                                                   bed_used_total,
                                                   created_by)
            select substr(uuid_short(), 4)            as id,
                   chain_id,
                   clinic_id,
                   ward_area_id,
                   ward_room_id,
                   count(1)                           as bed_total,
                   sum(if(use_status = 20, 1, 0))     as bed_used_total,
                   '00000000000000000000000000000000' as created_by
            from v1_ward_bed
            where is_deleted = 0
              and chain_id = '{chainId}'
            group by chain_id, clinic_id, ward_area_id, ward_room_id;
        """.format(chainId=self.chain_id))

        # 医院管家 ：有家医统计子权限的成员全部放大到有家医统计的权限
        self.basic_db_client.execute("""
            update
                clinic_employee a
                    inner join organ b on a.clinic_id = b.id and b.his_type = 100 and b.status = 1 and b.parent_id = a.chain_id
            set a.module_ids = CONCAT(a.module_ids, ',', '209')
            where a.status = 1
              and a.chain_id = '{chainId}'
              and !find_in_set('0', a.module_ids)
              and !find_in_set('6', a.module_ids)
              and !find_in_set('209', a.module_ids)
              and (find_in_set('9001', a.module_ids)
                or find_in_set('9002', a.module_ids)
                or find_in_set('9003', a.module_ids));
        """.format(chainId=self.chain_id))

        # 医院管家 ：有慢病管理统计子权限的成员全部放大到有家医统计的权限
        self.basic_db_client.execute("""
            update
                clinic_employee a
                    inner join organ b on a.clinic_id = b.id and b.his_type = 100 and b.status = 1 and b.parent_id = a.chain_id
            set a.module_ids = CONCAT(a.module_ids, ',', '207')
            where a.status = 1
              and a.chain_id = '{chainId}'
              and !find_in_set('0', a.module_ids)
              and !find_in_set('6', a.module_ids)
              and !find_in_set('207', a.module_ids)
              and (find_in_set('7001', a.module_ids)
                or find_in_set('7002', a.module_ids)
                or find_in_set('7003', a.module_ids));
        """.format(chainId=self.chain_id))

        # 门诊收费报表
        self.basic_db_client.execute("""
            update
                clinic_employee a
                    inner join organ b on a.clinic_id = b.id and b.his_type = 100 and b.status = 1 and b.parent_id = a.chain_id
            set a.module_ids = CONCAT(a.module_ids, ',', '10003')
            where a.status = 1
              and a.chain_id = '{chainId}'
              and !find_in_set('0', a.module_ids)
              and !find_in_set('6', a.module_ids)
              and !find_in_set('10003', a.module_ids)
              and find_in_set('210', a.module_ids);        
        """.format(chainId=self.chain_id))

        # 医保费用统计
        self.basic_db_client.execute("""
            update
                clinic_employee a
                    inner join organ b on a.clinic_id = b.id and b.his_type = 100 and b.status = 1 and b.parent_id = a.chain_id
            set a.module_ids = CONCAT(a.module_ids, ',', '8001')
            where a.status = 1
              and a.chain_id = '{chainId}'
              and !find_in_set('0', a.module_ids)
              and !find_in_set('6', a.module_ids)
              and !find_in_set('8001', a.module_ids)
              and find_in_set('208', a.module_ids);
        """.format(chainId=self.chain_id))


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    updateData = UpdateData(args.region_name, args.chain_id)
    updateData.run()


if __name__ == '__main__':
    main()
