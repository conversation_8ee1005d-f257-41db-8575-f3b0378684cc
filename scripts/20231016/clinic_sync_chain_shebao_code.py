#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import logging
import os
import sys
import argparse
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))



from multizone.db import DBClient
from multizone.rpc import regionRpcHost


default_id = '00000000000000000000000000000000'



import requests

class UpdateData:
    chain_id = None
    ods_db_client = None
    goods_db_client = None

    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.goods_db_client = DBClient(self.region_name, 'abc_cis_stock', 'abc_cis_goods', 'prod', True)
        self.ods_db_client = DBClient(self.region_name, 'ods', 'abc_cis_basic', 'prod', True)

    def run(self):
        self.flush_medicine_config()
        self.shebao_config()
        self.flush_goods_fee_compose_type()
        requests.get("""http://{rpcHost}/rpc/v3/goods/jenkins/clean-chain-redis-cache?chainId={chainId}""".format(chainId = self.chain_id,rpcHost=regionRpcHost(self.region_name)))

    def flush_medicine_config(self):
        # 口腔数据初始化-患者标签初始化
        self.goods_db_client.execute("""
            update v2_goods as g 
                inner join v2_goods_compose_opt v2gc 
                    on g.id = v2gc.parent_goods_id and v2gc.is_deleted = 0
                        and g.organ_id = v2gc.chain_id 
                    set g.compose_flag = IFNULL(g.compose_flag, 0) | 0x40
                    where g.organ_id = '{chainId}';
                """.format(chainId=self.chain_id))

    def shebao_config(self):
        goods_sql_res = self.ods_db_client.fetchall("""
           
select c.chain_id,
       c.clinic_id,
       c.region,
       c.same_region,
       c.region,
       g.id
 
from (
         select a.chain_id,
                a.clinic_id,
                a.region,
                case
                    when a.region is null and b.region is null then 0
                    when a.region = b.region then 0
                    else 4 end as same_region
         from abc_cis_shebao.shebao_clinic_config a
                  join (
             select chain_id, region
             from abc_cis_shebao.shebao_clinic_config
             where chain_id = clinic_id
         ) b on a.chain_id = b.chain_id
         where a.chain_id is not null
     ) c
         inner join abc_cis_goods.v2_goods_clinic_config g
                    on c.clinic_id = g.clinic_id
where c.chain_id ='{chainId}'; 
                
    """.format(chainId=self.chain_id))
        for stmt in goods_sql_res:
            sql = '''update v2_goods_clinic_config as g set g.shebao_region = '{region}',g.clinic_external_flag = g.clinic_external_flag | {same_region} where id ={id} '''.format(
                region =stmt['region'],
                same_region=  stmt['same_region'],
                id=stmt['id']
            )
            logging.info("SQL:"+sql)
            if sql is None:
                continue
            print(sql)
            self.goods_db_client.execute(sql)

    def flush_goods_fee_compose_type(self):
        fee_compose_type_sql = self.goods_db_client.fetchall("""
        select a.id 
from (
         select g.id, g.organ_id
         from v2_goods as g
         join v2_goods_clinic_config as cfg on g.organ_id = cfg.clinic_id and cfg.his_type != 100
         where g.fee_compose_type = 20)
         as a
         left join (select distinct parent_goods_id as goodsid
                    from v2_goods_compose_opt as o
                        join v2_goods_clinic_config as cc on o.chain_id = cc.clinic_id
                    where
                        case when cc.view_mode = 0 then o.clinic_id is null or o.clinic_id = ''
                            else o.clinic_id is not null end
                      and compose_type = 10
                      and is_deleted = 0 and cc.his_type != 100) as c
on a.id = c.goodsid
where c.goodsid is null and a.organ_id = '{chainId}';
        """.format(chainId=self.chain_id))
        for stmt in fee_compose_type_sql:
            sql = '''update v2_goods set  fee_compose_type = 0 where id ='{id}' '''.format(
                id=stmt['id']
            )
            if sql is None:
                continue
            # print(sql)
            self.goods_db_client.execute(sql)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    updateData = UpdateData(args.region_name, args.chain_id)
    updateData.run()



if __name__ == '__main__':
    main()
