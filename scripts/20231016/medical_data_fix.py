#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
import sys
import argparse
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from itertools import groupby

from idwork import IdWork

from multizone.db import DBClient
from multizone.rpc import regionRpcHost
import requests


default_id = '00000000000000000000000000000000'




class UpdateData:
    chain_id = None
    short_url_db_client = None
    emr_db_client = None
    scform_db_client = None
    basic_db_client = None
    id_work = None
    patient_tag_template_chain_id = None
    type = None
    businessType = None

    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.short_url_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_shorturl', 'prod', True)
        self.emr_db_client = DBClient(self.region_name, 'scrm_hospital', 'abc_his_emr', 'prod', True)
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', 'prod', True)

    def run(self):
        self.flush_medicine_config()

    ###

    def flush_medicine_config(self):


        organHisType = self.basic_db_client.fetchone(
            ''' select his_type from organ where id = '{chainId}' '''.format(chainId=self.chain_id))
        if organHisType:
            if organHisType['his_type'] == 0:
                print("organHisType is 0")
                return
        self.id_work = IdWork(self.emr_db_client, False)
        self.id_work.config()

        select_catalogue_sql = '''
        select id,name,clinic_id,chain_id,source_id,type from v2_short_url_catalogue where chain_id = '{chain_id}'  and level = 0 and is_deleted = 0 and type in (1,21)
        '''.format(chain_id = self.chain_id ,type=self.type)
        catalogues = self.short_url_db_client.fetchall(select_catalogue_sql)
        catalogues_map = {}
        for catalogue in catalogues:
            # catalogues_map[str(catalogue['id'])] = catalogue
            medicalInsertSql = self.createMedicalInsertSql(catalogue)
            self.emr_db_client.execute(medicalInsertSql)

        select_template_catalogue_sql = '''
                select id,name,clinic_id,chain_id,source_id,parent_id,owner_type, owner_id from v2_short_url_catalogue where chain_id = '{chain_id}' and level = 1 and is_deleted = 0  and type in (1,21)
                '''.format(chain_id = self.chain_id, type=self.type)
        template_catalogues = self.short_url_db_client.fetchall(select_template_catalogue_sql)

        template_catalogues_map = {}

        for template in template_catalogues:
            template_catalogues_map[str(template['id'])] = template


        select_meidcal_sql = '''
        select id, catalogue_id from v1_emr_medical where chain_id = '{chain_id}' and is_deleted = 0;
        '''.format(chain_id = self.chain_id)
        medicals = self.emr_db_client.fetchall(select_meidcal_sql)

        for medical in medicals :
            medical_catalogue = template_catalogues_map.get(str(medical['catalogue_id']))

            if medical_catalogue is None:
                continue
            # medical_type_catalogue = catalogues_map[medical_catalogue['parent_id']]
            sql = '''
            update v1_emr_medical set medical_type_id = '{medical_type_id}' where id = {id};
            '''.format(medical_type_id=medical_catalogue['parent_id'], id=medical['id'])
            self.emr_db_client.execute(sql)

        try:
            requests.get(
               """http://{rpcHost}/rpc/emr/data/copy?targetChainId={chainId}""".format(
                chainId=self.chain_id, rpcHost=regionRpcHost(self.region_name)))
        except Exception as e:
            print(e)


    def createMedicalInsertSql(self,catalogue):
        sql = '''
       INSERT INTO v1_emr_medical_type (id, clinic_id, chain_id, name, source_id, business_type, is_deleted, created, created_by, last_modified, last_modified_by) 
VALUES ({id}, '{clinic_id}', '{chain_id}', '{name}', {source_id}, {type}, 0, now(), '0', now(), '0');

        '''.format(id=catalogue['id'], clinic_id=catalogue['clinic_id'], chain_id=catalogue['chain_id'], name=catalogue['name'],
                   source_id= catalogue['source_id']  if catalogue['source_id'] is not None else 'NULL',
                    type= 0 if catalogue['type'] == 1 else 1)
        return sql;




def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)



    updateData = UpdateData(args.region_name, args.chain_id)
    updateData.run()

    # region = 'ShangHai'
    # chain_id = 'ffffffff00000000347857b55f734000'
    # updateData = UpdateData(region, chain_id)
    # updateData.run()



if __name__ == '__main__':
    main()
