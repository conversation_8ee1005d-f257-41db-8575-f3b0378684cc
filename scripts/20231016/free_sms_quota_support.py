#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
import argparse

default_id = '00000000000000000000000000000000'


def insert_switch(chain_id, region_name):
    basic_db_client = DBClient(region_name, 'abc_cis_account_base', 'abc_cis_message', 'prod', True)

    # 免费短信额度设为1000
    basic_db_client.execute("""
    UPDATE abc_cis_message.v2_message_config SET sms_quota_free=1000
    WHERE chain_id='{chainId}'
    """.format(chainId=chain_id))

    # 如果已经充值过——有计费额度，标记为不需要提示免费额度不足
    basic_db_client.execute("""
    update abc_cis_message.v2_message_config SET sms_quota_free_balance_notify_status='[0]'
    WHERE sms_quota>0 AND chain_id='{chainId}'
    """.format(chainId=chain_id))


def run(chain_id, region_name):
    insert_switch(chain_id, region_name)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id, args.region_name)


if __name__ == '__main__':
    main()
