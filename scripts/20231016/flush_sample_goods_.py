#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
import sys
import argparse
import json

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from idwork import IdWork
from multizone.db import DBClient

default_id = '00000000000000000000000000000000'


class UpdateData:
    chain_id = None
    goods_db_client = None
    id_work = None

    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.goods_db_client = DBClient(self.region_name, 'abc_cis_stock', 'abc_cis_goods', 'prod', True)
        self.id_work = IdWork(self.goods_db_client, False)
        self.id_work.config()

    def run(self):
        self.flush_sample_data(id_work=self.id_work)

    def flush_sample_data(self, id_work):
        # 采样管
        samples = self.goods_db_client.fetchall("""
            select * from v2_goods_examination_sample_pipe where is_deleted = 0 and chain_id = '{chainId}' and goods_id is null""".format(chainId=self.chain_id))
        if samples is None:
            return
        # 含有采样管的goods
        # 老的采样管是以样本和采样管作为唯一，如果采样管相同，样本不同，则需要刷一条采样组
        goods_list = self.goods_db_client.fetchall("""
        select * from v2_goods where organ_id = '{chainId}' and biz_extensions is not null and biz_extensions->'$.samplePipeId' and status != 99 and sample_id is null
        """.format(chainId=self.chain_id))
        # 获取最大code
        max_code = 0
        for sample in samples:
            code = self.get_code_number(sample['code'])
            if code > max_code:
                max_code = code
        # 以samplePipeId分组
        sample_pipe_id_group = {}
        for goods in goods_list:
            sample_data = json.loads(goods['biz_extensions'])
            sample_pipe_id = sample_data['samplePipeId']
            if sample_pipe_id in sample_pipe_id_group:
                sample_pipe_id_group[sample_pipe_id].append(goods)
            else:
                sample_pipe_id_group[sample_pipe_id] = [goods]

        update_sql_list = []
        for sample in samples:
            goods_id = id_work.getUUID()
            sample_id = sample['id']
            code = sample['code']
            sample_goods_list = sample_pipe_id_group.get(str(sample_id))
            # 第一个样本名称
            first_sample_type = None
            if sample_goods_list:
                for goods in sample_goods_list:
                    # print(goods)
                    sample_type = json.loads(goods['biz_extensions']).get('sampleType')
                    if sample_type:
                        first_sample_type = sample_type
                        # print(json.loads(goods['biz_extensions']).get('samplePipeId'), first_sample_type)
                        break
            # 都没有设置样本
            if first_sample_type is None:
                # 插入采样组goods
                insert_sql = f"""
                insert into v2_goods (id, status, name, organ_id, type, sub_type, type_id, fee_compose_type, fee_type_id, is_sell, inner_flag, package_unit, package_price, created_date, created_user_id, last_modified_date, last_modified_user_id)
                values ('{goods_id}', 1, '{code}', '{self.chain_id}', 26, 1, 73, 30, 73, 0, 1, '组', 0, now(), '{default_id}', now(), '{default_id}');
                """
                update_sql_list.append(insert_sql)
                # 更新采样组
                sample_update_sql = f"""update v2_goods_examination_sample_pipe set goods_id = '{goods_id}', pipe_name = '{sample['name']}', name = '{code}' where id = {sample_id};"""
                update_sql_list.append(sample_update_sql)
                if sample_goods_list is None:
                    continue
                for goods in sample_goods_list:
                    sample_id = json.loads(goods['biz_extensions']).get('samplePipeId')
                    goods_update_sql = f"""update v2_goods set sample_id = {sample_id} where id = '{goods['id']}';"""
                    update_sql_list.append(goods_update_sql)
            else:
                # 设置了样本
                # 原有采样管刷成goods
                insert_sql = f"""
                insert into v2_goods (id, status, name, organ_id, type, sub_type, type_id, fee_compose_type, fee_type_id, is_sell, inner_flag, package_unit, package_price, created_date, created_user_id, last_modified_date, last_modified_user_id)
                values ('{goods_id}', 1, '{code}', '{self.chain_id}', 26, 1, 73, 30, 73, 0, 1, '组', 0, now(), '{default_id}', now(), '{default_id}');
                """
                update_sql_list.append(insert_sql)
                # 是否已更新过原采样管
                update_old_sample = False
                # 需刷成采样组dict
                new_sample_dict = {}
                new_current_code = code
                if sample_goods_list is None:
                    sample_update_sql = f"""update v2_goods_examination_sample_pipe set goods_id = '{goods_id}', pipe_name = '{sample['name']}', name = '{code}', sample_type = '{first_sample_type}' where id = {sample_id};"""
                    update_sql_list.append(sample_update_sql)
                    continue
                for goods in sample_goods_list:
                    biz_data = json.loads(goods['biz_extensions'])
                    sample_type = biz_data.get('sampleType')
                    sample_id = biz_data.get('samplePipeId')
                    if sample_type is None or sample_type == '':
                        # TODO 未设置过样本类型的是否需要单独刷一个
                        # 直接更新goods
                        if sample_type is None:
                            sample_type = ''
                        sample_dict = new_sample_dict.get(sample_type)
                        if sample_dict:
                            sample_pipe_id = sample_dict.get('id')
                            goods_update_sql = f"""update v2_goods set sample_id = {sample_pipe_id}, biz_extensions = json_set(biz_extensions, '$.samplePipeId', '{sample_pipe_id}') where id = '{goods['id']}';"""
                            update_sql_list.append(goods_update_sql)
                        else:
                            max_code = max_code + 1
                            new_code = self.get_new_code(str(max_code))
                            new_goods_id = id_work.getUUID()
                            new_sample_pipe_id = id_work.getUIDLong()
                            new_sample = {
                                'code': new_code,
                                'id': new_sample_pipe_id,
                                'goods_id': new_goods_id
                            }
                            new_sample_dict[sample_type] = new_sample
                            new_sample_goods_insert_sql = f"""
                                insert into v2_goods (id, status, name, organ_id, type, sub_type, type_id, fee_compose_type, fee_type_id, is_sell, inner_flag, package_unit, package_price, created_date, created_user_id, last_modified_date, last_modified_user_id)
                                values ('{new_goods_id}', 1, '{new_code}', '{self.chain_id}', 26, 1, 73, 30, 73, 0, 1, '组', 0, now(), '{default_id}', now(), '{default_id}');
                            """
                            new_sample_insert_sql = f"""
                            insert into v2_goods_examination_sample_pipe(id, chain_id, code, name, color, additive, capacity, unit, item_category, description, created_by, created, last_modified_by, last_modified, goods_id, pipe_name, sample_type)
                            values ('{new_sample_pipe_id}', '{self.chain_id}', '{new_code}', '{new_code}', '{sample['color']}', '{sample['additive'] if sample['additive'] else ''}', '{sample['capacity']}', '{sample['unit'] if sample['unit'] else ''}', '{sample['item_category']}', '{sample['description'] if sample['description'] else ''}', '{default_id}', '{sample['created'].strftime("%Y-%m-%d %H:%M:%S")}', '{default_id}', '{sample['last_modified'].strftime("%Y-%m-%d %H:%M:%S")}', '{new_goods_id}', '{sample['name']}', '{sample_type if sample_type else ''}');
                            """
                            goods_update_sql = f"""update v2_goods set sample_id = {new_sample_pipe_id}, biz_extensions = json_set(biz_extensions, '$.samplePipeId', '{new_sample_pipe_id}') where id = '{goods['id']}';"""
                            update_sql_list.append(new_sample_goods_insert_sql)
                            update_sql_list.append(new_sample_insert_sql)
                            update_sql_list.append(goods_update_sql)
                        # goods_update_sql = f"""-- update v2_goods set sample_id = {sample_id} where id = '{goods['id']}';"""
                        # update_sql_list.append(goods_update_sql)
                    else:
                        if sample_type == first_sample_type:
                            # 样本名字相同
                            if not update_old_sample:
                                # 未更新采样组，更新采样组goods_id
                                sample_update_sql = f"""update v2_goods_examination_sample_pipe set goods_id = '{goods_id}', pipe_name = '{sample['name']}', name = '{code}', sample_type = '{first_sample_type}' where id = {sample_id};"""
                                update_sql_list.append(sample_update_sql)
                                update_old_sample = True
                            goods_update_sql = f"""update v2_goods set sample_id = {sample_id} where id = '{goods['id']}';"""
                            update_sql_list.append(goods_update_sql)
                        else:
                            # 样本名字不同，需将样本刷一条采样组
                            # 样本是否刷过
                            sample_dict = new_sample_dict.get(sample_type)
                            if sample_dict:
                                sample_pipe_id = sample_dict.get('id')
                                goods_update_sql = f"""update v2_goods set sample_id = {sample_pipe_id}, biz_extensions = json_set(biz_extensions, '$.samplePipeId', '{sample_pipe_id}') where id = '{goods['id']}';"""
                                update_sql_list.append(goods_update_sql)
                            else:
                                max_code = max_code + 1
                                new_code = self.get_new_code(str(max_code))
                                # new_current_code = new_code
                                new_goods_id = id_work.getUUID()
                                new_sample_pipe_id = id_work.getUIDLong()
                                new_sample = {
                                    'code': new_code,
                                    'id': new_sample_pipe_id,
                                    'goods_id': new_goods_id
                                }
                                new_sample_dict[sample_type] = new_sample
                                new_sample_goods_insert_sql = f"""
                                    insert into v2_goods (id, status, name, organ_id, type, sub_type, type_id, fee_compose_type, fee_type_id, is_sell, inner_flag, package_unit, package_price, created_date, created_user_id, last_modified_date, last_modified_user_id)
                                    values ('{new_goods_id}', 1, '{new_code}', '{self.chain_id}', 26, 1, 73, 30, 73, 0, 1, '组', 0, now(), '{default_id}', now(), '{default_id}');
                                """
                                new_sample_insert_sql = f"""
                                insert into v2_goods_examination_sample_pipe(id, chain_id, code, name, color, additive, capacity, unit, item_category, description, created_by, created, last_modified_by, last_modified, goods_id, pipe_name, sample_type)
                                values ('{new_sample_pipe_id}', '{self.chain_id}', '{new_code}', '{new_code}', '{sample['color']}', '{sample['additive'] if sample['additive'] else ''}', '{sample['capacity']}', '{sample['unit'] if sample['unit'] else ''}', '{sample['item_category']}', '{sample['description'] if sample['description'] else ''}', '{default_id}', '{sample['created'].strftime("%Y-%m-%d %H:%M:%S")}', '{default_id}', '{sample['last_modified'].strftime("%Y-%m-%d %H:%M:%S")}', '{new_goods_id}', '{sample['name']}', '{sample_type}');
                                """
                                goods_update_sql = f"""update v2_goods set sample_id = {new_sample_pipe_id}, biz_extensions = json_set(biz_extensions, '$.samplePipeId', '{new_sample_pipe_id}') where id = '{goods['id']}';"""
                                update_sql_list.append(new_sample_goods_insert_sql)
                                update_sql_list.append(new_sample_insert_sql)
                                update_sql_list.append(goods_update_sql)
            # break

        for sql in update_sql_list:
            # print(sql)
            self.goods_db_client.execute(sql)

    def flush_goods_sample_id(self):
        goods_list = self.goods_db_client.fetchall("""
        select * from v2_goods where organ_id = '{chainId}' and biz_extensions is not null and biz_extensions->'$.samplePipeId' and status != 99 and sample_id is null
        """.format(chainId=self.chain_id))
        if goods_list is None:
            return
        for goods in goods_list:
            biz_extensions = goods['biz_extensions']
            if biz_extensions is None:
                continue
            biz_data = json.loads(biz_extensions)
            if biz_data is None:
                continue
            sample_pipe_id = biz_data.get('samplePipeId')
            if sample_pipe_id is None:
                continue
            goods_update_sql = f"""update v2_goods set sample_id = {sample_pipe_id} where id = '{goods['id']}';"""
            print(goods_update_sql)
            # self.goods_db_client.execute(goods_update_sql)

    def get_new_code(self, code):
        """
        获取新的试管编号
        :param code: 原编号
        :return:
        """
        if code is None:
            number = 1
        else:
            number = int(''.join(filter(str.isdigit, code)))
            # number += 1
        return '#000' + f"{number:02d}"

    def get_code_number(self, code):
        """
        获取编号数字
        :param code: 原编号
        :return:
        """
        if code is None:
            number = 1
        else:
            number = int(''.join(filter(str.isdigit, code)))
        return number


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    update_data = UpdateData(args.region_name, args.chain_id)
    update_data.run()


if __name__ == '__main__':
    main()
