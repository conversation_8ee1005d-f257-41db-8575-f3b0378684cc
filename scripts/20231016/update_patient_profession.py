#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
import argparse

default_id = '00000000000000000000000000000000'


def update_profession(chain_id, region_name):
    basic_db_client = DBClient(region_name, 'abc_cis_account_base', 'abc_cis_patient', 'prod', True)

    basic_db_client.execute("""
    UPDATE abc_cis_patient.v2_patient SET profession='医务人员' WHERE profession='医生'
    AND chain_id='{chainId}'
    """.format(chainId=chain_id))

    basic_db_client.execute("""
    UPDATE abc_cis_patient.v2_patient SET profession='离退人员' WHERE profession='退休'
    AND chain_id='{chainId}'
    """.format(chainId=chain_id))

    basic_db_client.execute("""
    UPDATE abc_cis_patient.v2_patient SET profession='餐饮食品服务人员' WHERE profession='服务员'
    AND chain_id='{chainId}'
    """.format(chainId=chain_id))



def run(chain_id, region_name):
    update_profession(chain_id, region_name)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id, args.region_name)


if __name__ == '__main__':
    main()