#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""
这个脚本把患者的手机号，拉出来
"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import db
import argparse
import logging

logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)


class UpdateExecutor(object):

    def __init__(self):
        self._db_client_cache = {}

    def _get_db_client(self, cluster, database):
        k = '{0}-{1}'.format(cluster, database)
        if k in self._db_client_cache.keys():
            return self._db_client_cache[k]

        db_client = db.DBClient(cluster, database)
        self._db_client_cache[k] = db_client
        return db_client

    def updatePatient(self, chain_id):
        try:
            sqls = [
                # 刷入库单上 代煎代配的药房类型
                '''
                SET block_encryption_mode = 'aes-256-ecb'
                ''',
                '''
                    update abc_cis_patient.v2_patient
                    set mobile_cipher = TO_BASE64(AES_ENCRYPT(mobile, md5('d081bc67-b1a0-4d64-b58c-65e4dea69bfb')))
                    where mobile is not null and chain_id ='{chainId}'
                            and mobile != '';
                '''
                ,
                '''
                    update abc_cis_patient.v2_patient
                    set id_card_cipher = TO_BASE64(AES_ENCRYPT(id_card, md5('d081bc67-b1a0-4d64-b58c-65e4dea69bfb'))),
                        id_card_last6  = right(id_card, 6)
                    where id_card is not null and chain_id ='{chainId}'
                    and id_card != '';
                '''

            ]
            patient_db_client = self._get_db_client('abc_cis_mixed', 'abc_cis_patient')

            for sql in sqls:
                patient_db_client.execute(sql.format(chainId=chain_id))
        except Exception as e:
            print(e)


    def execute_chain(self, chain_id):
        #先刷Goods数据
        self.updatePatient(chain_id)

def run(chain_id):
    rgt = UpdateExecutor()
    rgt.execute_chain(chain_id)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)


if __name__ == '__main__':
    main()
