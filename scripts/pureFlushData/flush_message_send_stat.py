"""
刷新formIsDeleted 的数据
"""
import argparse
import os
import sys
from datetime import date, timedelta

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient


class UpdateData:
    employee_id = None
    short_url_db_client = None
    emr_db_client = None
    scform_db_client = None
    basic_db_client = None
    id_work = None
    patient_tag_template_chain_id = None
    type = None
    businessType = None

    def __init__(self, region_name, employee_id, env):
        self.employee_id = employee_id
        self.region_name = region_name
        self.adb_client = DBClient(self.region_name, 'abc_cis_account_base', 'abc_cis_message', env, True)
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_account_base', 'abc_cis_message', env, True)
        self.beginTime = date.today() - timedelta(days=365)
        # self.endTime = date.today() + timedelta(days=1)
        self.endTime = date.today() + timedelta(days=1)

    def run(self):
        self.updateMessageSendCount()

    def updateMessageSendCount(self):
        try:
            sql = '''
            select msl.event_type as eventType, max(msl.msg_log_id) as id
                from v2_message_single_log msl
                where msl.destination_owner_id = '{employeeId}'
                  and msl.message_channel = 2
                  and msl.event_type in (2, 3, 4)
                group by msl.event_type
            '''

            countSql = '''
                    select msl.event_type as eventType, count(1) as new_message_count
                    from v2_message_single_log msl
                    where msl.destination_owner_id = '{employeeId}'
                      and msl.message_channel = 2
                      and msl.read_count = 0
                      and msl.event_type in (2, 3, 4)
                    group by msl.event_type
            '''

            msgLogIds = self.adb_client.fetchall(sql.format(employeeId=self.employee_id))
            msgLogIdMap = {msgLogId['eventType']: msgLogId['id'] for msgLogId in
                           msgLogIds}

            counts = self.adb_client.fetchall(countSql.format(employeeId=self.employee_id))
            countMap = {count['eventType']: count['new_message_count'] for count in counts}

            insertSql = '''
            insert into v2_message_employee_send_stat (employee_id, `un_read_count`, xg_category, last_msg_id) values('{employeeId}', '{unReadCount}', '{xgCategory}', '{lastMsgId}') on duplicate key update un_read_count = values(un_read_count), last_msg_id = values(last_msg_id);
            '''
            for i in range(2, 5):
                msgLogId = msgLogIdMap.get(i)
                count = countMap.get(i)
                if count is None:
                    count = 0
                if msgLogId is not None:
                    finalSql = insertSql.format(employeeId=self.employee_id, unReadCount=count, xgCategory=i,
                                                lastMsgId=msgLogId)
                    # print(finalSql)

                    self.basic_db_client.execute(
                        insertSql.format(employeeId=self.employee_id, unReadCount=count, xgCategory=i,
                                         lastMsgId=msgLogId))



        except Exception as e:
            print(self.employee_id)
            print(e)


def main():
    basic_db_client = DBClient('ShangHai', 'abc_cis_account_base', 'abc_cis_message', 'prod', True)
    employeeSql = '''
        select owner_id as id
            from v2_message_mobile_client
    '''
    employees = basic_db_client.fetchall(employeeSql)
    for employee in employees:
        updateData = UpdateData('ShangHai', employee['id'], 'prod')
        updateData.run()


if __name__ == '__main__':
    main()
