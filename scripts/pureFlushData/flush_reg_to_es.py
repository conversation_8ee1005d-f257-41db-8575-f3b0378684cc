#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import db
import argparse
import logging
import es
import json
import time

import datetime
from dateutil.relativedelta import relativedelta

import sys

reload(sys)
sys.setdefaultencoding('utf-8')

logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)


class LogStashFlushData(object):

    def __init__(self):
        self._db_client_cache = {}

    def _get_db_client(self, cluster, database):
        k = '{0}-{1}'.format(cluster, database)
        if k in self._db_client_cache.keys():
            return self._db_client_cache[k]

        db_client = db.DBClient(cluster, database)
        self._db_client_cache[k] = db_client
        return db_client

    # 加载患者信息
    # 未解决问题 从adb里面 这样查数据 ，存在 找不到患者的情况
    def loadPatientMap(self, chain_id):
        # patientSql = '''
        #         select
        #                   p.id,
        #                   p.mobile_cipher                       as patientMobileCipher,
        #                   p.mobile_last4                        as patientMobileLast4,
        #                   p.id_card_cipher                      as patientIdCardCipher,
        #                   p.id_card_last6                       as patientIdCardLast6,
        #                   p.name                                as patientName,
        #                   p.sn                                  as patientSn,
        #                   p.is_member                           as patientIsMember,
        #                   p.sex                                 as patientSex
        #         from v2_patient as p
        #         where p.chain_id ='{chainId}'
        #         limit {offsetNum},{limitNum}
        #     '''
        # patient_db_client = self._get_db_client('adb', 'abc_cis_patient')
        # LOOP_COUNT = 1000
        # offset = 0
        patientIdToPatient = {}
        # while True:
        #     patientList = patient_db_client.fetchall(
        #         patientSql.format(chainId=chain_id, offsetNum=offset, limitNum=LOOP_COUNT))
        #     if patientList is None or len(patientList) == 0:
        #         break
        #     print('''.....loading patient (offSet:{0},limit:{1},{2})'''.format(offset, LOOP_COUNT,str(len(patientList))))
        #     offset += len(patientList)
        #     for patient in patientList:
        #         patientIdToPatient[patient['id']] = patient
        #
        #     if len(patientList) < LOOP_COUNT:
        #         break
        return patientIdToPatient

    # 查patientOrder信息
    def loadPatientOrderMap(self, patientOrderIdToPatientOrder,chain_id, patientOrderIdSql):
        patientOrderSql = '''
                select   po.no                                 as poNo,
                         po.id                                 as patientOrderId,
                         po.source                             as poSource
                from    v2_patientorder as po 
                where po.chain_id = '{chainId}' and po.id in ({patientOrderIdSql}) 
            '''
        patientorder_db_client = self._get_db_client('adb', 'abc_cis_patientorder')
        patientOrderList = patientorder_db_client.fetchall(
            patientOrderSql.format(chainId=chain_id, patientOrderIdSql=patientOrderIdSql))
        if patientOrderList is None or len(patientOrderList) == 0:
            return patientOrderIdToPatientOrder
        for patientOrder in patientOrderList:
            patientOrderIdToPatientOrder[patientOrder['patientOrderId']] = patientOrder
        return patientOrderIdToPatientOrder

    #查询输出到es的主表的sql
    def loadMainTableObjects(self, chain_id, clinic_id, beginDate, endDate):
        mainTableSql = ''' 
                select  rfi.id                 as registrationFormItemId,
                        rfi.is_deleted          as isDeleted,
                        rfi.status_v2           as statusV2,
                        rfi.reserve_date        as reserveDate,
                        rfi.chain_id            as chainId,
                        rfi.clinic_id           as clinicId,
                        rfi.department_id       as departmentId,
                        rfi.doctor_id           as doctorId,
                        rfi.patient_order_id    as patientOrderId,
                        rfi.registration_type   as registrationType,
                        rfi.patient_id          as patientId,
                        p.mobile_cipher                       as patientMobileCipher,
                        p.mobile_last4                        as patientMobileLast4,
                        p.id_card_cipher                      as patientIdCardCipher,
                        p.id_card_last6                       as patientIdCardLast6,
                        p.name                                as patientName,
                        p.sn                                  as patientSn,
                        p.is_member                           as patientIsMember,
                        p.sex                                 as patientSex
                from abc_cis_registration.v2_registration_form_item as rfi
                     left join abc_cis_patient.v2_patient as p on p.chain_id = rfi.chain_id and p.id = rfi.patient_id 
                where rfi.clinic_id ='{clinicId}' 
                        and  rfi.chain_id = '{chainId}'
                      and rfi.created >=  '{beginDate}' and rfi.created <= '{endDate}'
            '''
        reg_adb_client = self._get_db_client('adb', 'abc_cis_registration')
        recordList = reg_adb_client.fetchall(
            mainTableSql.format(chainId=chain_id,clinicId=clinic_id, beginDate=beginDate, endDate=endDate))
        return recordList

    def fillToEsObject(self, esItem, patientOrderIdToPatientOrder, patientIdToPatient):
        if esItem.get('patientOrderId') is not None and patientOrderIdToPatientOrder.get(
                esItem.get('patientOrderId')) is not None:
            po = patientOrderIdToPatientOrder.get(esItem.get('patientOrderId'))
            esItem['patientOrderNo'] = po.get('poNo')
            esItem['poSource'] = po.get('poSource')
        else:
            print("严重错误：未找到PatientOrder:" + esItem.get('patientOrderId'))

        # if esItem.get('patientId') is not None and patientIdToPatient.get(esItem.get('patientId')) is not None:
        #     patient = patientIdToPatient.get(esItem.get('patientId'))
        #     esItem['patientMobileCipher'] = patient.get('patientMobileCipher')
        #     esItem['patientMobileLast4'] = patient.get('patientMobileLast4')
        #     esItem['patientIdCardCipher'] = patient.get('patientIdCardCipher')
        #     esItem['patientIdCardLast6'] = patient.get('patientIdCardLast6')
        #     esItem['patientName'] = patient.get('patientName')
        #     esItem['patientSn'] = patient.get('patientSn')
        #     esItem['patientIsMember'] = patient.get('patientIsMember')
        # else:
        #     if esItem.get('patientId') != '00000000000000000000000000000000':
        #         print("严重错误：**** 未找到患者:" + esItem.get('patientId'))
        return esItem

    #不要捕获异常,异常了数据没刷成功可以继续刷
    def logStashFlushToEs(self, chain_id):
        # 倒退查 门店的最早创建时间
        selectMinCreatedSql = '''
            select  min(created)  as minCreated
            from    v2_registration_form_item as po 
            where po.clinic_id = '{clinicId}'   
        '''
        reg_adb_client = self._get_db_client('adb', 'abc_cis_registration')

        patientIdToPatient = self.loadPatientMap(chain_id)
        offset = 0
        esClient = es.ESClient('abc-search-prod-normal')
        basic_db_client = self._get_db_client('abc_cis_mixed', 'abc_cis_basic')
        organs = basic_db_client.fetchall(
            ''' select id as clinicId from organ where parent_id = '{chainId}' and node_type = 2 '''.format(
                chainId=chain_id))
        for organ in organs:
            clinic_id = organ['clinicId']
            now_date = datetime.datetime.now()
            now_date = now_date + relativedelta(days=1)
            PER_MONTH = 1
            index = 0
            minCreatedResult = reg_adb_client.fetchone(selectMinCreatedSql.format(clinicId=clinic_id))
            if minCreatedResult:
                minCreatedDate = minCreatedResult['minCreated']
            if minCreatedDate is None:
                print("未找最小创建时间 退出")
                continue
            minYYYYMMDD = minCreatedDate.strftime('%Y-%m-%d')
            while index < 64:
                sT = time.clock()
                endDate = (now_date - relativedelta(months=index * PER_MONTH)).strftime('%Y-%m-%d')
                beginDate = (now_date - relativedelta(months=(index + 1) * PER_MONTH)).strftime('%Y-%m-%d')
                if minYYYYMMDD > str(endDate):
                    print("不需要再往前找了:clinicId:" + clinic_id + " minYYYYMMDD=" + minYYYYMMDD + ",beginDate=" + str(
                        beginDate) + "," + endDate)
                    break

                recordList = self.loadMainTableObjects(chain_id, clinic_id, beginDate, endDate)
                index += 1
                if recordList is None or len(recordList) == 0:
                    print("beginDate :" + beginDate + "  -> " + endDate + "，loadSize = 0")
                    continue
                print( "clinicId:" + clinic_id + ",minCreated:" + minYYYYMMDD + ",beginDate :" + beginDate + "  -> " + endDate + "，loadSize = " + str( len(recordList)))

                patientOrderIdToPatientOrder = {}
                # ADB 虽然快 但是限制了2000个IN
                MAX_LEN = 2000
                listRun = [recordList[i:i+MAX_LEN] for i in range(0,len(recordList),MAX_LEN)]
                for list in listRun:
                    patientOrderIdSql = ','.join( [''' '{0}' '''.format(record['patientOrderId']) for record in list])
                    patientOrderIdToPatientOrder = self.loadPatientOrderMap(patientOrderIdToPatientOrder,chain_id, patientOrderIdSql)
                write_bulk = []
                for record in recordList:
                    writeToEsObject = self.fillToEsObject(record, patientOrderIdToPatientOrder, patientIdToPatient)
                    write_bulk.append({'index': {'_index': 'v3-cis-cdss-registration-form',
                                                 '_id': str(record['registrationFormItemId'])}})
                    write_bulk.append(json.dumps(writeToEsObject, ensure_ascii=False))
                dbTime = time.clock()
                esClient.bulkInsert('v3-cis-cdss-registration-form', write_bulk)
                esTime = time.clock()
                print('...write to es success:' + str(len(recordList)) + ",db=" + str(
                    dbTime - sT) + 'min,writeEs=' + str(esTime - dbTime))


def run(chain_id):
    rgt = LogStashFlushData()
    rgt.logStashFlushToEs(chain_id)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)


if __name__ == '__main__':
    main()
