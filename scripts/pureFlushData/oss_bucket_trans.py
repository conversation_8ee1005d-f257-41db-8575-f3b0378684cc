# -*- coding: utf-8 -*-
import oss2
from multizone.db import DBClient

if __name__ == '__main__':
    basic_db_cli = DBClient('Master', 'ods', 'abc_cis_basic', 'prod', no_tunnel=True)
    rows = basic_db_cli.fetchall("""
        select o.id, o.name, ro.region_id
        from abc_cis_basic.organ o
                 inner join abc_cis_basic.v2_clinic_region_organ ro on o.parent_id = ro.chain_id
        where o.node_type != 1;
    """)
    clinic_id_to_region_id = {row['id']: row['region_id'] for row in rows}

    # 填写RAM用户的访问密钥（AccessKey ID和AccessKey Secret）。
    accessKeyId = 'LTAI4FgYhPMqQmb4uWe9CqdB'
    accessKeySecret = '******************************'
    # 使用代码嵌入的RAM用户的访问密钥配置访问凭证。
    auth = oss2.Auth(accessKeyId, accessKeySecret)
    # 填写Bucket所在地域对应的Endpoint。以华东1（杭州）为例，Endpoint填写为https://oss-cn-hangzhou.aliyuncs.com。
    bucket = oss2.Bucket(auth, 'https://oss-cn-chengdu.aliyuncs.com', 'cd-cis-static-assets')
    region_id_to_bucket = {
        1: oss2.Bucket(auth, 'https://oss-cn-shanghai.aliyuncs.com', 'abc-archive-data'),
        2: oss2.Bucket(auth, 'https://oss-cn-hangzhou.aliyuncs.com', 'abc-archive-data-hz')
    }
    for obj in oss2.ObjectIteratorV2(bucket=bucket, prefix='v2_examination_device_callback_data/', delimiter='/'):
        clinic_id = obj.key.split('/')[1]
        region_id = clinic_id_to_region_id.get(clinic_id)
        if region_id is None:
            print('region_id is None')
            continue
        region_bucket = region_id_to_bucket.get(region_id)
        if region_bucket is None:
            print('region_bucket is None')
            continue
        for clinic_id_obj in oss2.ObjectIteratorV2(bucket=bucket, prefix=obj.key, delimiter='/'):
            for date_obj in oss2.ObjectIteratorV2(bucket=bucket, prefix=clinic_id_obj.key, delimiter='/'):
                json_value = bucket.get_object(date_obj.key).read()
                # 上传到分区
                print(date_obj.key)
                region_bucket.put_object(date_obj.key, json_value)
