"""
    刷门店默认字段配置数据信息
"""

import argparse
import os
import sys

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.rediscli import RedisClient

"""
    chain_id: 门店id
    region_name: 分区
    env: 环境
"""
def run(chain_id_input, region_name, env):
    ob_client = DBClient(region_name, 'ob', 'abc_cis_outpatient_record', env, True)
    outpatient_client = DBClient(region_name, 'abc_cis_outpatient', 'abc_cis_outpatient', env, True)
    outpatient_record_client = DBClient(region_name, 'abc_cis_charge_record', 'abc_cis_outpatient_record', env, True)

    sql = '''
        UPDATE v2_outpatient_medical_record
    SET chinese_examination = CONCAT(
            COALESCE(chinese_examination, ''),
            IF(tongue IS NOT NULL AND tongue != '', CONCAT('，', tongue), ''),
            IF(pulse IS NOT NULL AND pulse != '', CONCAT('，', pulse), '')
                              ),
        tongue              = null,
        pulse               = null
    WHERE chain_id = '{chainId}'
      AND ((tongue IS NOT NULL AND tongue != '') OR (pulse IS NOT NULL AND pulse != ''))
      AND created >= '2025-03-01';
    '''.format(chainId=chain_id_input)

    row_count = outpatient_client.execute(sql)
    print("abc_cis_outpatient affected rows: {}".format(row_count))

    row_count = outpatient_record_client.execute(sql)
    print("abc_cis_outpatient_record affected rows: {}".format(row_count))

def main():
    # parser = argparse.ArgumentParser()
    # parser.add_argument('--chain-id', help='连锁id')
    # parser.add_argument('--region-name', help='分区名字可直接查配置')
    # parser.add_argument('--env', help='环境 dev/test/prod')
    # args = parser.parse_args()
    # if not args.env:
    #     parser.print_help()
    #     sys.exit(-1)
    # run(args.chain_id, args.region_name, args.env)

    basic_db_client = DBClient("ShangHai", 'abc_cis_mixed', 'abc_cis_basic', "dev", True)
    organs = basic_db_client.fetchall("""
        select id
        from organ
        where id = parent_id
    """)
    if not organs:
        return

    for organ in organs:
        run(organ['id'], 'ShangHai', 'dev')

    # run('628f2c02d90c480fa26fbed3d579ebc2', 'ShangHai', 'test')


if __name__ == '__main__':
    main()
