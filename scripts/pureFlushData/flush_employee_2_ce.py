import argparse
import os
import sys

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.db import DBClient

scope = 'cli_emp'
key_prefix = 'outpatient.medicalRecord.'
medical_type1 = 'western'
medical_type2 = 'chinese'
medical_type3 = 'oral'
medical_type4 = 'dentistry'
medical_type5 = 'revisitDentistry'
medical_type6 = 'ophthalmology'

data = """(substr(uuid_short(), 4), '{realKey}', '{value}', '{scope}', null, 0, '{created_by}', '{created}', '{last_modified_by}', '{last_modified}','outpatient', 'medicalRecord', '{medicalType}', '{key_fourth}', null, '{scopeId}')"""

insertTypeSql = '''INSERT INTO v2_property_config_item (id, `key`, value, scope, scope_id, is_deleted, created_by, created, last_modified_by, last_modified, key_first, key_second, key_third, key_fourth, key_fifth, v2_scope_id) VALUES (substr(uuid_short(), 4), '{realKey}', '{value}', '{scope}', null, 0, '{created_by}', '{created}', '{last_modified_by}', '{last_modified}', 'outpatient', 'medicalRecord', '{medicalType}', null, null, '{scopeId}') ON DUPLICATE KEY UPDATE value=values(value)'''

employeeSettingsSql = '''select `key`, value, created, created_by,  last_modified, last_modified_by, key_third, key_second from v2_property_config_item where scope = 'employee' and key_first = 'outpatient' and v2_scope_id = '{employee_id}';'''

# 获取该连锁关联的所有门店医生
employeeSql = '''
    select b.clinic_id, b.employee_id
from organ a
         inner join clinic_employee b on a.id = b.chain_id
where a.id = '{chain_id}' and b.status = 1 and a.status = 1
'''


def getMedicalTypeByKey(key_second):
    if key_second == 'ophthalmologyMedicalRecord':
        return medical_type6
    elif key_second == 'dentistryMedicalRecord':
        return medical_type4
    elif key_second == 'revisitDentistryMedicalRecord':
        return medical_type5
    else:
        pass


def copyEmployeeSettings2Ce(region_name, env, chain_id):
    # 连接到 MySQL
    propertyDb = DBClient(region_name, 'abc_cis_mixed', 'abc_cis_basic', env, True)
    basicDb = DBClient(region_name, 'abc_cis_mixed', 'abc_cis_basic', env, True)
    clinicEmpoloyee = basicDb.fetchall(employeeSql.format(chain_id=chain_id))
    employeeSettingMap = {}

    # 查看该门店的医生是否需要在property中存在一份，需要copy
    # 需要对照key
    # 前端上g的时候刷一下灰度、直到全部刷完
    # 需要刷新一下之前门店的特殊病历配置 上线前一次性刷完
    for ce in clinicEmpoloyee:
        dataSqls = []
        insertTypeSqls = []
        clinic_id = ce['clinic_id']
        employee_id = ce['employee_id']
        employeeSettings = employeeSettingMap.get(employee_id)
        if employeeSettings is None:
            employeeSettings = propertyDb.fetchall(employeeSettingsSql.format(employee_id=employee_id))
            employeeSettingMap[employee_id] = employeeSettings

        for employeeSetting in employeeSettings:
            key = employeeSetting['key']
            key_second = employeeSetting['key_second']
            key_third = employeeSetting['key_third']
            value = employeeSetting['value']
            scopeId = clinic_id + '_' + employee_id
            created = employeeSetting['created']
            created_by = employeeSetting['created_by']
            last_modified = employeeSetting['last_modified']
            last_modified_by = employeeSetting['last_modified_by']

            if key_second == 'medicalRecord':
                if key_third == 'oralExamination':
                    key_third = 'dentistryExaminations'
                elif key_third == 'dentistryExaminations' or key_third == 'treatmentPlans' or key_third == 'uploadImage':
                    continue
                elif key_third == 'type':
                    insertTypeSqls.append(
                        insertTypeSql.format(realKey=key, value=value,
                                             scope=scope,
                                             scopeId=scopeId, created_by=created_by, created=created,
                                             last_modified_by=last_modified_by, last_modified=last_modified,
                                             medicalType=key_third))
                    continue
                elif key_third == '' or key == '':
                    continue

                # 复制3份出来
                dataSqls.append(
                    data.format(realKey=key_prefix + medical_type1 + '.' + key_third, value=value, scope=scope,
                                scopeId=scopeId, created_by=created_by, created=created,
                                last_modified_by=last_modified_by, last_modified=last_modified,
                                medicalType=medical_type1, key_fourth=key_third))
                dataSqls.append(
                    data.format(realKey=key_prefix + medical_type2 + '.' + key_third, value=value, scope=scope,
                                scopeId=scopeId, created_by=created_by, created=created,
                                last_modified_by=last_modified_by, last_modified=last_modified,
                                medicalType=medical_type2, key_fourth=key_third))
                dataSqls.append(
                    data.format(realKey=key_prefix + medical_type3 + '.' + key_third, value=value, scope=scope,
                                scopeId=scopeId, created_by=created_by, created=created,
                                last_modified_by=last_modified_by, last_modified=last_modified,
                                medicalType=medical_type3, key_fourth=key_third))
            elif key_second == 'ophthalmologyMedicalRecord' or key_second == 'dentistryMedicalRecord' or key_second == 'revisitDentistryMedicalRecord':
                # 复制1份出来
                medicalType = getMedicalTypeByKey(key_second)
                dataSqls.append(
                    data.format(realKey=key_prefix + medicalType + '.' + key_third, value=value, scope=scope,
                                scopeId=scopeId, created_by=created_by, created=created,
                                last_modified_by=last_modified_by, last_modified=last_modified,
                                medicalType=medicalType, key_fourth=key_third))
            else:
                pass

        if len(dataSqls) > 0:
            values = ', '.join(dataSqls)
            insertSql = f'''INSERT INTO v2_property_config_item (id, `key`, value, scope, scope_id, is_deleted, created_by,created, last_modified_by, last_modified, key_first, key_second,key_third, key_fourth, key_fifth, v2_scope_id) VALUES {values} ON DUPLICATE KEY UPDATE value=values(value) '''
            print(insertSql)
            propertyDb.execute(insertSql)

        if len(insertTypeSqls) > 0:
            for insertTypeSql1 in insertTypeSqls:
                print(insertTypeSql1)
                propertyDb.execute(insertTypeSql1)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()

    if (args.env == 'dev' or args.env == 'test') and args.chain_id == 'all':
        # 遍历所有的连锁
        basic_db_client = DBClient(args.region_name, 'abc_cis_mixed', 'abc_cis_basic', args.env, True)
        organs = basic_db_client.fetchall("""
            select id
            from organ
            where id = parent_id
        """)
        if not organs:
            return

        # ffffffff000000000c5a1308069aa000
        # copyEmployeeSettings2Ce(args.region_name, args.env, 'ffffffff000000000c5a1308069aa000')
        for organ in organs:
            print(f"门店 {organ['id']}")
            copyEmployeeSettings2Ce(args.region_name, args.env, organ['id'])
    else:
        try:
            print(f"门店 {args.chain_id}")
            copyEmployeeSettings2Ce(args.region_name, args.env, args.chain_id)
        except Exception as e:
            print(f"处理数据 门店: {args.chain_id} error: {e}")


if __name__ == '__main__':
    main()
