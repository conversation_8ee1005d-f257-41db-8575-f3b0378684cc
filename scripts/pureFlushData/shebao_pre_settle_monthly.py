#! /usr/bin/env python3
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON>yon<PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import argparse
import json
import requests
import time
from typing import List, Dict, Any

from multizone.db import DBClient

# 需要月结对账的省份（整个省都需要）
NATIONAL_MONTHLY_PROVINCE_REGION_LIST = [
    "anhui",
    "neimenggu",
    "xinjiang"
]

# 需要月结对账的城市（不是整个省都需求）
NATIONAL_MONTHLY_CITY_REGION_LIST = [
    "gansu_lanzhou",
    "jiangsu_nanjing",
    "jiangxi_nanchang",
    "ningxia_yinchuan"
]

# 查询特定门店的信息
def query_specific_clinic(client, table, chain_id, clinic_id) -> List[Dict]:
    sql = f"""
    SELECT chain_id, clinic_id, region 
    FROM {table}.shebao_clinic_config 
    WHERE chain_id = '{chain_id}' AND clinic_id = '{clinic_id}' AND status = 10 
    ORDER BY clinic_id
    """
    result = client.fetchall(sql)
    return result

# 查询城市列表中的门店
def query_city_clinics(client, table, city) -> List[Dict]:
    sql = f"""
    SELECT chain_id, clinic_id, region 
    FROM {table}.shebao_clinic_config 
    WHERE region = '{city}' AND status = 10 
    ORDER BY clinic_id
    """
    result = client.fetchall(sql)
    return result

# 查询省份列表中的门店
def query_province_clinics(client, table, province) -> List[Dict]:
    sql = f"""
    SELECT chain_id, clinic_id, region 
    FROM {table}.shebao_clinic_config 
    WHERE region LIKE '{province}%' AND status = 10 
    ORDER BY clinic_id
    """
    result = client.fetchall(sql)
    return result

# 分批调用RPC接口
def call_rpc_in_batches(clinics: List[Dict], month: str, url: str) -> None:
    # 批处理大小固定为50
    batch_size = 50
    # 设置请求头
    headers = {"content-type": "application/json"}
    if not clinics:
        print("没有符合条件的门店")
        return
        
    total = len(clinics)
    print(f"共发现{total}家需要处理的门店")
    
    # 分批处理
    for i in range(0, total, batch_size):
        batch = clinics[i:i+batch_size]
        batch_num = i // batch_size + 1
        total_batches = (total + batch_size - 1) // batch_size
        # 得到当前批次的第一家和最后一家门店
        first_clinic = batch[0] if batch else None
        last_clinic = batch[-1] if batch else None
        print(f"处理第{batch_num}/{total_batches}批，本批{len(batch)}家门店")
        
        # 准备请求参数
        config_simple_list = [{
            "chainId": clinic["chain_id"],
            "clinicId": clinic["clinic_id"],
            "region": clinic["region"]
        } for clinic in batch]
        
        payload = {
            "month": month,
            "configSimpleList": config_simple_list
        }
        
        # 发送请求
        try:
            print(f"发送请求到: {url}")
            r = requests.post(url, headers=headers, data=json.dumps(payload), timeout=60)
            print(f"批次{batch_num}响应状态码: {r.status_code}")
            
            if r.status_code == 200:
                print(f"批次{batch_num}处理成功")
            elif r.status_code == 504:
                # 504 Gateway Timeout 状态码，说明后台已经接收请求并在处理
                # 等待1分钟后继续处理下一批
                print(f"检测到504状态码，后台处理中，等待60秒后继续下一批...")
                time.sleep(60)  # 等待1分钟
            else:
                # 其他错误状态码，打印信息
                if first_clinic and last_clinic:
                    print(f"批次{batch_num}处理失败: {r.text[:200]}, 批次起始门店ID: {first_clinic['clinic_id']}, 批次结束门店ID: {last_clinic['clinic_id']}")
                else:
                    print(f"批次{batch_num}处理失败: {r.text[:200]}")
        except Exception as e:
            if first_clinic and last_clinic:
                print(f"批次{batch_num}处理异常: {str(e)}, 批次起始门店ID: {first_clinic['clinic_id']}, 批次结束门店ID: {last_clinic['clinic_id']}")
            else:
                print(f"批次{batch_num}处理异常: {str(e)}")
        
        # 无论成功失败，都继续处理下一批
        if i + batch_size < total:
            time.sleep(2)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chainId', help='连锁id')
    parser.add_argument('--clinicId', help='门店id')
    parser.add_argument('--month', help='月份 yyyy-MM')
    parser.add_argument('--regionName', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境dev/test/prod')
    args = parser.parse_args()
    # args.env = 'dev'
    # args.regionName = 'ShangHai'
    # args.chainId = 'ffffffff000000000c5a1308069aa000'
    # args.clinicId = 'ffffffff000000001c47a3f811144000'
    # args.month = '2025-04'
    if not args.regionName or not args.env:
        parser.print_help()
        sys.exit(-1)

    apiUrlPrefix = ""
    if args.env == "dev":
        apiUrlPrefix = "dev.rpc.abczs.cn"
    elif args.env == "test":
        apiUrlPrefix = "test.rpc.abczs.cn"
    elif args.env == "prod":
        apiUrlPrefix = "pre.rpc.abczs.cn"
    
    # 特殊分区处理
    if args.regionName == "HangZhou":
        apiUrlPrefix = "region2-pre.rpc.abczs.cn"

    requestUrl = f"http://{apiUrlPrefix}/rpc/shebao-settle/pre-settle-monthly"
    print(f"将使用的API端点: {requestUrl}")
    
    # 初始化数据库客户端
    shebao_ob_client = DBClient(args.regionName, 'ob', 'abc_cis_shebao', args.env, True)
    abc_cis_shebao = 'abc_cis_shebao'
    if args.env != 'prod':
        abc_cis_shebao = abc_cis_shebao + '_' + args.env

    # 如果指定了特定门店，只处理该门店
    if args.clinicId and args.chainId:
        print(f"将只处理指定门店: 连锁={args.chainId}, 门店={args.clinicId}")
        specific_clinic = query_specific_clinic(shebao_ob_client, abc_cis_shebao, args.chainId, args.clinicId)
        if specific_clinic:
            print(f"开始处理指定的门店")
            call_rpc_in_batches(specific_clinic, args.month, requestUrl)
        else:
            print(f"未找到指定门店或该门店状态不为10")
            return
    else:
        # 先处理城市列表
        print(f"开始查询城市列表中的门店...")
        for city in NATIONAL_MONTHLY_CITY_REGION_LIST:
            print(f"====== 处理城市: {city} ======")
            city_clinics = query_city_clinics(shebao_ob_client, abc_cis_shebao, city)
            print(f"在{city}找到{len(city_clinics)}家门店")
            
            if city_clinics:  # 只有在找到门店时才调用RPC
                print(f"开始处理{city}的门店")
                call_rpc_in_batches(city_clinics, args.month, requestUrl)
            else:
                print(f"{city}没有找到门店，跳过处理")
            
        # 再处理省份列表
        print(f"开始查询省份列表中的门店...")
        for province in NATIONAL_MONTHLY_PROVINCE_REGION_LIST:
            print(f"====== 处理省份: {province} ======")
            province_clinics = query_province_clinics(shebao_ob_client, abc_cis_shebao, province)
            print(f"在{province}找到{len(province_clinics)}家门店")
            
            if province_clinics:  # 只有在找到门店时才调用RPC
                print(f"开始处理{province}的门店")
                call_rpc_in_batches(province_clinics, args.month, requestUrl)
            else:
                print(f"{province}没有找到门店，跳过处理")
    
    print("所有省份和城市处理完毕")
if __name__ == '__main__':
    main()
