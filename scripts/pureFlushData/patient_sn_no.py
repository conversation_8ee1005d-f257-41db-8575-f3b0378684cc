"""
刷新formIsDeleted 的数据
"""
import argparse
import os
import sys
import time

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.db import DBClient


class UpdateData:
    chain_id = None
    patient_db_client = None

    def __init__(self, region_name, chain_id, env):
        self.chain_id = chain_id
        self.region_name = region_name
        env = 'prod' if not env else env
        self.patient_db_client = DBClient(self.region_name, 'abc_cis_account_base', 'abc_cis_patient', env, True)

    def run(self):
        print("开始刷新门店: {chainId} 的患者 sn_no 字段".format(chainId=self.chain_id))
        # 每次刷 500 条
        limit = 350
        cursor = self.next_cursor(None, limit)
        if not cursor:
            print(f"门店: {self.chain_id} 没有患者")
            return
        total = 0
        while cursor:
            result = self.patient_db_client.execute("""
                update v2_patient
                set sn_no = cast(sn as unsigned)
                where chain_id = '{chainId}' and id >= '{id}' and sn is not null and sn != '' and sn_no is null
                order by id
                limit {limit}
            """.format(chainId=self.chain_id, id=cursor, limit=limit))
            total += result
            cursor = self.next_cursor(cursor, limit)
            time.sleep(1)

        print("门店: {chainId}, 更新了{total}条数据".format(chainId=self.chain_id, total=total))

    def next_cursor(self, cursor, offset):
        if not cursor:
            result = self.patient_db_client.fetchone("""
                select min(id) as id
                from v2_patient
                where chain_id = '{chainId}'
            """.format(chainId=self.chain_id))
        else:
            result = self.patient_db_client.fetchone("""
                select id
                from v2_patient
                where chain_id = '{chainId}' and id >= '{id}'
                order by id
                limit {offset}, 1
            """.format(chainId=self.chain_id, id=cursor, offset=offset))

        if not result:
            return None
        else:
            return result['id']


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    if (args.env == 'dev' or args.env == 'test') and args.chain_id == 'all':
        # 遍历所有的连锁
        basic_db_client = DBClient(args.region_name, 'abc_cis_mixed', 'abc_cis_basic', args.env, True)
        organs = basic_db_client.fetchall("""
            select id
            from organ
            where id = parent_id
        """)
        if not organs:
            return

        for organ in organs:
            updateData = UpdateData(args.region_name, organ['id'], args.env)
            updateData.run()
    else:
        updateData = UpdateData(args.region_name, args.chain_id, args.env)
        updateData.run()


if __name__ == '__main__':
    main()
