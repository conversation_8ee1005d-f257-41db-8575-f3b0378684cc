#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.rpc import regionRpcHost
import json
import argparse
import requests


# 多库房把以前老的药房类型刷到新的标上
def flushData(abcRegion, chain_id):
    charge_db_client = DBClient(abcRegion, 'abc_cis_charge', 'abc_cis_charge', 'prod', True)

    sqls = [
        """update v2_charge_sheet set query_exception_type = 0 where chain_id = '{chainId}' and query_exception_type = 1 and is_deleted = 0 and created > '2024-01-01 00:00:00' and created < '2024-02-01 00:00:00';""".format(chainId=chain_id),
        """update v2_charge_sheet set query_exception_type = 0 where chain_id = '{chainId}' and query_exception_type = 1 and is_deleted = 0 and created > '2024-02-01 00:00:00' and created < '2024-03-01 00:00:00';""".format(chainId=chain_id),
        """update v2_charge_sheet set query_exception_type = 0 where chain_id = '{chainId}' and query_exception_type = 1 and is_deleted = 0 and created > '2024-03-01 00:00:00' and created < '2024-04-01 00:00:00';""".format(chainId=chain_id),
        """update v2_charge_sheet set query_exception_type = 0 where chain_id = '{chainId}' and query_exception_type = 1 and is_deleted = 0 and created > '2024-04-01 00:00:00' and created < '2024-05-01 00:00:00';""".format(chainId=chain_id),
        """update v2_charge_sheet set query_exception_type = 0 where chain_id = '{chainId}' and query_exception_type = 1 and is_deleted = 0 and created > '2024-05-01 00:00:00' and created < '2024-06-01 00:00:00';""".format(chainId=chain_id),
        """update v2_charge_sheet set query_exception_type = 0 where chain_id = '{chainId}' and query_exception_type = 1 and is_deleted = 0 and created > '2024-06-01 00:00:00' and created < '2024-07-01 00:00:00';""".format(chainId=chain_id),
        """update v2_charge_sheet set query_exception_type = 0 where chain_id = '{chainId}' and query_exception_type = 1 and is_deleted = 0 and created > '2024-07-01 00:00:00' and created < '2024-08-01 00:00:00';""".format(chainId=chain_id),
        """update v2_charge_sheet set query_exception_type = 0 where chain_id = '{chainId}' and query_exception_type = 1 and is_deleted = 0 and created > '2024-08-01 00:00:00' and created < '2024-09-01 00:00:00';""".format(chainId=chain_id),
        """update v2_charge_sheet set query_exception_type = 0 where chain_id = '{chainId}' and query_exception_type = 1 and is_deleted = 0 and created > '2024-09-01 00:00:00' and created < '2024-10-01 00:00:00';""".format(chainId=chain_id),
        """update v2_charge_sheet set query_exception_type = 0 where chain_id = '{chainId}' and query_exception_type = 1 and is_deleted = 0 and created > '2024-10-01 00:00:00' and created < '2024-11-01 00:00:00';""".format(chainId=chain_id),
        """update v2_charge_sheet set query_exception_type = 0 where chain_id = '{chainId}' and query_exception_type = 1 and is_deleted = 0 and created > '2024-11-01 00:00:00' and created < '2024-12-01 00:00:00';""".format(chainId=chain_id),
        """update v2_charge_sheet set query_exception_type = 0 where chain_id = '{chainId}' and query_exception_type = 1 and is_deleted = 0 and created > '2024-12-01 00:00:00' and created < '2025-01-01 00:00:00';""".format(chainId=chain_id),
        """update v2_charge_sheet set query_exception_type = 0 where chain_id = '{chainId}' and query_exception_type = 1 and is_deleted = 0 and created > '2025-01-01 00:00:00' and created < '2025-02-01 00:00:00';""".format(chainId=chain_id),
        """update v2_charge_sheet set query_exception_type = 0 where chain_id = '{chainId}' and query_exception_type = 1 and is_deleted = 0 and created > '2025-02-01 00:00:00' and created < '2025-03-01 00:00:00';""".format(chainId=chain_id),
        """update v2_charge_sheet set query_exception_type = 0 where chain_id = '{chainId}' and query_exception_type = 1 and is_deleted = 0 and created > '2025-03-01 00:00:00';""".format(chainId=chain_id)
    ]

    for sql in sqls:
        charge_db_client.execute(sql)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    flushData(args.region_name, args.chain_id)


if __name__ == '__main__':
    main()
