"""
删除medical-record repeat del
"""
import argparse
import os
import sys
from datetime import date, timedelta

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient


del_id_sql = '''
SELECT outpatient_sheet_id
FROM v2_outpatient_medical_record
where is_deleted = 0 and last_modified >= '2025-01-01' 
GROUP BY outpatient_sheet_id
HAVING COUNT(*) > 1
'''

medical_record_sql = '''
select id, outpatient_sheet_id, patient_id, patient_order_id
from v2_outpatient_medical_record
where outpatient_sheet_id = '{SheetId}'
  and is_deleted = 0;
'''



del_sql = '''
update v2_outpatient_medical_record
set is_deleted = 1
where id in ({ids});
'''

class UpdateData:
    def __init__(self, region_name, env):
        self.region_name = region_name
        self.ob_client = DBClient(self.region_name, 'ob', 'abc_cis_outpatient', env, True)
        self.outpatient_client = DBClient(self.region_name, 'abc_cis_outpatient', 'abc_cis_outpatient', env, True)
        self.beginTime = date.today() - timedelta(days=120)

    def run(self):
        attentionIds = []
        # 获取要删除的id_sql
        delIds = self.ob_client.fetchall(del_id_sql)

        if len(delIds) <= 0:
            return
        
        #循环查询delIds
        for delId in delIds:
            # 查询medical_record
            medicalRecords = self.outpatient_client.fetchall(medical_record_sql.format(SheetId=delId['outpatient_sheet_id']))

            if len(medicalRecords) <= 1:
                continue

            needDelIds = []
            keepIds = []
            for medicalRecord in medicalRecords:
                if medicalRecord['patient_id'] == '':
                    needDelIds.append(medicalRecord['id'])
                else:
                    keepIds.append(medicalRecord['id'])
            
            if len(needDelIds) <= 0 or len(keepIds) != 1:
                attentionIds.append(delId['outpatient_sheet_id'])
                continue
            
            # 删除
            print(del_sql.format(ids=','.join(f"'{id}'" for id in needDelIds)))
            self.outpatient_client.execute(del_sql.format(ids=','.join(f"'{id}'" for id in needDelIds)))
            print(f"Deleted {len(needDelIds)} duplicate records")
        print(attentionIds)

def main():
#     basic_db_client = DBClient('ShangHai', 'abc_cis_basic', 'abc_cis_basic', 'test', True)
#     chainSql = '''
#         select id
#         from organ
#         where node_type = 1;
#     '''
#     chains = basic_db_client.fetchall(chainSql)
#     for chain in chains:
#         updateData = UpdateData('ShangHai', chain['id'], 'test')
#         updateData.run()

    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    updateData = UpdateData(args.region_name, args.env)
    updateData.run()

    # region = 'ShangHai'
    # chain_id = '6a869c22abee4ffbaef3e527bbb70aeb'
    # updateData = UpdateData(region, chain_id, 'test')
    # updateData.run()


if __name__ == '__main__':
    main()
