#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import pandas as pd
from multizone.db import DBClient

def create_table(db_client):
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS changji_price_limit (
        id INT AUTO_INCREMENT PRIMARY KEY,
        registered_name VARCHAR(200) COMMENT '注册名',
        smallest_pack_num FLOAT COMMENT '最小包装数量',
        smallest_dosage_form_unit VARCHAR(50) COMMENT '最小剂型单位',
        smallest_pack_unit VARCHAR(50) COMMENT '最小包装单位',
        hilist_pric_uplmt_amt FLOAT COMMENT '限价金额',
        approved_code VARCHAR(50) COMMENT '批准文号',
        manufacture_name VARCHAR(200) COMMENT '生产企业名称',
        abc_match_count INT COMMENT 'ABC匹配药品数量',
        abc_median_price FLOAT COMMENT 'ABC价格中位值（大单位）',
        abc_clinic_name VARCHAR(200) COMMENT '价格中位值诊所名称'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    """
    db_client.execute(create_table_sql)

def insert_data(db_client, df):
    # 准备SQL模板
    insert_sql_template = "INSERT INTO changji_price_limit (registered_name, smallest_pack_num, smallest_dosage_form_unit, smallest_pack_unit, hilist_pric_uplmt_amt, approved_code, manufacture_name, abc_match_count, abc_median_price, abc_clinic_name) VALUES "
    
    # 每批处理的记录数
    batch_size = 200
    total_rows = len(df)
    
    # 批量处理数据
    for start_idx in range(0, total_rows, batch_size):
        end_idx = min(start_idx + batch_size, total_rows)
        batch_df = df.iloc[start_idx:end_idx]
        
        # 构建值列表
        values_parts = []
        for _, row in batch_df.iterrows():
            # 处理每个值，确保正确的SQL格式
            registered_name = "'" + str(row['registered_name']).replace("'", "''") + "'"
            smallest_pack_num = str(float(row['smallest_pack_num']) if pd.notna(row['smallest_pack_num']) else 0)
            smallest_dosage_form_unit = "'" + str(row['smallest_dosage_form_unit']).replace("'", "''") + "'"
            smallest_pack_unit = "'" + str(row['smallest_pack_unit']).replace("'", "''") + "'"
            hilist_pric_uplmt_amt = str(float(row['hilist_pric_uplmt_amt']) if pd.notna(row['hilist_pric_uplmt_amt']) else 0)
            # 去掉“国药准字”字符串
            approved_code_val = str(row['approved_code']).replace('国药准字', '').strip()
            approved_code = "'" + approved_code_val.replace("'", "''") + "'"
            manufacture_name = "'" + str(row['manufacture_name']).replace("'", "''") + "'"
            abc_match_count = str(int(row['ABC匹配药品数量']) if pd.notna(row['ABC匹配药品数量']) else 0)
            abc_median_price = str(float(row['ABC价格中位值（大单位）']) if pd.notna(row['ABC价格中位值（大单位）']) else 0)
            abc_clinic_name = "'" + str(row['价格中位值诊所名称']).replace("'", "''") + "'"
            
            # 组合成一条记录
            value_part = f"({registered_name}, {smallest_pack_num}, {smallest_dosage_form_unit}, {smallest_pack_unit}, {hilist_pric_uplmt_amt}, {approved_code}, {manufacture_name}, {abc_match_count}, {abc_median_price}, {abc_clinic_name})"
            values_parts.append(value_part)
        
        # 构建完整的SQL语句
        insert_sql = insert_sql_template + ",".join(values_parts)
        
        # 执行插入
        try:
            db_client.execute(insert_sql)
            print(f"已插入 {start_idx + len(batch_df)} / {total_rows} 条记录")
        except Exception as e:
            print(f"SQL语句: {insert_sql}")
            raise e
        print(f"已插入 {start_idx + len(batch_df)} / {total_rows} 条记录")

def update_abc_match_info(db_client):
    try:
        # 从 changji_price_limit 表读取数据
        select_sql = "SELECT id, smallest_pack_num, smallest_dosage_form_unit, smallest_pack_unit, approved_code FROM changji_price_limit"
        rows = db_client.fetchall(select_sql)
        
        total_rows = len(rows)
        print(f"开始处理{total_rows}条记录")
        
        # 逐行处理
        for i, row in enumerate(rows, 1):
            # 构建查询SQL
            match_sql = f"""
            SELECT 
                COUNT(DISTINCT g.id) as match_count,
                AVG(g.package_price) as median_price,
                (SELECT organ_id 
                 FROM v2_goods 
                 WHERE piece_num = {row['smallest_pack_num']} 
                 AND piece_unit = '{row['smallest_dosage_form_unit']}' 
                 AND package_unit = '{row['smallest_pack_unit']}' 
                 AND medicine_nmpn LIKE '%{row['approved_code']}%' 
                 ORDER BY ABS(package_price - 
                    (SELECT AVG(package_price) 
                     FROM v2_goods 
                     WHERE piece_num = {row['smallest_pack_num']} 
                     AND piece_unit = '{row['smallest_dosage_form_unit']}' 
                     AND package_unit = '{row['smallest_pack_unit']}' 
                     AND medicine_nmpn LIKE '%{row['approved_code']}%'))
                 LIMIT 1) as median_clinic_id
            FROM v2_goods g
            WHERE g.piece_num = {row['smallest_pack_num']}
            AND g.piece_unit = '{row['smallest_dosage_form_unit']}'
            AND g.package_unit = '{row['smallest_pack_unit']}'
            AND g.medicine_nmpn LIKE '%{row['approved_code']}%'
            """
            
            # 执行查询
            match_result = db_client.fetchall(match_sql)
            
            if match_result and len(match_result) > 0:
                match_data = match_result[0]
                # 更新记录
                update_sql = f"""
                UPDATE changji_price_limit 
                SET abc_match_count = {match_data['match_count'] or 0},
                    abc_median_price = {match_data['median_price'] or 0},
                    abc_clinic_name = '{match_data['median_clinic_id'] or ''}'
                WHERE id = {row['id']}
                """
                db_client.execute(update_sql)
            
            if i % 100 == 0:
                print(f"已处理 {i} / {total_rows} 条记录")
        
        print("药品匹配信息更新完成！")
        
    except Exception as e:
        print(f"更新药品匹配信息时出错：{str(e)}")
        raise e

def main():
    try:
        # 创建数据库连接
        db_client = DBClient('ShangHai', 'test', 'abc_cis_goods', 'test', False)
        
        # 创建表
        create_table(db_client)
        print("数据库表创建成功！")
        
        # 读取Excel文件
        df = pd.read_excel('/Users/<USER>/AbcClinic/PreGrayDataMigrate/scripts/pureFlushData/新疆昌吉目录限价.xlsx')
        print(f"成功读取Excel文件，共{len(df)}行数据")
        
        # 数据清洗和转换
        df = df.fillna(0)  # 将NaN值替换为0
        
        # 插入数据
        insert_data(db_client, df)
        print("数据导入成功！")
        
        # 更新药品匹配信息
        update_abc_match_info(db_client)
        print("数据更新完成！")
        
    except Exception as e:
        print(f"发生错误: {str(e)}")

if __name__ == "__main__":
    main()
