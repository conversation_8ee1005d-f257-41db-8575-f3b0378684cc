"""
刷新formIsDeleted 的数据
"""
import os
import sys
import argparse

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.db import DBClient


class UpdateData:
    chain_id = None
    short_url_db_client = None
    emr_db_client = None
    scform_db_client = None
    basic_db_client = None
    id_work = None
    patient_tag_template_chain_id = None
    type = None
    businessType = None

    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_outpatient', 'abc_cis_outpatient', 'prod', True)

    def run(self):
        self.updateFormItem()

    def updateFormItem(self):
        try:
            sqls = [
                '''
                    update v2_outpatient_product_form_item a
                        inner join v2_outpatient_product_form b on a.product_form_id = b.id
                    set a.is_deleted = 1
                    where a.is_deleted = 0
                      and b.is_deleted = 1
                      and b.chain_id = '{chainId}'
                '''
                ,
                '''
                    update v2_outpatient_prescription_form_item a
                        inner join v2_outpatient_prescription_form b on a.prescription_form_id = b.id
                    set a.is_deleted = 1
                    where a.is_deleted = 0
                      and b.is_deleted = 1
                      and b.chain_id = '{chainId}'
                '''
            ]
            for sql in sqls:
                self.basic_db_client.execute(sql.format(chainId=self.chain_id))
        except Exception as e:
            print(e)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    updateData = UpdateData(args.region_name, args.chain_id)
    updateData.run()

    # region = 'ShangHai'
    # chain_id = '19e12c3a4a094e9f8dfbfc176378802a'
    # updateData = UpdateData(region, chain_id)
    # updateData.run()

if __name__ == '__main__':
    main()
