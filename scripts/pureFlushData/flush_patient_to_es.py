#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import db
import argparse
import logging
import es
import json
import time

import sys

reload(sys)
sys.setdefaultencoding('utf-8')

logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)

class LogStashFlushData(object):

    def __init__(self):
        self._db_client_cache = {}

    def _get_db_client(self, cluster, database):
        k = '{0}-{1}'.format(cluster, database)
        if k in self._db_client_cache.keys():
            return self._db_client_cache[k]

        db_client = db.DBClient(cluster, database)
        self._db_client_cache[k] = db_client
        return db_client

    # 查patientOrder信息
    def loadPatientClinicMap(self, patientClinicMap, chain_id, patientIdSql):
        patientClinicIdSql = '''
                      select patient_id as patientId, clinic_id as clinicId
                      from abc_cis_patient.v2_patient_clinic
                      where chain_id = '{chainId}'
                        and patient_id in ({patientIdSql})
            '''
        patientclinic_db_client = self._get_db_client('adb', 'abc_cis_patient')
        patientClinicList = patientclinic_db_client.fetchall(patientClinicIdSql.format(chainId=chain_id, patientIdSql=patientIdSql))
        if patientClinicList is None or len(patientClinicList) == 0:
            return patientClinicMap
        for patientClinic in patientClinicList:
            if patientClinic["clinicId"] is not None:
                clinicIds = patientClinicMap.get(patientClinic['patientId'])
                if clinicIds is None:
                    patientClinicMap[patientClinic['patientId']] = []
                patientClinicMap.get(patientClinic['patientId']).append(patientClinic["clinicId"])
        return patientClinicMap

    # 查询输出到es的主表的sql
    def loadMainTableObjects(self, chain_id, offset, limit):
        patientSql = '''
                  select
                        p.id                                                 as id,
                        p.chain_id                                           as chainId,
                        p.name                                               as name,
                        p.sex                                                as sex,
                        p.mobile_cipher                                      as mobile,
                        p.mobile_last4                                       as mobileLast4,
                        p.id_card_cipher                                     as idCard,
                        p.id_card_last6                                      as idCardLast6,
                        p.is_member                                          as isMember,
                        p.birthday                                           as birthday,
                        p.last_outpatient_clinic_id                          as activeClinicId,
                        date_format(p.last_outpatient_date, '%Y-%m-%d %T')   as activeDate,
                        o.name                                               as clinicName,
                        p.sn                                                 as sn,
                        p.status                                             as status
                  from abc_cis_patient.v2_patient p
                          left join abc_cis_basic.organ o on o.id = p.last_outpatient_clinic_id
                          where p.chain_id = '{chainId}' and p.status = 1 
                          order by p.id
                          limit {offsetNum},{limitNum}
            '''
        reg_adb_client = self._get_db_client('adb', 'abc_cis_patient')
        recordList = reg_adb_client.fetchall(patientSql.format(chainId=chain_id, offsetNum=offset, limitNum=limit))
        return recordList

    def fillToEsObject(self, esItem, patientClinicMap):
        if patientClinicMap.get(esItem.get('id')) is not None:
            esItem['clinicIds'] = patientClinicMap.get(esItem.get('id'))
        return esItem

    # 不要捕获异常,异常了数据没刷成功可以继续刷
    def logStashFlushToEs(self, chain_id):
        sT = time.clock()
        esClient = es.ESClient('abc-search-prod')
        offset = 0
        LOOP_COUNT = 20000
        while True:
            recordList = self.loadMainTableObjects(chain_id, offset, LOOP_COUNT)
            if recordList is None or len(recordList) == 0:
                print("chainId :" + chain_id + "，loadSize = 0")
                break

            patientClinicMap = {}
            MAX_LEN = 2000
            listRun = [recordList[i:i + MAX_LEN] for i in range(0, len(recordList), MAX_LEN)]
            for list in listRun:
                patientIdSql = ','.join([''' '{0}' '''.format(record['id']) for record in list])
                patientClinicMap = self.loadPatientClinicMap(patientClinicMap, chain_id, patientIdSql)

            write_bulk = []
            for record in recordList:
                writeToEsObject = self.fillToEsObject(record, patientClinicMap)
                write_bulk.append({'index': {'_index': 'v3-cis-patient-prod', '_id': str(record['id'])}})
                write_bulk.append(json.dumps(writeToEsObject, ensure_ascii=False))
            dbTime = time.clock()
            esClient.bulkInsert('v3-cis-patient-prod', write_bulk)
            esTime = time.clock()
            print('...write to es success:' + str(len(recordList)) + ",db=" + str(dbTime - sT) + 'min,writeEs=' + str(
                esTime - dbTime))
            offset += LOOP_COUNT


def run(chain_id):
    rgt = LogStashFlushData()
    rgt.logStashFlushToEs(chain_id)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)


if __name__ == '__main__':
    main()
