"""
刷新formIsDeleted 的数据
"""
import argparse
import os
import sys
from datetime import datetime, timedelta

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()

    del_sql = "delete from v2_outpatient_sheet_draft WHERE created <= '2024-06-01' and is_deleted = 1 and chain_id = '{chain_id}'"

    outpatientClient = DBClient(args.region_name, 'abc_cis_outpatient', 'abc_cis_outpatient', args.env, True)
    #
    # start_date = datetime.strptime('2023-05-01', "%Y-%m-%d")
    # end_date = datetime.strptime('2024-06-01', "%Y-%m-%d")

    # allRows = 0
    # current_start_date = start_date
    # while current_start_date <= end_date:
    #     formatted_date = current_start_date.strftime("%Y-%m-%d")
    #     sql = del_sql.format(date=formatted_date, chain_id=args.chain_id)
    #     print(sql)
    #     rows = outpatientClient.execute(sql)
    #     allRows = allRows + rows
    #     current_start_date = current_start_date + timedelta(days=180)

    allRows = outpatientClient.execute(del_sql.format(chain_id=args.chain_id))
    print(f"Processing chainId: {args.chain_id}, 处理：{allRows}")
    # for single_date in (start_date + timedelta(days=n) for n in range((end_date - start_date).days + 1)):
    #     formatted_date = single_date.strftime("%Y-%m-%d")
    #     print(del_sql.format(date=formatted_date, chain_id=args.chain_id))
    #     rows = outpatientClient.execute(del_sql.format(date=formatted_date, chain_id=args.chain_id))
    #     print(f"Processing date: {formatted_date}, 处理：{rows}")


if __name__ == '__main__':
    main()
