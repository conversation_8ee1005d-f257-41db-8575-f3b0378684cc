#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import db
import argparse
import logging
import es

logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)

class LogStashFlushData(object):

    def __init__(self):
        self._db_client_cache = {}

    def _get_db_client(self, cluster, database):
        k = '{0}-{1}'.format(cluster, database)
        if k in self._db_client_cache.keys():
            return self._db_client_cache[k]

        db_client = db.DBClient(cluster, database)
        self._db_client_cache[k] = db_client
        return db_client

    def logStashFlushToEs(self, chain_id):
        try:
            odsClient = self._get_db_client('ods', 'abc_cis_charge')
            logStashCmds =[
                es.LogStashInfo(odsClient.db_config,'','abc-search-prod-normal',10000,'','v3-cis-cdss-charge-sheet','id',
                                 '''select a.id                                  as id,
                                    a.chain_id                            as chainId,
                                    a.clinic_id                           as clinicId,
                                    date_format(a.created, '%Y-%m-%d %T') as created,
                                    a.doctor_id                           as doctorId,
                                    IF(a.is_online,1,0)                           as isOnline,
                                    a.patient_id                          as patientId,
                                    a.patient_order_id                    as patientOrderId,
                                    a.type                                as type,
                                    a.execute_status                      as executeStatus,
                                    a.include_item_type                   as includeItemType,
                                    a.import_flag                         as importFlag,
                                    a.outpatient_status                   as outpatientStatus,
                                    a.is_draft                            as isDraft,
                                    IF(a.is_closed,1,0)                           as isClosed,
                                    IF(a.query_exception_type,1,0)                as queryExceptionType,
                                    IF(a.owed_status,1,0)                        as owedStatus,
                                    IF(a.check_status,1,0)                        as checkStatus,
                                    IF(a.send_to_patient_status,1,0)              as sendToPatientStatus,
                                    a.seller_id                           as sellerId,
                                    date_format(a.order_by_date, '%Y-%m-%d %T') as orderByDate,
                                    date_format(a.reserve_date, '%Y-%m-%d %T') as reserveDate,
                                    a.receivable_fee                      as receivableFee,
                                    if(a.is_deleted,1,0)                          as isDeleted,
                                    a.status                              as status,
                                    p.mobile_cipher                       as patientMobileCipher,
                                    p.mobile_last4                        as patientMobileLast4,
                                    p.id_card_cipher                      as patientIdCardCipher,
                                    p.id_card_last6                       as patientIdCardLast6,
                                    p.name                                as patientName,
                                    p.is_member                           as patientIsMember,
                                    p.sex                                 as patientSex,
                                    p.sn                                  as sn,
                                    po.no                                 as poNo,
                                    po.source                             as poSource
                                    from          {chargeDbName}.v2_charge_sheet as a
                                        left join {patientDbName}.v2_patient as p on p.id = a.patient_id and p.chain_id = a.chain_id
                                        left join {patientOrderDbName}.v2_patientorder as po on po.id = a.patient_order_id and po.clinic_id = a.clinic_id
                                    where a.chain_id ='{chainId}' 
                                 '''.format( chainId = chain_id,
                                            chargeDbName='abc_cis_charge',
                                            patientDbName ='abc_cis_patient',
                                            patientOrderDbName = 'abc_cis_patientorder'
                                            )
                                ),
                es.LogStashInfo(odsClient.db_config,'action => "update" ','abc-search-prod-normal',10000,'','v3-cis-cdss-charge-sheet','id',
                                '''                   select    charge_sheet_id as id   ,                              
                                                                group_concat(name)                  as medicineName
                                                        from {chargeDbName}.v2_charge_form_item 
                                                        where chain_id ='{chainId}' 
                                                        and source_item_type  not in(5,12,13,16)
                                                        group by charge_sheet_id
                                '''.format(chainId = chain_id,
                                        chargeDbName='abc_cis_charge',
                                        patientDbName ='abc_cis_patient',
                                        patientOrderDbName = 'abc_cis_patientorder'
                                        )
                                )

            ]
            for logStashCmd in logStashCmds:
                f = open("./logstash.l",'wr')
                f.write(logStashCmd.getLogStashSystemCommand())
                f.close()
                os.system('''logstash7 -f ./logstash.l''')
        except Exception as e:
            print(e)



    def execute_chain(self, chain_id):
        #先刷Goods数据
        self.logStashFlushToEs(chain_id)

def run(chain_id):
    rgt = LogStashFlushData()
    rgt.execute_chain(chain_id)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)


if __name__ == '__main__':
    main()
