#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
import json
import argparse
import logging

logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)

default_id = '00000000000000000000000000000000'
standard_chain_id_map = {
    'dev': {
        'template_chain_id': 'ffffffff000000000c5a1308069aa000'
    },
    'test': {
        'template_chain_id': 'ffffffff000000001cdb3b8007024000'
    },
    'prod': {
        'template_chain_id': 'ffffffff000000001f2d1f400a1e6000'
    }
}

rpc_config = {
    'local': {
        'host': 'dev.abczs.cn',
        'headers': {
            'abc-rpc': 'we-will-win'
        }
    },
    'dev': {
        'host': 'dev.rpc.abczs.cn',
    },
    'test': {
        'host': 'test.rpc.abczs.cn',
    },
    'prod': {
        'host': 'pre.rpc.abczs.cn',
    }
}

region_id_name_map = {
    '1': "ShangHai",
    '2': 'HangZhou'
}

f = open('./eye_inspection_value_adjust.sql', "w+")


def run(region_name, env):
    exam_dbcli = DBClient(region_name, 'ods', 'abc_cis_examination', env, False)
    rows = exam_dbcli.fetchall("""
    select examination_sheet_id,
       goods_id,
       groupId,
       json_arrayagg(CONCAT(inspectType, '_', id))                                                 as ids,
       json_arrayagg(CONCAT(inspectType, '_', if(value is null or value = 'null', 'none', value))) as `values`
from (
         select id,
                examination_sheet_id,
                goods_id,
                json_unquote(json_extract(examination_value, '$.value')) as value,
                json_extract(examination_value, '$.inspectType')         as inspectType,
                json_extract(examination_value, '$.groupId')             as groupId
         from v2_examination_sheet_result
         where examination_value is not null
           and json_extract(examination_value, '$.inspectType') != cast('null' as json)
           and json_extract(examination_value, '$.inspectType') != cast('2' as json)
     ) as t
group by examination_sheet_id, goods_id, groupId;
    """)
    for row in rows:
        ids = json.loads(row['ids'])
        values = json.loads(row['values'])
        inspect_type_id_map = {}
        inspect_type_value_map = {}
        for id in ids:
            inspect_type_id = id.split('_')
            inspect_type_id_map[inspect_type_id[0]] = inspect_type_id[1]
        for value in values:
            inspect_type_value = value.split('_')
            inspect_type_value_map[inspect_type_value[0]] = inspect_type_value[1]
        if inspect_type_value_map['0'] == inspect_type_value_map['1']:
            print('left == right')
            continue
        for inspect_type in ['0', '1']:
            if inspect_type == '0':
                fix_value = inspect_type_value_map['1']
            else:
                fix_value = inspect_type_value_map['0']

            if fix_value == 'none' or fix_value == 'null':
                fix_value = None
            sql = f"""
                    update v2_examination_sheet_result set examination_value = json_set(examination_value, '$.value', {f"'{fix_value}'" if fix_value is not None else 'null'}) where id = '{inspect_type_id_map[inspect_type]}';
                                    """
            # exam_dbcli.execute(sql)
            f.write(sql + os.linesep)


def main():
    # parser = argparse.ArgumentParser()
    # parser.add_argument('--env', help='环境 dev/test/prod')
    # parser.add_argument('--chain-id', help='连锁id')
    # args = parser.parse_args()
    # if not args.env or not args.chain_id or not args.clinic_id or not args.device_model_id:
    #     parser.print_help()
    #     sys.exit(-1)
    #
    # run(args.chain_id, args.env)
    run('Master', 'prod')


if __name__ == '__main__':
    main()
