#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.rpc import regionRpcHost
import json
import argparse
import requests


# 多库房把以前老的药房类型刷到新的标上
def flushData(abcRegion, chain_id):
    charge_db_client = DBClient(abcRegion, 'abc_cis_charge', 'abc_cis_charge', 'prod', True)
    charge_record_db_client = DBClient(abcRegion, 'abc_cis_charge_record', 'abc_cis_charge_record', 'prod', True)
    ob_client = DBClient(abcRegion, 'ob', 'abc_cis_charge', 'prod', True)

    updateRecordParentSqls = ob_client.fetchall("""select /*+ parallel(4) */
     concat('update v2_charge_transaction_record set total_cost_price = ', sum(b.cost_price), ' where id = ''', c.compose_parent_record_id, ''' and scene_type = 0;') as updateSql
from abc_cis_registration.v2_registration_form_item_fee_detail b
         inner join abc_cis_charge.v2_charge_form_item a on a.source_form_item_id = b.id and b.cost_price > 0 and b.is_deleted = 0
    and a.unit_cost_price != b.cost_price
    and a.created >= '2024-09-01 00:00:00'
    and a.created < '2024-10-26 00:00:00'
         inner join abc_cis_charge_record.v2_charge_transaction_record c
                    on a.id = c.charge_form_item_id and c.scene_type = 0
where a.created >= '2024-09-01 00:00:00'
  and a.created < '2024-10-26 00:00:00'
  and a.total_cost_price = 0
  and a.status != 0
  and a.chain_id = '{chainId}'
group by c.compose_parent_record_id""".format(
        chainId=chain_id))

    if (len(updateRecordParentSqls) != 0):
        for row2 in updateRecordParentSqls:
            sql = row2['updateSql']
            charge_db_client.execute(sql)
            charge_record_db_client.execute(sql)


    updateRecordNormalSqls = ob_client.fetchall("""select /*+ parallel(4) */
    concat('update v2_charge_transaction_record set total_cost_price = ', b.cost_price, ' where id = ''', c.id,
               ''' and scene_type = 0;') as updateSql
from abc_cis_registration.v2_registration_form_item_fee_detail b
         inner join v2_charge_form_item a on a.source_form_item_id = b.id and b.cost_price > 0 and b.is_deleted = 0
    and a.unit_cost_price != b.cost_price
    and a.created > '2024-09-01 00:00:00'
    and a.created < '2024-10-26 00:00:00'
         inner join abc_cis_charge_record.v2_charge_transaction_record c
                    on a.id = c.charge_form_item_id and c.scene_type = 0
where a.created > '2024-09-01 00:00:00'
  and a.created < '2024-10-26 00:00:00'
  and a.total_cost_price = 0
  and a.status != 0
  and a.chain_id = '{chainId}'""".format(
        chainId=chain_id))

    if (len(updateRecordNormalSqls) != 0):
        for row2 in updateRecordNormalSqls:
            sql = row2['updateSql']
            charge_db_client.execute(sql)
            charge_record_db_client.execute(sql)


    updateItemSqls = ob_client.fetchall("""select /*+ parallel(4) */
        concat('update v2_charge_form_item set unit_cost_price = ', b.cost_price,
                                     ', total_cost_price = ', b.cost_price,
                                     ' where id = ''', a.id, ''';') as updateSql
    from abc_cis_registration.v2_registration_form_item_fee_detail b
             inner join abc_cis_charge.v2_charge_form_item a on a.source_form_item_id = b.id and b.cost_price > 0 and b.is_deleted = 0 and
                                                 a.unit_cost_price != b.cost_price
    where a.created > '2024-09-01 00:00:00'
      and a.created < '2024-10-26 00:00:00'
      and a.total_cost_price = 0
      and a.status != 0 and a.chain_id = '{chainId}'""".format(
        chainId=chain_id))

    if (len(updateItemSqls) != 0):
        for row2 in updateItemSqls:
            charge_db_client.execute(row2['updateSql'])


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    flushData(args.region_name, args.chain_id)


if __name__ == '__main__':
    main()
