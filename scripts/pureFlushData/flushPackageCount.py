import os
import sys
import argparse
import pandas as pd
import json

import logging

logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)
import pymysql

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient


class ADBService:
    def __init__(self, _env):
        if _env == 'prod':
            self.conn = pymysql.connect(host='am-uf6ek001hu80idf1y90650.ads.aliyuncs.com',
                                        user='adb_cis_r_user',
                                        passwd='TU7URfJgERM6A50g',
                                        db='abc_cis_goods_log',
                                        charset='UTF8')
        elif _env == 'test':
            self.conn = pymysql.connect(host='pc-uf6p03ez7eoy4n103.mysql.polardb.rds.aliyuncs.com',
                                        user='m_test_stat',
                                        passwd='3a710cc673f2a3535a22960182F59588',
                                        db='abc_cis_goods_test',
                                        charset='UTF8')
        else:
            self.conn = pymysql.connect(host='for-aws-dev-test.mysql.polardb.rds.aliyuncs.com',
                                        user='h_dev_stat',
                                        passwd='04f29851eee894d9cf2962d54Ff41b50',
                                        db='abc_cis_goods_dev',
                                        charset='UTF8')
        self.conn.autocommit(1)
        self.cursor = self.conn.cursor()

    def execute(self, sql):
        self.cursor.execute(sql)
        return self.cursor.fetchall()

    def execute_fetch_one(self, sql):
        self.cursor.execute(sql)
        return self.cursor.fetchone()

    def commit(self):
        self.conn.commit()

    def close(self):
        self.cursor.close()
        self.conn.close()


class SnapshotDataRecover:
    chain_id = None
    basic_db_client = None

    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_stock_zip', 'abc_cis_goods_log', 'prod', True)
        self.ods_db_client = DBClient(self.region_name, 'ods', 'abc_cis_goods_log', 'prod', True)
        self.adb = ADBService('prod')
        self.end_date = '2022-05-01 00:00:00'

    def run(self):
        self.updateModfifyGoodsStockIn()



    def updateModfifyGoodsStockIn(self):
        sql = '''select s.id, s.goods_snap
                    from abc_cis_goods_log.v2_goods_stock_log_goods_snapshot as s
                    inner join abc_cis_goods_log.v2_goods_stock_log as l on l.id = s.id
                    where l.action = '药品规格修改'
                and l.chain_id = '{chainId}';
        '''.format(chainId=self.chain_id)
        # 遍历查询结果
        exam_update_sql_res = self.ods_db_client.fetchall(sql)
        for (id, goods_snap) in exam_update_sql_res:
            # 将 JSON 字符串解析为 Python 对象
            data = json.loads(goods_snap)
            pharmacyCount ={}
            changed = 0
            # 提取 stocks 数组并计算 pieceCount 之和
            for stock in data['stocks']:
                if pharmacyCount[stock['pharmacyNo']+"pi"] is None :
                    pharmacyCount[stock['pharmacyNo']+"pi"] = 0
                pharmacyCount[stock['pharmacyNo']+"pi"] = pharmacyCount[stock['pharmacyNo']+"pi"] + stock['pieceCount']
                if pharmacyCount[stock['pharmacyNo']+"pk"] is None :
                    pharmacyCount[stock['pharmacyNo']+"pk"] = 0
                pharmacyCount[stock['pharmacyNo']+"pk"] = pharmacyCount[stock['pharmacyNo']+"pk"] + stock['packageCount']

                if pharmacyCount["-1pi"] is None :
                    pharmacyCount["-1pi"] = 0
                pharmacyCount["-1pi"] = pharmacyCount["-1pi"] + stock['pieceCount']
                if pharmacyCount["-1pk"] is None :
                    pharmacyCount["-1pk"] = 0
                pharmacyCount["-1pk"] = pharmacyCount["-1pk"] + stock['packageCount']

            pieceNum = data["additional"]["pieceNum"]
            if pharmacyCount["-1pi"] > 0 and pharmacyCount["-1pi"] >= pieceNum :
                changed = 1
                data['stat']['pieceCount'] = pharmacyCount["-1pi"] % pieceNum
                data['stat']['packageCount'] = data['stat']['packageCount'] +pharmacyCount["-1pi"] // pieceNum

            for stat in data['pharmacyStats']:
                if pharmacyCount[stat['pharmacyNo']+"pi"] > 0 and pharmacyCount[stat['pharmacyNo']+"pi"] >= pieceNum :
                    changed = 1
                    stat['pieceCount'] = pharmacyCount[stat['pharmacyNo']+"pi"] % pieceNum
                    stat['packageCount'] = stat['packageCount'] + pharmacyCount[stat['pharmacyNo']+"pi"]  // pieceNum
            if changed == 1:
                updateSql = """update v2_goods_stock_log set goods_snap='{goodsSnap}' where id ={id} """.format(goodsSnap =json.dumps(data),id=id)
                logging.info("?updateSql:{}",updateSql,json.loads(goods_snap))


def main():
    params = argparse.ArgumentParser()
    params.add_argument('--chain-id', help='连锁id')
    params.add_argument('--region-name', help='分区名字可直接查配置')
    args = params.parse_args()
    if not args.chain_id:
        params.print_help()
        sys.exit(-1)

    snapshot = SnapshotDataRecover(args.region_name, args.chain_id)
    snapshot.run()


if __name__ == '__main__':
    main()
