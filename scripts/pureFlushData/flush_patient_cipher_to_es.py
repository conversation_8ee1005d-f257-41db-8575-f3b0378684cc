#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import db
import argparse
import logging
import es
import json

import sys
reload(sys)
sys.setdefaultencoding('utf-8')

logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)

class LogStashFlushData(object):

    def __init__(self):
        self._db_client_cache = {}

    def _get_db_client(self, cluster, database):
        k = '{0}-{1}'.format(cluster, database)
        if k in self._db_client_cache.keys():
            return self._db_client_cache[k]

        db_client = db.DBClient(cluster, database)
        self._db_client_cache[k] = db_client
        return db_client

    #更新现有es文档里面的值
    def logStashFlushToEs(self, chain_id):
        flushCipherDataToEsSql = '''
            select id as id,
                    mobile_last4 as mobileLast4,
                    mobile_cipher as mobile,
                    id_card_cipher as idCard,
                    id_card_last6 as idCardLast6
            from v2_patient where chain_id = '{chainId}' 
            order by id
            limit {offsetNum},{limitNum}
        '''
        reg_adb_client = self._get_db_client('adb', 'abc_cis_patient')

        esClient = es.ESClient('abc-search-prod-normal')
        offset = 0
        LOOP_COUNT = 1000
        while True:
            patientList = reg_adb_client.fetchall(flushCipherDataToEsSql.format(chainId = chain_id, offsetNum=offset, limitNum=LOOP_COUNT))
            if patientList is None or len(patientList) <= 0:
                break
            write_bulk  = []
            for patient in patientList:
                #这里是update
                write_bulk.append({'update': {'_index': 'v3-cis-crm-patient-prod' , '_id': str(patient['id'])}})
                #更新ES的 结构
                docUpdate ={"doc":{}}
                docUpdate["doc"]["mobileLast4"] = patient["mobileLast4"]
                docUpdate["doc"]["mobile"] = patient["mobile"]
                docUpdate["doc"]["idCard"] = patient["idCard"]
                docUpdate["doc"]["idCardLast6"] = patient["idCardLast6"]
                write_bulk.append(json.dumps(docUpdate, ensure_ascii=False))
            if patientList is not None and len(patientList) > 0:
                try:
                    esClient.bulkInsert('v3-cis-crm-patient-prod',write_bulk)
                except Exception:
                    pass
            offset += LOOP_COUNT
    def execute_chain(self, chain_id):
        self.logStashFlushToEs(chain_id)

def run(chain_id):
    rgt = LogStashFlushData()
    rgt.execute_chain(chain_id)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)

if __name__ == '__main__':
    main()
