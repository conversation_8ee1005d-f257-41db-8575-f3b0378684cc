# -*- coding: utf-8 -*-
"""
同批次多stock刷origin_flat_price字段
@name: flush_batch_multi_stock_origin_flat_price.py
@author: <PERSON><PERSON><PERSON>
@email: <EMAIL>
@date: 2024-12-10 19:37:24
"""
import argparse
import os
import sys
from datetime import datetime, timed<PERSON>ta
from decimal import Decimal, ROUND_HALF_UP

from anyio import sleep

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.db import DBClient

class UpdateData:
    chain_id = None
    goods_ob_client = None

    def __init__(self, region_name, chain_id, env):
        self.chain_id = chain_id
        self.region_name = region_name
        env = 'prod' if not env else env
        self.goods_ob_client = DBClient(self.region_name, 'ob', 'abc_cis_goods_log', env, True)
        self.goods_stock_ob_client = DBClient(self.region_name, 'ob', 'abc_cis_goods', env, True)
        self.goods_write_client = DBClient(self.region_name, 'abc_cis_stock_zip', 'abc_cis_goods_log', env, True)
        # self.goods_write_client = DBClient(self.region_name, 'abc_cis_stock', 'abc_cis_goods', env, True)

    def run(self):
        chain_list = []
        if self.chain_id:
            chain = {
                'chain_id': self.chain_id
            }
            chain_list.append(chain)
        else:
            chain_list = self.goods_stock_ob_client.fetchall("""
                select distinct chain_id from v2_goods_stock group by chain_id, organ_id, batch_id having count(1) > 1;
            """)
        if not chain_list:
            return
        print("chain count=", len(chain_list))
        for chain_id in chain_list:
            # 查询同批次多stock log
            goods_log_group_list = self.goods_ob_client.fetchall("""
                select chain_id, organ_id, goods_id, batch_id, order_id, order_detail_id, bat_id from v2_goods_stock_log
                where chain_id = '{chainId}' and action = '发药' and origin_flat_price is not null and origin_flat_price != 0
                and created_date > '2024-07-01 00:00:00'
                group by organ_id, goods_id, batch_id, bat_id, order_id, order_detail_id
                having count(1) > 1 AND MIN(origin_flat_price) = MAX(origin_flat_price);
            """.format(chainId=chain_id['chain_id']))

            if not goods_log_group_list:
                print('无数据刷，chainId=', chain_id['chain_id'])
                continue

            for goods_log in goods_log_group_list:
                # print(goods_log)
                # 查询log
                flush_goods_log_list = self.goods_ob_client.fetchall("""
                    select * from v2_goods_stock_log
                    where chain_id = '{chainId}' and organ_id = '{clinicId}' and goods_id = '{goodsId}'
                    and order_id = '{orderId}'
                    and batch_id = {batchId} and bat_id = '{batId}'
                    and action = '发药';
                """.format(chainId=chain_id['chain_id'], clinicId=goods_log['organ_id'], goodsId=goods_log['goods_id'],
                           orderId=goods_log['order_id'], batchId=goods_log['batch_id'], batId=goods_log['bat_id']))
                if flush_goods_log_list is None or len(flush_goods_log_list) == 1:
                    continue

                # 是否需要刷
                origin_flat_price = None
                same_origin_flat_price = False
                for flush_goods_log in flush_goods_log_list:
                    # print(flush_goods_log)
                    if origin_flat_price is None:
                        origin_flat_price = flush_goods_log['origin_flat_price']
                    else:
                        if origin_flat_price == flush_goods_log['origin_flat_price']:
                            same_origin_flat_price = True
                        else:
                            same_origin_flat_price = False
                if not same_origin_flat_price:
                    print('origin_flat_price金额不一致，不需要刷,', 'goodsId='f"{goods_log['goods_id']}", ',batchId='f"{goods_log['batch_id']}",
                          ',clinicId='f"{goods_log['organ_id']}", ',batId='f"{goods_log['bat_id']}")
                    continue
                # 总数量
                total_count = abs(sum([goods_log['piece_count'] + goods_log['package_count'] * Decimal(goods_log['piece_num']) for goods_log in flush_goods_log_list]))
                # print(total_count, origin_flat_price)
                left_price = origin_flat_price
                for flush_goods_log in flush_goods_log_list:
                    # 按数量占比摊
                    flat_price = ((abs(flush_goods_log['piece_count'] + flush_goods_log['package_count'] * Decimal(flush_goods_log['piece_num'])) * origin_flat_price / total_count)
                                  .quantize(Decimal('0.00'), rounding=ROUND_HALF_UP))
                    flat_price = min(flat_price, left_price)
                    flush_goods_log['origin_flat_price'] = flat_price
                    left_price -= flat_price
                # 将剩余的价格加到最后一个商品上
                flush_goods_log_list[-1]['origin_flat_price'] += left_price
                for flush_goods_log in flush_goods_log_list:
                    sql = """
                    update v2_goods_stock_log set origin_flat_price = {origin_flat_price} where id = {id};
                    """.format(origin_flat_price=flush_goods_log['origin_flat_price'], id=flush_goods_log['id'])
                    print(sql)
                    # self.goods_write_client.execute(sql)

            # sleep(0.5)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    # if not args.chain_id:
    #     parser.print_help()
    #     sys.exit(-1)

    updateData = UpdateData(args.region_name, args.chain_id, 'prod')
    updateData.run()


if __name__ == '__main__':
    main()
