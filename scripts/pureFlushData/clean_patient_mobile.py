#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""
清理患者手机号和身份证号
"""
import os
from datetime import datetime

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))

import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.db import DBClient
from scripts.common.utils.lists import ListUtils
from scripts.common.utils.sqls import SqlUtils
import argparse


class UpdateExecutor(object):
    chain_id = None
    patient_ob_client = None
    patient_write_client = None
    basic_ob_client = None
    env = None
    region_name = None

    def __init__(self, env, region_name, chain_id):
        self.env = env
        self.chain_id = chain_id
        self.region_name = region_name
        self.patient_ob_client = DBClient(self.region_name, 'ob', 'abc_cis_patient', self.env, True)
        self.basic_ob_client = DBClient(self.region_name, 'ob', 'abc_cis_basic', self.env, True)
        self.patient_write_client = DBClient(self.region_name, 'abc_cis_account_base', 'abc_cis_patient', self.env, True)

    def run(self):
        organ = self.basic_ob_client.fetchone("""
            select id, created_date
            from organ
            where id = '{chainId}'
        """.format(chainId=self.chain_id))

        if not organ:
            print(f"chain {self.chain_id} not exist")
            return

        # 如果门店的 created_date 不等于 null，并且创建时间大于 2024-01-01，就不执行
        if organ['created_date'] and organ['created_date'] > datetime.strptime('2024-01-01', '%Y-%m-%d'):
            print(f"chain {self.chain_id} created_date is {organ['created_date']}, skip")
            return

        cursor = "00000000000000000000000000000000"
        total_count = 0
        while True:
            patients = self.patient_ob_client.fetchall("""
                select id
                from v2_patient
                where chain_id = '{chainId}'
                  and id > '{cursor}'
                  and ((mobile is not null and length(mobile) > 4) or (id_card is not null and length(id_card) > 6))
                order by id
                limit 100
            """.format(chainId=self.chain_id, cursor=cursor))

            if not patients:
                break

            cursor = patients[-1]['id']
            total_count += len(patients)

            patient_ids = ListUtils.dist_mapping(patients, lambda patient: patient['id'])
            self.patient_write_client.execute("""
                update v2_patient
                set mobile  = RIGHT(mobile, 4),
                    id_card = RIGHT(id_card, 6)
                where id in ({ids});
            """.format(ids=SqlUtils.to_in_value(patient_ids)))

        print(f"chain {self.chain_id} total count: {total_count}")


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    try:
        executor = UpdateExecutor(args.env, args.region_name, args.chain_id)
        executor.run()
    except Exception as e:
        print(f'清理连锁 {args.chain_id} 患者明文手机号和身份证号失败', e)
        raise e


if __name__ == '__main__':
    main()
