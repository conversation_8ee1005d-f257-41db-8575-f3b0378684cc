#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import db
import argparse
import logging
import es
import json
import time

import datetime
from dateutil.relativedelta import relativedelta

import sys

reload(sys)
sys.setdefaultencoding('utf-8')

logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)


class LogStashFlushData(object):

    def __init__(self):
        self._db_client_cache = {}

    def _get_db_client(self, cluster, database):
        k = '{0}-{1}'.format(cluster, database)
        if k in self._db_client_cache.keys():
            return self._db_client_cache[k]

        db_client = db.DBClient(cluster, database)
        self._db_client_cache[k] = db_client
        return db_client

    # 查patientOrder信息
    def loadPatientOrderMap(self, patientOrderIdToPatientOrder, chain_id, patientOrderIdSql):
        patientOrderSql = '''
                select   po.no                                 as poNo,
                         po.id                                 as patientOrderId,
                         po.source                             as poSource
                from    v2_patientorder as po 
                where po.chain_id = '{chainId}' and po.id in ({patientOrderIdSql}) 
            '''
        patientorder_db_client = self._get_db_client('adb', 'abc_cis_patientorder')
        patientOrderList = patientorder_db_client.fetchall(
            patientOrderSql.format(chainId=chain_id, patientOrderIdSql=patientOrderIdSql))
        if patientOrderList is None or len(patientOrderList) == 0:
            return patientOrderIdToPatientOrder
        for patientOrder in patientOrderList:
            patientOrderIdToPatientOrder[patientOrder['patientOrderId']] = patientOrder
        return patientOrderIdToPatientOrder

    # 查patientOrder信息
    def loadDispensingSheetMedMap(self, sheetMedMap, chainId, clinicId, dispensingSheetIdSql):
        patientClinicIdSql = '''
        select
          id    as id,
          dispensing_sheet_id    as dispensingSheetId,
          name  as name
      from abc_cis_dispensing.v2_dispensing_form_item
      where clinic_id = '{clinicId}'
        and chain_id = '{chainId}'
        and dispensing_sheet_id in ({dispensingSheetIdSql})
        and is_deleted = 0
            '''
        dis_adb_client = self._get_db_client('adb', 'abc_cis_patient')
        sheetItemMedList = dis_adb_client.fetchall(
            patientClinicIdSql.format(clinicId=clinicId, chainId=chainId, dispensingSheetIdSql=dispensingSheetIdSql))
        if sheetItemMedList is None or len(sheetItemMedList) == 0:
            return sheetMedMap
        for sheetItem in sheetItemMedList:
            if sheetItem["dispensingSheetId"] is not None:
                sheet = sheetMedMap.get(sheetItem['dispensingSheetId'])
                if sheet is None:
                    sheetMedMap[sheetItem['dispensingSheetId']] = []
                item = {}
                item["id"] = sheetItem["id"]
                item["name"] = sheetItem["name"]
                sheetMedMap.get(sheetItem['dispensingSheetId']).append(item)
        return sheetMedMap

    # 取药单
    def loadDispenseOrder(self, chain_id, sheetOrderMap):
        mainTableSql = ''' 
               select  dispense_sheet_id as dispensingSheetId ,max(order_id) as dispenseOrderId
                from abc_cis_dispensing.v2_dispensing_order_rel_sheet where chain_id ='{chainId}'
                group by  dispense_sheet_id
            '''
        dis_adb_client = self._get_db_client('adb', 'abc_cis_dispensing')
        recordList = dis_adb_client.fetchall(mainTableSql.format(chainId=chain_id))
        if recordList is None or len(recordList) == 0:
            return sheetOrderMap
        for sheetItem in recordList:
            if sheetItem["dispensingSheetId"] is not None:
                sheetOrderMap[sheetItem['dispensingSheetId']] = sheetItem['dispenseOrderId']
        return sheetOrderMap

   #     IF(a.dispensing_tag & 0x01,1,0)              as dispensingTagBit0,
   #     IF(a.dispensing_tag & 0x02,1,0)              as dispensingTagBit1,
   #     IF(a.dispensing_tag & 0x04,1,0)              as dispensingTagBit2,
   #     IF(a.dispensing_tag & 0x08,1,0)              as dispensingTagBit3,
   #     IF(a.dispensing_tag & 0x10,1,0)              as dispensingTagBit4,
   #     IF(a.dispensing_tag & 0x20,1,0)              as dispensingTagBit5,
   #     IF(a.dispensing_tag & 0x40,1,0)              as dispensingTagBit6,
   #     IF(a.dispensing_tag & 0x80,1,0)              as dispensingTagBit7,
    # 查询输出到es的主表的sql
    def loadMainTableObjects(self, chain_id, clinic_id, beginDate, endDate):
        mainTableSql = ''' 
                select a.id                                        as id,
                      a.dispensing_tag                             as dispensingTag
                from abc_cis_dispensing.v2_dispensing_sheet as a
                where a.clinic_id ='{clinicId}' 
                        and  a.chain_id = '{chainId}'
                        and a.is_deleted = 0
                      and a.created >=  '{beginDate}' and a.created <= '{endDate}'
            '''
        dis_adb_client = self._get_db_client('adb', 'abc_cis_dispensing')
        recordList = dis_adb_client.fetchall(
            mainTableSql.format(chainId=chain_id, clinicId=clinic_id, beginDate=beginDate, endDate=endDate))
        return recordList
    def loadDispensingSheetMedMap(self, sheetMedMap, chainId, clinicId, dispensingSheetIdSql):
        patientClinicIdSql = '''
        select
          dispensing_sheet_id    as dispensingSheetId,
                group_concat( name) as medicineName
      from abc_cis_dispensing.v2_dispensing_form_item
      where clinic_id = '{clinicId}'
        and chain_id = '{chainId}'
        and dispensing_sheet_id in ({dispensingSheetIdSql})
        and is_deleted = 0
        group by dispensing_sheet_id 
            '''
        dis_adb_client = self._get_db_client('adb', 'abc_cis_patient')
        sheetItemMedList = dis_adb_client.fetchall(
            patientClinicIdSql.format(clinicId=clinicId, chainId=chainId, dispensingSheetIdSql=dispensingSheetIdSql))
        if sheetItemMedList is None or len(sheetItemMedList) == 0:
            return sheetMedMap
        for sheetItem in sheetItemMedList:
            if sheetItem["dispensingSheetId"] is not None:
                sheetMedMap[sheetItem['dispensingSheetId']] = sheetItem["medicineName"]
        return sheetMedMap

    def fillToEsObject(self, esItem ,sheetMedMap):
        if esItem.get('dispensingTag') is not None and esItem.get('dispensingTag') != 0:
            esItem['dispensingTagBit0'] = esItem.get('dispensingTag') & 0x01 != 0 and 1 or 0
            esItem['dispensingTagBit1'] = esItem.get('dispensingTag') & 0x02 != 0 and 1 or 0
            esItem['dispensingTagBit2'] = esItem.get('dispensingTag') & 0x04 != 0 and 1 or 0
            esItem['dispensingTagBit3'] = esItem.get('dispensingTag') & 0x08 != 0 and 1 or 0
            esItem['dispensingTagBit4'] = esItem.get('dispensingTag') & 0x10 != 0 and 1 or 0
            esItem['dispensingTagBit5'] = esItem.get('dispensingTag') & 0x20 != 0 and 1 or 0
            esItem['dispensingTagBit6'] = esItem.get('dispensingTag') & 0x40 != 0 and 1 or 0
            esItem['dispensingTagBit7'] = esItem.get('dispensingTag') & 0x80 != 0 and 1 or 0
        else:
            esItem['dispensingTagBit0'] = 0
            esItem['dispensingTagBit1'] = 0
            esItem['dispensingTagBit2'] = 0
            esItem['dispensingTagBit3'] = 0
            esItem['dispensingTagBit4'] = 0
            esItem['dispensingTagBit5'] = 0
            esItem['dispensingTagBit6'] = 0
            esItem['dispensingTagBit7'] = 0
        if esItem.get('id') is not None and sheetMedMap.get( esItem.get('id')) is not None:
            esItem['medicineName'] = sheetMedMap.get( esItem.get('id'))

        return esItem

    # 不要捕获异常,异常了数据没刷成功可以继续刷
    def logStashFlushToEs(self, chain_id):
        # 倒退查 门店的最早创建时间
        selectMinCreatedSql = '''
            select  min(created)  as minCreated
            from    v2_dispensing_sheet as po 
            where po.clinic_id = '{clinicId}'   
        '''
        reg_adb_client = self._get_db_client('adb', 'abc_cis_dispensing')

        esClient = es.ESClient('abc-search-prod-normal')
        basic_db_client = self._get_db_client('abc_cis_mixed', 'abc_cis_basic')
        organs = basic_db_client.fetchall(
            ''' select id as clinicId from organ where parent_id = '{chainId}' and node_type = 2 '''.format(
                chainId=chain_id))
        for organ in organs:
            clinic_id = organ['clinicId']
            now_date = datetime.datetime.now()
            now_date = now_date + relativedelta(days=1)
            PER_MONTH = 1
            index = 0
            minCreatedResult = reg_adb_client.fetchone(selectMinCreatedSql.format(clinicId=clinic_id))
            if minCreatedResult:
                minCreatedDate = minCreatedResult['minCreated']
            if minCreatedDate is None:
                print("未找最小创建时间 退出")
                continue
            minYYYYMMDD = minCreatedDate.strftime('%Y-%m-%d')
            while index < 64:
                sT = time.clock()
                endDate = (now_date - relativedelta(months=index * PER_MONTH)).strftime('%Y-%m-%d')
                beginDate = (now_date - relativedelta(months=(index + 1) * PER_MONTH)).strftime('%Y-%m-%d')
                if minYYYYMMDD > str(endDate):
                    print(
                        "不需要再往前找了:clinicId:" + clinic_id + " minYYYYMMDD=" + minYYYYMMDD + ",beginDate=" + str(
                            beginDate) + "," + endDate)
                    break

                recordList = self.loadMainTableObjects(chain_id, clinic_id, beginDate, endDate)
                index += 1
                if recordList is None or len(recordList) == 0:
                    print("beginDate :" + beginDate + "  -> " + endDate + "，loadSize = 0")
                    continue
                print( "clinicId:" + clinic_id + ",minCreated:" + minYYYYMMDD + ",beginDate :" + beginDate + "  -> " + endDate + "，loadSize = " + str( len(recordList)))

                sheetMedMap = {}
                # ADB 虽然快 但是限制了2000个IN
                MAX_LEN = 2000
                listRun = [recordList[i:i + MAX_LEN] for i in range(0, len(recordList), MAX_LEN)]
                for list in listRun:
                    dispensingSheetIdSql = ','.join([''' '{0}' '''.format(record['id']) for record in list])
                    sheetMedMap = self.loadDispensingSheetMedMap(sheetMedMap, chain_id, clinic_id, dispensingSheetIdSql)
                write_bulk = []
                for record in recordList:
                    writeToEsObject = self.fillToEsObject(record,sheetMedMap)
                    #这里是update
                    write_bulk.append({'update': {'_index': 'v3-abc-cdss-dispense-sheet-prod' , '_id': str(writeToEsObject['id'])}})
                    #更新ES的 结构
                    docUpdate ={"doc":{}}
                    docUpdate["doc"]["dispensingTagBit0"] = writeToEsObject["dispensingTagBit0"]
                    docUpdate["doc"]["dispensingTagBit1"] = writeToEsObject["dispensingTagBit1"]
                    docUpdate["doc"]["dispensingTagBit2"] = writeToEsObject["dispensingTagBit2"]
                    docUpdate["doc"]["dispensingTagBit3"] = writeToEsObject["dispensingTagBit3"]
                    docUpdate["doc"]["dispensingTagBit4"] = writeToEsObject["dispensingTagBit4"]
                    docUpdate["doc"]["dispensingTagBit5"] = writeToEsObject["dispensingTagBit5"]
                    docUpdate["doc"]["dispensingTagBit6"] = writeToEsObject["dispensingTagBit6"]
                    docUpdate["doc"]["dispensingTagBit7"] = writeToEsObject["dispensingTagBit7"]
                    docUpdate["doc"]["medicineName"] = writeToEsObject["medicineName"]
                    write_bulk.append(json.dumps(docUpdate, ensure_ascii=False))
                dbTime = time.clock()
                if recordList is not None and len(recordList) > 0:
                    esClient.bulkInsert('v3-abc-cdss-dispense-sheet-prod',write_bulk)
                esTime = time.clock()
                print('...write to es success:' + str(len(recordList)) + ",db=" + str(
                    dbTime - sT) + 'min,writeEs=' + str(esTime - dbTime))


def run(chain_id):
    rgt = LogStashFlushData()
    rgt.logStashFlushToEs(chain_id)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)


if __name__ == '__main__':
    main()
