from scripts.common.general_update_json_fields import update_json
import argparse
import sys

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--region-name', help='分区名字')
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--env', help='env')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    if args.region_name == 'HangZhou':
        raise Exception(f'杭州分区先不刷: {args.chain_id}')
    if args.chain_id in ['29686c6e59c84b81a006e3f87628c48d', '98ab764c1a29469695c0699d80bd59f2', '06bcec85ba934c079d70d13410167730', 'ffffffff000000001cf00d2809f48000', 'da5fdd689b6e499198f7bbc187ce3341', '6a869c22abee4ffbaef3e527bbb70aeb', 'ffffffff00000000094c2a500293c000', '900ddae1bcb6401fb8e0453fe5ee3878', '3cd08f7ed32c418284d18b671ec980f8', 'ffffffff00000000105f9cf8062ec000', 'ffffffff000000001fdce8000a244000', 'cd615d1f12df442cac1f03abe27479e7', 'ffffffff0000000020280ba80a2e4000', 'ffffffff0000000003af1f2800b50000', 'ffffffff00000000347a2ed3221dc001', 'ffffffff0000000013ae004007214000']:
        raise Exception(f'超过500w收费项的连锁先不刷: {args.chain_id}')
    env = args.env#'prod'
    update_json(
        args.region_name,
        args.chain_id,
        'abc_cis_charge',
        'abc_cis_charge',
        'v2_charge_form',
        None,
        {'usage_info': ['doseCount', 'isDecoction', 'processBagUnitCount', 'checked', 'verifySignatureStatus']},
        env
    )
    update_json(
        args.region_name,
        args.chain_id,
        'abc_cis_charge',
        'abc_cis_charge',
        'v2_charge_form_item',
        None,
        {'usage_info': ['doseCount', 'isDecoction', 'processBagUnitCount', 'checked', 'verifySignatureStatus'],
         'promotion_info': ['isMarkedByHealthCardPay'],
         'additional': None},
        env)
    update_json(
        args.region_name,
        args.chain_id,
        'abc_cis_charge',
        'abc_cis_charge',
        'v2_charge_transaction_record_additional',
        None,
        {'discount_info': ['isMarkedByHealthCardPay']},
        env)
    update_json(
        args.region_name,
        args.chain_id,
        'abc_cis_charge',
        'abc_cis_charge',
        'v2_charge_form_item_batch_info',
        None,
        {'promotion_info': ['isMarkedByHealthCardPay']},
        env)
    update_json(
        args.region_name,
        args.chain_id,
        'abc_cis_outpatient',
        'abc_cis_outpatient',
        'v2_outpatient_medical_record',
        None,
        {'dentistry_extend': None},
        env)
