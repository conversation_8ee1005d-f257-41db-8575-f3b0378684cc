#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import db
import argparse
import logging
import es
import json
import time

import datetime
from dateutil.relativedelta import relativedelta

import sys
reload(sys)
sys.setdefaultencoding('utf-8')

logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)

class LogStashFlushData(object):

    def __init__(self):
        self._db_client_cache = {}

    def _get_db_client(self, cluster, database):
        k = '{0}-{1}'.format(cluster, database)
        if k in self._db_client_cache.keys():
            return self._db_client_cache[k]

        db_client = db.DBClient(cluster, database)
        self._db_client_cache[k] = db_client
        return db_client

    def logStashFlushToEs(self, chain_id):
        try:

            patientOrderSql ='''
                select   po.no                                 as poNo,
                         po.id                                 as patientOrderId,
                         po.source                             as poSource
                from    v2_patientorder as po 
                where po.chain_id = '{chainId}' and po.id in ({patientOrderIdSql}) 
            '''
            selectMinCreatedSql ='''
                select  min(last_modified)  as minCreated
                from    v2_charge_sheet as po 
                where po.clinic_id = '{clinicId}'   
            '''

            patientSql = '''
            select                  p.id,
                                    p.mobile_cipher                       as patientMobileCipher,
                                    p.mobile_last4                        as patientMobileLast4,
                                    p.id_card_cipher                      as patientIdCardCipher,
                                    p.id_card_last6                       as patientIdCardLast6,
                                    p.name                                as patientName,
                                    p.sn                                  as patientSn,
                                    p.is_member                           as patientIsMember,
                                    p.sex   as patientSex
            from v2_patient as p
            where p.chain_id ='{chainId}'
            limit {offset},{limit}
            
            '''
            medNameSQL = '''                   select    charge_sheet_id as id   ,                              
                                            group_concat(name)                  as medicineName
                                    from v2_charge_form_item 
                                    where clinic_id ='{clinicId}' 
                                    and  charge_sheet_id in ({chargeSheetIdSql})
                                    and product_type not in(5,12,13,16)
                                    group by charge_sheet_id
            '''
            chargeSheetSql = '''select a.id                                  as id,
                                    a.chain_id                            as chainId,
                                    a.clinic_id                           as clinicId,
                                    date_format(a.created, '%Y-%m-%d %T') as created,
                                    a.doctor_id                           as doctorId,
                                    IF(a.is_online,1,0)                           as isOnline,
                                    a.patient_id                          as patientId,
                                    a.patient_order_id                    as patientOrderId,
                                    a.type                                as type,
                                    a.execute_status                      as executeStatus,
                                    a.include_item_type                   as includeItemType,
                                    IF(a.include_item_type & 0x01 ,1,0)            as includeItemTypeBit0,
                                    IF(a.include_item_type & 0x02 ,1,0)            as includeItemTypeBit1,
                                    IF(a.include_item_type & 0x04 ,1,0)            as includeItemTypeBit2,
                                    IF(a.include_item_type & 0x08 ,1,0)            as includeItemTypeBit3,
                                    IF(a.include_item_type & 0x10 ,1,0)            as includeItemTypeBit4,
                                    IF(a.include_item_type & 0x20 ,1,0)            as includeItemTypeBit5,
                                    IF(a.include_item_type & 0x40 ,1,0)            as includeItemTypeBit6,
                                    IF(a.include_item_type & 0x80 ,1,0)            as includeItemTypeBit7,
                                    a.import_flag                         as importFlag,
                                    a.outpatient_status                   as outpatientStatus,
                                    a.is_draft                            as isDraft,
                                    IF(a.is_closed,1,0)                           as isClosed,
                                    IF(a.query_exception_type,1,0)                as queryExceptionType,
                                    IF(a.owed_status,1,0)                        as owedStatus,
                                    IF(a.check_status,1,0)                        as checkStatus,
                                    IF(a.send_to_patient_status,1,0)              as sendToPatientStatus,
                                    a.seller_id                           as sellerId,
                                    date_format(a.order_by_date, '%Y-%m-%d %T') as orderByDate,
                                    date_format(a.reserve_date, '%Y-%m-%d %T') as reserveDate,
                                    a.receivable_fee                      as receivableFee,
                                    if(a.is_deleted,1,0)                          as isDeleted,
                                    a.status                              as status,
                                    p.mobile_cipher                       as patientMobileCipher,
                                    p.mobile_last4                        as patientMobileLast4,
                                    p.id_card_cipher                      as patientIdCardCipher,
                                    p.id_card_last6                       as patientIdCardLast6,
                                    p.name                                as patientName,
                                    p.sn                                  as patientSn,
                                    p.is_member                           as patientIsMember,
                                    p.sex   as patientSex
                                    from          v2_charge_sheet as a inner join abc_cis_patient.v2_patient as p  on a.patient_id = p.id
                                    where a.clinic_id ='{clinicId}' 
                                    and a.last_modified >=  '{beginDate}' and a.last_modified <= '{endDate}'
                                 '''
            charge_db_client = self._get_db_client('adb', 'abc_cis_charge')
            charge_adb_client = self._get_db_client('adb', 'abc_cis_charge')
            patientorder_db_client = self._get_db_client('adb', 'abc_cis_patientorder')
            offset = 0
            charge_db_client.execute('''set group_concat_max_len = 102400''')
            esClient = es.ESClient('abc-search-prod-normal')
            basic_db_client = self._get_db_client('abc_cis_mixed', 'abc_cis_basic')
            organs = basic_db_client.fetchall(''' select id as clinicId from organ where parent_id = '{chainId}' and node_type = 2 '''.format(chainId=chain_id))
            for organ in organs:
                clinic_id = organ['clinicId']
                now_date = datetime.datetime.now()
                now_date = now_date + relativedelta(days= 1)
                PER_MONTH =1
                index = 0
                minCreatedResult = charge_adb_client.fetchone(selectMinCreatedSql.format(clinicId=clinic_id))
                if minCreatedResult:
                    minCreatedDate = minCreatedResult['minCreated']
                if minCreatedDate is None:
                    print("未找到发药单最小创建时间 退出")
                    continue
                minYYYYMMDD = minCreatedDate.strftime('%Y-%m-%d')
                while index < 64:
                    sT = time.clock()
                    endDate = (now_date - relativedelta(months= index*PER_MONTH) ).strftime('%Y-%m-%d')
                    beginDate = (now_date -  relativedelta(months= (index+1) *PER_MONTH) ).strftime('%Y-%m-%d')
                    if minYYYYMMDD > str(endDate):
                        print("不需要再往前找了:clinicId:"+clinic_id+" minYYYYMMDD="+minYYYYMMDD+",beginDate=" +str(beginDate) +","+endDate)
                        break
                    chargeSheetList = charge_db_client.fetchall(chargeSheetSql.format(clinicId=clinic_id,beginDate = beginDate,endDate = endDate))
                    index += 1
                    if chargeSheetList is None or len(chargeSheetList) == 0:
                        print("beginDate :"+beginDate+"  -> "+endDate+"，loadSize = 0")
                        continue
                    print("clinicId:"+clinic_id+",minCreated:"+minYYYYMMDD+",beginDate :"+beginDate+"  -> "+endDate+"，loadSize = "+str(len(chargeSheetList)))
                    write_bulk  = []
                    MAX_LEN = 2000
                    listRun = [chargeSheetList[i:i+MAX_LEN] for i in range(0,len(chargeSheetList),MAX_LEN)]
                    patientOrderIdToPatientOrder = {}
                    chargeSheetIdToMedName = {}
                    for list in listRun:
                        patientOrderIdSql = ','.join([ ''' '{0}' '''.format(chargeSheet['patientOrderId']) for chargeSheet in list ])
                        chargeSheetIdSql = ','.join([ ''' '{0}' '''.format(chargeSheet['id']) for chargeSheet in list ])
                        medNameList = charge_db_client.fetchall(medNameSQL.format(clinicId=clinic_id,chargeSheetIdSql = chargeSheetIdSql))
                        if medNameList is not None:
                            for medName in medNameList:
                                chargeSheetIdToMedName[medName['id']] = medName['medicineName']
                        patientOrderList = patientorder_db_client.fetchall(patientOrderSql.format(chainId=chain_id,patientOrderIdSql = patientOrderIdSql))
                        if patientOrderList is not None:
                            for patientOrder in patientOrderList:
                                patientOrderIdToPatientOrder[patientOrder['patientOrderId']] = patientOrder

                    for chargeSheet in chargeSheetList:
                       if chargeSheetIdToMedName.get(chargeSheet.get('id')) is not None:
                           chargeSheet['medicineName'] = chargeSheetIdToMedName.get(chargeSheet.get('id'))
                       if chargeSheet.get('patientOrderId') is not None and patientOrderIdToPatientOrder.get(chargeSheet.get('patientOrderId')) is not  None:
                           po = patientOrderIdToPatientOrder.get(chargeSheet.get('patientOrderId'))
                           chargeSheet['poNo'] = po.get('poNo')
                           chargeSheet['poSource'] = po.get('poSource')
                       # BigDecimal 单独处理
                       chargeSheet['receivableFee'] = str(chargeSheet.get('receivableFee'))
                       write_bulk.append({'index': {'_index': 'v3-cis-cdss-charge-sheet' , '_id': str(chargeSheet['id'])}})
                       write_bulk.append(json.dumps(chargeSheet, ensure_ascii=False))
                    dbTime= time.clock()
                    esClient.bulkInsert('v3-cis-cdss-charge-sheet',write_bulk)
                    esTime = time.clock()
                    print('...write to es success:'+str(len(chargeSheetList))+",db="+str(dbTime-sT)+'min,writeEs='+str(esTime-dbTime))
        except Exception as e:
            print(e)



    def execute_chain(self, chain_id):
        #先刷Goods数据
        self.logStashFlushToEs(chain_id)

def run(chain_id):
    rgt = LogStashFlushData()
    rgt.execute_chain(chain_id)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)


if __name__ == '__main__':
    main()
