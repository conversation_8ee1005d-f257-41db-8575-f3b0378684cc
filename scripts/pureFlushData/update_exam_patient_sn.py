#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import logging
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

reload(sys)
sys.setdefaultencoding('utf-8')
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db
import es
import json
import argparse
import requests
import datetime

# from idwork import IdWork
#
# db_client = db.DBClient('abc_cis_outpatient', 'abc_cis_examination')
# id_work = IdWork(db_client, False)
# id_work.config()

# {
#     "query": {
#         "term": {
#             "patientId": ""
#         }
#     },
#     "script": {
#         "source": "ctx._source.patientSn = 'new_value'"
#     }
# }

default_id = '00000000000000000000000000000000'


def updateExaminationSheetEs(chain_id, table_name, index_name):
    # try:
        examination_cli = db.DBClient('adb', 'abc_cis_examination')
        patient_cli = db.DBClient('adb', 'abc_cis_patient')
        es_cli = es.ESClient('abc-search-prod-normal')
        start_date = examination_cli.fetchone(
            """select date(min(created)) as startDate from {0} where chain_id = '{1}';""".format(table_name,
                                                                                                 chain_id))[
            'startDate']
        if start_date is None:
            print 'start_date is None'
            return
        today = datetime.date.today()
        # 计算时间间隔
        days = (today - start_date).days
        gap = 30
        for i in range(0, days, gap):
            sql = """select id, patient_id from {0} where created >= '{1}' and created <= '{2}' and chain_id = '{3}';""".format(
                table_name,
                '{} 00:00:00'.format(start_date + datetime.timedelta(days=i + (0 if i == 0 else 1))),
                '{} 23:59:59'.format(start_date + datetime.timedelta(days=i + gap)), chain_id)
            rows = examination_cli.fetchall(sql)
            # 获取所有的patient_id
            patient_ids = list(set([row['patient_id'] for row in rows]))
            # patient_ids 拆分一组2000个
            patient_ids2000 = [patient_ids[i:i + 2000] for i in range(0, len(patient_ids), 2000)]
            patient_id_sn = {}
            for patient_ids in patient_ids2000:
                patients = patient_cli.fetchall("""select id, sn from v2_patient where id in ({})""".format(
                    ','.join(['"{}"'.format(patient_id) for patient_id in patient_ids])))
                # 转为字典追加
                patient_id_sn.update({patient['id']: patient['sn'] for patient in patients})

            # rows拆分 一组2000个
            rows2000 = [rows[i:i + 2000] for i in range(0, len(rows), 2000)]
            for rows2 in rows2000:
                values = []
                for row in rows2:
                    if row['patient_id'] not in patient_id_sn:
                        print 'patient_id not in patient_id_sn', row['patient_id']
                        continue
                    body = {
                        "doc": {
                            "patientSn": patient_id_sn[row['patient_id']]
                        }
                    }
                    values.append({'update': {'_index': index_name, '_id': str(row['id'])}})
                    values.append(json.dumps(body, ensure_ascii=False))
                # 批量更新
                if len(values) == 0:
                    print 'req_body is empty'
                    continue
                es_cli.bulkInsert(index_name, values)


    # except Exception as e:
    #     print("update {} to-es error".format(table_name), e)


def run(chain_id):
    updateExaminationSheetEs(chain_id, 'v2_examination_sheet', 'v3-examination-sheet-prod')
    updateExaminationSheetEs(chain_id, 'v2_examination_merge_sheet', 'v3-examination-merge-sheet-prod')
    updateExaminationSheetEs(chain_id, 'v2_examination_apply_sheet', 'v3-examination-apply-sheet-prod')


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)

if __name__ == '__main__':
    main()
