#! /usr/bin/env python2
# -*- coding: utf-8 -*-


import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
import argparse
import logging
import pandas as pd
from decimal import Decimal, ROUND_HALF_UP

# 配置日志
log_file = os.path.join(CURRENT_DIR, 'goods_stock_log_fix-杭州.sql')
fileHandler = logging.FileHandler(log_file, encoding='utf-8')

logger = logging.getLogger('default')
logger.addHandler(fileHandler)


def update_stock_log(goods_client, goods_log_client, batch_id, organ_id, chain_id, goods_id, log_id,
                     new_package_count, new_piece_count, new_stock_change_cost):
    """更新单条进销存记录的所有相关成本
    
    Args:
        goods_client: goods 数据库客户端
        goods_log_client: goods_log 数据库客户端
        batch_id: 批次ID
        organ_id: 机构ID
        chain_id: 连锁ID
        goods_id: 商品ID
        log_id: 初始化入库记录ID
        new_package_count: 新的大包装数
        new_piece_count: 新的小包装数
        new_stock_change_cost: 新的变更成本额
    """
    # 获取该批次的所有进销存记录
    query_sql = """
        select id,
               stock_id,
               stock_piece_count,
               stock_package_count,
               stock_total_cost,
               
               batch_id,
               batch_piece_count,
               batch_package_count,
               batch_total_cost,
               
               pharmacy_no,
               pharmacy_piece_count,
               pharmacy_package_count,
               pharmacy_total_cost,
               
               goods_id,
               goods_piece_count,
               goods_package_count,
               goods_total_cost,
               
               piece_count,
               package_count,
               stock_change_cost
        FROM v2_goods_stock_log 
        WHERE id >= {logId}
        AND goods_id = '{goodsId}'
        AND chain_id = '{chainId}'
        AND organ_id = '{clinicId}'
        ORDER BY id ASC;
    """.format(logId=log_id, goodsId=goods_id, chainId=chain_id, clinicId=organ_id)
    print(f"查询进销存记录: {query_sql}")
    records = goods_log_client.fetchall(query_sql)

    if not records:
        print(f"未找到批次 {batch_id} 的进销存记录")
        return False

    # 累计批次，药房，药品的差价
    total_stock_cost_diff = 0
    total_batch_cost_diff = 0
    total_pharmacy_cost_diff = 0
    total_goods_cost_diff = 0

    total_stock_piece_count_diff = 0
    total_stock_package_count_diff = 0
    total_batch_piece_count_diff = 0
    total_batch_package_count_diff = 0
    total_pharmacy_piece_count_diff = 0
    total_pharmacy_package_count_diff = 0
    total_goods_piece_count_diff = 0
    total_goods_package_count_diff = 0

    # 处理第一条记录
    first_record = records[0]
    if first_record['batch_id'] != batch_id:
        print(f"第一条记录批次ID {first_record['batch_id']} 不等于 {batch_id}")
        return False

    # 计算差价
    cost_diff = (new_stock_change_cost - Decimal(first_record['stock_change_cost'])).quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP)
    # 累计批次，药房，药品的成本差值
    total_stock_cost_diff += cost_diff
    total_batch_cost_diff += cost_diff
    total_pharmacy_cost_diff += cost_diff
    total_goods_cost_diff += cost_diff

    # 计算库存差值
    piece_count_diff = Decimal(new_piece_count) - Decimal(first_record['piece_count'])
    package_count_diff = Decimal(new_package_count) - Decimal(first_record['package_count'])
    # 累计批次，药房，药品的库存差值
    total_stock_piece_count_diff += piece_count_diff
    total_stock_package_count_diff += package_count_diff
    total_batch_piece_count_diff += piece_count_diff
    total_batch_package_count_diff += package_count_diff
    total_pharmacy_piece_count_diff += piece_count_diff
    total_pharmacy_package_count_diff += package_count_diff
    total_goods_piece_count_diff += piece_count_diff
    total_goods_package_count_diff += package_count_diff

    if ((first_record['stock_piece_count'] + total_stock_piece_count_diff) < 0
            or (first_record['stock_package_count'] + total_stock_package_count_diff) < 0
            or (first_record['stock_total_cost'] + total_stock_cost_diff) < 0):
        print(f"stock_log:{first_record} 库存不足，预期变更库存：pieceCount:{total_stock_piece_count_diff}，packageCount:{total_stock_package_count_diff}，cost:{total_stock_cost_diff}")
        return False

    update_sql_list = []

    # 更新第一条记录
    update_sql = """
        UPDATE v2_goods_stock_log 
        SET 
            piece_count = {newPieceCount},
            package_count = {newPackageCount},
            stock_change_cost = {newStockCost},
            stock_total_cost = stock_total_cost + {costDiff},
            batch_total_cost = batch_total_cost + {costDiff},
            pharmacy_total_cost = pharmacy_total_cost + {costDiff},
            goods_total_cost = goods_total_cost + {costDiff},
            stock_piece_count = stock_piece_count + {pieceCountDiff},
            stock_package_count = stock_package_count + {packageCountDiff},
            batch_piece_count = batch_piece_count + {pieceCountDiff},
            batch_package_count = batch_package_count + {packageCountDiff},
            pharmacy_piece_count = pharmacy_piece_count + {pieceCountDiff},
            pharmacy_package_count = pharmacy_package_count + {packageCountDiff},
            goods_piece_count = goods_piece_count + {pieceCountDiff},
            goods_package_count = goods_package_count + {packageCountDiff}
        WHERE id = {recordId};
    """.format(
        newPieceCount=new_piece_count,
        newPackageCount=new_package_count,
        newStockCost=new_stock_change_cost,
        costDiff=cost_diff,
        pieceCountDiff=piece_count_diff,
        packageCountDiff=package_count_diff,
        recordId=first_record['id']
    )
    # 打印日志
    update_sql_list.append(update_sql)
    # goods_log_client.execute(update_sql)

    # 更新后续同批次的记录
    update = True
    for record in records[1:]:
        if record['stock_id'] == first_record['stock_id']:
            if ((record['stock_piece_count'] + total_stock_piece_count_diff) < 0
                    or (record['stock_package_count'] + total_stock_package_count_diff) < 0
                    or (record['stock_total_cost'] + total_stock_cost_diff) < 0):
                print(f"stock_log:{record} stock 库存不足，预期变更库存：pieceCount:{total_stock_piece_count_diff}，packageCount:{total_stock_package_count_diff}，cost:{total_stock_cost_diff}")
                update = False
                break

            update_sql = """
                    UPDATE v2_goods_stock_log 
                    SET stock_total_cost = stock_total_cost + {stockCostDiff},
                        batch_total_cost = batch_total_cost + {batchCostDiff},
                        pharmacy_total_cost = pharmacy_total_cost + {pharmacyCostDiff},
                        goods_total_cost = goods_total_cost + {goodsCostDiff},
                        stock_piece_count = stock_piece_count + {stockPieceCountDiff},
                        stock_package_count = stock_package_count + {stockPackageCountDiff},
                        batch_piece_count = batch_piece_count + {batchPieceCountDiff},
                        batch_package_count = batch_package_count + {batchPackageCountDiff},
                        pharmacy_piece_count = pharmacy_piece_count + {pharmacyPieceCountDiff},
                        pharmacy_package_count = pharmacy_package_count + {pharmacyPackageCountDiff},
                        goods_piece_count = goods_piece_count + {goodsPieceCountDiff},
                        goods_package_count = goods_package_count + {goodsPackageCountDiff}
                    WHERE id = {recordId};
            """.format(
                stockCostDiff=total_stock_cost_diff,
                batchCostDiff=total_batch_cost_diff,
                pharmacyCostDiff=total_pharmacy_cost_diff,
                goodsCostDiff=total_goods_cost_diff,
                stockPieceCountDiff=total_stock_piece_count_diff,
                stockPackageCountDiff=total_stock_package_count_diff,
                batchPieceCountDiff=total_batch_piece_count_diff,
                batchPackageCountDiff=total_batch_package_count_diff,
                pharmacyPieceCountDiff=total_pharmacy_piece_count_diff,
                pharmacyPackageCountDiff=total_pharmacy_package_count_diff,
                goodsPieceCountDiff=total_goods_piece_count_diff,
                goodsPackageCountDiff=total_goods_package_count_diff,
                recordId=record['id']
            )
            update_sql_list.append(update_sql)
            # goods_log_client.execute(update_sql)
        elif record['batch_id'] == first_record['batch_id']:
            if ((record['batch_piece_count'] + total_batch_piece_count_diff) < 0
                    or (record['batch_package_count'] + total_batch_package_count_diff) < 0
                    or (record['batch_total_cost'] + total_batch_cost_diff) < 0):
                print(f"stock_log:{record} batch 库存不足，预期变更库存：pieceCount:{total_batch_piece_count_diff}，packageCount:{total_batch_package_count_diff}，cost:{total_batch_cost_diff}")
                update = False
                break

            update_sql = """
                UPDATE v2_goods_stock_log 
                SET batch_total_cost = batch_total_cost + {batchCostDiff},
                    pharmacy_total_cost = pharmacy_total_cost + {pharmacyCostDiff},
                    goods_total_cost = goods_total_cost + {goodsCostDiff},
                    batch_piece_count = batch_piece_count + {batchPieceCountDiff},
                    batch_package_count = batch_package_count + {batchPackageCountDiff},
                    pharmacy_piece_count = pharmacy_piece_count + {pharmacyPieceCountDiff},
                    pharmacy_package_count = pharmacy_package_count + {pharmacyPackageCountDiff},
                    goods_piece_count = goods_piece_count + {goodsPieceCountDiff},
                    goods_package_count = goods_package_count + {goodsPackageCountDiff}
                WHERE id = {recordId};
            """.format(
                batchCostDiff=total_batch_cost_diff,
                pharmacyCostDiff=total_pharmacy_cost_diff,
                goodsCostDiff=total_goods_cost_diff,
                batchPieceCountDiff=total_batch_piece_count_diff,
                batchPackageCountDiff=total_batch_package_count_diff,
                pharmacyPieceCountDiff=total_pharmacy_piece_count_diff,
                pharmacyPackageCountDiff=total_pharmacy_package_count_diff,
                goodsPieceCountDiff=total_goods_piece_count_diff,
                goodsPackageCountDiff=total_goods_package_count_diff,
                recordId=record['id']
            )
            update_sql_list.append(update_sql)
            # goods_log_client.execute(update_sql)
        elif first_record['pharmacy_no'] == record['pharmacy_no']:
            if ((record['pharmacy_piece_count'] + total_pharmacy_piece_count_diff) < 0
                    or (record['pharmacy_package_count'] + total_pharmacy_package_count_diff) < 0
                    or (record['pharmacy_total_cost'] + total_pharmacy_cost_diff) < 0):
                print(
                    f"stock_log:{record} pharmacy 库存不足，预期变更库存：pieceCount:{total_pharmacy_piece_count_diff}，packageCount:{total_pharmacy_package_count_diff}，cost:{total_pharmacy_cost_diff}")
                update = False
                break

            update_sql = """
                UPDATE v2_goods_stock_log 
                SET pharmacy_total_cost = pharmacy_total_cost + {pharmacyCostDiff},
                    goods_total_cost = goods_total_cost + {goodsCostDiff},
                    pharmacy_piece_count = pharmacy_piece_count + {pharmacyPieceCountDiff},
                    pharmacy_package_count = pharmacy_package_count + {pharmacyPackageCountDiff},
                    goods_piece_count = goods_piece_count + {goodsPieceCountDiff},
                    goods_package_count = goods_package_count + {goodsPackageCountDiff}
                WHERE id = {recordId};
            """.format(
                pharmacyCostDiff=total_pharmacy_cost_diff,
                goodsCostDiff=total_goods_cost_diff,
                pharmacyPieceCountDiff=total_pharmacy_piece_count_diff,
                pharmacyPackageCountDiff=total_pharmacy_package_count_diff,
                goodsPieceCountDiff=total_goods_piece_count_diff,
                goodsPackageCountDiff=total_goods_package_count_diff,
                recordId=record['id']
            )
            update_sql_list.append(update_sql)
            # goods_log_client.execute(update_sql)
        else:
            if ((record['goods_piece_count'] + total_goods_piece_count_diff) < 0
                    or (record['goods_package_count'] + total_goods_package_count_diff) < 0
                    or (record['goods_total_cost'] + total_goods_cost_diff) < 0):
                print(f"stock_log:{record} goods 库存不足，预期变更库存：pieceCount:{total_goods_piece_count_diff}，packageCount:{total_goods_package_count_diff}，cost:{total_goods_cost_diff}")
                update = False
                break
            update_sql = """
                UPDATE v2_goods_stock_log 
                SET goods_total_cost = goods_total_cost + {goodsCostDiff},
                    goods_piece_count = goods_piece_count + {goodsPieceCountDiff},
                    goods_package_count = goods_package_count + {goodsPackageCountDiff}
                WHERE id = {recordId};
            """.format(
                goodsCostDiff=total_goods_cost_diff,
                goodsPieceCountDiff=total_goods_piece_count_diff,
                goodsPackageCountDiff=total_goods_package_count_diff,
                recordId=record['id']
            )
            update_sql_list.append(update_sql)

    # 更新库存
    goods_stock = goods_client.fetchone("""select id, piece_count, package_count, left_cost from v2_goods_stock where id= '{stock_id}'""".format(stock_id=first_record['stock_id']))
    if not goods_stock:
        print(f"未找到库存 {first_record['stock_id']}")
        update = False
    elif ((goods_stock['piece_count'] + total_stock_piece_count_diff) < 0
          or (goods_stock['package_count'] + total_stock_package_count_diff) < 0
          or (goods_stock['left_cost'] + total_stock_cost_diff) < 0):
        print(f"stock:{goods_stock} 库存不足，预期变更库存：pieceCount:{total_stock_piece_count_diff}，packageCount:{total_stock_package_count_diff}，cost:{total_stock_cost_diff}")
        update = False

    if update:
        update_sql = """
            update v2_goods_stock
            set piece_count = piece_count + {pieceCountDiff},
                package_count = package_count + {packageCountDiff},
                left_cost = left_cost + {stockChangeCostDiff}
            where id = {id};
        """.format(
            pieceCountDiff=total_stock_piece_count_diff,
            packageCountDiff=total_stock_package_count_diff,
            stockChangeCostDiff=total_stock_cost_diff,
            id=first_record['stock_id']
        )
        update_sql_list.append(update_sql)
        for update_sql in update_sql_list:
            logger.info(f"{update_sql}\n")
    else:
        print(f"修复 stock_log:{log_id} 失败，存在库存不足")

    return update


def fix_stock_log(abcRegion, excel_path):
    """修复进销存记录错误问题
    
    Args:
        abcRegion: 数据库区域
        excel_path: Excel文件路径
    """
    # 连接数据库
    # db_client = DBClient(abcRegion, 'dev', 'abc_cis_goods', 'test', True)
    goods_log_client = DBClient(abcRegion, 'ob', 'abc_cis_goods_log', 'prod', False, jumper_user='yinxiaoyang_mini')
    goods_client = DBClient(abcRegion, 'ob', 'abc_cis_goods', 'prod', False, jumper_user='yinxiaoyang_mini')
    ob_client = DBClient(abcRegion, 'ob', 'abc_cis_goods_log', 'prod', False, jumper_user='yinxiaoyang_mini')

    try:
        # 读取Excel文件
        df = pd.read_excel(excel_path)

        # 处理每一行数据
        success_count = 0
        for index, row in df.iterrows():
            log_id = row['id']
            new_package_count = Decimal(str(row['package_count']))
            new_piece_count = Decimal(str(row['piece_count']))
            new_stock_change_cost = Decimal(str(row['stock_change_cost']))
            stock_log = ob_client.fetchone("""select * from v2_goods_stock_log where id = '{logId}'""".format(logId=log_id))
            if not stock_log:
                print(f"未找到进销存记录 {log_id}")
                continue

            batch_id = stock_log['batch_id']
            organ_id = stock_log['organ_id']
            chain_id = stock_log['chain_id']
            goods_id = stock_log['goods_id']

            print(
                f"处理进销存记录 id: {log_id}, 批次ID: {batch_id}, 机构: {organ_id}, 商品: {goods_id}, 新变更大包装数: {new_package_count}, 新变更小包装数: {new_piece_count}, 新变更成本价: {new_stock_change_cost}")
            result = update_stock_log(goods_client, goods_log_client, batch_id, organ_id, chain_id, goods_id, log_id, new_package_count, new_piece_count, new_stock_change_cost)
            if result:
                success_count += 1

        print(f"共 {len(df)} 条，可修复 {success_count} 条")

    except Exception as e:
        print(f"修复过程中出错:", e)
        raise


def main():
    parser = argparse.ArgumentParser()
    # parser.add_argument('--region-name', help='分区名字可直接查配置')
    # parser.add_argument('--env', help='环境')
    # parser.add_argument('--excel-path', default='测试数据.xlsx', help='Excel文件路径')
    # args = parser.parse_args()

    # if not args.region_name:
    #     parser.print_help()
    #     sys.exit(-1)

    try:
        # excel_path = os.path.join(CURRENT_DIR, "测试数据.xlsx")
        excel_path = '/Users/<USER>/Downloads/杭州发药数据修复.xlsx'
        print(f"开始处理Excel文件: {excel_path}")
        fix_stock_log('HangZhou', excel_path)
        print("处理完成")
    except Exception as e:
        print(f"程序执行出错: {str(e)}")
        sys.exit(-1)
    fileHandler.flush()


if __name__ == '__main__':
    main()
