#! /usr/bin/env python2
# -*- coding: utf-8 -*-

import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.rpc import regionRpcHost
import json
import argparse
import logging
import pandas as pd
from decimal import Decimal, ROUND_HALF_UP

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('goods_stock_cost_fix.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)


def update_stock_costs(goods_client,db_client, batch_id, organ_id, chain_id, goods_id, log_id, new_package_cost_price,in_clinic):
    """更新单条进销存记录的所有相关成本
    
    Args:
        db_client: 数据库客户端
        batch_id: 批次ID
        organ_id: 机构ID
        goods_id: 商品ID
        log_id: 初始化入库记录ID
        new_package_cost_price: 新的成本单价
    """
    # 获取该批次的所有进销存记录
    query_sql = """
        SELECT id, batch_id, bat_id, package_cost_price,action,_in_tax_rat, order_id, order_detail_id,pharmacy_no,
               piece_count, piece_num, package_count,
               stock_change_cost, stock_total_cost,
               batch_total_cost, pharmacy_total_cost, goods_total_cost,
               organ_id, goods_id
        FROM v2_goods_stock_log 
        WHERE id >= {logId}
        AND goods_id = '{goodsId}'
        AND chain_id = '{chainId}'
        AND organ_id = '{clinicId}'
        ORDER BY id ASC
    """.format(logId=log_id, goodsId=goods_id, chainId=chain_id, clinicId=organ_id)
    logging.info(f"更新进销存记录 {query_sql}")
    records = db_client.fetchall(query_sql)

    if not records:
        logging.error(f"未找到批次 {batch_id} 的进销存记录")
        return

    # 累计批次，药房，药品的差价
    total_batch_diff = 0
    total_pharmacy_diff = 0
    total_goods_diff = 0

    # 处理第一条记录（初始入库）调拨入库
    first_record = records[0]
    if first_record['batch_id'] != batch_id:
        logging.error(f"第一条记录批次ID {first_record['batch_id']} 不等于 {batch_id}")
        return
    # 计算新的stock_change_cost
    new_stock_change_cost = ((Decimal(first_record['piece_count']) / Decimal(first_record['piece_num']) +
                             Decimal(first_record['package_count'])) * Decimal(new_package_cost_price)).quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP)

    # 计算差价
    cost_diff = (new_stock_change_cost - Decimal(first_record['stock_change_cost'])).quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP)
    # 累计批次，药房，药品的差价
    total_batch_diff += cost_diff
    total_pharmacy_diff += cost_diff
    total_goods_diff += cost_diff

    # 更新第一条记录
    update_sql = """
        UPDATE v2_goods_stock_log 
        SET package_cost_price = {newPrice},
            stock_change_cost = {newStockCost},
            stock_total_cost = stock_total_cost + {costDiff},
            batch_total_cost = batch_total_cost + {costDiff},
            pharmacy_total_cost = pharmacy_total_cost + {costDiff},
            goods_total_cost = goods_total_cost + {costDiff}
        WHERE id = {recordId}
    """.format(
        newPrice=new_package_cost_price,
        newStockCost=new_stock_change_cost,
        costDiff=cost_diff,
        recordId=first_record['id']
    )
    # 打印日志
    logging.info(f"更新进销存记录 {update_sql}")
    db_client.execute(update_sql)
    update_bat_sql = """
                    UPDATE v2_goods_stock_log_bat 
                    SET total_cost_price = {newStockCost}
                    WHERE id = '{batId}'
                """.format(newStockCost=abs(new_stock_change_cost), batId=first_record['bat_id'])
    logging.info(f"更新进销存记录 {update_bat_sql}")
    db_client.execute(update_bat_sql)
    if in_clinic == 1:
        update_goods_stock_sql = """update v2_goods_stock set package_cost_price = {packageCostPrice} , left_cost = round((piece_count/piece_num + package_count)* {packageCostPrice},4)  where batch_id = {batchId}""".format(
            packageCostPrice=new_package_cost_price,
            batchId=batch_id
        )
        logging.info(f"更新进销存记录 {update_goods_stock_sql}")
        goods_client.execute(update_goods_stock_sql)
        update_goods_stock_in_sql = """update v2_goods_stock_in set package_cost_price = {packageCostPrice} , total_cost = {totalCost},use_unit_cost_price={packageCostPrice},use_total_cost_price={totalCost}  where id = {id}""".format(
            packageCostPrice=new_package_cost_price,
            totalCost=new_stock_change_cost,
            id=first_record['order_detail_id']
        )
        logging.info(f"更新进销存记录 {update_goods_stock_in_sql}")
        goods_client.execute(update_goods_stock_in_sql)



    # 更新后续同批次的记录
    for record in records[1:]:
        # 如果是同一个批次的进销存
        if record['batch_id'] == first_record['batch_id']:
            # 计算新的stock_change_cost
            new_stock_change_cost = ((Decimal(record['piece_count']) / Decimal(record['piece_num']) +
                                  Decimal(record['package_count'])) * Decimal(new_package_cost_price)).quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP)
            record_cost_diff = (new_stock_change_cost - Decimal(record['stock_change_cost'])).quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP)

            total_batch_diff += record_cost_diff
            total_pharmacy_diff += record_cost_diff
            total_goods_diff += record_cost_diff
            update_sql = """
                UPDATE v2_goods_stock_log 
            SET package_cost_price = {newPrice},
                stock_change_cost = {newStockCost},
                stock_total_cost = stock_total_cost + {stockCostDiff},
                batch_total_cost = batch_total_cost + {batchCostDiff},
                pharmacy_total_cost = pharmacy_total_cost + {pharmacyCostDiff},
                goods_total_cost = goods_total_cost + {goodsCostDiff}
            WHERE id = {recordId}
        """.format(
                newPrice=new_package_cost_price,
                newStockCost=new_stock_change_cost,
                stockCostDiff=record_cost_diff,
                batchCostDiff=total_batch_diff,
                pharmacyCostDiff=total_pharmacy_diff,
                goodsCostDiff=total_goods_diff,
                recordId=record['id']
            )
            logging.info(f"更新进销存记录 {update_sql}")
            db_client.execute(update_sql)
            # 如果发生在一个批次上
            if record['_in_tax_rat'] == 1:
                update_bat_sql = """
                    UPDATE v2_goods_stock_log_bat 
                    SET total_cost_price = {newStockCost}
                    WHERE id = '{batId}'
                """.format(newStockCost=abs(new_stock_change_cost), batId=record['bat_id'])
                db_client.execute(update_bat_sql)
                logging.info(f"更新进销存记录 {update_bat_sql}")
            else:  # 用sql join log表sum出总成本，绝对值
                update_bat_sql = """
                    UPDATE v2_goods_stock_log_bat 
                    SET total_cost_price = (SELECT ABS(SUM(stock_change_cost)) FROM v2_goods_stock_log WHERE bat_id = '{batId}')
                    WHERE id = '{batId}'
                """.format(batId=record['bat_id'])
                logging.info(f"更新进销存记录 {update_bat_sql}")
                db_client.execute(update_bat_sql)
            # 累计批次，药房，药品的差价
        # 如果是同一药房,更新这条记录的药房成本和goods成本
        elif first_record['pharmacy_no'] == record['pharmacy_no']:
            update_sql = """
                UPDATE v2_goods_stock_log 
                SET pharmacy_total_cost = pharmacy_total_cost + {pharmacyCostDiff},
                    goods_total_cost = goods_total_cost + {goodsCostDiff}
                WHERE id = {recordId}
            """.format(
                pharmacyCostDiff=total_pharmacy_diff,
                goodsCostDiff=total_goods_diff,
                recordId=record['id']
            )
            db_client.execute(update_sql)
            logging.info(f"更新进销存记录 {update_sql}")
        # 更新药品的成本
        else:
            update_sql = """
                UPDATE v2_goods_stock_log 
                SET goods_total_cost = goods_total_cost + {goodsCostDiff}
                WHERE id = {recordId}
            """.format(
                goodsCostDiff=total_goods_diff,
                recordId=record['id']
            )
            db_client.execute(update_sql)
            logging.info(f"更新进销存记录 {update_sql}")




def fix_stock_cost(abcRegion, excel_path):
    """修复进销存成本价错误
    
    Args:
        abcRegion: 数据库区域
        excel_path: Excel文件路径
    """
    # 连接数据库
    # db_client = DBClient(abcRegion, 'dev', 'abc_cis_goods', 'test', True)
    db_client = DBClient(abcRegion, 'abc_cis_stock_zip', 'abc_cis_goods_log', 'prod', False)
    goods_client = DBClient(abcRegion, 'abc_cis_stock', 'abc_cis_goods', 'prod', False)
    ob_client = DBClient(abcRegion, 'ob', 'abc_cis_goods_log', 'prod', False)
    #有调拨的批次集合
    batch_set = {
        94119639, 94119645
    }
    
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_path)

        # 处理每一行数据
        for index, row in df.iterrows():
            batch_id = row['ABC批次ID']  # 批次ID
            organ_id = row['ABC门店ID']  # 机构ID
            goods_id = row['ABC药品ID']
            extra_id = row['张仲景批次ID']
            stock_in_id = row['id']
            
            # 更新stock_in的extend_data字段
            # update_extend_data_sql = """
            # UPDATE v2_goods_stock_in
            # SET extend_data = JSON_SET(
            #     COALESCE(extend_data, '{{}}'),
            #     '$.outDetailId',
            #     '{extra_id}'
            # )
            # WHERE id = {stock_in_id}
            # """.format(extra_id=extra_id, stock_in_id=stock_in_id)
            #
            # goods_client.execute(update_extend_data_sql)
            # logging.info(f"更新stock_in extend_data成功，stock_in_id: {stock_in_id}, extra_id: {extra_id}")

            chain_id = 'ffffffff0000000034e4f1631fc90000'
            # 查询goods_stock_log获取ID
            log_id = row['_id'];  # get_init_stock_log_id(db_client, organ_id, goods_id, batch_id,row['ABC批次ID'])
            if not log_id:
                continue

            new_cost_price = row['新的成本单价']  # 新的成本单价

            logging.info( f"处理进销存记录 批次ID: {batch_id}, 机构: {organ_id}, 商品: {goods_id}, 新成本价: {new_cost_price}")
            update_stock_costs(goods_client,db_client, batch_id, organ_id, chain_id, goods_id, log_id, new_cost_price,1)
            #如果批次在batch_set中，查询调拨的其他批次
            if batch_id in batch_set:
                query_batch_ids_sql = ("""select id,organ_id,stock_id,batch_id from v2_goods_stock_log where action ='调拨入库' and batch_id = {batchId} and chain_id='{chainId}' and organ_id !='{clinicId}'"""
                                       .format(batchId=batch_id, chainId=chain_id, clinicId=organ_id))
                query_batch_ids = ob_client.fetchall(query_batch_ids_sql)
                for query_batch_id in query_batch_ids:
                    update_stock_costs(goods_client, db_client,batch_id, query_batch_id['organ_id'], chain_id, goods_id, query_batch_id['id'], new_cost_price,2)
            


        logging.info(f"成功处理 {len(df)} 条记录")

    except Exception as e:
        logging.error(f"修复过程中出错: {str(e)}")
        raise


def main():
    parser = argparse.ArgumentParser()
    # parser.add_argument('--region-name', help='分区名字可直接查配置')
    # parser.add_argument('--env', help='环境')
    # parser.add_argument('--excel-path', default='测试数据.xlsx', help='Excel文件路径')
    # args = parser.parse_args()

    # if not args.region_name:
    #     parser.print_help()
    #     sys.exit(-1)

    try:
        excel_path = os.path.join(CURRENT_DIR, "测试数据.xlsx")
        logging.info(f"开始处理Excel文件: {excel_path}")
        fix_stock_cost('HangZhou', excel_path)
        logging.info("处理完成")
    except Exception as e:
        logging.error(f"程序执行出错: {str(e)}")
        sys.exit(-1)


if __name__ == '__main__':
    main()
