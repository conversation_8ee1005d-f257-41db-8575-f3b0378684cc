#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
将 v2_goods_stock_traceable_code_log 表中的历史数据迁移到 v2_goods_stock_traceable_code_log_250208 表
仅保留每个 clinic_id, pharmacy_no, goods_id, action 组合的最近 10 条记录
作者: yinxiaoyang
日期: 2025-02-08
"""

import os
import sys
import argparse
import time
from datetime import datetime

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient

def get_db_client(region_name='ShangHai', env='prod'):
    """获取数据库连接"""
    try:
        return DBClient(region_name, 'abc_cis_stock', 'abc_cis_goods', env, True)
    except Exception as e:
        print(f"[ERROR] 数据库连接失败: {str(e)}")
        sys.exit(1)

def get_records_to_migrate(db_client, chain_id, batch_size):
    """获取需要迁移的记录ID列表"""
    select_ids_sql = """
    SELECT id
    FROM (
        SELECT 
            id,
            ROW_NUMBER() OVER (
                PARTITION BY clinic_id, pharmacy_no, goods_id, action
                ORDER BY created DESC
            ) as row_num
        FROM 
            v2_goods_stock_traceable_code_log
        WHERE chain_id = '{chain_id}'
    ) ranked
    WHERE row_num > 10
    LIMIT {batch_size}
    """.format(chain_id=chain_id, batch_size=batch_size)
    
    records = db_client.fetchall(select_ids_sql)
    return [str(record['id']) for record in records] if records else []

def migrate_batch(db_client, record_ids):
    """迁移一批数据"""
    if not record_ids:
        return 0, 0
    
    id_list = "','".join(record_ids)
    
    # 1. 将要迁移的数据插入到新表
    insert_sql = """
    INSERT INTO v2_goods_stock_traceable_code_log_250208
    SELECT * FROM v2_goods_stock_traceable_code_log
    WHERE id IN ('{ids}')
    """.format(ids=id_list)
    
    affected_rows = db_client.execute(insert_sql)
    
    # 2. 从原表删除已迁移的数据
    delete_sql = """
    DELETE FROM v2_goods_stock_traceable_code_log
    WHERE id IN ('{ids}')
    """.format(ids=id_list)
    
    deleted_rows = db_client.execute(delete_sql)
    
    return affected_rows, deleted_rows

def migrate_data(region_name='ShangHai', env='prod', chain_id=None, batch_size=500):
    """迁移数据的主函数"""
    if not chain_id:
        print(f"[ERROR] 必须指定 chain_id")
        sys.exit(1)

    db_client = get_db_client(region_name, env)
    total_migrated = 0
    total_deleted = 0
    
    try:
        while True:
            # 获取一批需要迁移的记录
            record_ids = get_records_to_migrate(db_client, chain_id, batch_size)
            if not record_ids:
                break

            # 延迟 1s
            time.sleep(1)
                
            # 迁移这一批数据
            migrated, deleted = migrate_batch(db_client, record_ids)
            total_migrated += migrated
            total_deleted += deleted
            
            print(f"[INFO] chain_id: {chain_id} - 当前已迁移总计: {total_migrated} 条记录")
            
        print(f"[INFO] chain_id: {chain_id} - 数据迁移完成，总共迁移: {total_migrated} 条记录")
            
    except Exception as e:
        print(f"[ERROR] chain_id: {chain_id} - 数据迁移失败: {str(e)}")
        raise

def parse_args():
    parser = argparse.ArgumentParser(description='数据迁移脚本')
    parser.add_argument('--region-name', default='ShangHai', help='区域名称')
    parser.add_argument('--env', default='prod', help='环境(prod/dev/test)')
    parser.add_argument('--chain-id', required=True, help='连锁ID')
    return parser.parse_args()

if __name__ == "__main__":
    args = parse_args()
    try:
        migrate_data(args.region_name, args.env, args.chain_id, 200)
    except Exception as e:
        print(f"[ERROR] chain_id: {args.chain_id} - 程序执行失败: {str(e)}")
        sys.exit(1)
