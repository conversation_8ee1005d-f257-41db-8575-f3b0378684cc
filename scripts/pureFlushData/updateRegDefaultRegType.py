#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""
这个脚本把患者的手机号，拉出来
"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import json
import es
import argparse
import logging

logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)


class UpdateExecutor(object):

    def __init__(self):
        self._es_client_cache = {}

    def _get_es_client(self, es_cluster):
        k = '{0}'.format(es_cluster)
        if k in self._es_client_cache.keys():
            return self._es_client_cache[k]

        es_client = es.ESClient(es_cluster)
        self._es_client_cache[k] = es_client
        return es_client

    def updateRegEs(self, chain_id):
        try:
            query_count_dsl = '''
{{
    "bool": {{
      "must": [
        {{
          "term": {{
            "chainId": {{
              "value": "{chainId}"
            }}
          }}
        }}
      ],
      "must_not": [
        {{
          "exists": {{
            "field": "registrationType"
          }}
        }}
      ]
    }}
}}'''
            update_script ='''{{
    "source": "ctx._source.registrationType=0",
    "lang": "painless"
  }}'''
            update_dsl ='''
{{
    "bool": {{
      "must": [
        {{
          "term": {{
            "chainId": {{
              "value": "{chainId}"
            }}
          }}
        }}
      ],
      "must_not": [
        {{
          "exists": {{
            "field": "registrationType"
          }}
        }}
      ]
    }}
}}
            '''
            reg_es_client = self._get_es_client('abc-search-prod-normal')
            # reg_es_client.count('prod-registration-form',query_count_dsl.format(chainId=chain_id))
            reg_es_client.update_by_query('prod-registration-form',update_dsl.format(chainId=chain_id),update_script)
            # reg_es_client.count('prod-registration-form',query_count_dsl.format(chainId=chain_id))

        except Exception as e:
            print(e)


    def execute_chain(self, chain_id):
        #先刷Goods数据
        self.updateRegEs(chain_id)

def run(chain_id):
    rgt = UpdateExecutor()
    rgt.execute_chain(chain_id)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)


if __name__ == '__main__':
    main()
