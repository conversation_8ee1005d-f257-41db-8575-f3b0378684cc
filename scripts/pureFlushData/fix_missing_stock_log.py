#! /usr/bin/env python2
# -*- coding: utf-8 -*-

import os
import sys
import logging
from decimal import Decimal, ROUND_HALF_UP
import datetime

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.rpc import regionRpcHost

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fix_missing_stock_log.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

def insert_missing_stock_logs(obs_client,goods_client, log_client):
    """补充缺失的进销存日志记录"""
    query_sql = """
    select  /*+ query_timeout(100000000)  */ a.*
    from (select i.id,
                i.order_id,
                i.goods_id,
                i.pharmacy_no,
                i.chain_id,
                i.goods,
                o.to_organ_id,
                i.batch_id,
                i.use_count,
                i.use_unit,
                i.use_unit_cost_price,
                i.use_total_cost_price,
                i.piece_count,
                i.package_count,
                i.piece_num,
                i.package_cost_price,
                round((i.piece_count/i.piece_num+i.package_count)*i.package_cost_price,4) as total_cost,
                o.supplier_id,
                i.created_date,
                i.created_user_id
        from abc_cis_goods.v2_goods_stock_in as i
        inner join abc_cis_goods.v2_goods_stock_in_order as o on i.order_id = o.id
        where i.type = 7
            and o.status = 2
            and (i.piece_count != 0 or i.package_count != 0)
            and i.created_date >= '2025-01-01')
            as a
            left join (select *
                    from abc_cis_goods_log.v2_goods_stock_log
                    where action = '修正入库' and created_date >= '2025-01-01') as l
                    on a.chain_id = l.chain_id and a.to_organ_id =l.organ_id and a.goods_id = l.goods_id and l.action = '修正入库'
    where l.id is null
    """

    logging.info(query_sql)
    missing_records = obs_client.fetchall(query_sql)
    logging.info(f"找到 {len(missing_records)} 条缺失的进销存记录")

    # 清空SQL文件
    sql_file_path = os.path.join(CURRENT_DIR, 'insert_missing_stock_log.sql')
    with open(sql_file_path, 'w', encoding='utf-8') as f:
        f.write(f"-- 生成时间: {datetime.datetime.now()}\n")
        f.write(f"-- 总记录数: {len(missing_records)}\n\n")

    for record in missing_records:
        #找到丢失进销存的前一条进销存 日志记录
        # 1. 查找同一stock_id的前一条记录
        last_stock_sql = """
        SELECT /*+ query_timeout(100000000)  */ id, stock_total_cost, stock_piece_count, stock_package_count
        FROM v2_goods_stock_log
        WHERE chain_id = '{chainId}'
          AND organ_id = '{organId}'
          AND stock_id = '{stockId}'
          AND action !='修改追溯码'
          AND created_date < '{createdDate}'
        ORDER BY created_date DESC
        LIMIT 1
        """.format(chainId=record['chain_id'], organId=record['to_organ_id'], stockId=record['batch_id'], createdDate=record['created_date'])
        logging.info(last_stock_sql)
        last_stock_log = obs_client.fetchone(last_stock_sql)
        
        # 2. 查找同一batch_id的前一条记录
        last_batch_sql = """
        SELECT /*+ query_timeout(100000000)  */ id, batch_total_cost, batch_piece_count, batch_package_count
        FROM v2_goods_stock_log
        WHERE chain_id = '{chainId}'
          AND organ_id = '{organId}'
          AND batch_id = '{batchId}'
          AND action !='修改追溯码'
          AND created_date < '{createdDate}'
        ORDER BY created_date DESC
        LIMIT 1
        """.format(chainId=record['chain_id'], organId=record['to_organ_id'], batchId=record['batch_id'], createdDate=record['created_date'])
        last_batch_log = obs_client.fetchone(last_batch_sql)

        # 3. 查找同一pharmacy_no的前一条记录
        last_pharmacy_sql = """
        SELECT /*+ query_timeout(100000000)  */ id, pharmacy_total_cost, pharmacy_piece_count, pharmacy_package_count
        FROM v2_goods_stock_log
        WHERE chain_id = '{chainId}'
          AND organ_id = '{organId}'
          AND pharmacy_no = '{pharmacyNo}'
          AND goods_id = '{goodsId}'
          AND action !='修改追溯码'
          AND created_date < '{createdDate}'
        ORDER BY created_date DESC
        LIMIT 1
        """.format(chainId=record['chain_id'], organId=record['to_organ_id'], pharmacyNo=record['pharmacy_no'], goodsId=record['goods_id'], createdDate=record['created_date'])
        last_pharmacy_log = obs_client.fetchone(last_pharmacy_sql)

        # 4. 查找同一goods_id的前一条记录
        last_goods_sql = """
        SELECT /*+ query_timeout(100000000)  */ id, goods_total_cost, goods_piece_count, goods_package_count
        FROM v2_goods_stock_log
        WHERE chain_id = '{chainId}'
          AND organ_id = '{organId}'
          AND goods_id = '{goodsId}'
          AND action !='修改追溯码'
          AND created_date < '{createdDate}'
        ORDER BY created_date DESC
        LIMIT 1
        """.format(chainId=record['chain_id'], organId=record['to_organ_id'], goodsId=record['goods_id'], createdDate=record['created_date'])
        last_goods_log = obs_client.fetchone(last_goods_sql)

        # 计算stock_change_cost
        stock_change_cost = Decimal(record['total_cost']).quantize(Decimal('0.0001'), 
                                                                 rounding=ROUND_HALF_UP)
        
        # 计算各种成本
        stock_total_cost = (Decimal(last_stock_log['stock_total_cost']) if last_stock_log else Decimal('0')) + stock_change_cost
        batch_total_cost = (Decimal(last_batch_log['batch_total_cost']) if last_batch_log else Decimal('0')) + stock_change_cost
        pharmacy_total_cost = (Decimal(last_pharmacy_log['pharmacy_total_cost']) if last_pharmacy_log else Decimal('0')) + stock_change_cost
        goods_total_cost = (Decimal(last_goods_log['goods_total_cost']) if last_goods_log else Decimal('0')) + stock_change_cost
        # 计算各种数量
        stock_piece_count = (Decimal(last_stock_log['stock_piece_count']) if last_stock_log else Decimal('0')) + Decimal(record['piece_count'] or 0)
        batch_piece_count = (Decimal(last_batch_log['batch_piece_count']) if last_batch_log else Decimal('0')) + Decimal(record['piece_count'] or 0)
        pharmacy_piece_count = (Decimal(last_pharmacy_log['pharmacy_piece_count']) if last_pharmacy_log else Decimal('0')) + Decimal(record['piece_count'] or 0)
        goods_piece_count = (Decimal(last_goods_log['goods_piece_count']) if last_goods_log else Decimal('0')) + Decimal(record['piece_count'] or 0)
        stock_package_count = (Decimal(last_stock_log['stock_package_count']) if last_stock_log else Decimal('0')) + Decimal(record['package_count'] or 0)
        batch_package_count = (Decimal(last_batch_log['batch_package_count']) if last_batch_log else Decimal('0')) + Decimal(record['package_count'] or 0)
        pharmacy_package_count = (Decimal(last_pharmacy_log['pharmacy_package_count']) if last_pharmacy_log else Decimal('0')) + Decimal(record['package_count'] or 0)
        goods_package_count = (Decimal(last_goods_log['goods_package_count']) if last_goods_log else Decimal('0')) + Decimal(record['package_count'] or 0)
        # 记录日志
        if not last_stock_log:
            logging.warning(f"未找到相同stock_id的前序记录: chain_id={record['chain_id']}, "
                        f"organ_id={record['to_organ_id']}, stock_id={record['stock_id']}")
        if not last_batch_log:
            logging.warning(f"未找到相同batch_id的前序记录: chain_id={record['chain_id']}, "
                        f"organ_id={record['to_organ_id']}, batch_id={record['batch_id']}")
        if not last_pharmacy_log:
            logging.warning(f"未找到相同pharmacy_no的前序记录: chain_id={record['chain_id']}, "
                        f"organ_id={record['to_organ_id']}, pharmacy_no={record['pharmacy_no']}")
        if not last_goods_log:
            logging.warning(f"未找到相同goods_id的前序记录: chain_id={record['chain_id']}, "
                        f"organ_id={record['to_organ_id']}, goods_id={record['goods_id']}")

        # 插入进销存日志
        insert_sql = """
        INSERT INTO v2_goods_stock_log (
            chain_id, organ_id, goods_id,goods, action,
            piece_num, piece_count, package_count,
            package_cost_price, 
            stock_piece_count, stock_package_count, stock_change_cost, stock_total_cost,
            batch_piece_count, batch_package_count, batch_total_cost,
            pharmacy_piece_count, pharmacy_package_count, pharmacy_total_cost,
            goods_piece_count, goods_package_count, goods_total_cost,
            bat_id, order_id, order_detail_id,
            batch_id, stock_id,_in_tax_rat, pharmacy_no, supplier_id,
            created_date, created_user_id,patient_order_id
        ) VALUES (
            '{chainId}', '{organId}', '{goodsId}','{goods}', '修正入库',
            1, {pieceCount}, {packageCount},
            {packageCostPrice},
            {stockPieceCount}, {stockPackageCount}, {stockChangeCost}, {stockTotalCost},
            {batchPieceCount}, {batchPackageCount}, {batchTotalCost},
            {pharmacyPieceCount}, {pharmacyPackageCount}, {pharmacyTotalCost},
            {goodsPieceCount}, {goodsPackageCount}, {goodsTotalCost},
            '{batId}', '{orderId}', '{detailId}',
            '{batchId}','{stockId}', 1, {pharmacyNo},'{supplierId}',
            '{createdDate}', '{createdUserId}', '{patientOrderId}'
        )
        """.format(
            chainId=record['chain_id'],
            organId=record['to_organ_id'],
            goodsId=record['goods_id'],
            goods=record['goods'],
            pieceCount=record['piece_count'] or 0,
            packageCount=record['package_count'] or 0,
            packageCostPrice=record['package_cost_price'],
            stockPieceCount=stock_piece_count,
            stockPackageCount=stock_package_count,
            stockChangeCost=stock_change_cost,
            stockTotalCost=stock_total_cost,
            batchPieceCount=batch_piece_count,
            batchPackageCount=batch_package_count,
            batchTotalCost=batch_total_cost,
            pharmacyPieceCount=pharmacy_piece_count,
            pharmacyPackageCount=pharmacy_package_count,
            pharmacyTotalCost=pharmacy_total_cost,
            goodsPieceCount=goods_piece_count,
            goodsPackageCount=goods_package_count,
            goodsTotalCost=goods_total_cost,
            batId=f"stockinorder{record['id']}",  # 添加stockinorder前缀
            orderId=record['order_id'],
            detailId=record['id'],
            batchId=record['batch_id'],
            stockId=record['batch_id'],
            pharmacyNo=record['pharmacy_no'],
            supplierId=record['supplier_id'],
            createdDate=record['created_date'],
            createdUserId=record['created_user_id'],
            patientOrderId='00000000000000000000000000000000' #用32个0来表示是刷的数据
        )
        logging.info(insert_sql)
        
        #再帮我生成插入 v2_goods_stock_log_bat的sql语句
        insert_bat_sql = """
        INSERT INTO v2_goods_stock_log_bat (
            _id, id, action, order_id, order_detail_id,
            chain_id, organ_id, goods_id,
            total_cost_price, total_origin_price, total_count,
            in_tax_rat, out_tax_rat,
            created_user_id, created_date,
            last_modified_user_id, last_modified_date
        ) VALUES (
            {_id}, '{id}', '修正入库', '{orderId}', '{detailId}',
            '{chainId}', '{organId}', '{goodsId}',
            {totalCostPrice}, 0, {totalCount},
            1, 0,
            '{createdUserId}', '{createdDate}',
            '{createdUserId}', '{createdDate}'
        )
        """.format(
            _id=record['id'],  # 使用stock_in的id作为_id
            id=f"stockinorder{record['id']}",   # 添加stockinorder前缀
            orderId=record['order_id'],
            detailId=record['id'],
            chainId=record['chain_id'],
            organId=record['to_organ_id'],
            goodsId=record['goods_id'],
            totalCostPrice=abs(stock_change_cost),  # 使用绝对值
            totalCount=abs(
                (Decimal(record['piece_count'] or 0) / Decimal(record['piece_num'] or 1)) + 
                Decimal(record['package_count'] or 0)
            ),  # 转换成大单位，处理None值
            createdUserId=record['created_user_id'],
            createdDate=record['created_date']
        )
        logging.info(insert_bat_sql)

        # 将SQL写入文件
        with open(sql_file_path, 'a', encoding='utf-8') as f:
            f.write(insert_sql + ';\n')
            f.write(insert_bat_sql + ';\n\n')
        
        # params = {}
        # try:
        #     log_client.execute(insert_sql, params)
        #     logging.info(f"成功插入进销存日志: chain_id={record['chain_id']}, "
        #                 f"organ_id={record['to_organ_id']}, goods_id={record['goods_id']}")
        # except Exception as e:
        #     logging.error(f"插入进销存日志失败: {str(e)}")

def main():
    # parser = argparse.ArgumentParser(description='补充缺失的进销存日志记录')
    # parser.add_argument('--region', required=True, help='数据库区域')
    # args = parser.parse_args()

    # 连接数据库
    abcRegion = 'ShangHai'

    # 连接数据库
    # log_client = DBClient(abcRegion, 'abc_cis_stock_zip', 'abc_cis_goods_log', 'prod', False)
    # goods_client = DBClient(abcRegion, 'abc_cis_stock', 'abc_cis_goods', 'prod', False)
    ob_client = DBClient(abcRegion, 'ob', 'abc_cis_goods_log', 'prod', False)

    try:
        insert_missing_stock_logs( ob_client,ob_client, ob_client)
    except Exception as e:
        logging.error(f"处理失败: {str(e)}")
    finally:
        # 只需要关闭一次，因为是同一个连接
        ob_client.close()

if __name__ == '__main__':
    main()
