'''
删除message_single_log重复的数据
'''

import argparse
import os
import sys

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.db import DBClient


class UpdateData:
    chain_id = None
    basic_db_client = None
    msg_db_client = None

    def __init__(self, region_name, chain_id, env):
        self.chain_id = chain_id
        self.region_name = region_name
        env = 'prod' if not env else env
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', env, True)
        self.msg_ob_client = DBClient(self.region_name, 'ob', 'abc_cis_message', env, True)
        self.msg_db_client = DBClient(self.region_name, 'abc_cis_account_base', 'abc_cis_message', env, True)

    def run(self):
        print("处理数据 门店：{chainId}".format(chainId=self.chain_id))
        total = 0
        sql = '''select distinct employee_id  from  clinic_employee where chain_id ='{chainId}'; '''
        employeeIds = self.basic_db_client.fetchall(sql.format(chainId=self.chain_id))
        if employeeIds is None or len(employeeIds) == 0:
            return

        for employeeId in employeeIds:
            try:
                employeeIdValue = employeeId['employee_id']
                sql = '''select id from v2_message_single_log where chain_id = '00000000000000000000000000000000' 
                and clinic_id = '00000000000000000000000000000000' and event_type = '100001' and message_channel = 3 
                and destination_owner_id = '{employeeId}' group by msg_log_id;'''
                ids = self.msg_ob_client.fetchall(sql.format(employeeId=employeeIdValue))
                if ids is None or len(ids) == 0:
                    continue

                id_string = ", ".join(f"'{item['id']}'" for item in ids)
                id_string = id_string[1:-1]
                # ids 转换成数组
                delSql = '''delete from v2_message_single_log where chain_id = '00000000000000000000000000000000'
                and clinic_id = '00000000000000000000000000000000'
                and event_type = '100001'
                and message_channel = 3
                and destination_owner_id = '{employeeId}' 
                and id not in ('{ids}'); '''
                ret = self.msg_db_client.execute(delSql.format(employeeId=employeeIdValue, ids=id_string))
                total = total + ret;
                print("处理数据 门店: {chainId} 的员工 {employeeId}  数据 {ret}".format(chainId=self.chain_id,employeeId=employeeIdValue, ret=ret))
            except Exception as e:
                print("处理数据 门店: {chainId} 的员工 {employeeId} error")
                print(e)

        print("处理数据 门店: {chainId}, 更新了{total}条数据".format(chainId=self.chain_id, total=total))

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    if (args.env == 'dev' or args.env == 'test') and args.chain_id == 'all':
        # 遍历所有的连锁
        basic_db_client = DBClient(args.region_name, 'abc_cis_mixed', 'abc_cis_basic', args.env, True)
        organs = basic_db_client.fetchall("""
            select id
            from organ
            where id = parent_id
        """)
        if not organs:
            return

        for organ in organs:
            updateData = UpdateData(args.region_name, organ['id'], args.env)
            updateData.run()
    else:
        updateData = UpdateData(args.region_name, args.chain_id, args.env)
        updateData.run()


if __name__ == '__main__':
    main()
