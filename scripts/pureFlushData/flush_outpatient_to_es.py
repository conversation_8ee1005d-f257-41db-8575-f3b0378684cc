#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import db
import argparse
import logging
import es
import json
import time

import datetime
from dateutil.relativedelta import relativedelta

import sys

reload(sys)
sys.setdefaultencoding('utf-8')

logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)


class LogStashFlushData(object):

    def __init__(self):
        self._db_client_cache = {}

    def _get_db_client(self, cluster, database):
        k = '{0}-{1}'.format(cluster, database)
        if k in self._db_client_cache.keys():
            return self._db_client_cache[k]

        db_client = db.DBClient(cluster, database)
        self._db_client_cache[k] = db_client
        return db_client

    # 加载患者信息
    # 未解决问题 从adb里面 这样查数据 ，存在 找不到患者的情况
    def loadPatientMap(self, chain_id):
        # patientSql = '''
        #         select
        #                   p.id,
        #                   p.mobile_cipher                       as patientMobileCipher,
        #                   p.mobile_last4                        as patientMobileLast4,
        #                   p.id_card_cipher                      as patientIdCardCipher,
        #                   p.id_card_last6                       as patientIdCardLast6,
        #                   p.name                                as patientName,
        #                   p.sn                                  as patientSn,
        #                   p.is_member                           as patientIsMember,
        #                   p.sex                                 as patientSex
        #         from v2_patient as p
        #         where p.chain_id ='{chainId}'
        #         limit {offsetNum},{limitNum}
        #     '''
        # patient_db_client = self._get_db_client('adb', 'abc_cis_patient')
        # LOOP_COUNT = 1000
        # offset = 0
        patientIdToPatient = {}
        # while True:
        #     patientList = patient_db_client.fetchall(
        #         patientSql.format(chainId=chain_id, offsetNum=offset, limitNum=LOOP_COUNT))
        #     if patientList is None or len(patientList) == 0:
        #         break
        #     print('''.....loading patient (offSet:{0},limit:{1},{2})'''.format(offset, LOOP_COUNT,str(len(patientList))))
        #     offset += len(patientList)
        #     for patient in patientList:
        #         patientIdToPatient[patient['id']] = patient
        #
        #     if len(patientList) < LOOP_COUNT:
        #         break
        return patientIdToPatient

    # 查patientOrder信息
    def loadPatientOrderMap(self,patientOrderIdToPatientOrder, chain_id, patientOrderIdSql):
        patientOrderSql = '''
                select   po.no                                 as poNo,
                         po.id                                 as patientOrderId,
                         po.source                             as poSource
                from    v2_patientorder as po 
                where po.chain_id = '{chainId}' and po.id in ({patientOrderIdSql}) 
            '''
        patientorder_db_client = self._get_db_client('adb', 'abc_cis_patientorder')
        patientOrderList = patientorder_db_client.fetchall(
            patientOrderSql.format(chainId=chain_id, patientOrderIdSql=patientOrderIdSql))
        if patientOrderList is None or len(patientOrderList) == 0:
            return patientOrderIdToPatientOrder
        for patientOrder in patientOrderList:
            patientOrderIdToPatientOrder[patientOrder['patientOrderId']] = patientOrder
        return patientOrderIdToPatientOrder
    # 查patientOrder信息
    def loadOutpatientMedNameMap(self, outpatientSheetIdToMedName,chain_id,clinic_id, outpatientSheetIdSql):
        outpatientMedNameSql = '''
                select outpatient_sheet_id as id,
                        group_concat(name) as productMedicineName
                from abc_cis_outpatient.v2_outpatient_product_form_item
                where outpatient_sheet_id in ({outpatientSheetIdSql})
                and clinic_id ='{clinicId}'  and is_deleted = 0
                group by outpatient_sheet_id
            '''
        outpatient_adb_client = self._get_db_client('adb', 'abc_cis_outpatient')
        list = outpatient_adb_client.fetchall(
            outpatientMedNameSql.format(chainId=chain_id,clinicId=clinic_id,outpatientSheetIdSql=outpatientSheetIdSql))
        if list is  None:
            return outpatientSheetIdToMedName
        for medName in list:
            if outpatientSheetIdToMedName.get(medName['id']) is None:
                outpatientSheetIdToMedName[medName['id']] = {}
            outpatientSheetIdToMedName.get(medName['id'])['productMedicineName'] = medName['productMedicineName']

        return outpatientSheetIdToMedName

    #
    def loadOutpatientProductNameMap(self, outpatientSheetIdToMedName,chain_id,clinic_id, outpatientSheetIdSql):
        outpatientProdcutNameSql = '''
                select outpatient_sheet_id as id,
                       group_concat(name) as prescriptionMedicineName
                from abc_cis_outpatient.v2_outpatient_prescription_form_item
                where outpatient_sheet_id in ({outpatientSheetIdSql})
                 and clinic_id ='{clinicId}'  and is_deleted = 0
                group by outpatient_sheet_id
            '''
        outpatient_adb_client = self._get_db_client('adb', 'abc_cis_outpatient')
        list = outpatient_adb_client.fetchall(
            outpatientProdcutNameSql.format(chainId=chain_id, clinicId = clinic_id,outpatientSheetIdSql=outpatientSheetIdSql))
        if list is not None:
            for productName in list:
                if outpatientSheetIdToMedName.get(productName['id']) is None:
                    outpatientSheetIdToMedName[productName['id']] = {}
                outpatientSheetIdToMedName.get(productName['id'])['prescriptionMedicineName'] = productName['prescriptionMedicineName']
        return outpatientSheetIdToMedName

    #加载诊断信息
    def loadOutpatientDiagnoseNameMap(self, outpatientSheetIdToMedName,chain_id,clinic_id, outpatientSheetIdSql):
        outpatientDiagonseNameSql = '''
                select outpatient_sheet_id as id,
                        diagnosis
                from abc_cis_outpatient.v2_outpatient_medical_record
                where 
                    outpatient_sheet_id in ({outpatientSheetIdSql})
                        and   clinic_id ='{clinicId}' 
            '''
        outpatient_adb_client = self._get_db_client('adb', 'abc_cis_outpatient')
        list = outpatient_adb_client.fetchall(
            outpatientDiagonseNameSql.format(chainId=chain_id, clinicId = clinic_id,outpatientSheetIdSql=outpatientSheetIdSql))
        if list is not None:
            for disgnoseItem in list:
                if outpatientSheetIdToMedName.get(disgnoseItem['id']) is None:
                    outpatientSheetIdToMedName[disgnoseItem['id']] = {}
                outpatientSheetIdToMedName.get(disgnoseItem['id'])['diagnosis'] = disgnoseItem['diagnosis']
        return outpatientSheetIdToMedName

    #查询输出到es的主表的sql
    def loadMainTableObjects(self, chain_id, clinic_id, beginDate, endDate):
        mainTableSql = ''' 
              select a.id                                  as id,
                     a.chain_id                            as chainId,
                     a.clinic_id                           as clinicId,
                     date_format(a.created, '%Y-%m-%d %T') as created,
                     a.doctor_id                           as doctorId,
                     a.department_id                       as departmentId,
                     a.copywriter_id                       as copywriterId,
                     a.is_online                           as isOnline,
                     a.patient_id                          as patientId,
                     a.patient_order_id                    as patientOrderId,
                     a.type                                as type,
                     a.is_reserved                         as isReserved,
                     date_format(a.reserve_date, '%Y-%m-%d %T') as reserveDate,
                     a.is_deleted                          as isDeleted,
                     a.status                              as status,
                     p.mobile_cipher                       as patientMobileCipher,
                     p.mobile_last4                        as patientMobileLast4,
                     p.id_card_cipher                      as patientIdCardCipher,
                     p.id_card_last6                       as patientIdCardLast6,
                     p.name                                as patientName,
                     p.sn                                  as patientSn,
                     p.is_member                           as patientIsMember,
                     p.sex                                 as patientSex
              from abc_cis_outpatient.v2_outpatient_sheet as a
                     left join abc_cis_patient.v2_patient as p on p.chain_id = a.chain_id and p.id = a.patient_id 
              where a.clinic_id ='{clinicId}' and   a.chain_id = '{chainId}' and a.import_flag = 0 
                   and a.last_modified >=  '{beginDate}' and a.last_modified <= '{endDate}'
            '''
        outpatient_adb_client = self._get_db_client('adb', 'abc_cis_outpatient')
        recordList = outpatient_adb_client.fetchall(
            mainTableSql.format(chainId=chain_id,clinicId=clinic_id, beginDate=beginDate, endDate=endDate))
        return recordList

    def fillToEsObject(self, esItem, patientOrderIdToPatientOrder, outpatientSheetIdToFillObject):
        if esItem.get('patientOrderId') is not None and patientOrderIdToPatientOrder.get(
                esItem.get('patientOrderId')) is not None:
            po = patientOrderIdToPatientOrder.get(esItem.get('patientOrderId'))
            esItem['patientOrderNo'] = po.get('poNo')
            esItem['poSource'] = po.get('poSource')
        else:
            print("严重错误：未找到PatientOrder:" + esItem.get('patientOrderId'))

        if esItem.get('id') is not None and outpatientSheetIdToFillObject.get(
                esItem.get('id')) is not None:
            fillObj = outpatientSheetIdToFillObject.get(esItem.get('id'))
            esItem['diagnosis'] = fillObj.get('diagnosis')
            esItem['productMedicineName'] = fillObj.get('productMedicineName')
            esItem['prescriptionMedicineName'] = fillObj.get('prescriptionMedicineName')
        else:
            print("严重错误：未找到门诊单药品信息:" + esItem.get('id'))
        return esItem

    def logStashFlushToEs(self, chain_id):
        # 倒退查 门店的最早创建时间
        selectMinCreatedSql = '''
            select  min(last_modified)  as minCreated
            from    v2_outpatient_sheet as po 
            where po.clinic_id = '{clinicId}'   
        '''
        outpatient_adb_client = self._get_db_client('adb', 'abc_cis_outpatient')

        patientIdToPatient = self.loadPatientMap(chain_id)
        offset = 0
        esClient = es.ESClient('abc-search-prod-normal')
        basic_db_client = self._get_db_client('abc_cis_mixed', 'abc_cis_basic')
        organs = basic_db_client.fetchall(
            ''' select id as clinicId from organ where parent_id = '{chainId}' and node_type = 2 '''.format(
                chainId=chain_id))
        for organ in organs:
            clinic_id = organ['clinicId']
            now_date = datetime.datetime.now()
            now_date = now_date + relativedelta(days=1)
            PER_MONTH = 1
            index = 0
            minCreatedResult = outpatient_adb_client.fetchone(selectMinCreatedSql.format(clinicId=clinic_id))
            if minCreatedResult:
                minCreatedDate = minCreatedResult['minCreated']
            if minCreatedDate is None:
                print("未找最小创建时间 退出")
                continue
            minYYYYMMDD = minCreatedDate.strftime('%Y-%m-%d')
            while index < 64:
                sT = time.clock()
                endDate = (now_date - relativedelta(months=index * PER_MONTH)).strftime('%Y-%m-%d')
                beginDate = (now_date - relativedelta(months=(index + 1) * PER_MONTH)).strftime('%Y-%m-%d')
                if minYYYYMMDD > str(endDate):
                    print("不需要再往前找了:clinicId:" + clinic_id + " minYYYYMMDD=" + minYYYYMMDD + ",beginDate=" + str(
                        beginDate) + "," + endDate)
                    break

                recordList = self.loadMainTableObjects(chain_id, clinic_id, beginDate, endDate)
                index += 1
                if recordList is None or len(recordList) == 0:
                    print("beginDate :" + beginDate + "  -> " + endDate + "，loadSize = 0")
                    continue
                print( "clinicId:" + clinic_id + ",minCreated:" + minYYYYMMDD + ",beginDate :" + beginDate + "  -> " + endDate + "，loadSize = " + str( len(recordList)))

                # ADB 虽然快 但是限制了2000个IN
                outpatientSheetIdToFillObject = {}
                patientOrderIdToPatientOrder = {}
                MAX_LEN = 2000
                listRun = [recordList[i:i+MAX_LEN] for i in range(0,len(recordList),MAX_LEN)]
                for list in listRun:
                    outpatientSheetIdSql = ','.join([ ''' '{0}' '''.format(record['id']) for record in list ])
                    patientOrderIdSql = ','.join( [''' '{0}' '''.format(record['patientOrderId']) for record in list])
                    self.loadPatientOrderMap(patientOrderIdToPatientOrder,chain_id, patientOrderIdSql)
                    self.loadOutpatientMedNameMap(outpatientSheetIdToFillObject,chain_id,clinic_id,outpatientSheetIdSql)
                    self.loadOutpatientProductNameMap(outpatientSheetIdToFillObject,chain_id,clinic_id,outpatientSheetIdSql)
                    self.loadOutpatientDiagnoseNameMap(outpatientSheetIdToFillObject,chain_id,clinic_id,outpatientSheetIdSql)
                write_bulk = []
                for record in recordList:
                    writeToEsObject = self.fillToEsObject(record, patientOrderIdToPatientOrder, outpatientSheetIdToFillObject)
                    write_bulk.append({'index': {'_index': 'v3-cis-cdss-outpatient-sheet',
                                                 '_id': str(record['id'])}})
                    write_bulk.append(json.dumps(writeToEsObject, ensure_ascii=False))
                dbTime = time.clock()
                esClient.bulkInsert('v3-cis-cdss-outpatient-sheet', write_bulk)
                esTime = time.clock()
                print('...write to es success:' + str(len(recordList)) + ",db=" + str(
                    dbTime - sT) + 'min,writeEs=' + str(esTime - dbTime))

def run(chain_id):
    rgt = LogStashFlushData()
    rgt.logStashFlushToEs(chain_id)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)


if __name__ == '__main__':
    main()
