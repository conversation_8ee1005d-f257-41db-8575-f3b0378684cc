from scripts.common.general_update_es import updateEs
import argparse
import sys


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--region-name', help='分区')
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.region_name or not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    updateEs(args.region_name, args.chain_id, 'abc_cis_invoice', 'v2_invoice_record', 'select id, invoice_management_id as invoiceManagementId, invoice_management_device_id as invoiceManagementDeviceId from v2_invoice_record where (invoice_management_id is not null or invoice_management_device_id is not null)', 'v3-cis-invoice-record', 'prod')
    # region_name = 'ShangHai'
    # env = 'test'
    # basic_db_client = DBClient(region_name, 'adb', 'abc_cis_basic', env)
    # chains = basic_db_client.fetchall('select * from organ where node_type = 1;')
    # for chain in chains:
    #     updateEs(region_name, chain['id'], 'abc_cis_invoice', 'v2_invoice_record', 'select id, invoice_management_id as invoiceManagementId, invoice_management_device_id as invoiceManagementDeviceId from v2_invoice_record where (invoice_management_id is not null or invoice_management_device_id is not null)', 'v3-cis-invoice-record', env)


if __name__ == '__main__':
    main()
