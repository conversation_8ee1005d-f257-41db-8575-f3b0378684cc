#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import db
import argparse
import logging
import es
import requests

logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)

class LogStashFlushData(object):

    def __init__(self):
        self._db_client_cache = {}

    def _get_db_client(self, cluster, database):
        k = '{0}-{1}'.format(cluster, database)
        if k in self._db_client_cache.keys():
            return self._db_client_cache[k]

        db_client = db.DBClient(cluster, database)
        self._db_client_cache[k] = db_client
        return db_client

    def readPostBodyFromFileAndSendToRpc(self ):
        try:
            f = open("./req_body.txt",'r')
            lines = f.readlines()
            for body in lines:
                print(str(body))
                rsp = requests.post('http://pre.rpc.abczs.cn/rpc/v3/goods/undispense', json= body)
                print(str(rsp))
                return
        except Exception as e:
            print(e)



    def execute_chain(self ):
        #先刷Goods数据
        self.logStashFlushToEs()

def run():
    rgt = LogStashFlushData()
    rgt.execute_chain()


def main():
    run()


if __name__ == '__main__':
    main()
