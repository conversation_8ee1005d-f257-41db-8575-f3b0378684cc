#! /usr/bin/env python3
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import argparse


from multizone.db import DBClient

# 清理档案删除对码信息还存在的信息
def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--env', help='环境dev/test/prod')
    parser.add_argument('--regionName', help='ShangHai/HangZhou')
    parser.add_argument('--chainId', help='需要清理的连锁ID')
    args = parser.parse_args()
    # args.env = 'dev'
    # args.regionName = 'ShangHai'
    # args.chainId = 'ffffffff000000000c5a1308069aa000'
    if not args.regionName or not args.env:
        parser.print_help()
        sys.exit(-1)

    shebao_ods_client = DBClient(args.regionName, 'abc_cis_bill', 'abc_cis_shebao', args.env, True)
    shebao_ob_client = DBClient(args.regionName, 'ob', 'abc_cis_shebao', args.env, True)


    abc_cis_shebao = 'abc_cis_shebao'
    abc_cis_goods = 'abc_cis_goods'
    if args.env != 'prod':
        abc_cis_shebao = abc_cis_shebao + '_' + args.env
        abc_cis_goods = abc_cis_goods + '_' + args.env

    sqlUpdateSelect = f"""
         select concat("update shebao_chengdu_matched_code set is_deleted = 1 where chain_id = '", a.chain_id, "' and goods_id = '",a.goods_id,"';") as update_sql from {abc_cis_shebao}.shebao_chengdu_matched_code a left join {abc_cis_goods}.v2_goods b on a.chain_id = b.organ_id and a.goods_id = b.id
            where region = 'liaoning_shenyang' and a.is_deleted = 0 and (b.id is null or b.status = 99) ;
        """
    if args.chainId:
        sqlUpdateSelect = f"""
         select concat("update shebao_chengdu_matched_code set is_deleted = 1 where chain_id = '", a.chain_id, "' and goods_id = '",a.goods_id,"';") as update_sql from {abc_cis_shebao}.shebao_chengdu_matched_code a left join {abc_cis_goods}.v2_goods b on a.chain_id = b.organ_id and a.goods_id = b.id
            where chain_id = '{args.chainId}' and a.is_deleted = 0 and (b.id is null or b.status = 99) ;
        """
    print('sqlUpdateSelect:', sqlUpdateSelect)
    update_sql_res = shebao_ob_client.fetchall(sqlUpdateSelect)
    for update_sql in update_sql_res:
        update_sql = update_sql['update_sql']
        print('update_sql:', update_sql)
        shebao_ods_client.execute(update_sql)
if __name__ == '__main__':
    main()
