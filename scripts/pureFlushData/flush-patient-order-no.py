import redis
import os
import sys
import argparse
from datetime import date, timedelta

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.rediscli import RedisClient
from multizone.db import DBClient


def processPatientOrderNo(region_name, env):
    # 连接到 MySQL
    ob = DBClient(region_name, 'ob', 'abc_cis_patientorder', env, True)
    db = DBClient(region_name, 'abc_cis_outpatient', 'abc_cis_patientorder', env, True)
    redis_client = RedisClient(region_name, 'abc-redis', 61, env, True)

    # 获取符合条件的 clinic_id 列表
    clinic_ids = ob.fetchall("""
        SELECT clinic_id
        FROM (
            SELECT clinic_id, no, COUNT(1)
            FROM v2_patientorder
            WHERE type = 0
              AND no != 0
              AND created >= '2024-09-01'
              AND is_deleted = 0
            GROUP BY clinic_id, no
            HAVING COUNT(1) > 1
        ) temp
        GROUP BY clinic_id;
    """)

    # 循环处理每个 clinic_id
    allcount = 0
    for clinic_id in clinic_ids:
        oldSqls = []
        try:
            clinic_id = clinic_id['clinic_id']
            clinic_id_count = 0
            # 设置初始 no 的最大
            counter = ob.fetchone("""
                        SELECT MAX(no) as no FROM v2_patientorder
                        WHERE type = 0
                          AND is_deleted = 0
                          AND clinic_id = '{clinic_id}';
                    """.format(clinic_id=clinic_id))['no']

            if counter is None:
                continue

            # 获取满足条件的记录 ID 列表
            ids = ob.fetchall("""
                        SELECT MAX(id) AS id, no
                        FROM v2_patientorder
                        WHERE type = 0
                          AND created >= '2024-09-01'
                          AND is_deleted = 0
                          and no != 0
                          AND clinic_id = '{clinic_id}'
                        GROUP BY no
                        HAVING COUNT(1) > 1
                        ORDER BY id ASC;
                    """.format(clinic_id=clinic_id))
            # 更新每个 ID 的 no 值
            print('new sql: ')
            for id in ids:
                record_id = id['id']
                old_no = id['no']
                oldSql = """
                            UPDATE v2_patientorder
                            SET no = {counter}
                            WHERE id = '{record_id}';
                        """.format(counter=old_no, record_id=record_id)
                oldSqls.append(oldSql)
                counter += 1
                sql = """
                            UPDATE v2_patientorder
                            SET no = {counter}
                            WHERE id = '{record_id}';
                        """.format(counter=counter, record_id=record_id)
                print(sql)
                db.execute(sql)
                allcount = allcount + 1
                clinic_id_count = clinic_id_count + 1

            print(f"flush clinicId: {clinic_id}", '执行数量: ', clinic_id_count)
            print(f"备份:")
            for oldSql in oldSqls:
                print(oldSql)

            # 删除 Redis 中的键
            redis_key = f"patient_order:{clinic_id}:0"
            redis_client.client.delete(redis_key)
            print(f"Deleted Redis key: {redis_key}")
        except Exception as e:
            print(f"处理数据 门店: {clinic_id} error: {e}")
    print('总共执行数量 ', allcount)

    # # 删除所有的redis key
    # de1 = 1
    # for key in redis_client.client.scan_iter("patient_order:*"):
    #     redis_client.client.delete(key)
    #     print(f"Deleted Redis key: {key}  ----", de1)
    #     de1 = de1 + 1


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    processPatientOrderNo(args.region_name, args.env)
    # processPatientOrderNo('HangZhou', 'prod')


if __name__ == '__main__':
    main()