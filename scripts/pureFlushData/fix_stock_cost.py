#! /usr/bin/env python2
# -*- coding: utf-8 -*-

from math import fabs
import os
import re
import sys
import logging
from decimal import Decimal, ROUND_HALF_UP
import datetime

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.rpc import regionRpcHost

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fix_stock_cost.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

def fix_stock_costs(obs_client, goods_client, log_client):
    """修复进销存成本计算的问题"""
    
    # 获取所有的修正入库记录
    query_sql = """
    SELECT /*+ query_timeout(100000000)  */
        id, action, created_date,
        chain_id, organ_id, goods_id,
        order_id, order_detail_id,piece_num,
        piece_count, package_count,
        stock_change_cost,
        stock_id, stock_piece_count, stock_package_count,
        batch_id, batch_piece_count, batch_package_count,
        pharmacy_no, pharmacy_piece_count, pharmacy_package_count,
        goods_piece_count, goods_package_count
    FROM v2_goods_stock_log
    WHERE action = '修正入库'
        AND created_date >= '2025-01-01'
    """
    
    records = obs_client.fetchall(query_sql)
    logging.info(f"找到 {len(records)} 条修正入库记录需要处理")
    
    # 创建SQL文件
    update_sql_file = "ShangHai_update_stock_cost.sql"
    backup_sql_file = "ShangHai_backup_stock_cost.sql"
    
    with open(update_sql_file, "w") as f:
        f.write("-- 更新SQL\n")
    
    with open(backup_sql_file, "w") as f:
        f.write("-- 备份SQL\n")
    
    sql_gray ="""select chain_id,created from abc_cis_basic.v2_data_migrate_script_organ_execute where  script_id = 244; """
    gray_records = obs_client.fetchall(sql_gray)
    # 字典
    gray_records = {record['chain_id']: record['created'] for record in gray_records}
    
    for record in records:
        #如果时间拉灰度之前过滤掉
        gray_date = gray_records.get(record['chain_id'])
        if gray_date is None:
            logging.warning(f"Chain ID {record['chain_id']} 未找到灰度时间，跳过处理")
            continue
        if record['created_date'] < gray_date:
            continue
        # # 1. 查找同一stock_id的前一条记录
        # last_stock_sql = """
        # SELECT /*+ query_timeout(100000000)  */ id, stock_total_cost, stock_piece_count, stock_package_count,piece_num
        # FROM v2_goods_stock_log
        # WHERE chain_id = '{chain_id}'
        #   AND organ_id = '{organ_id}'
        #   AND goods_id = '{goods_id}'
        #   AND stock_id = '{stock_id}'
        #   AND action != '修改追溯码'
        #   AND created_date < '{created_date}'
        # ORDER BY created_date DESC
        # LIMIT 1
        # """.format(
        #     chain_id=record['chain_id'],
        #     organ_id=record['organ_id'],
        #     stock_id=record['stock_id'],
        #     goods_id=record['goods_id'],
        #     created_date=record['created_date']
        # )
        # last_stock_log = obs_client.fetchone(last_stock_sql)
        #
        # # 2. 查找同一batch_id的前一条记录
        # last_batch_sql = """
        # SELECT /*+ query_timeout(100000000)  */ id, batch_total_cost, batch_piece_count, batch_package_count,piece_num
        # FROM v2_goods_stock_log
        # WHERE chain_id = '{chain_id}'
        #   AND organ_id = '{organ_id}'
        #   AND goods_id = '{goods_id}'
        #   AND batch_id = '{batch_id}'
        #   AND action != '修改追溯码'
        #   AND created_date < '{created_date}'
        # ORDER BY created_date DESC
        # LIMIT 1
        # """.format(
        #     chain_id=record['chain_id'],
        #     organ_id=record['organ_id'],
        #     goods_id=record['goods_id'],
        #     batch_id=record['batch_id'],
        #     created_date=record['created_date']
        # )
        # last_batch_log = obs_client.fetchone(last_batch_sql)
        
        # 3. 查找同一pharmacy_no的前一条记录
        last_pharmacy_sql = """
        SELECT /*+ query_timeout(100000000)  */ id, pharmacy_total_cost, pharmacy_piece_count, pharmacy_package_count,piece_num
        FROM v2_goods_stock_log
        WHERE chain_id = '{chain_id}'
          AND organ_id = '{organ_id}'
          AND pharmacy_no = '{pharmacy_no}'
          AND goods_id = '{goods_id}'
          AND action != '修改追溯码'
          AND created_date < '{created_date}'
        ORDER BY created_date DESC
        LIMIT 1
        """.format(
            chain_id=record['chain_id'],
            organ_id=record['organ_id'],
            pharmacy_no=record['pharmacy_no'],
            goods_id=record['goods_id'],
            created_date=record['created_date']
        )
        last_pharmacy_log = obs_client.fetchone(last_pharmacy_sql)
        
        # 4. 查找同一goods_id的前一条记录
        last_goods_sql = """
        SELECT /*+ query_timeout(100000000)  */ id, goods_total_cost, goods_piece_count, goods_package_count,piece_num
        FROM v2_goods_stock_log
        WHERE chain_id = '{chain_id}'
          AND organ_id = '{organ_id}'
          AND goods_id = '{goods_id}'
          AND pharmacy_type = 0
          AND action != '修改追溯码'
          AND created_date < '{created_date}'
        ORDER BY created_date DESC
        LIMIT 1
        """.format(
            chain_id=record['chain_id'],
            organ_id=record['organ_id'],
            goods_id=record['goods_id'],
            created_date=record['created_date']
        )
        last_goods_log = obs_client.fetchone(last_goods_sql)
        
        # 记录日志
        if not last_pharmacy_log:
            logging.warning(f"未找到相同pharmacy_no的前序记录: chain_id={record['chain_id']}, "
                        f"organ_id={record['organ_id']}, pharmacy_no={record['pharmacy_no']}")
        if not last_goods_log:
            logging.warning(f"未找到相同goods_id的前序记录: chain_id={record['chain_id']}, "
                        f"organ_id={record['organ_id']}, goods_id={record['goods_id']}")
        
        # 查找后续连续的修正记录
        next_records_sql = """
        SELECT /*+ query_timeout(100000000)  */
            id, action, created_date,piece_num,
            chain_id, organ_id, goods_id,
            order_id, order_detail_id,
            piece_count, package_count,
            stock_change_cost,
            stock_id, stock_piece_count, stock_package_count,stock_total_cost,
            batch_id, batch_piece_count, batch_package_count,batch_total_cost,
            pharmacy_no, pharmacy_piece_count, pharmacy_package_count,pharmacy_total_cost,
            goods_piece_count, goods_package_count,goods_total_cost
        FROM v2_goods_stock_log 
        WHERE chain_id = '{chain_id}'
            AND organ_id = '{organ_id}'
            AND goods_id = '{goods_id}'
            AND created_date >= '{created_date}'
        ORDER BY created_date ASC
        LIMIT 200
        """.format(
            chain_id=record['chain_id'],
            organ_id=record['organ_id'],
            goods_id=record['goods_id'],
            created_date=record['created_date']
        )
        next_records = obs_client.fetchall(next_records_sql)
        #如果没有下一个记录
        if not next_records:
            break

        # 如果前序记录不存在，跳过处理
        if not last_pharmacy_log or not last_goods_log:
            logging.warning(f"缺少必要的前序记录，跳过处理: chain_id={record['chain_id']}, "
                        f"organ_id={record['organ_id']}, goods_id={record['goods_id']}")
            continue

        pre_piece_num = Decimal(str(last_pharmacy_log['piece_num']))
        pre_goods_piece_num = Decimal(str(last_goods_log['piece_num']))
        # 检查下 如果规格不一样，打错误日志，并不计算
        if pre_piece_num != pre_goods_piece_num:
            logging.warning(f"规格不一样: chain_id={record['chain_id']}, "
                        f"organ_id={record['organ_id']}, goods_id={record['goods_id']}, "
                        f"piece_num={record['piece_num']}, pre_piece_num={pre_piece_num}, "
                        )
            continue
        # 计算新的总成本 
        cal_pharmacy_total_cost = Decimal(str(last_pharmacy_log['pharmacy_total_cost'])) if last_pharmacy_log else Decimal('0')
        cal_pharmacy_piece_count = Decimal(str(last_pharmacy_log['pharmacy_piece_count'])) if last_pharmacy_log else Decimal('0')
        cal_pharmacy_package_count = Decimal(str(last_pharmacy_log['pharmacy_package_count'])) if last_pharmacy_log else Decimal('0')
        cal_total_pharmacy_piece_count = pre_piece_num * cal_pharmacy_package_count + cal_pharmacy_piece_count
        
        cal_goods_piece_count = Decimal(str(last_goods_log['goods_piece_count'])) if last_goods_log else Decimal('0') + Decimal(str(record['piece_count'] or 0))
        cal_goods_package_count = Decimal(str(last_goods_log['goods_package_count'])) if last_goods_log else Decimal('0') + Decimal(str(record['package_count'] or 0))
        cal_goods_cost = Decimal(str(last_goods_log['goods_total_cost'])) if last_goods_log else Decimal('0') + Decimal(str(record['stock_change_cost']))
        cal_total_goods_piece_count = cal_goods_piece_count + cal_goods_package_count * pre_piece_num
        #第一条是修正入库，取第一条的时间
        cal_created_date = next_records[0]['created_date']
        #检查第一条是否有跳变
        first_record = next_records[0]
        piece_num = Decimal(str(first_record['piece_num']))
        # 检查下 如果规格不一样，打错误日志，并不计算
        if pre_piece_num != piece_num:
            logging.warning(f"规格不一样: chain_id={record['chain_id']}, "
                        f"organ_id={record['organ_id']}, goods_id={record['goods_id']}, "
                        f"piece_num={record['piece_num']}, pre_piece_num={pre_piece_num}, "
                        )
            continue
        next_stock_change_cost = Decimal(str(first_record['stock_change_cost'])).quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP)
        next_piece_count = Decimal(str(first_record['piece_count'] or 0))
        next_package_count = Decimal(str(first_record['package_count'] or 0))
        next_total_piece_count = next_package_count * piece_num + next_piece_count

        cal_total_pharmacy_piece_count_1st = cal_total_pharmacy_piece_count + next_total_piece_count
        cal_pharmacy_total_cost_1st = cal_pharmacy_total_cost + next_stock_change_cost

        cal_total_goods_piece_count_1st = cal_total_goods_piece_count + next_total_piece_count
        cal_goods_cost_1st = cal_goods_cost + next_stock_change_cost
        
        
        db_next_pharmacy_change_cost = Decimal(str(first_record['pharmacy_total_cost'])).quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP)
        db_next_pharmacy_piece_count = Decimal(str(first_record['pharmacy_piece_count'] or 0))
        db_next_pharmacy_package_count = Decimal(str(first_record['pharmacy_package_count'] or 0))
        db_next_pharmacy_total_piece_count = db_next_pharmacy_package_count * piece_num + db_next_pharmacy_piece_count
        #药品
        db_next_goods_change_cost = Decimal(str(first_record['goods_total_cost'])).quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP)
        db_next_goods_piece_count = Decimal(str(first_record['goods_piece_count'] or 0))
        db_next_goods_package_count = Decimal(str(first_record['goods_package_count'] or 0))
        db_next_goods_total_piece_count = db_next_goods_package_count * piece_num + db_next_goods_piece_count
        needFixlog = False
        if db_next_pharmacy_total_piece_count != cal_total_pharmacy_piece_count_1st:
            needFixlog = True
        if db_next_goods_total_piece_count != cal_total_goods_piece_count_1st:
            needFixlog = True

        if not needFixlog:
            continue

        
        # 遍历后续记录并更新,用index开始遍历
        index = 0
        while index < len(next_records):
            next_record = next_records[index]
            #和前一条比较是否是对的

            #如果不是 action 不是修正打头的，退出
            if not next_record['action'].startswith('修正'):
                break 

            #如果next_record时间比cal_created_date大超过10s
            if (cal_created_date - next_record['created_date']).total_seconds() > 10:
                break
            piece_num = Decimal(str(next_record['piece_num']))
            # 检查下 如果规格不一样，打错误日志，并不计算
            if pre_piece_num != piece_num:
                logging.warning(f"规格不一样: chain_id={record['chain_id']}, "
                            f"organ_id={record['organ_id']}, goods_id={record['goods_id']}, "
                            f"piece_num={record['piece_num']}, pre_piece_num={pre_piece_num}, "
                            )
                continue

            next_stock_change_cost = Decimal(str(next_record['stock_change_cost'])).quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP)
            next_piece_count = Decimal(str(next_record['piece_count'] or 0))
            next_package_count = Decimal(str(next_record['package_count'] or 0))
            next_total_piece_count = next_package_count * piece_num + next_piece_count

            cal_total_pharmacy_piece_count = cal_total_pharmacy_piece_count + next_total_piece_count
            cal_pharmacy_total_cost = cal_pharmacy_total_cost + next_stock_change_cost

            cal_total_goods_piece_count = cal_total_goods_piece_count + next_total_piece_count
            cal_goods_cost = cal_goods_cost + next_stock_change_cost
            
            
            db_next_pharmacy_change_cost = Decimal(str(next_record['pharmacy_total_cost'])).quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP)
            db_next_pharmacy_piece_count = Decimal(str(next_record['pharmacy_piece_count'] or 0))
            db_next_pharmacy_package_count = Decimal(str(next_record['pharmacy_package_count'] or 0))
            db_next_pharmacy_total_piece_count = db_next_pharmacy_package_count * piece_num + db_next_pharmacy_piece_count
            #药品
            db_next_goods_change_cost = Decimal(str(next_record['goods_total_cost'])).quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP)
            db_next_goods_piece_count = Decimal(str(next_record['goods_piece_count'] or 0))
            db_next_goods_package_count = Decimal(str(next_record['goods_package_count'] or 0))
            db_next_goods_total_piece_count = db_next_goods_package_count * piece_num + db_next_goods_piece_count

            # 比较计算值和数据库值，如果不同则更新
            if ( cal_total_pharmacy_piece_count != db_next_pharmacy_total_piece_count):

                # 生成备份SQL
                back_update_sql = """
                UPDATE v2_goods_stock_log
                SET pharmacy_total_cost = {pharmacy_total_cost},
                    pharmacy_piece_count = {pharmacy_piece_count},
                    pharmacy_package_count = {pharmacy_package_count}
                WHERE id = {id} 
                    AND chain_id = '{chain_id}'
                    AND organ_id = '{organ_id}';
                """.format(
                    id=next_record['id'],
                    chain_id=next_record['chain_id'],
                    organ_id=next_record['organ_id'],
                    pharmacy_total_cost=db_next_pharmacy_change_cost if db_next_pharmacy_change_cost is not None else 'NULL',
                    pharmacy_piece_count=db_next_pharmacy_piece_count if db_next_pharmacy_piece_count is not None else 'NULL',
                    pharmacy_package_count=db_next_pharmacy_package_count if db_next_pharmacy_package_count is not None else 'NULL'
                )
                
                with open(backup_sql_file, "a") as f:
                    f.write(back_update_sql + "\n")
                # 计算标准小单位数量
                standard_piece_count = cal_total_pharmacy_piece_count % piece_num  # 转换为标准小单位
                standard_package_count = cal_total_pharmacy_piece_count // piece_num  # 转换为标准小单位
                
                update_sql = """
                UPDATE v2_goods_stock_log
                SET pharmacy_total_cost = {pharmacy_total_cost},
                    pharmacy_piece_count = {pharmacy_piece_count},
                    pharmacy_package_count = {pharmacy_package_count}
                WHERE id = {id}
                    AND chain_id = '{chain_id}'
                    AND organ_id = '{organ_id}';
                """.format(
                    id=next_record['id'],
                    chain_id=next_record['chain_id'],
                    organ_id=next_record['organ_id'],
                    pharmacy_total_cost=cal_pharmacy_total_cost,
                    pharmacy_piece_count=standard_piece_count,  # 使用转换后的标准小单位数量
                    pharmacy_package_count=standard_package_count  # 计算包装数量
                )

                logging.info("写入药房修改sql ： %s", update_sql)
                with open(update_sql_file, "a") as f:
                    f.write(update_sql + "\n")
                
            
            # 比较药品计算值和数据库值，如果不同则更新
            if ( cal_total_goods_piece_count != db_next_goods_total_piece_count):
                
                #参考药房的备份sql备份药品的字段
                backup_sql = """
                UPDATE v2_goods_stock_log
                SET goods_total_cost = {goods_total_cost},
                    goods_piece_count = {goods_piece_count},
                    goods_package_count = {goods_package_count}
                WHERE id = {id}
                    AND chain_id = '{chain_id}'
                    AND organ_id = '{organ_id}';
                """.format(
                    id=next_record['id'],
                    chain_id=next_record['chain_id'],
                    organ_id=next_record['organ_id'],
                    goods_total_cost=db_next_goods_change_cost if db_next_goods_change_cost is not None else 'NULL',
                    goods_piece_count=db_next_goods_piece_count if db_next_goods_piece_count is not None else 'NULL',
                    goods_package_count=db_next_goods_package_count if db_next_goods_package_count is not None else 'NULL'
                )
                with open(backup_sql_file, "a") as f:
                    f.write(backup_sql + "\n")

                
                # 计算药品标准小单位数量
                standard_goods_piece_count = cal_total_goods_piece_count % piece_num  # 转换为标准小单位
                standard_goods_package_count = cal_total_goods_piece_count // piece_num  # 转换为标准小单位

                update_sql = """
                UPDATE v2_goods_stock_log
                SET goods_total_cost = {goods_total_cost},
                    goods_piece_count = {goods_piece_count},
                    goods_package_count = {goods_package_count}
                WHERE id = {id}
                    AND chain_id = '{chain_id}'
                    AND organ_id = '{organ_id}';
                """.format(
                    id=next_record['id'],
                    chain_id=next_record['chain_id'],
                    organ_id=next_record['organ_id'],
                    goods_total_cost=cal_goods_cost,
                    goods_piece_count=standard_goods_piece_count,
                    goods_package_count=standard_goods_package_count
                )

                logging.info("写入修改sql ： %s", update_sql)
                with open(update_sql_file, "a") as f:
                    f.write(update_sql + "\n")
            
            index += 1

def main():
    """主函数"""
    # abcRegion = 'HangZhou'
    abcRegion = 'ShangHai'
    ob_client = DBClient(abcRegion, 'ob', 'abc_cis_goods_log', 'prod', False)

    try:
        fix_stock_costs(ob_client, ob_client, ob_client)
        logging.info("修复完成")
    except Exception as e:
        logging.error(f"执行过程中发生错误: {str(e)}")
    finally:
        ob_client.close()

if __name__ == '__main__':
    main()
