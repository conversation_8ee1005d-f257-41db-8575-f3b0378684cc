#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
将 v2_goods_stock_traceable_code_log 表中的历史数据迁移到 v2_goods_stock_traceable_code_log_250208 表
仅保留每个 clinic_id, pharmacy_no, goods_id, action 组合的最近 10 条记录
作者: yinxiaoyang
日期: 2025-02-08
"""

import os
import sys
import argparse
import time
from datetime import datetime

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient

def get_db_client(region_name='ShangHai', env='prod'):
    """获取数据库连接"""
    try:
        return DBClient(region_name, 'abc_cis_mixed', 'abc_cis_property', env, True)
    except Exception as e:
        print(f"[ERROR] 数据库连接失败: {str(e)}")
        sys.exit(1)


def migrate_data(region_name='<PERSON>g<PERSON><PERSON>', env='prod', chain_id=None, batch_size=500):
    """迁移数据的主函数"""
    if not chain_id:
        print(f"[ERROR] 必须指定 chain_id")
        sys.exit(1)

    db_client = get_db_client(region_name, env)

    db_client.execute('''INSERT ignore INTO v2_property_config_item (id, `key`, value, scope, is_deleted, created_by, created,
                                     last_modified_by, last_modified, key_first, key_second, key_third,
                                     key_fourth, key_fifth, v2_scope_id)
values (substr(uuid_short(), 5), 'chainBasic.deepseek.enable', '1', 'chain', 0, '00000000000000000000000000000000',
        now(), '00000000000000000000000000000000', now(), 'chainBasic', 'deepseek', 'enable', null, null,
        '{chainId}') on duplicate key update value = 1;'''.format(chainId=chain_id))

def parse_args():
    parser = argparse.ArgumentParser(description='数据迁移脚本')
    parser.add_argument('--region-name', default='ShangHai', help='区域名称')
    parser.add_argument('--env', default='prod', help='环境(prod/dev/test)')
    parser.add_argument('--chain-id', required=True, help='连锁ID')
    return parser.parse_args()

if __name__ == "__main__":
    args = parse_args()
    try:
        migrate_data(args.region_name, args.env, args.chain_id, 200)
    except Exception as e:
        print(f"[ERROR] chain_id: {args.chain_id} - 程序执行失败: {str(e)}")
        sys.exit(1)
