import logging
import os
import sys

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import argparse
from multizone.db import DBClient
from multizone.es import ESClient
import json
import pkg_resources
from datetime import datetime


# 目录更新
def updateEs(start_region, region_name, cluster, db_name, table_name, select_sql, index_name, env, isNormal=True, no_tunnel=True):
    # 注意：这里使用的是'elasticsearch'作为分发名称
    version = pkg_resources.get_distribution('elasticsearch').version
    logging.info(f'Elasticsearch version: {version}')
    # try:
    db_client = DBClient(region_name, cluster, db_name, env, no_tunnel)
    # 查询所有region
    if start_region:
        sql = f"""SELECT DISTINCT(region) FROM {table_name} WHERE region > '{start_region}';"""
    else:
        sql = f"""SELECT DISTINCT(region) FROM {table_name};"""
    # e.g {region: 'anhui_hefei'}
    region_list = db_client.fetchall(sql)
    # 自己定义region_list，分批刷数据
    if len(region_list) == 0:
        print('region_list is empty')
        return

    for region in region_list:
        region = region['region']
        logging.info(
            'start------> region：{}， env：{}， regionName：{}， cluster：{}， index_name：{}'.format(region, env, region_name,
                                                                                              cluster, index_name))
        writeToEs(region_name, cluster, 'abc_cis_shebao', table_name, select_sql, index_name, env, region, db_client,
                  True, True)
        logging.info('finish------> region：{}'.format(region))


def writeToEs(region_name, cluster, db_name, table_name, select_sql, index_name, env, region, db_client, isNormal=True,
              no_tunnel=True):
    es_cli = ESClient(region_name, env, isNormal, no_tunnel)
    limit = 20000
    offset = 0
    row_id = 0
    while True:
        sql = f"""{select_sql}
             {'and' if select_sql.__contains__('where') else 'where'} region = '{region}' and id > '{row_id}' order by id limit {offset},{limit};"""
        # print(sql)
        rows = db_client.fetchall(sql)
        select_count = len(rows)
        if select_count == 0:
            break
        row_id = rows[len(rows) - 1]['id']

        # rows拆分 一组2000个
        rows2000 = [rows[i:i + 2000] for i in range(0, select_count, 2000)]
        for rows2 in rows2000:
            values = []
            for row in rows2:
                # row['begndate'] = validate_date(row['begndate'])
                # row['enddate'] = validate_date(row['enddate'])
                body = {
                    k: v for k, v in row.items()
                }
                values.append({'index': {'_index': index_name, '_id': str(row['id'])}})
                values.append(json.dumps(body, ensure_ascii=False))
            # 批量更新
            if len(values) == 0:
                print('req_body is empty')
                continue
            # print(values)
            try:
                es_cli.bulkInsert(index_name, values)
            except Exception as e:
                # print("index_name: {} to-es error".format(index_name), e)
                print("index_name: {} to-es error".format(index_name))


def validate_date(date_string):
    try:
        # 尝试将字符串转换为日期对象
        datetime.strptime(date_string, "%Y-%m-%d")
        return date_string  # 转换成功，‌返回原字符串
    except Exception:
        try:
            # 尝试另一种日期格式
            datetime.strptime(date_string, "%Y-%m-%d %H:%M:%S")
            return date_string  # 转换成功，‌返回原字符串
        except Exception:
            return None  # 转换失败，‌返回空字符串


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--regionName', help='ShangHai/HangZhou')
    parser.add_argument('--env', help='环境dev/test/prod')
    parser.add_argument('--type', help='类型-0 限价 1中西成药目录 2耗材目录 3中药饮片目录')
    parser.add_argument('--startRegion', help='从哪个region开始')
    args = parser.parse_args()
    if not args.regionName or not args.env or not args.type:
        parser.print_help()
        sys.exit(-1)

    # if args.type != '1' and args.type != '2' and args.type != '3':
    #     return
    # 目前只有耗材没刷
    if args.type != '2':
        return

    def table_name():
        if args.type == '1':
            return 'shebao_national_dict_drug'
        elif args.type == '2':
            return 'shebao_national_dict_material'
        elif args.type == '3':
            return 'shebao_national_dict_drug_tcm'
        else:
            parser.print_help()
            sys.exit(-1)

    def index_name():
        if args.type == '1':
            return 'v3-{}-shebao-national-dict-drug'.format(args.env)
        elif args.type == '2':
            return 'v3-{}-shebao-national-dict-material'.format(args.env)
        elif args.type == '3':
            return 'v3-{}-shebao-national-dict-drug-tcm'.format(args.env)
        else:
            parser.print_help()
            sys.exit(-1)

    def sql_str():
        if args.type == '1':
            return '''SELECT id,
        region,
        shebao_code,
        trade_name,
        registered_name,
        admin_standard_code,
        dosage_form_code,
        dosage_form_name,
        category_code,
        category_name,
        spec_name,
        spec_code,
        registered_dosage_form,
        registered_spec_name,
        registered_spec_code,
        freq,
        sales_unit,
        otc_flag,
        otc_name,
        pack_material_code,
        pack_material_name,
        pack_spec,
        pack_num,
        route,
        smallest_usage_unit,
        smallest_sales_unit,
        smallest_dosage_unit,
        smallest_pack_num,
        smallest_pack_unit,
        smallest_dosage_form_unit,
        smallest_pack_unit_name,
        smallest_dosage_form_unit_name,
        conversion_ratio,
        expire_date,
        smallest_price_unit,
        manufacture_name,
        unusual_price_limited_flag,
        unusual_flag,
        restricted_range,
        restricted_flag,
        registered_no,
        LOWER(REGEXP_REPLACE(approved_code, '[一-龥]', '')) as approved_code,
        national_remark,
        base_flag_name,
        base_flag,
        remark,
        valid_flag,
        child,
        national_dosage_form,
        national_fee_type,
        DATE_FORMAT(last_modified, "%Y-%m-%dT%H:%i:%s.000+00:00") as last_modified,
        extend_info
  FROM shebao_national_dict_drug'''
        elif args.type == '3':
            sql = '''SELECT id,
         region,
         shebao_code,
         name,
         compound_flag,
         quality_level,
         year,
         used_part,
         safe_dosage,
         usage2,
         xingwei,
         guijing,
         variety,
         valid_flag,
         record_no,
         material_name,
         effect_category,
         seed_source,
         national_policy,
         province_policy,
         standard_name,
         DATE_FORMAT(last_modified, "%Y-%m-%dT%H:%i:%s.000+00:00") as last_modified,
         extend_info,
         if(granules_flag is null, if(name like '%颗粒' or length(shebao_code) = 20, 1, 0), granules_flag) as granules_flag,
         regexp_substr(replace(extend_info, ' ', ''), '(?<="key":"药品企业","value":")[^"]+') as manufacture_name
  FROM shebao_national_dict_drug_tcm'''
            return sql
        elif args.type == '2':
            return '''SELECT id,
                region,
                shebao_code,
                name,
                yi_bao_tong_yong_ming,
                chan_pin_xing_hao,
                gui_ge_dai_ma,
                gui_ge,
                hao_cai_fen_lei,
                gui_ge_xing_hao,
                bao_zhuang_gui_ge,
                bao_zhuang_shu_liang,
                chan_pin_bao_zhuang_cai_zhi,
                bao_zhuang_dan_wei,
                chan_pin_zhuan_huan_bi,
                zui_xiao_shi_yong_dan_wei,
                chan_pin_biao_zhun,
                xian_zhi_shi_yong_biao_zhi,
                yi_bao_xian_yong_fan_wei,
                zui_xiao_xiao_shou_dan_wei,
                LOWER(REGEXP_REPLACE(zhu_ce_bei_an_hao, '[一-龥]', '')) as zhu_ce_bei_an_hao,
                zhu_ce_bei_an_hao as zhu_ce_bei_an_hao_original,
                zhu_ce_bei_an_chan_pin_ming_cheng,
                sheng_chan_qi_ye_ming_cheng,
                wei_yi_ji_lu_hao,
                DATE_FORMAT(last_modified, "%Y-%m-%dT%H:%i:%s.000+00:00") as last_modified,
                extend_info
  FROM shebao_national_dict_material'''
        else:
            parser.print_help()
            sys.exit(-1)

    updateEs(args.startRegion, args.regionName, 'ob', 'abc_cis_shebao', table_name(), sql_str(), index_name(), args.env, True, True)


#   updateEs('ShangHai', 'ob', 'abc_cis_shebao', 'shebao_national_dict_drug', '''SELECT id,
#       region,
#       shebao_code,
#       trade_name,
#       registered_name,
#       admin_standard_code,
#       dosage_form_code,
#       dosage_form_name,
#       category_code,
#       category_name,
#       spec_name,
#       spec_code,
#       registered_dosage_form,
#       registered_spec_name,
#       registered_spec_code,
#       freq,
#       sales_unit,
#       otc_flag,
#       otc_name,
#       pack_material_code,
#       pack_material_name,
#       pack_spec,
#       pack_num,
#       route,
#       smallest_usage_unit,
#       smallest_sales_unit,
#       smallest_dosage_unit,
#       smallest_pack_num,
#       smallest_pack_unit,
#       smallest_dosage_form_unit,
#       smallest_pack_unit_name,
#       smallest_dosage_form_unit_name,
#       conversion_ratio,
#       expire_date,
#       smallest_price_unit,
#       manufacture_name,
#       unusual_price_limited_flag,
#       unusual_flag,
#       restricted_range,
#       restricted_flag,
#       registered_no,
#       LOWER(REGEXP_REPLACE(approved_code, '[一-龥]', '')) as approved_code,
#       national_remark,
#       base_flag_name,
#       base_flag,
#       remark,
#       valid_flag,
#       child,
#       national_dosage_form,
#       national_fee_type,
#       DATE_FORMAT(last_modified, "%Y-%m-%dT%H:%i:%s.000+00:00") as last_modified,
#       extend_info
# FROM shebao_national_dict_drug''', 'v3-dev-shebao-national-dict-drug', 'dev', True, True)
#     updateEs(None ,'ShangHai', 'ob', 'abc_cis_shebao', 'shebao_national_dict_material', '''SELECT id,
#               region,
#               shebao_code,
#               name,
#               yi_bao_tong_yong_ming,
#               chan_pin_xing_hao,
#               gui_ge_dai_ma,
#               gui_ge,
#               hao_cai_fen_lei,
#               gui_ge_xing_hao,
#               bao_zhuang_gui_ge,
#               bao_zhuang_shu_liang,
#               chan_pin_bao_zhuang_cai_zhi,
#               bao_zhuang_dan_wei,
#               chan_pin_zhuan_huan_bi,
#               zui_xiao_shi_yong_dan_wei,
#               chan_pin_biao_zhun,
#               xian_zhi_shi_yong_biao_zhi,
#               yi_bao_xian_yong_fan_wei,
#               zui_xiao_xiao_shou_dan_wei,
#               LOWER(REGEXP_REPLACE(zhu_ce_bei_an_hao, '[一-龥]', '')) as zhu_ce_bei_an_hao,
#               zhu_ce_bei_an_hao as zhu_ce_bei_an_hao_original,
#               zhu_ce_bei_an_chan_pin_ming_cheng,
#               sheng_chan_qi_ye_ming_cheng,
#               wei_yi_ji_lu_hao,
#               DATE_FORMAT(last_modified, "%Y-%m-%dT%H:%i:%s.000+00:00") as last_modified,
#               extend_info
#     FROM shebao_national_dict_material''', 'v3-dev-shebao-national-dict-material', 'dev', True, True)
#
#   updateEs('ShangHai', 'ob', 'abc_cis_shebao', 'shebao_national_dict_drug_tcm', '''SELECT id,
#        region,
#        shebao_code,
#        name,
#        compound_flag,
#        quality_level,
#        year,
#        used_part,
#        safe_dosage,
#        usage2,
#        xingwei,
#        guijing,
#        variety,
#        valid_flag,
#        record_no,
#        material_name,
#        effect_category,
#        seed_source,
#        national_policy,
#        province_policy,
#        standard_name,
#        DATE_FORMAT(last_modified, "%Y-%m-%dT%H:%i:%s.000+00:00") as last_modified,
#        extend_info,
#        granules_flag,
#        regexp_substr(replace(extend_info, ' ', ''), '(?<="key":"药品企业","value":")[^"]+') as manufacture_name
# FROM shebao_national_dict_drug_tcm''', 'v3-dev-shebao-national-dict-drug-tcm', 'dev', True, True)


if __name__ == '__main__':
    main()
