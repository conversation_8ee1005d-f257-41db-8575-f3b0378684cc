"""
重新计算 goods_log 的 origin_flat_price 字段
"""
import argparse
import os
import sys
from datetime import datetime, timedelta
from decimal import Decimal, ROUND_HALF_UP


CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.db import DBClient
from scripts.common.utils.sqls import SqlUtils

class UpdateData:
    chain_id = None
    goods_ob_client = None

    def __init__(self, region_name, chain_id, env):
        self.chain_id = chain_id
        self.region_name = region_name
        env = 'prod' if not env else env
        self.goods_ob_client = DBClient(self.region_name, 'ob', 'abc_cis_goods_log', env, True)
        self.goods_write_client = DBClient(self.region_name, 'abc_cis_stock_zip', 'abc_cis_goods_log', env, True)

    def run(self, begin_time, end_time):
        cursor = '0'
        while True:
            # 查询指定日期的存在问题的进销存数据
            goods_log_bats = self.goods_ob_client.fetchall("""
                select bat_id
                from (select distinct bat_id
                      from v2_goods_stock_log
                      where created_date between '{begin_time}' and '{end_time}'
                        and chain_id = '{chain_id}'
                        and origin_total_price != 0
                        and origin_flat_price = 0
                        and action in ('发药')
                      and bat_id > '{cursor}'
                      ) t
                order by bat_id
                limit 100;
            """.format(chain_id=self.chain_id, begin_time=begin_time, end_time=end_time, cursor=cursor))

            if not goods_log_bats:
                break

            bat_ids = [goods_log_bat['bat_id'] for goods_log_bat in goods_log_bats]
            cursor = bat_ids[-1]

            # 查询 bat_id 的所有进销存数据
            bat_id_in_str = SqlUtils.to_in_value(bat_ids)
            all_goods_logs = self.goods_ob_client.fetchall("""
                select id, bat_id, origin_total_price, piece_count, package_count, piece_num
                from v2_goods_stock_log
                where bat_id in ({bat_id_in_str});
            """.format(bat_id_in_str=bat_id_in_str))

            if not all_goods_logs:
                continue

            # 按照 bat_id 分组
            goods_logs_group_by_bat_id = {}
            for goods_log in all_goods_logs:
                if goods_log['bat_id'] not in goods_logs_group_by_bat_id:
                    goods_logs_group_by_bat_id[goods_log['bat_id']] = []
                goods_logs_group_by_bat_id[goods_log['bat_id']].append(goods_log)

            # 每个组中计算 origin_flat_price
            for bat_id, bat_goods_logs in goods_logs_group_by_bat_id.items():
                target_total_price = bat_goods_logs[0]['origin_total_price']
                if len(bat_goods_logs) > 1:
                    total_count = abs(sum([goods_log['piece_count'] + goods_log['package_count'] * Decimal(goods_log['piece_num']) for goods_log in bat_goods_logs]))
                    left_price = target_total_price
                    for goods_log in bat_goods_logs:
                        flat_price = ((abs(goods_log['piece_count'] + goods_log['package_count'] * Decimal(goods_log['piece_num'])) / total_count * target_total_price)
                                      .quantize(Decimal('0.00'), rounding=ROUND_HALF_UP))
                        flat_price = min(flat_price, left_price)
                        goods_log['origin_flat_price'] = flat_price
                        left_price -= flat_price
                    # 将剩余的价格加到最后一个商品上
                    bat_goods_logs[-1]['origin_flat_price'] += left_price
                else:
                    bat_goods_logs[0]['origin_flat_price'] = target_total_price

                for goods_log in bat_goods_logs:
                    sql = ("""update v2_goods_stock_log set origin_flat_price = {origin_flat_price} where id = {id};"""
                           .format(origin_flat_price=goods_log['origin_flat_price'], id=goods_log['id']))
                    if len(bat_goods_logs) > 1:
                        print(sql)
                    self.goods_write_client.execute(sql)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    updateData = UpdateData(args.region_name, args.chain_id, args.env)
    begin_time = datetime.strptime('2024-10-01 00:00:00', '%Y-%m-%d %H:%M:%S')
    end_time = datetime.strptime('2024-10-29 00:00:00', '%Y-%m-%d %H:%M:%S')
    while begin_time < end_time:
        current_begin_time = begin_time
        current_end_date = current_begin_time + timedelta(hours=2)
        updateData.run(begin_time, end_time)
        begin_time = current_end_date


if __name__ == '__main__':
    main()
