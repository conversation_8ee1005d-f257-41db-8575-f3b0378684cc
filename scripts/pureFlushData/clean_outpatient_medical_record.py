"""
刷新formIsDeleted 的数据
"""
import argparse
import os
import sys
from datetime import datetime, timedelta

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient


medical_record_sql = '''
select a.id
from v2_outpatient_medical_record a
         inner join v2_outpatient_sheet b
                    on a.outpatient_sheet_id = b.id and a.clinic_id = b.clinic_id
where a.is_deleted = 0
  and b.is_deleted = 1
  and a.chain_id = '{chainId}'
  and b.chain_id = '{chainId}';
'''

medical_record_del_sql = '''
update v2_outpatient_medical_record a
set a.is_deleted = 1
where a.id in ({ids});
'''

medical_record_attachemnt_del_sql = '''
update v2_outpatient_medical_record_attachment a
set a.is_deleted = 1
where a.medical_record_id in ({ids});
'''


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()

    obClient = DBClient(args.region_name, 'ob', 'abc_cis_outpatient', args.env, True)
    outpatientClient = DBClient(args.region_name, 'abc_cis_outpatient', 'abc_cis_outpatient', args.env, True)

    # 获取要删除的medicalRecordIds
    medicalRecordIds = obClient.fetchall(medical_record_sql.format(chainId=args.chain_id))
    print(f"Processing chainId: {args.chain_id}, 处理：{len(medicalRecordIds)}")
    
    # split medicalRecordIds into chunks
    chunk_size = 200
    chunks = [medicalRecordIds[i:i + chunk_size] for i in range(0, len(medicalRecordIds), chunk_size)]
    for chunk in chunks:
        # Extract IDs from objects and format them
        formatted_medicalRecordIds = ','.join(f"'{record['id']}'" for record in chunk)
        # delete medical records
        allRows = outpatientClient.execute(medical_record_del_sql.format(ids=formatted_medicalRecordIds))
        print(f"medical records Processing chainId: {args.chain_id}, 处理：{allRows}, sql: {medical_record_del_sql.format(ids=formatted_medicalRecordIds)}")

        # delete medical record attachments
        allRows1 = outpatientClient.execute(medical_record_attachemnt_del_sql.format(ids=formatted_medicalRecordIds))
        print(f"medical record attachments Processing chainId : {args.chain_id}, 处理：{allRows1}")


if __name__ == '__main__':
    main()
