"""
SCRM 优化
patient_clinic: 新增患者首诊医生、首次收费时间
patient: 患者最近就诊医生
"""
import argparse
import concurrent.futures
import os
import sys
import threading
from queue import Queue

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.db import DBClient
from scripts.common.utils.lists import ListUtils

logger = None

# 创建一个任务队列
task_queue = Queue()

# 创建一个线程池
worker_count = 8
executor = concurrent.futures.ThreadPoolExecutor(max_workers=worker_count)

# 创建一个线程局部数据对象
thread_local_data = threading.local()


class DbClientHolder:
    patient_wdb_client = None
    ob_client = None
    env = None

    def __init__(self, env, region_name):
        self.env = env
        self.region_name = region_name
        self.patient_wdb_client = DBClient(self.region_name, 'abc_cis_account_base', 'abc_cis_patient', self.env, True)
        self.ob_client = DBClient(self.region_name, 'ob', 'abc_cis_outpatient', self.env, True)


def execute_sql(db_client, sql):
    # print(sql)
    db_client.execute(sql)


def get_db_client(env, region_name):
    # 检查线程局部数据中是否已经有一个数据库连接
    db_client_holder = getattr(thread_local_data, 'db_client', None)
    # 如果没有，创建一个新的连接并存储在线程局部数据中
    if db_client_holder is None:
        db_client_holder = DbClientHolder(env, region_name)
        setattr(thread_local_data, 'db_client', db_client_holder)
    # 返回数据库连接
    return db_client_holder


class Scheduler:
    chain_id = None
    patient_ob_client = None
    env = None
    region_name = None

    def __init__(self, env, region_name, chain_id):
        self.env = env
        self.chain_id = chain_id
        self.region_name = region_name
        self.patient_ob_client = DBClient(self.region_name, 'ob', 'abc_cis_patient', self.env, True)

    def run(self):
        for i in range(worker_count):
            executor.submit(worker, self.env, self.region_name, self.chain_id)

        cursor = "00000000000000000000000000000000"
        size = 250
        while True:
            # 先分页遍历患者
            patients = self.patient_ob_client.fetchall("""
                select id
                from v2_patient
                where chain_id = '{chainId}' and status = 1 and id > '{cursor}'
                order by id
                limit {size}
            """.format(chainId=self.chain_id, cursor=cursor, size=size))
            if not patients:
                break
            cursor = patients[-1]['id']

            patient_ids = ListUtils.dist_mapping(patients, lambda patient: patient['id'])
            task_queue.put((patient_ids))

        task_queue.join()

        # 停止工作线程
        for i in range(worker_count):
            task_queue.put(None)


def update_patient_clinic(chain_id, patient_ids, db_client_holder):
    # 将 patient_ids 转为逗号分隔的字符串
    patient_ids_str = ','.join([f"'{pid}'" for pid in patient_ids])

    # 查询最小门诊时间
    outpatient_query = f"""
    SELECT clinic_id, patient_id, MIN(diagnosed_date) as first_outpatient_date
    FROM abc_cis_outpatient.v2_outpatient_sheet
    WHERE patient_id IN ({patient_ids_str})
    AND status > 0
    AND is_deleted = 0
    GROUP BY clinic_id, patient_id;
    """

    # 查询最小收费时间
    charge_query = f"""
    SELECT clinic_id, patient_id, MIN(IF(type != 1, charged_time, NULL)) as first_charge_date
    FROM abc_cis_charge.v2_charge_sheet
    WHERE patient_id IN ({patient_ids_str})
    AND patient_order_id IS NOT NULL
    AND charged_time IS NOT NULL
    AND is_deleted = 0
    GROUP BY clinic_id, patient_id;
    """

    # 执行查询获取结果
    outpatient_results = db_client_holder.ob_client.fetchall(outpatient_query)
    charge_results = db_client_holder.ob_client.fetchall(charge_query)

    # 将查询结果组织为字典格式，方便后续操作
    outpatient_dict = {(row['clinic_id'], row['patient_id']): row['first_outpatient_date'] for row in outpatient_results}
    charge_dict = {(row['clinic_id'], row['patient_id']): row['first_charge_date'] for row in charge_results}

    # 准备插入数据的值部分
    values = []
    for key in set(outpatient_dict.keys()).union(charge_dict.keys()):
        clinic_id, patient_id = key
        first_outpatient_date = outpatient_dict.get(key, 'NULL')
        first_charge_date = charge_dict.get(key, 'NULL')

        # 如果日期为NULL则不插入日期值
        first_outpatient_date = f"'{first_outpatient_date}'" if first_outpatient_date != 'NULL' else 'NULL'
        first_charge_date = f"'{first_charge_date}'" if first_charge_date and first_charge_date != 'NULL' else 'NULL'

        # 32个0表示默认值
        values.append(f"('{clinic_id}', '{chain_id}', '{patient_id}', {first_outpatient_date}, {first_charge_date}, '00000000000000000000000000000000', '00000000000000000000000000000000')")

    # 如果没有值则不继续执行插入
    if not values:
        info("No records to insert.")
        return

    # 拼接插入语句
    insert_query = f"""
        INSERT INTO v2_patient_clinic (clinic_id, chain_id, patient_id, first_outpatient_date, first_charge_date, created_by, last_modified_by)
        VALUES {','.join(values)}
        ON DUPLICATE KEY UPDATE
            first_outpatient_date = VALUES(first_outpatient_date),
            first_charge_date = VALUES(first_charge_date);
        """

    # 执行插入/更新
    execute_sql(db_client_holder.patient_wdb_client, insert_query)


def worker(env, region_name, chain_id):
    db_client_holder = None
    while True:
        task = task_queue.get()
        if task is None:
            break
        patient_ids = task
        if not patient_ids:
            task_queue.task_done()
            break

        if db_client_holder is None:
            db_client_holder = get_db_client(env, region_name)

        # 刷患者首诊医生和时间
        update_patient_clinic(chain_id, patient_ids, db_client_holder)
        info(f"{chain_id} 处理完成 {len(patient_ids)} rows")
        task_queue.task_done()


def info(msg, *args, **kwargs):
    if logger:
        logger.info(msg, *args, **kwargs)
    else:
        print(msg)


def error(msg, *args, **kwargs):
    if logger:
        logger.error(msg, *args, **kwargs)
    else:
        print(msg)


def warn(msg, *args, **kwargs):
    if logger:
        logger.warn(msg, *args, **kwargs)
    else:
        print(msg)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    scheduler = Scheduler(args.env, args.region_name, args.chain_id)
    scheduler.run()


if __name__ == '__main__':
    main()
