"""
修复 goods shortId 重复的问题
1. 将 goods 表中有的 shortId 但是 goods_gen_id 表中没有该 shortId 的数据插入到 goods_gen_id 表中
2. 为 shortId 重复的数据重新生成 shortId
"""
import argparse
import os
import sys

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.db import DBClient

MAX_SHORT_ID = 999999999999999


class UpdateData:
    chain_id = None
    goods_ob_client = None

    def __init__(self, region_name, chain_id, env):
        self.chain_id = chain_id
        self.region_name = region_name
        env = 'prod' if not env else env
        self.goods_ob_client = DBClient(self.region_name, 'ob', 'abc_cis_goods', env, True)
        self.goods_write_client = DBClient(self.region_name, 'abc_cis_stock', 'abc_cis_goods', env, True)

    def run(self):
        # 1. 将 goods 表中有的 shortId 但是 goods_gen_id 表中没有该 shortId 的数据插入到 goods_gen_id 表中
        self.complete_goods_gen_id_data()

        # 2. 找到所有 shortId 重复的 goods 数据
        self.replace_duplicate_short_id_goods()

    def complete_goods_gen_id_data(self):
        not_exist_in_gen_id_goods_list = self.goods_ob_client.fetchall("""
            select /*+ query_timeout(180000000)*/ distinct a.short_id
            from v2_goods a
                     left join v2_goods_gen_id b on (a.organ_id = b.clinic_id and CAST(a.short_id AS SIGNED) = b.gen_id)
            where a.organ_id = '{chainId}'
              and a.short_id is not null
              and a.short_id != ''
              and CAST(a.short_id AS SIGNED) is not null
              and CAST(a.short_id AS SIGNED) <= {maxShortId}
              and b.id is null
        """.format(chainId=self.chain_id, maxShortId=MAX_SHORT_ID))

        if not not_exist_in_gen_id_goods_list:
            print(f"门店: {self.chain_id} 没有 shortId 在 goods_gen_id 表中不存在的数据")
            return

        total_count = len(not_exist_in_gen_id_goods_list)
        success_count = 0
        out_of_range_count = 0
        for goods in not_exist_in_gen_id_goods_list:
            # 转换为 number
            try:
                short_id = int(goods['short_id'])
                if short_id > MAX_SHORT_ID or short_id < 0:
                    out_of_range_count += 1
                    continue
                self.goods_write_client.execute("""
                    INSERT INTO v2_goods_gen_id (type, clinic_id, gen_id) VALUES (1, '{clinicId}', {shortId});
                """.format(clinicId=self.chain_id, shortId=short_id))
                success_count += 1
            except Exception as e:
                # print("shortId 转换为 number 失败", e)
                # 因为 shortId 是唯一索引，所以可能会插入失败，忽略错误
                continue

        print(f"{self.chain_id}, 共有{total_count}条需要插入 gen_id 的数据, 成功插入{success_count}条数据, shortId 超出范围的数据有{out_of_range_count}条")

    def replace_duplicate_short_id_goods(self):
        exist_duplicate_short_id_goods_list = self.goods_ob_client.fetchall("""
            select /*+ query_timeout(180000000)*/ short_id, group_concat(id order by id) as goods_id_str
            from v2_goods
            where status <> 99
              and short_id is not null
              and short_id != ''
              and organ_id = '{chainId}'
            group by short_id
            having count(1) > 1;
        """.format(chainId=self.chain_id))

        if not exist_duplicate_short_id_goods_list:
            print(f"{self.chain_id} 没有 shortId 重复的 goods 数据")
            return

        total_count = 0
        success_count = 0
        for goods in exist_duplicate_short_id_goods_list:
            goods_id_str = goods['goods_id_str']
            goods_ids = goods_id_str.split(',')
            # 忽略第一条，为后面的数据重新生成 shortId
            total_count += len(goods_ids) - 1
            for goods_id in goods_ids[1:]:
                auto_inc_max_short_id = self.goods_write_client.fetchone("""
                    SELECT max(gen_id) as short_id
                    FROM v2_goods_gen_id
                    WHERE clinic_id = '{chainId}' AND type = 1
                """.format(chainId=self.chain_id))

                if not auto_inc_max_short_id:
                    auto_inc_max_short_id = 1
                else:
                    auto_inc_max_short_id = auto_inc_max_short_id['short_id'] + 1

                if auto_inc_max_short_id > MAX_SHORT_ID:
                    # 填坑逻辑
                    auto_inc_max_short_id = self.goods_write_client.fetchone("""
                        select MAX(count) as count 
                        FROM (
                           SELECT  (@i:=@i+1) as i,short_id_n as count
                           FROM (
                                   SELECT gen_id as short_id_n 
                                   FROM v2_goods_gen_id
                                   WHERE   clinic_id = '{chainId}' AND  type = 1
                                   ORDER BY short_id_n ASC 
                                            ) as a,
                               (   SELECT @i:=0) AS it
                           WHERE (@i+1) >= short_id_n  AND @i <= short_id_n ) 
                        AS x
                    """.format(chainId=self.chain_id))

                    if not auto_inc_max_short_id:
                        auto_inc_max_short_id = 1
                    else:
                        auto_inc_max_short_id = int(auto_inc_max_short_id['count']) + 1

                if auto_inc_max_short_id > MAX_SHORT_ID:
                    print(f"{self.chain_id} shortId 超出范围")
                    break

                # 不足 6 位的前面补 0
                auto_inc_max_short_id_str = str(auto_inc_max_short_id).zfill(6)
                self.goods_write_client.executemany([
                    # 插入 shortId 到 goods_gen_id 表
                    """
                    INSERT INTO v2_goods_gen_id (type, clinic_id, gen_id) VALUES (1, '{clinicId}', {shortId});
                    """.format(clinicId=self.chain_id, shortId=auto_inc_max_short_id),
                    # 反写 goods 表的 shortId
                    """
                    UPDATE v2_goods SET short_id = '{shortId}' WHERE id = '{goodsId}';
                    """.format(shortId=auto_inc_max_short_id_str, goodsId=goods_id)
                ])
                success_count += 1

        print(f"{self.chain_id}, 共有{total_count}条 shortId 重复的数据, 成功重新生成 shortId 的数据有{success_count}条")


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    updateData = UpdateData(args.region_name, args.chain_id, args.env)
    try:
        updateData.run()
    except Exception as e:
        print(f"{args.chain_id} 修复 goods shortId 重复的问题失败: {e}")
        raise e


if __name__ == '__main__':
    main()
