#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.rpc import regionRpcHost
import json
import argparse
import requests


# 多库房把以前老的药房类型刷到新的标上
def flushData(abcRegion, chain_id):
    charge_db_client = DBClient(abcRegion, 'abc_cis_charge', 'abc_cis_charge', 'prod', True)
    ob_client = DBClient(abcRegion, 'ob', 'abc_cis_charge', 'prod', True)

    updateRecordParentSqls = ob_client.fetchall("""select /*+ parallel(4) */
    concat('update v2_charge_form_item set name = ''', a.doctor_name, ''', product_snapshot = JSON_SET(product_snapshot, ''$.doctorId'', ''', a.doctor_id,''', ''$.departmentId'', ''', a.department_id, ''', ''$.doctorName'', ''', a.doctor_name ,''', ''$.departmentName'', ''', a.department_name, ''') where id = ''', b.id,''';') as updateSql
            from abc_cis_registration.v2_registration_form_item a
         inner join abc_cis_charge.v2_charge_form_item b on a.id = b.source_form_item_id and b.product_type = 5 and b.status = 1
                                                                and a.last_modified > '2025-01-03 19:00:00'
                                                                and a.chain_id = '{chainId}'
         where a.last_modified > '2025-01-03 19:00:00'
           and b.product_type = 5 and b.status = 1
           and a.chain_id = '{chainId}'
and (a.doctor_id != replace(json_extract(b.product_snapshot, '$.doctorId'), '"', '') or a.department_id!= replace(json_extract(b.product_snapshot, '$.departmentId'), '"', ''))""".format(
        chainId=chain_id))

    if (len(updateRecordParentSqls) != 0):
        for row2 in updateRecordParentSqls:
            sql = row2['updateSql']
            charge_db_client.execute(sql)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    flushData(args.region_name, args.chain_id)


if __name__ == '__main__':
    main()
