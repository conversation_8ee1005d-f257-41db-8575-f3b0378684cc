import argparse
import json
import os
import sys
import time
import arrow

import pandas as pd

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient


class SnapshotDataRecover:
    chain_id = None
    basic_db_client = None

    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_stock_zip', 'abc_cis_goods_log', 'prod', True)
        self.end_date = arrow.get(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())).shift(minutes=-10).format('YYYY-MM-DD HH:mm:ss')

    def run(self):
        self.flush_snapshot_data()

    def flush_snapshot_data(self):
        if self.chain_id:
            find_lack_data_sql = '''
            select /*+ max_execution_time(300000)*/ chain_id, organ_id, goods_id
            from abc_cis_goods_log.v2_goods_stock_log
            where created_date < '{0}' and chain_id = '{1}' and created_date >= '2024-01-01 00:00:00'
            group by chain_id, organ_id, goods_id
            '''.format(self.end_date, self.chain_id)
        else:
            find_lack_data_sql = '''
            select /*+ max_execution_time(300000)*/ chain_id, organ_id, goods_id
            from abc_cis_goods_log.v2_goods_stock_log log
                     left join abc_cis_goods_log.v2_goods_stock_log_goods_snapshot snap on log.id = snap.id
            where log.created_date < '{0}'
              and snap.id is null
            group by chain_id, organ_id, goods_id
            '''.format(self.end_date)
        rows = self.basic_db_client.fetchall(find_lack_data_sql)
        lack_data_df = pd.DataFrame(rows, columns=['chain_id', 'organ_id', 'goods_id'])
        for index, row in lack_data_df.iterrows():
            # 查询所有的进销存记录
            query_record_sql = '''
            select id,
               stock_id,
               ifnull(pharmacy_no, 0) as pharmacy_no,
               action,
               piece_num,
               piece_count,
               package_count,
               stock_piece_count,
               stock_package_count
            from abc_cis_goods_log.v2_goods_stock_log
            where chain_id = '{0}'
              and organ_id = '{1}'
              and goods_id = '{2}'
              and created_date < '{3}'
            order by created_date, id
            '''.format(row['chain_id'], row['organ_id'], row['goods_id'], self.end_date)
            record_rows = self.basic_db_client.fetchall(query_record_sql)
            all_record_df = pd.DataFrame(record_rows, columns=['id', 'stock_id', 'pharmacy_no', 'action', 'piece_num',
                                                               'piece_count', 'package_count', 'stock_piece_count',
                                                               'stock_package_count'])
            # 从第一条开始刷snapshot，如果是采购入库或者是调拨入库，则会产生一个新的批次号，此时snapshot中的批次增加，
            # 如果进销存log中的批次剩余库存为0，则该批次移除
            # 如果已有快照，则直接忽略
            stock_add = set()
            stocks = list()
            snap = {'stocks': stocks}
            for j, col in all_record_df.iterrows():
                if '采购入库' == col['action'] or '调拨入库' == col['action'] or col['stock_id'] not in stock_add:
                    stock_add.add(col['stock_id'])
                    batch = {'stockId': int(col['stock_id']), 'pharmacy_no': int(col['pharmacy_no']),
                             'pieceCount': float(col['stock_piece_count']),
                             'packageCount': float(col['stock_package_count'])}
                    stocks.append(batch)
                else:
                    for s in stocks:
                        if s['stockId'] == int(col['stock_id']):
                            s['pharmacy_no'] = int(col['pharmacy_no'])
                            s['pieceCount'] = float(col['stock_piece_count'])
                            s['packageCount'] = float(col['stock_package_count'])
                goods_snap = json.dumps(snap)
                insert_snap_sql = '''
                            INSERT IGNORE INTO abc_cis_goods_log.v2_goods_stock_log_goods_snapshot(id, piece_num, goods_snap, created) 
                            VALUES'''
                insert_snap_sql = insert_snap_sql + '''({0}, {1}, '{2}', '1999-02-01 00:00:00')'''\
                    .format(col['id'], col['piece_num'], goods_snap)
                print(insert_snap_sql)
                self.basic_db_client.execute(insert_snap_sql)


# 刷进销存数据
class CleanGoodsStockLogCost:
    chain_id = None
    goods_log_db_client = None
    adb_client = None

    def __init__(self, region_name, chain_id):
        self.env = 'prod'
        self.chain_id = chain_id
        self.region_name = region_name
        self.date_after = '2024-03-01'
        self.date_now = '2024-05-08'
        self.end_date = arrow.get(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())).shift(minutes=-10).format('YYYY-MM-DD HH:mm:ss')
        # self.goods_log_db_client = DBClient(self.region_name, 'abc_cis_stock_zip', 'abc_cis_goods_log', self.env, True)
        self.goods_log_db_client = DBClient(self.region_name, 'abc_cis_stock_zip', 'abc_cis_goods_log', self.env, True)
        self.adb_client = DBClient(self.region_name, 'adb', 'abc_cis_goods', self.env, True)

    def run(self, goodsId):
        try:
            self.findOutTheReflushGoodsIdListV2(goodsId)
        except Exception as e:
            print("2id:", self.chain_id, ' error:: ', str(e))
            raise e

    def findOutTheReflushGoodsIdList(self):
        sqlFindInOrderLog = """
        select distinct goods_id as goodsId
                from abc_cis_goods_log.v2_goods_stock_log
                where chain_id = '{chainId}'
                    and created_date >= '{dateAfter}'
                    and created_date <= '{dateNow}'
                    and pharmacy_total_cost < 0
                     ;
                    """ \
            .format(chainId=self.chain_id, dateAfter=self.date_after, dateNow=self.date_now)
        goods_sql_res = self.adb_client.fetchall(sqlFindInOrderLog)
        for stmt in goods_sql_res:
            goodsId = stmt['goodsId']
            sqlClearGOodsStockLogField = """ update abc_cis_goods_log.v2_goods_stock_log
                            set pharmacy_type          = null,
                                stock_change_cost      = null,
                                stock_total_cost       = null,
                                pharmacy_piece_count   = null,
                                pharmacy_package_count = null,
                                pharmacy_total_cost= null,
                                batch_piece_count      = null,
                                batch_package_count    = null,
                                batch_total_cost       = null,
                                goods_total_cost       = null,
                                last_modified          = null
                            where goods_id = '{goodsId}'
                              and created_date >= '{dateAfter}'
                              and created_date <= '{dateNow}' ;
                               """ \
                .format(chainId=self.chain_id, dateAfter=self.date_after, dateNow=self.date_now,goodsId=goodsId)
            print(sqlClearGOodsStockLogField)
            self.goods_log_db_client.execute(sqlClearGOodsStockLogField)
            self.reflushStockInfoIntoLog(goodsId)
            self.flush_snapshot_data(goodsId)

    def findOutTheReflushGoodsIdListV2(self, goodsId):
        sqlClearGOodsStockLogField = """ update abc_cis_goods_log.v2_goods_stock_log
                        set pharmacy_type          = null,
                            stock_change_cost      = null,
                            stock_total_cost       = null,
                            pharmacy_piece_count   = null,
                            pharmacy_package_count = null,
                            pharmacy_total_cost= null,
                            batch_piece_count      = null,
                            batch_package_count    = null,
                            batch_total_cost       = null,
                            goods_total_cost       = null,
                            last_modified          = null
                        where goods_id = '{goodsId}'
                          and created_date >= '{dateAfter}'
                          and created_date <= '{dateNow}' ;
                           """ \
            .format(chainId=self.chain_id, dateAfter=self.date_after, dateNow=self.date_now,goodsId=goodsId)
        print(sqlClearGOodsStockLogField)
        self.goods_log_db_client.execute(sqlClearGOodsStockLogField)
        self.reflushStockInfoIntoLog(goodsId)
        self.flush_snapshot_data(goodsId)

    def reflushStockInfoIntoLog(self, goodsId):
        sqlFindInOrderLog = """
            select stock_id as stockId,  goods_id as goodsId, id as logId,piece_num as pieceNum
            from abc_cis_goods_log.v2_goods_stock_log as a
            where a.created_date >= '{dateAfter}'
                and a.created_date <= '{dateNow}'
                and a.chain_id = '{chainId}'
                and a.goods_id ='{goodsId}'
                and a.action = '采购入库';""" \
            .format(chainId=self.chain_id, dateAfter=self.date_after, dateNow=self.date_now, goodsId=goodsId)
        goods_sql_res = self.adb_client.fetchall(sqlFindInOrderLog)
        for stmt in goods_sql_res:
            inOrderStockId = stmt['stockId']
            goodsId = stmt['goodsId']
            logId = stmt['logId']
            replayGoodsStock = {}
            replayGoodsStock['pieceNum'] = stmt['pieceNum']
            replayGoodsStock['replayGoodsStockList'] = []
            sqlFindStock = """select 
                    id as stockId,
                    batch_id as batchId,
                    organ_id as clinicId,
                    pharmacy_no as pharmacyNo,
                    package_cost_price as packageCostPrice, 
                    pharmacy_type as pharmacyType
                from abc_cis_goods.v2_goods_stock
                where chain_id ='{chainId}' and goods_id = '{goodsId}'
                and id < '{stockId}';""" \
                .format(chainId=self.chain_id, goodsId=goodsId, stockId=inOrderStockId)
            stock_sql_res = self.adb_client.fetchall(sqlFindStock)
            for stmtStock in stock_sql_res:
                try:
                    replayGoodsStockItem = {}
                    replayGoodsStockItem['stockId'] = stmtStock['stockId']
                    replayGoodsStockItem['batchId'] = stmtStock['batchId']
                    replayGoodsStockItem['pharmacyNo'] = stmtStock['pharmacyNo']
                    replayGoodsStockItem['clinicId'] = stmtStock['clinicId']
                    replayGoodsStockItem['pharmacyType'] = stmtStock['pharmacyType']
                    replayGoodsStockItem['packageCostPrice'] = float(stmtStock['packageCostPrice'])
                    sqlStockBefore = """select id, 
                                    stock_package_count as  packageCount, 
                                    stock_piece_count as pieceCount , 
                                    package_cost_price as packageCostPrice, 
                                    stock_id as stockId
                                from abc_cis_goods_log.v2_goods_stock_log as a
                                where chain_id ='{chainId}' and stock_id = '{stockId}'
                                and id < '{logId}'
                                order by id desc
                            limit 1""" \
                        .format(chainId=self.chain_id, logId=logId, stockId=stmtStock['stockId'])
                    before_stock_sql_res = self.adb_client.fetchall(sqlStockBefore)
                    if not before_stock_sql_res or len(before_stock_sql_res) < 0:
                        continue
                    bfStock = before_stock_sql_res[0]
                    if bfStock['packageCount'] == 0 and bfStock['pieceCount'] == 0:
                        continue
                    replayGoodsStockItem['packageCount'] = float(bfStock['packageCount'])
                    replayGoodsStockItem['pieceCount'] = float(bfStock['pieceCount'])
                    replayGoodsStockItem['leftCost'] = float(
                        (bfStock['packageCount'] + bfStock['pieceCount'] / stmt['pieceNum']) * bfStock[
                            'packageCostPrice'])
                    replayGoodsStock['replayGoodsStockList'].append(replayGoodsStockItem)
                except Exception as e:
                    print("1id:", self.chain_id, ' error:: ', str(e))

            if len(replayGoodsStock['replayGoodsStockList']) == 0:
                continue
            sqlAppendStock = """
                            UPDATE v2_goods_stock_log 
                                    SET goods = JSON_SET(goods, '$.replayGoodsStock', CAST('{replayGoodsStock}' as JSON)) 
                                    WHERE id ={logId};""".format(logId=stmt['logId'],
                                                                 replayGoodsStock=json.dumps(replayGoodsStock))
            self.goods_log_db_client.execute(sqlAppendStock)
    def flush_snapshot_data(self,goodsId):
        if self.chain_id:
            find_lack_data_sql = '''
            select /*+ max_execution_time(300000)*/ chain_id, organ_id, goods_id
            from abc_cis_goods_log.v2_goods_stock_log
            where created_date < '{0}' and chain_id = '{1}' and created_date >= '2024-01-01 00:00:00'
            and goods_id='{2}'
            group by chain_id, organ_id, goods_id
            '''.format(self.end_date, self.chain_id,goodsId)
        else:
            find_lack_data_sql = '''
            select /*+ max_execution_time(300000)*/ chain_id, organ_id, goods_id
            from abc_cis_goods_log.v2_goods_stock_log log
            where log.created_date < '{0}'
            and log.goods_id='{1}'
            group by chain_id, organ_id, goods_id
            '''.format(self.end_date,goodsId)
        # print(find_lack_data_sql)
        rows = self.adb_client.fetchall(find_lack_data_sql)
        lack_data_df = pd.DataFrame(rows, columns=['chain_id', 'organ_id', 'goods_id'])
        for index, row in lack_data_df.iterrows():
            # 查询所有的进销存记录
            query_record_sql = '''
            select id,
               stock_id,
               ifnull(pharmacy_no, 0) as pharmacy_no,
               action,
               piece_num,
               piece_count,
               package_count,
               stock_piece_count,
               stock_package_count
            from abc_cis_goods_log.v2_goods_stock_log
            where chain_id = '{0}'
              and organ_id = '{1}'
              and goods_id = '{2}'
              and created_date < '{3}'
            order by created_date, id
            '''.format(row['chain_id'], row['organ_id'], row['goods_id'], self.end_date)
            # print(query_record_sql)
            record_rows = self.goods_log_db_client.fetchall(query_record_sql)
            all_record_df = pd.DataFrame(record_rows, columns=['id', 'stock_id', 'pharmacy_no', 'action', 'piece_num',
                                                               'piece_count', 'package_count', 'stock_piece_count',
                                                               'stock_package_count'])
            # 从第一条开始刷snapshot，如果是采购入库或者是调拨入库，则会产生一个新的批次号，此时snapshot中的批次增加，
            # 如果进销存log中的批次剩余库存为0，则该批次移除
            # 如果已有快照，则直接忽略
            stock_add = set()
            pharmacy_add = set()
            stocks = list()
            pharmacyStatsList = list()
            snap = {'stocks': stocks, 'pharmacyStats': pharmacyStatsList}
            for j, col in all_record_df.iterrows():
                if '采购入库' == col['action'] or '调拨入库' == col['action'] or col['stock_id'] not in stock_add:
                    stock_add.add(col['stock_id'])
                    batch = {'stockId': int(col['stock_id']), 'pharmacy_no': int(col['pharmacy_no']),
                             'pieceCount': float(col['stock_piece_count']),
                             'packageCount': float(col['stock_package_count'])}
                    stocks.append(batch)
                else:
                    for s in stocks:
                        if s['stockId'] == int(col['stock_id']):
                            s['pharmacy_no'] = int(col['pharmacy_no'])
                            s['pieceCount'] = float(col['stock_piece_count'])
                            s['packageCount'] = float(col['stock_package_count'])
                # 把stocks数组，按pharmacy_no进行合并，如果pharmacy_no相同，则合并，pieceCount进行相加，packageCount进行相加
                # 如果合并后的pieceCount和packageCount都为0，则该批次移除
                # 如果合并后的pieceCount和packageCount不为0，则更新snap
                if col['pharmacy_no'] not in pharmacy_add:
                    pharmacy_add.add(col['pharmacy_no'])
                    pharmacy = {'pharmacy_no': int(col['pharmacy_no']),
                                'pieceCount': float(col['stock_piece_count']),
                                'packageCount': float(col['stock_package_count'])}
                    pharmacyStatsList.append(pharmacy)
                else:
                    for p in pharmacyStatsList:
                        if p['pharmacy_no'] == int(col['pharmacy_no']):
                            p['pieceCount'] += float(col['piece_count'])
                            p['packageCount'] += float(col['package_count'])

                goods_snap = json.dumps(snap)
                insert_snap_sql = '''
                            INSERT IGNORE INTO abc_cis_goods_log.v2_goods_stock_log_goods_snapshot(id, piece_num, goods_snap, created) 
                            VALUES'''
                insert_snap_sql = insert_snap_sql + '''({0}, {1}, '{2}', '1999-02-01 00:00:00')''' \
                    .format(col['id'], col['piece_num'], goods_snap)
                print(insert_snap_sql)
                self.goods_log_db_client.execute(insert_snap_sql)


def main():
    params = argparse.ArgumentParser()
    params.add_argument('--chain-id', help='连锁id')
    params.add_argument('--goods-id', help='连锁id')
    params.add_argument('--region-name', help='分区名字可直接查配置')
    args = params.parse_args()
    if not args.chain_id:
        params.print_help()
        sys.exit(-1)

    clean = CleanGoodsStockLogCost(args.region_name, args.chain_id)
    clean.run(args.goods_id)
    # snapshot = SnapshotDataRecover(args.region_name, args.chain_id)
    # snapshot.run()


if __name__ == '__main__':
    main()
