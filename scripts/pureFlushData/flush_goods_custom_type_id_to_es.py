#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import db
import argparse
import logging
import es
import json
import time

import datetime
from dateutil.relativedelta import relativedelta

import sys
reload(sys)
sys.setdefaultencoding('utf-8')

logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)

class LogStashFlushData(object):

    def __init__(self):
        self._db_client_cache = {}

    def _get_db_client(self, cluster, database):
        k = '{0}-{1}'.format(cluster, database)
        if k in self._db_client_cache.keys():
            return self._db_client_cache[k]

        db_client = db.DBClient(cluster, database)
        self._db_client_cache[k] = db_client
        return db_client

    #更新现有es文档里面的值
    def logStashFlushToEs(self, chain_id):
        flushCutomtypeIdToEsSql ='''
            select id as id,
                    type_id as typeId,
                    IF(custom_type_id ,custom_type_id,-1*type_id) as customTypeId 
            from v2_goods where organ_id = '{chainId}'
        '''
        #goods在高可用队列里面
        esClient = es.ESClient('abc-search-prod')
        #直接读主库
        goods_db_client = self._get_db_client('abc_cis_stock', 'abc_cis_goods')
        #一个连锁一个连锁去更新
        goodsList = goods_db_client.fetchall(flushCutomtypeIdToEsSql.format(chainId = chain_id))
        write_bulk  = []
        for goods in goodsList:
            #这里是update
            write_bulk.append({'update': {'_index': 'prod-cis-search-goods-nested' , '_id': str(goods['id'])}})
            #更新ES的 结构
            docUpdate ={"doc":{}}
            docUpdate["doc"]["customTypeId"] = goods["customTypeId"]
            write_bulk.append(json.dumps(docUpdate, ensure_ascii=False))
        if goodsList is not None and len(goodsList) > 0:
            esClient.bulkInsert('prod-cis-search-goods-nested',write_bulk)

    def execute_chain(self, chain_id):
        self.logStashFlushToEs(chain_id)

def run(chain_id):
    rgt = LogStashFlushData()
    rgt.execute_chain(chain_id)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)

if __name__ == '__main__':
    main()
