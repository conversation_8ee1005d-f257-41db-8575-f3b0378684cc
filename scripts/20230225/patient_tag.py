#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import logging
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

reload(sys)
sys.setdefaultencoding('utf-8')
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db
import es
import json
import argparse
import requests

default_id = '00000000000000000000000000000000'


def updatePatientTag(chain_id):
    patient_db_client = db.DBClient('abc_cis_account_base', 'abc_cis_patient')
    sqls = [
            #插入慢性病标签组
            """insert into abc_cis_patient.v2_patient_tag_type
                    (id, chain_id, name, status, created_by, created, 
                    last_modified_by, last_modified, gen_mode)
                select substr(uuid_short(), 5),
                                '{chainId}',
                                '慢性病标签',
                                1,
                                '00000000000000000000000000000003',
                                current_timestamp,
                                '00000000000000000000000000000003',
                                current_timestamp,
                                0
                                ;
            """,
            #导入"慢性病标签组"下的所有子标签
            """
                insert into abc_cis_patient.v2_patient_tag_type
                        (id, chain_id, name, status, tag_type, gen_mode,
                        parent_id, style, base_tag_id, 
                        created, created_by, last_modified,
                        last_modified_by)
                    select substr(uuid_short(), 5),
                            a.chain_id,
                            b.name,
                            b.status,
                            b.tag_type,
                            b.gen_mode,
                            c.id     as parent_id,
                            b.style,
                            b.sys_id as base_tag_id,
                            current_timestamp,
                            '00000000000000000000000000000003',
                            current_timestamp,
                            '00000000000000000000000000000003'
                    from (select    chain_id,
                                    name,
                                    status,
                                    tag_type,
                                    gen_mode,
                                    style,
                                    id as sys_id
                                from abc_cis_patient.v2_patient_tag_type
                                where chain_id = 'sys' and tag_type = '慢性病标签') b
                            inner join abc_cis_patient.v2_patient_tag_type a
                            on 1
                            inner join abc_cis_patient.v2_patient_tag_type c
                                on a.chain_id = '{chainId}'
                                and a.chain_id = c.chain_id
                                and c.name = '慢性病标签'
                                and c.parent_id is null
                            group by a.chain_id, b.chain_id, b.name, b.tag_type;
            """,
            #插入消费能力标签组
            """insert into abc_cis_patient.v2_patient_tag_type
                    (id, chain_id, name, status, created_by, created, 
                    last_modified_by, last_modified, gen_mode)
                select substr(uuid_short(), 5),
                                chain_id,
                                '消费能力标签',
                                1,
                                '00000000000000000000000000000003',
                                current_timestamp,
                                '00000000000000000000000000000003',
                                current_timestamp,
                                0
                                from (  select distinct(chain_id) 
                                        from abc_cis_patient.v2_patient_tag_type
                                        where chain_id = '{chainId}'
                                        ) b;
            """,
            #导入"消费能力标签组"下的所有子标签
            """
                insert into abc_cis_patient.v2_patient_tag_type
                        (id, chain_id, name, status, tag_type, gen_mode,
                        parent_id, style, base_tag_id, 
                        created, created_by, last_modified,
                        last_modified_by)
                    select substr(uuid_short(), 5),
                            a.chain_id,
                            b.name,
                            b.status,
                            b.tag_type,
                            b.gen_mode,
                            c.id     as parent_id,
                            b.style,
                            b.sys_id as base_tag_id,
                            current_timestamp,
                            '00000000000000000000000000000003',
                            current_timestamp,
                            '00000000000000000000000000000003'
                    from (select    chain_id,
                                    name,
                                    status,
                                    tag_type,
                                    gen_mode,
                                    style,
                                    id as sys_id
                                from abc_cis_patient.v2_patient_tag_type
                                where chain_id = 'sys' and tag_type = '消费能力标签') b
                            inner join abc_cis_patient.v2_patient_tag_type a
                            on 1
                            inner join abc_cis_patient.v2_patient_tag_type c
                                on a.chain_id = '{chainId}'
                                and a.chain_id = c.chain_id
                                and c.name = '消费能力标签'
                                and c.parent_id is null
                            group by a.chain_id, b.chain_id, b.name, b.tag_type;
            """,
            #更新患者标签id：将系统tag_id换成连锁tag_id
            """
                update abc_cis_patient.v2_patient_tag a
                inner join abc_cis_patient.v2_patient_tag_type b
                        on a.chain_id=b.chain_id and a.tag_id=b.base_tag_id and b.base_tag_id is not null
                set a.old_tag_id=a.tag_id,
                    a.tag_id=b.id,
                    a.last_modified=current_timestamp,
                    a.last_modified_by='00000000000000000000000000000003'
                    where a.chain_id = '{chainId}'
            """,
            # 将旧的"慢性病标签"组下新增的标签移到连锁的"慢性病标签"组下
            """
                update abc_cis_patient.v2_patient_tag_type a
                set a.parent_id=
                        (select e.new_parent_id from (select b.id as new_parent_id
                                                      from v2_patient_tag_type b
                                                      where b.name='慢性病标签'
                                                        and b.parent_id is null
                                                        and b.chain_id='{chainId}') e),
                    a.last_modified=current_timestamp,
                    a.last_modified_by='00000000000000000000000000000003'
                where a.parent_id=
                      (select d.old_parent_id from (select c.id as old_parent_id
                                                    from abc_cis_patient.v2_patient_tag_type c
                                                    where
                                                            c.chain_id='sys'
                                                      and c.name='慢性病标签'
                                                      and c.parent_id is null) d)
                  and a.chain_id='{chainId}';
            """
        ]

    for sql in sqls:
        patient_db_client.execute(sql.format(chainId=chain_id))

def updateScrmPatientTag(chain_id):
    scrm_db_client = db.DBClient('scrm_hospital', 'abc_scrm_customer')
    sql = """
     select a.tag_id as tagId, b.id as patientTagId, a.id as id
     from abc_scrm_customer.customer_tag a
     inner join abc_cis_patient.v2_patient_tag_type b
                 on a.chain_id = b.chain_id and a.tag_id = b.base_tag_id and b.base_tag_id is not null
    where a.chain_id = '{chainId}';
    """
    adb_client = db.DBClient('adb', 'abc_scrm_customer')
    rows = adb_client.fetchall(sql.format(chainId = chain_id))
    if len(rows) <= 0:
        logging.warn("is empty")
        return
    for item in rows:
        scrm_db_client.execute("""update customer_tag  
        set old_tag_id= '{tagId}', tag_id='{patientTagId}',
            last_modified=current_timestamp, last_modified_by='00000000000000000000000000000003'
            where id = '{id}'
            """.format(tagId = item["tagId"],patientTagId=item["patientTagId"],id=item["id"]))
def run(chain_id):
    updatePatientTag(chain_id)
    updateScrmPatientTag(chain_id)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)


if __name__ == '__main__':
    main()
