#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import logging
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

reload(sys)
sys.setdefaultencoding('utf-8')
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db
import argparse
import requests

# from idwork import IdWork
#
# db_client = db.DBClient('abc_cis_outpatient', 'abc_cis_examination')
# id_work = IdWork(db_client, False)
# id_work.config()

default_id = '00000000000000000000000000000000'


def tickChainLoginUser(chain_id):
    sql = '''select distinct employee_id  from  clinic_employee where chain_id ='{chainId}'; '''
    basic_db_client = db.DBClient('abc_cis_mixed', 'abc_cis_basic')
    employeeIds = basic_db_client.fetchall(sql.format(chainId = chain_id))
    if employeeIds is None or len(employeeIds) == 0:
        return
    for employeeId in employeeIds:
        #踢用户下线
        rsp = requests.post('http://pre.rpc.abczs.cn/rpc/v3/clinics/employees/force-logout', json={ 'chainId': chain_id,'employeeId':employeeId['employee_id'] })
        logging.error("logout rsp="+ str(rsp))

def run(chain_id):
    tickChainLoginUser(chain_id)




def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)


if __name__ == '__main__':
    main()
