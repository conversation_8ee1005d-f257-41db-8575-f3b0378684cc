#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import logging
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

reload(sys)
sys.setdefaultencoding('utf-8')
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db
import es
import json
import argparse
import requests

# from idwork import IdWork
#
# db_client = db.DBClient('abc_cis_outpatient', 'abc_cis_examination')
# id_work = IdWork(db_client, False)
# id_work.config()

default_id = '00000000000000000000000000000000'

def updateOrgan(chain_id):
    db_client = db.DBClient('abc_cis_mixed', 'abc_cis_basic')
    # 刷普通门店检查检验的deviceType
    db_client.execute("""
        update organ set bus_support_flag = 5 where parent_id = '${chainId}' and his_type = 2;
    """.format(chainId = chain_id))
    db_client.execute("""
        update organ set bus_support_flag = 3 where parent_id = '${chainId}' and his_type = 1;
    """.format(chainId = chain_id))


def transUsageCorrelation(chain_id):
    goods_dbcli = db.DBClient('abc_cis_stock', 'abc_cis_goods')
    goods_dbcli.execute("""update v2_goods_clinic_config set lock_configs ='[{{"lockFlag": 0, "sceneType": 0}}]' where his_type != 100 and  chain_id ='{}'  """.format(chain_id))
    outpatient_dbcli = db.DBClient('abc_cis_outpatient', 'abc_cis_outpatient')
    rows = outpatient_dbcli.fetchall("""
        select id,
               chain_id,
               clinic_id,
               0,
               null,
               usage_type,
               usages,
               goods_items,
               is_deleted,
               created,
               created_by,
               last_modified,
               last_modified_by
        from v2_outpatient_infusion_related_config where chain_id = '{}';
    """.format(chain_id))
    if len(rows) <= 0:
        logging.warn("usages is empty")
        return
    values = ["""({id}, '{chain_id}', '{clinic_id}', 0, null, {usage_type}, '{usages}', '{goods_items}',
                                              {is_deleted}, '{created}', '{created_by}', '{last_modified}', '{last_modified_by}')"""
                  .format(id=row['id'], chain_id=row['chain_id'], clinic_id=row['clinic_id'],
                          usage_type=row['usage_type'], usages=row['usages'], goods_items=row['goods_items'],
                          is_deleted=row['is_deleted'], created=row['created'], created_by=row['created_by'],
                          last_modified=row['last_modified'], last_modified_by=row['last_modified_by']) for row in rows]
    goods_dbcli.execute("""
                insert into v2_goods_association_config(id, chain_id, clinic_id, type, goods_id, usage_type, usages, goods_items,
                                                  is_deleted, created, created_by, last_modified, last_modified_by)
                values {};
            """.format(",".join(values)))


def updateGoods(chain_id):
    db_client = db.DBClient('abc_cis_stock', 'abc_cis_goods')
    # 刷普通门店检查检验的deviceType
    db_client.execute("""
        update v2_goods as g inner join v2_goods_chain_config as c on g.organ_id = c.chain_id
        set g.device_type =
            CASE
            WHEN JSON_VALID(g.biz_extensions) THEN trim(BOTH '"' from IFNULL(g.biz_extensions -> '$.itemCategory'
                , IF(g.sub_type = 1, '6', '0')))
            ELSE IF(g.sub_type = 1, '6', '0') END
        where g.organ_id = '{}' 
            and g.type = 3
            and c.his_type != 2
            and (g.biz_extensions -> '$.itemCategory' is not null and g.biz_extensions -> '$.itemCategory' != '' and
               json_type(g.biz_extensions -> '$.itemCategory') != 'NULL');
    """.format(chain_id))


    db_client.execute("""
        update v2_goods as g inner join v2_goods_chain_config as c on g.organ_id = c.chain_id
        set device_type = IF(sub_type = 1, 6, 0)
        where g.organ_id = '{}' 
            and g.type = 3
            and c.his_type != 2
            and (biz_extensions -> '$.itemCategory' is null or biz_extensions -> '$.itemCategory' = '' or json_type(biz_extensions -> '$.itemCategory') = 'NULL');
    """.format(chain_id))


    db_client.execute("""
            update v2_goods as g inner join v2_goods_chain_config as c on g.organ_id = c.chain_id
                set g.device_type =
                    CASE
                    WHEN JSON_VALID(biz_extensions) THEN trim(BOTH '"' from IFNULL(biz_extensions -> '$.itemCategory'
                        , IF(sub_type = 1, '6', '103')))
                    ELSE IF(sub_type = 1, '6', '103') END
            where g.organ_id = '{}' 
                and type = 3
                and c.his_type = 2
                and g.combine_type = 0
                and (biz_extensions -> '$.itemCategory' is not null and biz_extensions -> '$.itemCategory' != '' and
                   json_type(biz_extensions -> '$.itemCategory') != 'NULL');
        """.format(chain_id))


    db_client.execute("""
            update v2_goods as g inner join v2_goods_chain_config as c on g.organ_id = c.chain_id
                set device_type = IF(sub_type = 1, 6, 103)
            where g.organ_id = '{}' 
                and type = 3
                and g.combine_type = 0
                and c.his_type = 2
                and (biz_extensions -> '$.itemCategory' is null or biz_extensions -> '$.itemCategory' = '' or json_type(biz_extensions -> '$.itemCategory') = 'NULL');
        """.format(chain_id))

    #置检查项目绑定到默认设备上

    db_client.execute("""
            update v2_goods_examination_device as d
            set d.device_model_id = 10000
            where d.chain_id = '{}' 
                and d.goods_type = 3
                and d.goods_sub_type = 2
                and d.inner_flag = 1;
        """.format(chain_id))

    #科门店的检查项目是普通检查项目

    db_client.execute("""
            update v2_goods as g inner join v2_goods_chain_config as c on g.organ_id = c.chain_id
                set g.extend_spec = '0'
            where g.organ_id = '{}' 
                and g.type = 3
                and g.sub_type = 2
                and c.his_type != 2;
        """.format(chain_id))

    #门店的检查项目是眼科检查

    db_client.execute("""
            update v2_goods as g inner join v2_goods_chain_config as c on g.organ_id = c.chain_id
            set g.extend_spec = '20'
            where g.organ_id = '{}' 
                and g.type = 3
                and g.sub_type = 2
                and c.his_type = 2;
        """.format(chain_id))
    db_client.execute("""
        update v2_goods_stat
        set v2_goods_stat.stock_package_count = package_count,
            v2_goods_stat.stock_piece_count   = piece_count,
            stock_count                       = current_count
        where chain_id = '{}'
    """.format(chain_id))


def updateExamination(chain_id):
    db_client = db.DBClient('abc_cis_outpatient', 'abc_cis_examination')

    # 刷sample_no
    db_client.execute("""
        update v2_examination_sheet
        set sample_no = order_no
        where sample_no is null and chain_id = '{}';
    """.format(chain_id))

    db_client.execute("""
        update v2_examination_merge_sheet
        set sample_no = order_no
        where sample_no is null and chain_id = '{}';
    """.format(chain_id))

    # 合并单刷sample_type、sample_pipe
    db_client.execute("""
        update v2_examination_merge_sheet ms
        inner join v2_examination_sheet s on s.merge_sheet_id = ms.id
        set ms.sample_type = s.sample_type,
            ms.sample_pipe = s.sample_pipe 
        where ms.chain_id = '{}';
    """.format(chain_id))

    # 刷doctor_id、department_id、business_type
    # 门诊
    # db_client.execute("""
    #     update
    #         v2_examination_sheet es
    #             left join v2_examination_merge_sheet ms on ms.id = es.merge_sheet_id
    #             inner join abc_cis_outpatient.v2_outpatient_product_form_item fi on es.outpatient_form_item_id = fi.id
    #             inner join abc_cis_outpatient.v2_outpatient_sheet os on os.id = fi.outpatient_sheet_id
    #             inner join abc_cis_outpatient.v2_outpatient_medical_record mr on mr.outpatient_sheet_id = os.id
    #     set es.business_type        = 10,
    #         es.doctor_department_id = os.department_id,
    #         es.seller_id            = os.doctor_id,
    #         es.seller_department_id = os.department_id,
    #         es.diagnosis_infos      = mr.extend_diagnosis_infos,
    #         ms.business_type        = 10,
    #         ms.doctor_id            = os.doctor_id,
    #         ms.doctor_department_id = os.department_id,
    #         ms.seller_id            = os.doctor_id,
    #         ms.seller_department_id = os.department_id,
    #         ms.diagnosis_infos      = mr.extend_diagnosis_infos
    #     where es.outpatient_form_item_id is not null
    #       and es.outpatient_form_item_id != ''
    #       and es.chain_id = '{}';
    # """.format(chain_id))

    # # 收费
    # db_client.execute("""
    #     update
    #         v2_examination_sheet es
    #             left join v2_examination_merge_sheet ms on ms.id = es.merge_sheet_id
    #             inner join abc_cis_charge.v2_charge_sheet cs on es.charge_sheet_id = cs.id
    #             inner join abc_cis_charge.v2_charge_sheet_additional csa on csa.id = cs.id
    #     set es.business_type        = 20,
    #         es.doctor_department_id = csa.department_id,
    #         es.seller_id            = cs.seller_id,
    #         es.seller_department_id = cs.seller_department_id,
    #         es.diagnosis_infos      = csa.extend_diagnosis_infos,
    #         ms.business_type        = 20,
    #         ms.doctor_id            = csa.transcribe_doctor_id,
    #         ms.doctor_department_id = csa.department_id,
    #         ms.seller_id            = cs.seller_id,
    #         ms.seller_department_id = cs.seller_department_id,
    #         ms.diagnosis_infos      = csa.extend_diagnosis_infos
    #     where es.charge_sheet_id is not null
    #       and es.charge_sheet_id != ''
    #       and es.chain_id = '{}'
    #       and (es.outpatient_form_item_id is null
    #         or es.outpatient_form_item_id = '');
    # """.format(chain_id))
    #

    # 刷subType
    basic_dbcli = db.DBClient('abc_cis_mixed', 'abc_cis_basic')
    organ = basic_dbcli.fetchone("""select * from organ where id = '{}'""".format(chain_id))
    if organ['his_type'] == 0 or organ['his_type'] == 1:
        try:
            db_client.execute("""
                update v2_examination_sheet
                set sub_type = 0
                where type = 2
                  and chain_id = '{}';
            """.format(chain_id))
        except Exception as e:
            print("update v2_examination_sheet.sub_type error", e)

    if organ['his_type'] == 2:

        try:
            db_client.execute("""
                update v2_examination_sheet
                set sub_type = 20
                where type = 2
                  and chain_id = '{}';
            """.format(chain_id))
        except Exception as e:
            print("update v2_examination_sheet.sub_type error", e)


# 刷doctor_id、department_id、business_type
def updateExaminationDoctorAndDepartment(chain_id):
    try:
        examination_cli = db.DBClient('abc_cis_outpatient', 'abc_cis_examination')
        outpatient_dbcli = db.DBClient('abc_cis_outpatient', 'abc_cis_outpatient')
        charge_dbcli = db.DBClient('abc_cis_charge', 'abc_cis_charge')

        # 门诊
        outpatient_exam_sheets = examination_cli.fetchall("""
                select * from v2_examination_sheet
                where outpatient_form_item_id is not null
                  and outpatient_form_item_id != '' 
                  and chain_id = '{}';
            """.format(chain_id))
        patient_order_ids = list(set([sheet['patient_order_id'] for sheet in outpatient_exam_sheets]))
        if len(patient_order_ids) <= 0:
            logging.warn("patient_order_ids is empty")
            return
        outpatient_sheets = outpatient_dbcli.fetchall("""
            select os.patient_order_id, os.doctor_id, os.department_id, mr.extend_diagnosis_infos 
            from v2_outpatient_sheet os
            inner join v2_outpatient_medical_record mr on mr.outpatient_sheet_id = os.id
            where os.patient_order_id in ({})
        """.format(','.join("""'{}'""".format(patient_order_id) for patient_order_id in patient_order_ids)))
        outpatient_sheet_map = {outpatient_sheet['patient_order_id']: outpatient_sheet for outpatient_sheet in outpatient_sheets}
        for patient_order_id in patient_order_ids:
            outpatient_sheet = outpatient_sheet_map.get(patient_order_id)
            if outpatient_sheet is None:
                logging.warn("outpatient_sheet is None")
                continue

            try:
                examination_cli.execute("""
                                    update
                                        v2_examination_sheet es
                                            left join v2_examination_merge_sheet ms on ms.id = es.merge_sheet_id
                                    set es.business_type        = 10,
                                        es.doctor_department_id = {department_id},
                                        es.seller_id            = {doctor_id},
                                        es.seller_department_id = {department_id},
                                        es.diagnosis_infos      = {extend_diagnosis_infos},
                                        ms.business_type        = 10,
                                        ms.doctor_id            = {doctor_id},
                                        ms.doctor_department_id = {department_id},
                                        ms.seller_id            = {doctor_id},
                                        ms.seller_department_id = {department_id},
                                        ms.diagnosis_infos      = {extend_diagnosis_infos}
                                    where es.patient_order_id = '{patient_order_id}';
                                """.format(doctor_id="""'{}'""".format(outpatient_sheet['doctor_id']) if outpatient_sheet['doctor_id'] is not None else 'null',
                                           department_id="""'{}'""".format(outpatient_sheet['department_id']) if outpatient_sheet['department_id'] is not None else 'null',
                                           extend_diagnosis_infos="""'{}'""".format(outpatient_sheet['extend_diagnosis_infos']) if outpatient_sheet['extend_diagnosis_infos'] is not None else 'null',
                                           patient_order_id=patient_order_id))
            except Exception as e:
                print("update v2_examination_sheet by-patient-order-id error", e)

        # 收费
        charge_exam_sheets = examination_cli.fetchall("""
                    select * from v2_examination_sheet
                    where charge_sheet_id is not null
                      and charge_sheet_id != ''
                      and chain_id = '{}'
                      and (outpatient_form_item_id is null
                        or outpatient_form_item_id = '');
                        """.format(chain_id))
        charge_sheet_ids = list(set([sheet['charge_sheet_id'] for sheet in charge_exam_sheets]))
        if len(charge_sheet_ids) <= 0:
            logging.warn("charge_sheet_ids is empty")
            return
        charge_sheets = charge_dbcli.fetchall("""
                select cs.id, csa.transcribe_doctor_id as doctor_id, csa.department_id, cs.seller_id, cs.seller_department_id, csa.extend_diagnosis_infos
                from v2_charge_sheet cs
                inner join v2_charge_sheet_additional csa on csa.id = cs.id
                where cs.id in ({})
            """.format(','.join("""'{}'""".format(charge_sheet_id) for charge_sheet_id in charge_sheet_ids)))
        charge_sheet_map = {charge_sheet['id']: charge_sheet for charge_sheet in charge_sheets}
        for charge_sheet_id in charge_sheet_ids:
            charge_sheet = charge_sheet_map.get(charge_sheet_id)
            if charge_sheet is None:
                logging.warn("charge_sheet is None")
                continue

            try:
                examination_cli.execute("""
                                    update
                                        v2_examination_sheet es
                                            left join v2_examination_merge_sheet ms on ms.id = es.merge_sheet_id
                                    set es.business_type        = 20,
                                        es.doctor_department_id = {department_id},
                                        es.seller_id            = {seller_id},
                                        es.seller_department_id = {seller_department_id},
                                        es.diagnosis_infos      = {extend_diagnosis_infos},
                                        ms.business_type        = 20,
                                        ms.doctor_id            = {doctor_id},
                                        ms.doctor_department_id = {department_id},
                                        ms.seller_id            = {seller_id},
                                        ms.seller_department_id = {seller_department_id},
                                        ms.diagnosis_infos      = {extend_diagnosis_infos}
                                    where es.charge_sheet_id = '{charge_sheet_id}';
                                """.format(doctor_id="""'{}'""".format(charge_sheet['doctor_id']) if charge_sheet['doctor_id'] is not None else 'null',
                                           department_id="""'{}'""".format(charge_sheet['department_id']) if charge_sheet['department_id'] is not None else 'null',
                                           seller_id="""'{}'""".format(charge_sheet['seller_id']) if charge_sheet['seller_id'] is not None else 'null',
                                           seller_department_id="""'{}'""".format(charge_sheet['seller_department_id']) if charge_sheet['seller_department_id'] is not None else 'null',
                                           extend_diagnosis_infos="""'{}'""".format(charge_sheet['extend_diagnosis_infos']) if charge_sheet['extend_diagnosis_infos'] is not None else 'null',
                                           charge_sheet_id=charge_sheet_id))
            except Exception as e:
                print("update v2_examination_sheet by-charge-sheet-id error", e)
    except Exception as ex:
        print("update v2_examination_sheet error", ex)


def updateExaminationSheetEs(chain_id):
    try:
        examination_cli = db.DBClient('adb', 'abc_cis_examination')
        patient_cli = db.DBClient('adb', 'abc_cis_patient')
        patient_order_cli = db.DBClient('adb', 'abc_cis_patientorder')
        es_cli = es.ESClient('abc-search-prod-normal')
        rows = examination_cli.fetchall("""
            select  a.id,
              a.chain_id                                 as chainId,
              a.clinic_id                                as clinicId,
              a.patient_id                               as patientId,
              a.patient_order_id                         as patientOrderId,
              a.type,
              a.sub_type                                 as subType,
              a.business_type                            as businessType,
              a.device_model_id                          as deviceModelId,
              a.ward_area_id                             as wardAreaId,
              date_format(a.created, '%Y-%m-%d %T')      as created,
              a.examination_name                         as examinationName,
              a.examination_id                           as examinationId,
              a.status,
              a.order_no                                 as orderNo,
              a.sample_no                                as sampleNo,
              a.sample_type                              as sampleType,
              a.sample_pipe                              as samplePipe,
              a.sampler_id                               as samplerId,
              date_format(a.sample_time, '%Y-%m-%d %T')  as sampleTime,
              a.sample_status                            as sampleStatus,
              a.collector_id                             as collectorId,
              date_format(a.collect_time, '%Y-%m-%d %T') as collectTime,
              a.reject_reason                            as rejectReason,
              a.reserve_status                           as reserveStatus,
              a.doctor_id                                as doctorId,
              a.doctor_department_id                     as doctorDepartmentId,
              a.seller_id                                as sellerId,
              a.seller_department_id                     as sellerDepartmentId,
              a.diagnosis_infos                          as diagnosisInfos,
              a.examination_apply_sheet_id               as examinationApplySheetId
            from v2_examination_sheet as a
            where a.chain_id = '{}';
        """.format(chain_id))
        if len(rows) <= 0:
            logging.warn("es_exam_sheets is empty")
            return
        values = []
        examination_apply_sheet_ids = list(set([row['examinationApplySheetId'] for row in rows if row['examinationApplySheetId'] is not None]))
        patient_ids = list(set([row['patientId'] for row in rows if row['patientId'] is not None]))
        patient_order_ids = list(set([row['patientOrderId'] for row in rows if row['patientOrderId'] is not None]))
        exam_apply_sheets = examination_cli.fetchall("""
            select * from v2_examination_apply_sheet where chain_id = '{0}' and id in ({1})
        """.format(chain_id, ','.join(["""'{}'""".format(examination_apply_sheet_id) for examination_apply_sheet_id in examination_apply_sheet_ids]))) if len(examination_apply_sheet_ids) > 0 else []
        gap = 2000
        patients = []
        for i in range(0, len(patient_ids) / gap + 1):
            segments = []
            for j in range(i * gap, (i + 1) * gap):
                if j >= len(patient_ids):
                    break
                segments.append(patient_ids[j])
            patients.extend(patient_cli.fetchall("""
                select * from v2_patient where chain_id = '{0}' and id in ({1})
            """.format(chain_id, ','.join(["""'{}'""".format(patient_id) for patient_id in segments]))) if len(patient_ids) > 0 else [])

        patient_orders = []
        for i in range(0, len(patient_order_ids) / gap + 1):
            segments = []
            for j in range(i * gap, (i + 1) * gap):
                if j >= len(patient_order_ids):
                    break
                segments.append(patient_order_ids[j])
            patient_orders.extend(patient_order_cli.fetchall("""
                select * from v2_patientorder where chain_id = '{0}' and id in ({1})
            """.format(chain_id, ','.join(["""'{}'""".format(patient_order_id) for patient_order_id in segments]))) if len(patient_order_ids) > 0 else [])
        exam_apply_sheet_no_map = {row['id']: row['no'] for row in exam_apply_sheets}
        patient_map = {patient['id']: patient for patient in patients}
        patient_order_no_map = {patient_order['id']: patient_order['no'] for patient_order in patient_orders}
        for row in rows:
            patient = patient_map.get(row['patientId'], {})
            body = {
                "businessType": row['businessType'],
                "chainId": row['chainId'],
                "clinicId": row['clinicId'],
                "collectTime": row['collectTime'],
                "collectorId": row['collectorId'],
                "created": row['created'],
                "deviceModelId": long(row['deviceModelId']) if row['deviceModelId'] is not None else None,
                "diagnosisInfos": row['diagnosisInfos'],
                "doctorDepartmentId": row['doctorDepartmentId'],
                "doctorId": row['doctorId'],
                "examinationApplySheetId": long(row['examinationApplySheetId']) if row[
                                                                                       'examinationApplySheetId'] is not None else None,
                "examinationApplySheetNo": exam_apply_sheet_no_map.get(row['examinationApplySheetId']),
                "examinationName": row['examinationName'],
                "examinationId": row['examinationId'],
                "id": row['id'],
                "orderNo": row['orderNo'],
                "patientId": row['patientId'],
                "patientIdCardCipher": patient.get('id_card_cipher'),
                "patientIdCardLast6": patient.get('id_card_last6'),
                "patientMobileCipher": patient.get('mobile_cipher'),
                "patientMobileLast4": patient.get('mobile_last4'),
                "patientName": patient.get('name'),
                "patientOrderId": row['patientOrderId'],
                "patientOrderNo": patient_order_no_map.get(row['patientOrderId']),
                "rejectReason": row['rejectReason'],
                "reserveStatus": row['reserveStatus'],
                "sampleNo": row['sampleNo'],
                "samplePipe": row['samplePipe'],
                "sampleStatus": row['sampleStatus'],
                "sampleTime": row['sampleTime'],
                "sampleType": row['sampleType'],
                "samplerId": row['samplerId'],
                "sellerDepartmentId": row['sellerDepartmentId'],
                "sellerId": row['sellerId'],
                "status": row['status'],
                "subType": row['subType'],
                "type": row['type'],
                "wardAreaId": long(row['wardAreaId']) if row['wardAreaId'] is not None else None,
            }
            # logging.info("body = {}".format(body))
            values.append({'index': {'_index': 'v3-examination-sheet-prod', '_id': str(row['id'])}})
            values.append(json.dumps(body, ensure_ascii=False))
        es_cli.bulkInsert('v3-examination-sheet-prod', values)
    except Exception as e:
        print("update v2_examination_sheet to-es error", e)


def updateExaminationMergeSheetEs(chain_id):
    try:
        examination_cli = db.DBClient('adb', 'abc_cis_examination')
        patient_cli = db.DBClient('adb', 'abc_cis_patient')
        patient_order_cli = db.DBClient('adb', 'abc_cis_patientorder')
        es_cli = es.ESClient('abc-search-prod-normal')
        rows = examination_cli.fetchall("""
            select a.id,
              a.chain_id                                 as chainId,
              a.clinic_id                                as clinicId,
              a.patient_id                               as patientId,
              a.patient_order_id                         as patientOrderId,
              a.type,
              a.sub_type                                 as subType,
              a.business_type                            as businessType,
              a.device_model_id                          as deviceModelId,
              a.ward_area_id                             as wardAreaId,
              date_format(a.created, '%Y-%m-%d %T')      as created,
              a.name                                     as examinationName,
              a.status,
              a.order_no                                 as orderNo,
              a.sample_no                                as sampleNo,
              a.sample_type                              as sampleType,
              a.sample_pipe                              as samplePipe,
              a.sampler_id                               as samplerId,
              date_format(a.sample_time, '%Y-%m-%d %T')  as sampleTime,
              a.sample_status                            as sampleStatus,
              a.collector_id                             as collectorId,
              date_format(a.collect_time, '%Y-%m-%d %T') as collectTime,
              a.reject_reason                            as rejectReason,
              a.doctor_id                                as doctorId,
              a.doctor_department_id                     as doctorDepartmentId,
              a.seller_id                                as sellerId,
              a.seller_department_id                     as sellerDepartmentId,
              a.diagnosis_infos                          as diagnosisInfos,
              a.is_deleted                               as isDeleted,
              a.examination_apply_sheet_id               as examinationApplySheetId
            from v2_examination_merge_sheet as a
            where a.chain_id = '{}';
        """.format(chain_id))
        if len(rows) <= 0:
            logging.warn("es_exam_merge_sheets is empty")
            return
        values = []
        examination_apply_sheet_ids = list(set([row['examinationApplySheetId'] for row in rows if row['examinationApplySheetId'] is not None]))
        patient_ids = list(set([row['patientId'] for row in rows if row['patientId'] is not None]))
        patient_order_ids = list(set([row['patientOrderId'] for row in rows if row['patientOrderId'] is not None]))
        exam_apply_sheets = examination_cli.fetchall("""
            select * from v2_examination_apply_sheet where chain_id = '{0}' and id in ({1})
        """.format(chain_id, ','.join(["""'{}'""".format(examination_apply_sheet_id) for examination_apply_sheet_id in examination_apply_sheet_ids]))) if len(examination_apply_sheet_ids) > 0 else []
        gap = 2000
        patients = []
        for i in range(0, len(patient_ids) / gap + 1):
            segments = []
            for j in range(i * gap, (i + 1) * gap):
                if j >= len(patient_ids):
                    break
                segments.append(patient_ids[j])
            patients.extend(patient_cli.fetchall("""
                    select * from v2_patient where chain_id = '{0}' and id in ({1})
                """.format(chain_id, ','.join(["""'{}'""".format(patient_id) for patient_id in segments]))) if len(patient_ids) > 0 else [])

        patient_orders = []
        for i in range(0, len(patient_order_ids) / gap + 1):
            segments = []
            for j in range(i * gap, (i + 1) * gap):
                if j >= len(patient_order_ids):
                    break
                segments.append(patient_order_ids[j])
            patient_orders.extend(patient_order_cli.fetchall("""
                    select * from v2_patientorder where chain_id = '{0}' and id in ({1})
                """.format(chain_id, ','.join(["""'{}'""".format(patient_order_id) for patient_order_id in segments]))) if len(patient_order_ids) > 0 else [])
        exam_apply_sheet_no_map = {row['id']: row['no'] for row in exam_apply_sheets}
        patient_map = {patient['id']: patient for patient in patients}
        patient_order_no_map = {patient_order['id']: patient_order['no'] for patient_order in patient_orders}
        for row in rows:
            patient = patient_map.get(row['patientId'], {})
            body = {
                "businessType": row['businessType'],
                "chainId": row['chainId'],
                "clinicId": row['clinicId'],
                "collectTime": row['collectTime'],
                "collectorId": row['collectorId'],
                "created": row['created'],
                "deviceModelId": long(row['deviceModelId']) if row['deviceModelId'] is not None else None,
                "diagnosisInfos": row['diagnosisInfos'],
                "isDeleted": row['isDeleted'],
                "doctorDepartmentId": row['doctorDepartmentId'],
                "doctorId": row['doctorId'],
                "examinationApplySheetId": long(row['examinationApplySheetId']) if row[
                                                                                       'examinationApplySheetId'] is not None else None,
                "examinationApplySheetNo": exam_apply_sheet_no_map.get(row['examinationApplySheetId']),
                "examinationName": row['examinationName'],
                "id": long(row['id']),
                "orderNo": row['orderNo'],
                "patientId": row['patientId'],
                "patientIdCardCipher": patient.get('id_card_cipher'),
                "patientIdCardLast6": patient.get('id_card_last6'),
                "patientMobileCipher": patient.get('mobile_cipher'),
                "patientMobileLast4": patient.get('mobile_last4'),
                "patientName": patient.get('name'),
                "patientOrderId": row['patientOrderId'],
                "patientOrderNo": patient_order_no_map.get(row['patientOrderId']),
                "rejectReason": row['rejectReason'],
                "sampleNo": row['sampleNo'],
                "samplePipe": row['samplePipe'],
                "sampleStatus": row['sampleStatus'],
                "sampleTime": row['sampleTime'],
                "sampleType": row['sampleType'],
                "samplerId": row['samplerId'],
                "sellerDepartmentId": row['sellerDepartmentId'],
                "sellerId": row['sellerId'],
                "status": row['status'],
                "subType": row['subType'],
                "type": row['type'],
                "wardAreaId": long(row['wardAreaId']) if row['wardAreaId'] is not None else None,
            }
            # logging.info("body = {}".format(body))
            values.append({'index': {'_index': 'v3-examination-merge-sheet-prod', '_id': str(row['id'])}})
            values.append(json.dumps(body, ensure_ascii=False))
        es_cli.bulkInsert('v3-examination-merge-sheet-prod', values)
    except Exception as e:
        print("update v2_examination_merge_sheet to-es error", e)


def run(chain_id):
    updateOrgan(chain_id)
    transUsageCorrelation(chain_id)
    updateExaminationDoctorAndDepartment(chain_id)
    updateGoods(chain_id)
    updateExamination(chain_id)
    # updateExaminationSheetEs(chain_id)
    # updateExaminationMergeSheetEs(chain_id)
    #清理GoodsRedisCache
    requests.get("""http://pre.rpc.abczs.cn/rpc/v3/goods/jenkins/clean-chain-redis-cache?chainId={}""".format(chain_id))



def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)


if __name__ == '__main__':
    main()
