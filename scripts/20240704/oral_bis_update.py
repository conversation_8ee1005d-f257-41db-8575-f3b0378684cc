# -*- coding: utf-8 -*-
"""
@name: flush_advice_support_nid.py
@author: <PERSON><PERSON><PERSON>
@email: <EMAIL>
@date: 2023-11-21 22:41:44
"""
import argparse
import sys
import requests
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.rediscli import RedisClient

from multizone.db import DBClient
from multizone.rpc import regionRpcHost

DEFAULT_ID = '00000000000000000000000000000000'


class FlushHospitalLimitPriceRule:
    chain_id = None

    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', 'prod', True)

    def run(self):

        organHisType = self.basic_db_client.fetchone(
            ''' select his_type from organ where id = '{chainId}' '''.format(chainId=self.chain_id))

        if organHisType:

            if organHisType['his_type'] == 100:
                self.basic_db_client.execute(
                    '''
                    update abc_cis_basic.v2_clinic_current_purchase_item a
                        inner join abc_cis_basic.organ b on a.clinic_id = b.id
                    set a.is_deleted = 1
                    where a.purchase_item_key = 'multi-medical-record-eye'
                      and a.clinic_id not in ('ffffffff0000000034780ddea19a8000', 'ffffffff0000000034780ddea19a8004')
                      and b.parent_id = '{chainId}';
                    '''.format(
                        chainId=self.chain_id)
                )
                self.basic_db_client.execute(
                    '''
                    update abc_cis_basic.organ
                    set bus_support_flag = 3
                    where  id not in ('ffffffff0000000034780ddea19a8000', 'ffffffff0000000034780ddea19a8004')
                      and parent_id = '{chainId}';
                    '''.format(
                        chainId=self.chain_id)
                )


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    update_data = FlushHospitalLimitPriceRule(args.region_name, args.chain_id)
    update_data.run()


if __name__ == '__main__':
    main()
