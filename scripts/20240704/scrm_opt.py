"""
SCRM 优化
patient_clinic: 新增患者首诊医生、首次收费时间
patient: 患者最近就诊医生
"""
import argparse
import os
import sys

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.db import DBClient

from scripts.common.utils.lists import ListUtils
from scripts.common.utils.sqls import SqlUtils


def execute_sql(client, sql):
    client.execute(sql)
    # print(sql)
    pass


class UpdateData:
    chain_id = None
    patient_wdb_client = None
    patient_ods_client = None
    outpatient_ods_client = None
    charge_ods_client = None
    env = None

    def __init__(self, env, region_name, chain_id):
        self.env = env
        self.chain_id = chain_id
        self.region_name = region_name
        self.patient_wdb_client = DBClient(self.region_name, 'abc_cis_account_base', 'abc_cis_patient', self.env, True)
        self.patient_ods_client = DBClient(self.region_name, 'ods', 'abc_cis_patient', self.env, True)
        self.outpatient_ods_client = DBClient(self.region_name, 'ods', 'abc_cis_outpatient', self.env, True)
        self.charge_ods_client = DBClient(self.region_name, 'ods', 'abc_cis_charge', self.env, True)

    def run(self):
        cursor = "00000000000000000000000000000000"
        size = 50
        total = 0
        while True:
            # 先分页遍历患者
            patients = self.patient_ods_client.fetchall("""
                select id
                from v2_patient
                where chain_id = '{chainId}' and status = 1 and id > '{cursor}'
                order by id
                limit {size}
            """.format(chainId=self.chain_id, cursor=cursor, size=size))
            if not patients:
                break
            cursor = patients[-1]['id']

            patient_ids = ListUtils.dist_mapping(patients, lambda patient: patient['id'])
            # 刷患者首诊医生
            self.update_patient_clinic_first_outpatient_doctor_id(patient_ids)
            # 刷患者首次收费时间
            self.update_patient_clinic_first_charge_time(patient_ids)
            # 刷患者最近就诊医生
            self.update_patient(patient_ids)
            total += len(patients)
        print(f"已处理{total}条患者数据")

    def update_patient_clinic_first_charge_time(self, patient_ids):
        # 患者首次收费时间
        patient_id_in_str = SqlUtils.to_in_value(patient_ids)
        patient_clinic_data = self.charge_ods_client.fetchall("""
            select patient_id, clinic_id, first_charged_time
            from v2_charge_sheet
            where chain_id = '{chain_id}' and patient_id in ({patient_id_in_str}) and status = 2 and is_deleted = 0 and first_charged_time is not null
            group by clinic_id, patient_id
            order by first_charged_time asc
        """.format(chain_id=self.chain_id, patient_id_in_str=patient_id_in_str))

        if not patient_clinic_data:
            return

        case_when_str = ""
        patient_clinic_in_str = ""
        for data in patient_clinic_data:
            case_when_str += f"when patient_id = '{data['patient_id']}' and '{data['clinic_id']}' then '{data['first_charged_time']}' "
            patient_clinic_in_str += f"('{data['clinic_id']}', '{data['patient_id']}'),"
        # 去掉末尾的逗号
        patient_clinic_in_str = patient_clinic_in_str[:-1]

        # 通过 case when 的方式批量更新
        sql = """
                    update v2_patient_clinic
                    set first_charge_date = case {case_when_str} end
                    where ((clinic_id, patient_id)) in ({patient_clinic_in_str})
                """.format(case_when_str=case_when_str, patient_clinic_in_str=patient_clinic_in_str)
        execute_sql(self.patient_wdb_client, sql)

    def update_patient_clinic_first_outpatient_doctor_id(self, patient_ids):
        # 患者首诊医生
        patient_id_in_str = SqlUtils.to_in_value(patient_ids)
        patient_clinic_data = self.outpatient_ods_client.fetchall("""
            select patient_id, clinic_id, doctor_id
            from v2_outpatient_sheet
            where chain_id = '{chain_id}' and patient_id in ({patient_id_in_str}) and status = 1 and is_deleted = 0 and doctor_id is not null and doctor_id != ''
            group by clinic_id, patient_id
            order by diagnosed_date asc
        """.format(chain_id=self.chain_id, patient_id_in_str=patient_id_in_str))

        if not patient_clinic_data:
            return

        # 通过 case when 的方式批量更新
        case_when_str = ""
        patient_clinic_in_str = ""
        for data in patient_clinic_data:
            case_when_str += "when patient_id = '{patient_id}' and clinic_id = '{clinic_id}' then '{doctor_id}' ".format(
                patient_id=data['patient_id'], clinic_id=data['clinic_id'], doctor_id=data['doctor_id'])
            patient_clinic_in_str += f"('{data['clinic_id']}', '{data['patient_id']}'),"
        # 去掉末尾的逗号
        patient_clinic_in_str = patient_clinic_in_str[:-1]

        sql = """
                    update v2_patient_clinic
                    set first_outpatient_doctor_id = case {case_when_str} end
                    where ((clinic_id, patient_id)) in ({patient_clinic_in_str})
                """.format(case_when_str=case_when_str, patient_clinic_in_str=patient_clinic_in_str)

        execute_sql(self.patient_wdb_client, sql)

    def update_patient(self, patient_ids):
        # 患者最近就诊医生
        patient_id_in_str = SqlUtils.to_in_value(patient_ids)
        patient_data = self.outpatient_ods_client.fetchall("""
            select patient_id, doctor_id, diagnosed_date
            from v2_outpatient_sheet
            where chain_id = '{chain_id}' and status = 1 and patient_id in ({patient_id_in_str}) and is_deleted = 0 and doctor_id is not null and doctor_id != ''
            group by patient_id
            order by diagnosed_date desc
        """.format(chain_id=self.chain_id, patient_id_in_str=patient_id_in_str))

        if not patient_data:
            return

        case_when_str = ""
        update_patient_id_in_str = ""
        for data in patient_data:
            case_when_str += "when id = '{patient_id}' then '{doctor_id}' ".format(patient_id=data['patient_id'], doctor_id=data['doctor_id'])
            update_patient_id_in_str += f"'{data['patient_id']}',"
        # 去掉末尾的逗号
        update_patient_id_in_str = update_patient_id_in_str[:-1]

        sql = """
            update v2_patient
            set last_outpatient_doctor_id = case {case_when_str} end
            where id in ({patient_id_in_str})
        """.format(case_when_str=case_when_str, patient_id_in_str=update_patient_id_in_str)
        execute_sql(self.patient_wdb_client, sql)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    updateData = UpdateData(args.env, args.region_name, args.chain_id)
    updateData.run()


if __name__ == '__main__':
    main()
