# -*- coding: utf-8 -*-
"""
@name: flush_goods_supplier_name_py.py
@author: <PERSON><PERSON><PERSON>
@email: <EMAIL>
@date: 2024-07-24 18:25:27
"""
import argparse
import json
import sys
import requests
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import argparse
import os
from multizone.db import DBClient
from pypinyin import pinyin, lazy_pinyin, Style
import sys

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))


class UpdateData:
    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.goods_db_client = DBClient(self.region_name, 'abc_cis_stock', 'abc_cis_goods', 'prod', True)

    def flush_supplier_name_py(self):
        supplier_list = self.goods_db_client.fetchall("""
                  select *
                  from v2_goods_supplier
                  where is_deleted = 0 and name is not null and name != ''  and chain_id = '{chainId}'
            """.format(chainId=self.chain_id))
        if supplier_list is None:
            return
        for supplier in supplier_list:
            supplier_id = supplier['id']
            name = supplier['name']
            if supplier_id is None or name is None or len(name.strip()) == 0:
                continue
            first_py = ''.join([word[0].upper() for word in lazy_pinyin(name)])
            full_py = ''.join(lazy_pinyin(name)).upper()
            name_py = f'{first_py}|{full_py}'
            if len(name_py) > 256:
                name_py = name_py[:256]
            name_py = name_py.replace("'", "''")
            # print(supplier['id'], supplier['name'], supplier['name_py_first'])
            update_sql = """
                update v2_goods_supplier set name_py_first = '{name_py}' where id = '{id}'
            """.format(name_py=name_py, id=supplier_id)
            # print(update_sql)
            self.goods_db_client.execute(update_sql)
        pass

    def run(self):
        self.flush_supplier_name_py()


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    update_data = UpdateData(args.region_name, args.chain_id)
    update_data.run()


if __name__ == '__main__':
    main()