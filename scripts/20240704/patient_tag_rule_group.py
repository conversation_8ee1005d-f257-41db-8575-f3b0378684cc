# -*- coding: utf-8 -*-
"""
@name: flush_goods_extend_data.py
@author: <PERSON><PERSON><PERSON>
@email: <EMAIL>
@date: 2024-07-04 23:21:52
"""
import argparse
import os
import sys

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from idwork import IdWork
from scripts.common.utils.lists import ListUtils
from scripts.common.utils.sqls import SqlUtils


class UpdateData:
    env = None
    chain_id = None
    patient_db_client = None
    id_work = None

    def __init__(self, env, region_name, chain_id):
        self.env = env
        self.chain_id = chain_id
        self.patient_db_client = DBClient(region_name, 'abc_cis_account_base', 'abc_cis_patient', env, True)
        self.id_work = IdWork(self.patient_db_client, False)

    def run(self):
        # 查询连锁所有没有标签规则组归属的标签规则
        chain_tag_rules = self.patient_db_client.fetchall('''
            select id, tag_id
            from v2_patient_tag_rule
            where chain_id = '{chainId}' and tag_rule_group_id is null
        '''.format(chainId=self.chain_id))

        if not chain_tag_rules:
            return

        # 按照 tag_id 分组
        tag_id_to_tag_rules = ListUtils.group_by(chain_tag_rules, lambda e: e['tag_id'])

        self.id_work.config()
        for tag_id, tag_rules in tag_id_to_tag_rules.items():
            # 为每个 tag_id 创建一个默认标签规则组
            tag_rule_group_id = self.id_work.getUIDLong()
            tag_rules_ids = [tag_rule['id'] for tag_rule in tag_rules]
            self.patient_db_client.executemany([
                # 更新标签规则的 tag_rule_group_id
                '''
                    update v2_patient_tag_rule
                    set tag_rule_group_id = '{tagRuleGroupId}'
                    where id in ({tagRuleIds})
                '''.format(tagRuleGroupId=tag_rule_group_id, tagRuleIds=SqlUtils.to_in_value(tag_rules_ids)),
                # 创建标签规则组
                '''
                    insert into v2_patient_tag_rule_group (id, chain_id, tag_id, is_deleted, created, created_by, last_modified_by, last_modified)
                    values ('{id}', '{chainId}', '{tagId}', 0, current_timestamp, '00000000000000000000000000000000', '00000000000000000000000000000000', current_timestamp)
                '''.format(id=tag_rule_group_id, chainId=self.chain_id, tagId=tag_id)
            ])


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    update_data = UpdateData(args.env, args.region_name, args.chain_id)
    update_data.run()


if __name__ == '__main__':
    main()
