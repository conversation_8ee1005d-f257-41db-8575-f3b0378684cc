"""
SCRM 优化——线索
"""
import argparse
import os
import sys

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.db import DBClient


class UpdateData:
    chain_id = None
    scrm_wdb_client = None
    env = None

    def __init__(self, env, region_name, chain_id):
        self.env = env
        self.chain_id = chain_id
        self.region_name = region_name
        self.scrm_wdb_client = DBClient(self.region_name, 'scrm_hospital', 'abc_scrm_customer', self.env, True)

    def run(self):
        # 先刷添加状态
        self.scrm_wdb_client.execute("""
            update customer_resource_pool
            set customer_add_status = 10
            where status in (11, 20) and chain_id = '{chainId}';
        """.format(chainId=self.chain_id))

        # 再刷客户状态
        self.scrm_wdb_client.execute("""
            update customer_resource_pool
            set status = 10
            where status = 11 and chain_id = '{chainId}';
        """.format(chainId=self.chain_id))

        # 再刷客户和员工关联跟进时间
        self.scrm_wdb_client.execute("""
            update customer_employee_relate
            set followup_time = created
            where chain_id = '{chainId}'
        """.format(chainId=self.chain_id))


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    updateData = UpdateData(args.env, args.region_name, args.chain_id)
    updateData.run()


if __name__ == '__main__':
    main()
