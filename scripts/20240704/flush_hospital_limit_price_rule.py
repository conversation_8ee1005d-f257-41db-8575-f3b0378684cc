# -*- coding: utf-8 -*-
"""
@name: flush_advice_support_nid.py
@author: <PERSON><PERSON><PERSON>
@email: <EMAIL>
@date: 2023-11-21 22:41:44
"""
import argparse
import sys
import requests
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.rediscli import RedisClient

from multizone.db import DBClient
from multizone.rpc import regionRpcHost

DEFAULT_ID = '00000000000000000000000000000000'


class FlushHospitalLimitPriceRule:
    chain_id = None

    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', 'prod', True)
        self.charge_db_client = DBClient(self.region_name, 'abc_cis_charge', 'abc_cis_charge', 'prod', True)
        self.his_charge_db_client = DBClient(self.region_name, 'scrm_hospital', 'abc_his_charge', 'prod', True)

    def run(self):

        organHisType = self.basic_db_client.fetchone(
            ''' select his_type from organ where id = '{chainId}' '''.format(chainId=self.chain_id))

        if organHisType:

            if organHisType['his_type'] == 100:
                # 删除医院的治疗理疗类型限价规则
                self.charge_db_client.execute(
                    ''' update v2_charge_medicare_limit_price_type set is_deleted = 1 where type in (4,5,6) and is_deleted = 0 and chain_id = '{chainId}' '''.format(
                        chainId=self.chain_id)
                );
                # 更新住院的维度金额
                self.his_charge_db_client.execute(
                    ''' update v2_his_charge_sheet
                            set shebao_total_price             = total_price,
                                shebao_charged_price           = charged_price,
                                shebao_uncharged_price         = uncharged_price,
                                self_pay_total_price           = 0,
                                self_pay_charged_total_price   = 0,
                                self_pay_uncharged_total_price = 0 where chain_id = '{chainId}' '''.format(chainId=self.chain_id)
                )
                self.his_charge_db_client.execute(
                    ''' update v2_his_charge_form
                            set shebao_total_price             = total_price,
                                shebao_charged_price           = charged_price,
                                shebao_uncharged_price         = uncharged_price,
                                self_pay_total_price           = 0,
                                self_pay_charged_total_price   = 0,
                                self_pay_uncharged_total_price = 0 where chain_id = '{chainId}' '''.format(chainId=self.chain_id)
                )
                self.his_charge_db_client.execute(
                    ''' update v2_his_charge_form_item
                            set shebao_unit_price            = unit_price,
                                shebao_fraction_price        = fraction_price,
                                shebao_total_price           = total_price,
                                shebao_charged_total_price   = charged_total_price,
                                self_pay_total_price         = 0,
                                self_pay_charged_total_price = 0 where chain_id = '{chainId}' '''.format(chainId=self.chain_id)
                )
                self.his_charge_db_client.execute(
                    ''' update v2_his_charge_settle
                            set shebao_total_price   = total_price,
                                self_pay_total_price = 0 where chain_id = '{chainId}' '''.format(chainId=self.chain_id)
                )
                self.his_charge_db_client.execute(
                    ''' update v2_his_charge_form_item_record
                            set shebao_amount   = amount,
                                self_pay_amount = 0 where chain_id = '{chainId}' '''.format(chainId=self.chain_id)
                )
                self.his_charge_db_client.execute(
                    ''' update v2_his_charge_form_item_batch_info
                            set shebao_unit_price            = unit_price,
                                shebao_total_price           = total_price,
                                shebao_charged_total_price   = charged_total_price,
                                self_pay_total_price         = 0,
                                self_pay_charged_total_price = 0 where chain_id = '{chainId}' '''.format(chainId=self.chain_id)
                )
                self.his_charge_db_client.execute(
                    ''' update v2_his_charge_form_item_batch_info_record
                            set shebao_amount   = amount,
                                self_pay_amount = 0 where chain_id = '{chainId}' '''.format(chainId=self.chain_id)
                )
                self.his_charge_db_client.execute(
                    ''' update v2_his_charge_sheet a inner join v2_his_charge_settle b on a.patient_order_id = b.patient_order_id and a.clinic_id = b.clinic_id
                            set a.settle_charged_price = charged_price,
                                a.settle_total_price = a.total_price,
                                a.settle_uncharged_price = a.uncharged_price
                            where b.pay_status != 0 and a.chain_id = '{chainId}' '''.format(chainId=self.chain_id)
                )
                self.his_charge_db_client.execute(
                    ''' update v2_his_charge_form a inner join v2_his_charge_settle b on a.patient_order_id = b.patient_order_id and a.clinic_id = b.clinic_id
                            set a.settle_charged_price = charged_price,
                                a.settle_total_price = a.total_price,
                                a.settle_uncharged_price = a.uncharged_price
                            where b.pay_status != 0 and a.chain_id = '{chainId}' '''.format(chainId=self.chain_id)
                )
                self.his_charge_db_client.execute(
                    ''' update v2_his_charge_form_item a inner join v2_his_charge_settle b on a.patient_order_id = b.patient_order_id and a.clinic_id = b.clinic_id
                            set a.settle_unit_price = unit_price,
                                a.settle_fraction_price = fraction_price,
                                a.settle_total_price = a.total_price,
                                a.settle_charged_total_price = a.charged_total_price
                            where b.pay_status != 0 and a.chain_id = '{chainId}' '''.format(chainId=self.chain_id)
                )
                self.his_charge_db_client.execute(
                    ''' update v2_his_charge_settle a set a.settle_total_price = a.total_price, settle_type = 0 where a.pay_status != 0 and a.chain_id = '{chainId}' '''.format(chainId=self.chain_id)
                )
                self.his_charge_db_client.execute(
                    ''' update v2_his_charge_form_item_record a inner join v2_his_charge_settle b on a.patient_order_id = b.patient_order_id and a.clinic_id = b.clinic_id
                            set a.settle_amount = amount
                            where b.pay_status != 0 and a.chain_id = '{chainId}' '''.format(chainId=self.chain_id)
                )
                self.his_charge_db_client.execute(
                    ''' update v2_his_charge_form_item_batch_info a inner join v2_his_charge_settle b on a.patient_order_id = b.patient_order_id and a.clinic_id = b.clinic_id
                            set a.settle_unit_price = unit_price,
                                a.settle_total_price = a.total_price,
                                a.settle_charged_total_price = a.charged_total_price
                            where b.pay_status != 0 and a.chain_id = '{chainId}' '''.format(chainId=self.chain_id)
                )
                self.his_charge_db_client.execute(
                    ''' update v2_his_charge_form_item_batch_info_record a inner join v2_his_charge_settle b on a.patient_order_id = b.patient_order_id and a.clinic_id = b.clinic_id
                            set a.settle_amount = amount
                            where b.pay_status != 0 and a.chain_id = '{chainId}' '''.format(chainId=self.chain_id)
                )
                existedRuleData = self.charge_db_client.fetchone(
                    ''' select count(id) as existedRuleDataCount from v2_charge_medicare_limit_price_product where chain_id = '{chainId}' and type = 2 and is_deleted = 0 '''.format(
                        chainId=self.chain_id)
                )
                if existedRuleData['existedRuleDataCount'] > 0:
                    print("organHisType is  100")
                    url = '''http://{rpcHost}/rpc/charges/jenkins/refresh-hospital-limit-price-product'''.format(
                        rpcHost=regionRpcHost(self.region_name))
                    rsp = requests.post(url, json={
                        'chainIds': [self.chain_id]
                    })
                    print(rsp.content)
                    return


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    update_data = FlushHospitalLimitPriceRule(args.region_name, args.chain_id)
    update_data.run()


if __name__ == '__main__':
    main()
