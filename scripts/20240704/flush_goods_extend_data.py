# -*- coding: utf-8 -*-
"""
@name: flush_goods_extend_data.py
@author: <PERSON><PERSON><PERSON>
@email: <EMAIL>
@date: 2024-07-04 23:21:52
"""
import argparse
import json
import sys
import requests
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.rediscli import RedisClient

from multizone.db import DBClient


# 定义位标志
NORMAL = 1 << 0  # 0 常温
COOL = 1 << 1    # 阴凉
COLD = 1 << 2    # 冷藏
FROZEN = 1 << 3  # 冷冻
DARK = 1 << 4    # 避光
SEAL = 1 << 5    # 密封

# 定义标志对应的名称
FLAGS = {
    NORMAL: '常温',
    COOL: '阴凉',
    COLD: '冷藏',
    FROZEN: '冷冻',
    DARK: '避光',
    SEAL: '密封'
}

# 定义一个函数来检查并转换标志
def convert_flags(value):
    result = []
    for flag, name in FLAGS.items():
        if value & flag:
            result.append(name)
    return '，'.join(result)


class UpdateData:
    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.goods_db_client = DBClient(self.region_name, 'abc_cis_stock', 'abc_cis_goods', 'prod', True)
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', 'prod', True)

    def flush_goods_storage_to_extend_data(self):
        goods_list = self.goods_db_client.fetchall("""
        select * from v2_goods where organ_id = '{chainId}' and status = 1 and ((medicine_nmpn_start_expiry_date is not null or medicine_nmpn_end_expiry_date is not null) or storage_type is not null );
        """.format(chainId=self.chain_id))
        for goods in goods_list:
            goods_id = goods['id']
            medicine_nmpn_start_expiry_date = goods['medicine_nmpn_start_expiry_date']
            medicine_nmpn_end_expiry_date = goods['medicine_nmpn_end_expiry_date']
            storage = goods['storage_type']
            print(goods_id, medicine_nmpn_start_expiry_date, medicine_nmpn_end_expiry_date, storage)
            # if storage:
            #     print(convert_flags(storage))
            extend_data = {}
            if medicine_nmpn_start_expiry_date:
                extend_data['medicineNmpnStartExpiryDate'] = medicine_nmpn_start_expiry_date
            if medicine_nmpn_end_expiry_date:
                extend_data['medicineNmpnEndExpiryDate'] = medicine_nmpn_end_expiry_date
            if storage:
                extend_data['storage'] = convert_flags(storage)
            if extend_data:
                # extend_data = json.dumps(extend_data, ensure_ascii=False)
                update_sql = """
                update v2_goods set extend_data = '{extend_data}' where id = '{goodsId}'
                """.format(extend_data=json.dumps(extend_data, ensure_ascii=False), goodsId=goods_id)
                print(update_sql)
                self.goods_db_client.execute(update_sql)
        pass

    def flush_clinic_scope(self):
        organ_his_type = self.basic_db_client.fetchone("""
        select his_type from organ where id = '{chainId}'""".format(chainId=self.chain_id))
        if organ_his_type is None:
            return
        if organ_his_type['his_type'] != 10:
            return
        chain_id = self.chain_id
        organ_sql = f"""
                UPDATE organ
                SET
                business_scope = REPLACE(business_scope, '{{"id": "1", "name": "西药"}}',
                                         '{{"id": "1001", "parentId": "1", "name": "化学制剂"}}'),
                business_scope = REPLACE(business_scope, '{{"id": "5", "name": "化学原料药"}}',
                                         '{{"id": "1000", "parentId": "1", "name": "化学原料药"}}'),
                business_scope = REPLACE(business_scope, '{{"id": "6", "name": "化学药制剂"}}',
                                         '{{"id": "1001", "parentId": "1", "name": "化学药制"}}'),
                business_scope = REPLACE(business_scope, '{{"id": "7", "name": "抗生素原料药"}}',
                                         '{{"id": "1003", "parentId": "1", "name": "抗生素原料药"}}'),
                business_scope = REPLACE(business_scope, '{{"id": "8", "name": "抗生素制剂"}}',
                                         '{{"id": "1004", "parentId": "1", "name": "抗生素制剂"}}'),
                business_scope = REPLACE(business_scope, '{{"id": "9", "name": "生化药品"}}',
                                         '{{"id": "1002", "parentId": "1", "name": "生化药品"}}'),
                business_scope = REPLACE(business_scope, '{{"id": "11", "name": "注射制剂"}}',
                                         '{{"id": "1001", "parentId": "1", "name": "化学药制"}}'),
                business_scope = REPLACE(business_scope, '{{"id": "3", "name": "中药材"}}',
                                         '{{"id": "4001", "parentId": "4", "name": "不含配方"}}'),
                business_scope = REPLACE(business_scope, '{{"id": "4", "name": "中药饮片"}}',
                                         '{{"id": "4000", "parentId": "4", "name": "含配方"}}'),
                business_scope = REPLACE(business_scope, '{{"id": "15", "name": "罂粟壳"}}',
                                         '{{"id": "4002", "parentId": "4", "name": "含罂粟壳"}}'),
                business_scope = REPLACE(business_scope, '{{"id": "16", "name": "食品"}}',
                                         '{{"id": "16050", "parentId": "16", "name": "其他食品"}}'),
                business_scope = REPLACE(business_scope, '{{"id": "17", "name": "保健食品"}}',
                                         '{{"id": "16010", "parentId": "16", "name": "保健食品"}}'),
                business_scope = REPLACE(business_scope, '{{"id": "18", "name": "含婴幼儿配方乳制品"}}',
                                         '{{"id": "16030", "parentId": "16", "name": "婴幼儿配方食品"}}'),
                business_scope = REPLACE(business_scope, '{{"id": "19", "name": "其他特殊食品"}}',
                                         '{{"id": "16020", "parentId": "16", "name": "特殊医学用途配方食品"}},
                                         {{"id": "16040", "parentId": "16", "name": "特殊膳食食品"}}'),
                business_scope = REPLACE(business_scope, '{{"id": "28", "name": "农副产品"}}',
                                         '{{"id": "16050", "parentId": "16", "name": "其他食品"}}'),
                business_scope = REPLACE(business_scope, '{{"id": "23", "name": "普通化妆品"}}',
                                         '{{"id": "23010", "parentId": "23", "name": "普通化妆品"}}'),
                business_scope = REPLACE(business_scope, '{{"id": "24", "name": "特殊化妆品"}}',
                                         '{{"id": "23020", "parentId": "23", "name": "特殊化妆品"}}'),
                business_scope = REPLACE(business_scope, '{{"id": "13", "name": "二类精神药品"}}',
                                         '{{"id": "12", "name": "第二类精神药品"}}'),
                business_scope = REPLACE(business_scope, '{{"id": "12", "name": "含麻药品"}}',
                                         '{{"id": "12", "name": "含麻黄碱类复方制剂"}}'),
                business_scope = REPLACE(business_scope, '{{"id": "10", "name": "生物制品（疫苗除外）"}}',
                                         '{{"id": "10", "name": "生物制品"}}')
                WHERE parent_id = '{chain_id}'
                """
        self.basic_db_client.execute(organ_sql)
        # 供应商
        supplier_sql = f"""
UPDATE v2_goods_supplier SET
    business_scope = REPLACE(business_scope, '{{"id": "1"}}', '{{"id": "1001", "parentId": "1"}}'),
    business_scope = REPLACE(business_scope, '{{"id": "5"}}', '{{"id": "1000", "parentId": "1"}}'),
    business_scope = REPLACE(business_scope, '{{"id": "6"}}', '{{"id": "1001", "parentId": "1"}}'),
    business_scope = REPLACE(business_scope, '{{"id": "7"}}', '{{"id": "1003", "parentId": "1"}}'),
    business_scope = REPLACE(business_scope, '{{"id": "8"}}', '{{"id": "1004", "parentId": "1"}}'),
    business_scope = REPLACE(business_scope, '{{"id": "9"}}', '{{"id": "1002", "parentId": "1"}}'),
    business_scope = REPLACE(business_scope, '{{"id": "11"}}', '{{"id": "1001", "parentId": "1"}}'),
    business_scope = REPLACE(business_scope, '{{"id": "3"}}', '{{"id": "4001", "parentId": "4"}}'),
    business_scope = REPLACE(business_scope, '{{"id": "4"}}', '{{"id": "4000", "parentId": "4"}}'),
    business_scope = REPLACE(business_scope, '{{"id": "15"}}', '{{"id": "4002", "parentId": "4"}}'),
    business_scope = REPLACE(business_scope, '{{"id": "16"}}', '{{"id": "16050", "parentId": "16"}}'),
    business_scope = REPLACE(business_scope, '{{"id": "17"}}', '{{"id": "16010", "parentId": "16"}}'),
    business_scope = REPLACE(business_scope, '{{"id": "18"}}', '{{"id": "16030", "parentId": "16"}}'),
    business_scope = REPLACE(business_scope, '{{"id": "19"}}', '{{"id": "16020", "parentId": "16"}}, {{"id": "16040", "parentId": "16"}}'),
    business_scope = REPLACE(business_scope, '{{"id": "28"}}', '{{"id": "16050", "parentId": "16"}}'),
    business_scope = REPLACE(business_scope, '{{"id": "23"}}', '{{"id": "23010", "parentId": "23"}}'),
    business_scope = REPLACE(business_scope, '{{"id": "24"}}', '{{"id": "23020", "parentId": "23"}}'),
    business_scope = REPLACE(business_scope, '{{"id": "13"}}', '{{"id": "12"}}'),
    business_scope = REPLACE(business_scope, '{{"id": "12"}}', '{{"id": "12"}}'),
    business_scope = REPLACE(business_scope, '{{"id": "10"}}', '{{"id": "10"}}')
WHERE chain_id = '{chain_id}'
"""
        self.goods_db_client.execute(supplier_sql)
        pass

    def run(self):
        self.flush_goods_storage_to_extend_data()
        self.flush_clinic_scope()

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    update_data = UpdateData(args.region_name, args.chain_id)
    update_data.run()


if __name__ == '__main__':
    main()
