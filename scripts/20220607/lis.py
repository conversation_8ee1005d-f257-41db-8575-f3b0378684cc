#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys 
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db 
import argparse
import requests

def run_for_examination(chain_id):
    db_client = db.DBClient('abc_cis_outpatient', 'abc_cis_examination')
    sql = '''
        update v2_examination_sheet set device_data = items_value where items_value is not null and chain_id = '{0}';
    '''.format(chain_id)
    db_client.execute(sql)

    sql = '''
        update v2_examination_sheet set device_model_id = 0 where type = 1 and chain_id = '{0}';
    '''.format(chain_id)
    db_client.execute(sql)

    goods_db_client = db.DBClient('abc_cis_stock', 'abc_cis_goods')
    sql = '''
        select id from v2_goods_examination_device where goods_type = 3 and goods_sub_type = 2 and inner_flag = 1 and chain_id = '{0}';
    '''.format(chain_id)
    result = goods_db_client.fetchone(sql)

    if result:
        device_model_id = result['id']
        sql = '''
        update v2_examination_sheet set device_model_id = '{1}' where chain_id = '{0}' and type = 2;
        '''.format(chain_id, device_model_id)
        db_client.execute(sql)

    # call rpc
    rsp = requests.post('http://pre.rpc.abczs.cn/rpc/examinations/items/jenkins/flush/goodsId', json={'chainIds': chain_id})
    print rsp.content


def run_for_goods(chain_id):
    db_client = db.DBClient('abc_cis_stock', 'abc_cis_goods')
    #.把老的检查goods 组合项目 全改成 单个项目
    sql = '''
        update v2_goods set combine_type  = 0  where type = 3 and sub_type = 2 and combine_type = 1 and organ_id = '{0}';
    '''.format(chain_id)
    db_client.execute(sql)

    #.为每个连锁 插入一条 检查 未知设备
    sql = u'''
    INSERT INTO v2_goods_examination_device (id, device_model_id, chain_id, clinic_id, goods_type, goods_sub_type,
                                         device_type, name, inspect_device_model, is_virtual, inner_flag, status, sort,
                                         created_by, created, last_modified_by, last_modified, short_id)
    VALUES (substr(uuid_short(), 4), null, '{0}', '{0}', 3, 2, 0, '未知设备', '未知型号', 1, 1, 10, 0,
            '00000000000000000000000000000000', now(), '00000000000000000000000000000000', now(), '000001');
    '''.format(chain_id)
    db_client.execute(sql)
    
    #将连锁内 检查goods绑定到这个 刚刚建的未知设备
    sql = '''
    update v2_goods as a inner join 
        v2_goods_examination_device as b on a.organ_id = b.chain_id and a.type_id = 21 and
                                                                    b.goods_type = 3 and b.goods_sub_type = 2 and
                                                                    b.inner_flag = 1
    set a.biz_relevant_id = b.id
    where a.biz_relevant_id is null and a.organ_id = '{0}'
    '''.format(chain_id)
    db_client.execute(sql)

    #建连锁内的所有检验goods绑定到未知设备型号上
    sql = '''
    update v2_goods set v2_goods.biz_relevant_id ='0' where type_id = 20 and v2_goods.biz_relevant_id is null and organ_id = '{0}';
    '''.format(chain_id)
    db_client.execute(sql)

    #清理goodsRedis缓存
    for port in ['6179', '6279', '6379']:
        command = '''
        redis-cli -h 172.19.119.224 -n 0 -p {1} keys '_scgoods:g:info:{0}:*' | xargs redis-cli -h 172.19.119.224 -n 0 -p {1} del
        '''.format(chain_id, port)
        os.system(command)


def run(chain_id):
    run_for_goods(chain_id)
    run_for_examination(chain_id)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)

if __name__ == '__main__':
    main()
