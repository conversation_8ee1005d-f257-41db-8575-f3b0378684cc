#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys 
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db 
import argparse


UPDATE_CHARGE_FORM_ITEM_SQLS = [
# 更新加工费 product_id
{
  'query': '''
    select id from v2_charge_form_item
    where chain_id = '{0}'
      and product_id = '00000000000000000000000000000004'
      and name = '加工费';
  ''',
  'update': '''
    update v2_charge_form_item
    set product_id       = '00000000000000000000000000000010',
        product_sub_type = 0
    where 
      chain_id = '{0}'
      and id in ({1});
  ''',
},
# 更新煎药费 product_sub_type
{
  'query': '''
    select id from v2_charge_form_item
    where chain_id = '{0}'
      and product_id = '00000000000000000000000000000004'
      and name in ('煎药费', '煎药');
  ''',
  'update': '''
    update v2_charge_form_item
    set product_sub_type = 1
    where chain_id = '{0}'
    and id in ({1});
  '''
},
# 手工煎药
{
  'query': '''
    select id from v2_charge_form_item
    where chain_id = '{0}'
      and product_id = '00000000000000000000000000000004'
      and name in ('手工煎药', '人工煎药');
  ''',
  'update': '''
    update v2_charge_form_item
    set product_sub_type = 1,
    product_id       = '00000000000000000000000000000017'
    where chain_id = '{0}'
    and id in ({1});
  '''
},
# 机器煎药（普通）
{
  'query': '''
    select id from v2_charge_form_item
    where chain_id = '{0}'
  and product_id = '00000000000000000000000000000004'
  and name in ('机器煎药（普通）', '机器煎药');
  ''',
  'update': '''
    update v2_charge_form_item
    set product_sub_type = 1,
    product_id       = '00000000000000000000000000000018'
    where chain_id = '{0}'
    and id in ({1});
  '''
},
# 机器煎药（浓缩）
{
  'query': '''
    select id from v2_charge_form_item
    where chain_id = '{0}'
  and product_id = '00000000000000000000000000000004'
  and name = '机器煎药（浓缩）';
  ''',
  'update': '''
    update v2_charge_form_item
    set product_sub_type = 1,
    product_id       = '00000000000000000000000000000019'
    where chain_id = '{0}'
    and id in ({1});
  '''
},
# 制膏
{
  'query': '''
    select id from v2_charge_form_item
    where chain_id = '{0}'
  and product_id = '00000000000000000000000000000004'
  and name = '制膏';
  ''',
  'update': '''
    update v2_charge_form_item
    set product_sub_type = 2,
    product_id       = '00000000000000000000000000000011'
    where chain_id = '{0}'
    and id in ({1});
  '''
},
# 儿童膏方
{
  'query': '''
    select id from v2_charge_form_item
    where chain_id = '{0}'
  and product_id = '00000000000000000000000000000004'
  and name = '儿童膏方';
  ''',
  'update': '''
    update v2_charge_form_item
    set product_sub_type = 2,
    product_id       = '00000000000000000000000000000020'
    where chain_id = '{0}'
    and id in ({1});
  '''
},
# 成人膏方
{
  'query': '''
    select id from v2_charge_form_item
    where chain_id = '{0}'
  and product_id = '00000000000000000000000000000004'
  and name = '成人膏方';
  ''',
  'update': '''
    update v2_charge_form_item
set product_sub_type = 2,
    product_id       = '00000000000000000000000000000021'
    where chain_id = '{0}'
    and id in ({1});
  '''
},
# 打粉
{
  'query': '''
    select id from v2_charge_form_item
    where chain_id = '{0}'
  and product_id = '00000000000000000000000000000004'
  and name = '打粉';
  ''',
  'update': '''
    update v2_charge_form_item
set product_sub_type = 3,
    product_id       = '00000000000000000000000000000012'
    where chain_id = '{0}'
    and id in ({1});
  '''
},
# 粗粉
{
  'query': '''
    select id from v2_charge_form_item
    where chain_id = '{0}'
  and product_id = '00000000000000000000000000000004'
  and name = '粗粉';
  ''',
  'update': '''
    update v2_charge_form_item
set product_sub_type = 3,
    product_id       = '00000000000000000000000000000022'
    where chain_id = '{0}'
    and id in ({1});
  '''
},
# 细粉
{
  'query': '''
    select id from v2_charge_form_item
    where chain_id = '{0}'
  and product_id = '00000000000000000000000000000004'
  and name = '细粉';
  ''',
  'update': '''
    update v2_charge_form_item
set product_sub_type = 3,
    product_id       = '00000000000000000000000000000023'
    where chain_id = '{0}'
    and id in ({1});
  '''
},
# 超细粉
{
  'query': '''
    select id from v2_charge_form_item
    where chain_id = '{0}'
  and product_id = '00000000000000000000000000000004'
  and name = '超细粉';
  ''',
  'update': '''
    update v2_charge_form_item
set product_sub_type = 3,
    product_id       = '00000000000000000000000000000024'
    where chain_id = '{0}'
    and id in ({1});
  '''
},
# 制丸
{
  'query': '''
    select id from v2_charge_form_item
    where chain_id = '{0}'
  and product_id = '00000000000000000000000000000004'
  and name = '制丸';
  ''',
  'update': '''
    update v2_charge_form_item
set product_sub_type = 4,
    product_id       = '00000000000000000000000000000013'
    where chain_id = '{0}'
    and id in ({1});
  '''
},
# 水丸
{
  'query': '''
    select id from v2_charge_form_item
    where chain_id = '{0}'
  and product_id = '00000000000000000000000000000004'
  and name = '水丸';
  ''',
  'update': '''
    update v2_charge_form_item
set product_sub_type = 4,
    product_id       = '00000000000000000000000000000025'
    where chain_id = '{0}'
    and id in ({1});
  '''
},
# 蜜丸
{
  'query': '''
    select id from v2_charge_form_item
    where chain_id = '{0}'
  and product_id = '00000000000000000000000000000004'
  and name = '蜜丸';
  ''',
  'update': '''
    update v2_charge_form_item
set product_sub_type = 4,
    product_id       = '00000000000000000000000000000026'
    where chain_id = '{0}'
    and id in ({1});
  '''
},
# 水蜜丸
{
  'query': '''
    select id from v2_charge_form_item
    where chain_id = '{0}'
  and product_id = '00000000000000000000000000000004'
  and name = '水蜜丸';
  ''',
  'update': '''
    update v2_charge_form_item
set product_sub_type = 4,
    product_id       = '00000000000000000000000000000027'
    where chain_id = '{0}'
    and id in ({1});
  '''
},
# 浓缩丸
{
  'query': '''
    select id from v2_charge_form_item
    where chain_id = '{0}'
  and product_id = '00000000000000000000000000000004'
  and name = '浓缩丸';
  ''',
  'update': '''
    update v2_charge_form_item
set product_sub_type = 4,
    product_id       = '00000000000000000000000000000028'
    where chain_id = '{0}'
    and id in ({1});
  '''
},
# 颗粒
{
  'query': '''
    select id from v2_charge_form_item
    where chain_id = '{0}'
  and product_id = '00000000000000000000000000000004'
  and name = '颗粒';
  ''',
  'update': '''
    update v2_charge_form_item
set product_sub_type = 5,
    product_id       = '00000000000000000000000000000014'
    where chain_id = '{0}'
    and id in ({1});
  '''
},
# 茶包
{
  'query': '''
    select id from v2_charge_form_item
    where chain_id = '{0}'
  and product_id = '00000000000000000000000000000004'
  and name = '茶包';
  ''',
  'update': '''
    update v2_charge_form_item
set product_sub_type = 6,
    product_id       = '00000000000000000000000000000015'
    where chain_id = '{0}'
    and id in ({1});
  '''
},
# 胶囊
{
  'query': '''
    select id from v2_charge_form_item
    where chain_id = '{0}'
  and product_id = '00000000000000000000000000000004'
  and name = '胶囊';
  ''',
  'update': '''
    update v2_charge_form_item
set product_sub_type = 7,
    product_id       = '00000000000000000000000000000016'
    where chain_id = '{0}'
    and id in ({1});
  '''
}
]

# 更新完 v2_charge_form_item 后更新 v2_charge_transaction_record
UPDATE_CHARGE_TRANSACTION_RECORD_SQL = '''
update v2_charge_transaction_record a 
inner join v2_charge_form_item b on a.charge_form_item_id = b.id and
                                                                          b.chain_id = a.chain_id and
                                                                          b.product_id in
                                                                          ('00000000000000000000000000000010',
                                                                           '00000000000000000000000000000004',
                                                                           '00000000000000000000000000000011',
                                                                           '00000000000000000000000000000012',
                                                                           '00000000000000000000000000000013',
                                                                           '00000000000000000000000000000014',
                                                                           '00000000000000000000000000000015',
                                                                           '00000000000000000000000000000016',
                                                                           '00000000000000000000000000000017',
                                                                           '00000000000000000000000000000018',
                                                                           '00000000000000000000000000000019',
                                                                           '00000000000000000000000000000020',
                                                                           '00000000000000000000000000000021',
                                                                           '00000000000000000000000000000022',
                                                                           '00000000000000000000000000000023',
                                                                           '00000000000000000000000000000024',
                                                                           '00000000000000000000000000000025',
                                                                           '00000000000000000000000000000026',
                                                                           '00000000000000000000000000000027',
                                                                           '00000000000000000000000000000028'
                                                                              )
set a.product_id       = b.product_id,
    a.product_sub_type = b.product_sub_type
where a.chain_id = '{0}'
  and b.chain_id = '{0}'
  and a.product_id = '00000000000000000000000000000004'
  and a.charge_form_item_id in ({1})
  and b.id in ({1})
'''


def query_and_update(item, chain_id):
    adb_client = db.DBClient('adb', 'abc_cis_charge')
    charge_items = adb_client.fetchall(item['query'].format(chain_id))

    if not charge_items or len(charge_items) == 0:
      return
    
    db_client = db.DBClient('abc_cis_charge', 'abc_cis_charge')
    batch = (len(charge_items) - 1) / 1000 + 1
    for i in range(batch):
      start = i * 1000
      end = start + 1000 if start + 1000 < len(charge_items) else len(charge_items)
      update_sql = item['update'].format(chain_id, ','.join(['\'' + charge_item['id'] + '\'' for charge_item in charge_items[start:end]]))
      db_client.execute(update_sql)
      update_record_sql = UPDATE_CHARGE_TRANSACTION_RECORD_SQL.format(chain_id, ','.join(['\'' + charge_item['id'] + '\'' for charge_item in charge_items[start:end]]))
      db_client.execute(update_record_sql)


def run(chain_id):
    for item in UPDATE_CHARGE_FORM_ITEM_SQLS:
       query_and_update(item, chain_id)
    # db_client = db.DBClient('abc_cis_charge', 'abc_cis_charge')
    # db_client.execute(UPDATE_CHARGE_TRANSACTION_RECORD_SQL.format(chain_id))


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)

if __name__ == '__main__':
    main()
