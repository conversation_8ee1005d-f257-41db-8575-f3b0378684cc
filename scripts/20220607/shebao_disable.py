#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys 
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db 
import argparse


def query_shebao_disable(chain_id):
    sql = '''
        select t.id, t.chain_id, t.clinic_id, t.goods_id
        from abc_cis_shebao.shebao_chengdu_matched_code t
        where t.is_deleted = 0
        and t.national_standard_code = 'DISABLED'
        and t.chain_id = '{0}'
    '''.format(chain_id)

    db_client = db.DBClient('abc_cis_bill', 'abc_cis_shebao')
    return db_client.fetchall(sql)

def update_shebao_disable(chain_id):
    sql = '''
        update abc_cis_shebao.shebao_chengdu_matched_code t
        set t.is_deleted = 1,
            t.last_modified = now()
        where t.is_deleted = 0
        and t.national_standard_code = 'DISABLED'
        and t.chain_id = '{0}'
    '''.format(chain_id)

    db_client = db.DBClient('abc_cis_bill', 'abc_cis_shebao')
    return db_client.execute(sql)

def update_or_insert_goods_extend(goods_db_client, goods):
    sql = '''
        select * from abc_cis_goods.v2_goods_extend where chain_id = '{0}' and organ_id = '{1}' and goods_id = '{2}'
    '''.format(goods['chain_id'], goods['clinic_id'], goods['goods_id'])

    goods_extend = goods_db_client.fetchone(sql)

    if goods_extend:
        update_sql = '''
            update abc_cis_goods.v2_goods_extend set shebao_pay_mode = 2 where chain_id = '{0}' and organ_id = '{1}' and goods_id = '{2}'
        '''.format(goods['chain_id'], goods['clinic_id'], goods['goods_id'])
        goods_db_client.execute(update_sql)
    else:
        goods_db_client.insert_item('v2_goods_extend', {
            'chain_id': goods['chain_id'],
            'organ_id': goods['clinic_id'], 
            'goods_id': goods['goods_id'],
            'shebao_pay_mode': 2,
        })

def run(chain_id):
    goods_list = query_shebao_disable(chain_id)
    if not goods_list or len(goods_list) == 0:
        return 

    goods_db_client = db.DBClient('abc_cis_stock', 'abc_cis_goods')
    for goods in goods_list:
        update_or_insert_goods_extend(goods_db_client, goods)

    update_shebao_disable(chain_id)



def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)

if __name__ == '__main__':
    main()
