#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys 
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db 
import argparse
import requests

def run(chain_id):
    db_client = db.DBClient('abc_cis_charge', 'abc_cis_charge')
    sql = '''
      INSERT IGNORE INTO v2_charge_pay_mode_relation (id, clinic_id, chain_id, pay_mode_config_id, sort, is_enable, is_deleted, created, created_by, last_modified_by, last_modified)
      select substr(uuid_short(), 4), chain_id, chain_id, 20, max(sort) + 1, 1, 0, now(), '00000000000000000000000000000000', '00000000000000000000000000000000', now() 
      from v2_charge_pay_mode_relation
      where chain_id = '{0}' and is_deleted = 0 group by chain_id;
    '''.format(chain_id)
    db_client.execute(sql)

    rsp = requests.post('http://pre.rpc.abczs.cn/rpc/charges/jenkins/hospital/refresh-to-combine-order', json= {
      'chainIds': chain_id,
      'hospitalSheetIds': ''
    })
    print rsp.content

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)

if __name__ == '__main__':
    main()
