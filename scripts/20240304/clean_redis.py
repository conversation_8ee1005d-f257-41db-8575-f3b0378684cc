import os
import sys

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.rediscli import RedisClient
from multizone.db import DBClient

if __name__ == '__main__':
    adb_cli = DBClient('ShangHai', 'adb', 'abc_cis_basic', 'prod', True)
    chains = adb_cli.fetchall("""select o.id, o.node_type, cro.region_id
                                from organ o
                                         inner join v2_clinic_region_organ cro on o.id = cro.chain_id
                                where node_type = 1
                                  and status = 1""")

    region_id_to_redis_cli = {
        1: RedisClient('ShangHai', 'abc-redis', 35, 'prod', False),
        2: RedisClient('HangZhou', 'abc-redis', 35, 'prod', False),
    }
    for chain in chains:
        chain_id = chain['id']
        node_type = chain['node_type']
        region_id = chain['region_id']
        if region_id == 2:
            continue
        redis_cli = region_id_to_redis_cli[region_id]
        redis_cli.client.delete(f"examination:item_code:{chain_id}::1")
        redis_cli.client.delete(f"examination:item_code:{chain_id}::2")
