# -*- coding: utf-8 -*-
"""
@name: flush_advice_support_nid.py
@author: <PERSON><PERSON><PERSON>
@email: <EMAIL>
@date: 2023-11-21 22:41:44
"""
import argparse
import sys
import requests
import os
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.rediscli import RedisClient

from multizone.db import DBClient
from multizone.rpc import regionRpcHost


DEFAULT_ID = '00000000000000000000000000000000'


class UpdateData:
    chain_id = None
    basic_db_client = None
    goods_db_client = None
    his_advice_client = None

    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', 'prod', True)
        self.goods_db_client = DBClient(self.region_name, 'abc_cis_stock', 'abc_cis_goods', 'prod', True)
        # self.his_advice_client = DBClient(self.region_name, 'scrm_hospital', 'abc_his_advice', 'prod', True)
        # self.his_charge_client = DBClient(self.region_name, 'scrm_hospital', 'abc_his_charge', 'prod', True)
        # self.dispense_db_client = DBClient(self.region_name, 'abc_cis_stock_zip', 'abc_cis_dispensing', 'prod', True)
        # self.adb_db_client =DBClient(self.region_name, 'adb', 'abc_cis_dispensing', 'prod', True)

    def run(self):
        organHisType = self.basic_db_client.fetchone(
            ''' select his_type from organ where id = '{chainId}' '''.format(chainId=self.chain_id))
        if organHisType:
            if organHisType['his_type'] == 100:
                print("organHisType is  100")

                self.flush_advice_goods_need_executive()
                requests.get("""http://{rpcHost}/rpc/v3/goods/jenkins/clean-chain-redis-cache?chainId={chainId}"""
                             .format(chainId=self.chain_id, rpcHost=regionRpcHost(self.region_name)))

                return



    def flush_advice_goods_need_executive(self):
        """
        治疗医嘱老数据处理，都需要执行
        :return:
        """
        goods_id_list = self.goods_db_client.fetchall("""
            SELECT g.id as id
                FROM v2_goods g
                WHERE
                   g.organ_id = '{chainId}'
                   AND g.status != 99
                   AND g.type IN (4)
        """.format(chainId=self.chain_id))
        if goods_id_list is None or len(goods_id_list) == 0:
            return
        update_sql_list = []
        for goods_id_dict in goods_id_list:
            goods_id = goods_id_dict['id']
            update_sql = """
            update v2_goods set need_executive = need_executive | 0x2 where id = '{goodsId}'
            """.format(goodsId=goods_id)
            # print(update_sql)
            update_sql_list.append(update_sql)

        for sql in update_sql_list:
            # print(sql)
            self.goods_db_client.execute(sql)


    #手写医嘱下达



def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    update_data = UpdateData(args.region_name, args.chain_id)
    update_data.run()




if __name__ == '__main__':
    main()

