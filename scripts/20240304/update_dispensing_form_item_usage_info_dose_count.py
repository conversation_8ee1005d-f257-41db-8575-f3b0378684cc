#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import logging
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.rpc import regionRpcHost
from multizone.db import DBClient
import argparse
import requests

default_id = '00000000000000000000000000000000'


def run(region_name, chain_id, env):
    dispensing_db_cli = DBClient(region_name, 'abc_cis_stock_zip', 'abc_cis_dispensing', env, True)
    ods_db_cli = DBClient(region_name, 'ods', 'abc_cis_dispensing', env, True)
    organ = ods_db_cli.fetchone(f"""select * from abc_cis_basic.organ where id = '{chain_id}'""")
    if organ is None:
        return
    if organ['his_type'] != 100:
        print(f'skip not hospital chain_id: {chain_id}')
        return
    sql_list1 = ods_db_cli.fetchall(f"""
        select concat('update v2_dispensing_form set usage_info = json_set(usage_info, \\'$.doseCount\\', ', single_dosage_count,')', ' where id = \\'', id, '\\';') as `sql`
        from (select df.id as id,
                     df.chain_id as chain_id,
                     usage_info -> '$.doseCount' as doseCount,
                     single_dosage_count
              from v2_dispensing_form df
                       inner join abc_his_advice.v1_advice_rule dr on df.advice_rule_id = dr.id
              where usage_info is not null
                and usage_info != cast('null' as json) and dr.type = 0 and df.type = 10) as r
        where doseCount != cast(single_dosage_count as decimal ) and chain_id = '{chain_id}';
    """)
    sql_list2 = ods_db_cli.fetchall(f"""
        select concat('update v2_dispensing_form set usage_info = json_set(usage_info, \\'$.singleDosageCount\\', ', '\\'', single_dosage_count, '\\'', ')', ' where id = \\'', df.id, '\\';') as `sql`
        from v2_dispensing_form df
         inner join abc_his_advice.v1_advice_rule dr on df.advice_rule_id = dr.id
        where df.chain_id = '{chain_id}';
    """)
    if len(sql_list1) != 0:
        for sql1 in sql_list1:
            dispensing_db_cli.execute(sql1['sql'])
    if len(sql_list2) != 0:
        for sql2 in sql_list2:
            dispensing_db_cli.execute(sql2['sql'])




def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    run(args.region_name, args.chain_id, 'prod')
    # run('ShangHai', 'ffffffff00000000146808c695534000', 'dev')


if __name__ == '__main__':
    main()
