"""
刷新formIsDeleted 的数据
"""
import os
import sys
import argparse
import json
from datetime import date, datetime, time, timedelta

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import requests
from multizone.rpc import regionRpcHost
from multizone.db import DBClient


# 刷进销存数据
class UpdateData:
    chain_id = None
    goods_db_client = None
    goods_log_db_client = None
    adb_client = None


    def __init__(self, region_name, chain_id):
        self.env = 'prod'
        self.chain_id = chain_id
        self.region_name = region_name
        self.goods_db_client = DBClient(self.region_name, 'abc_cis_stock', 'abc_cis_goods', self.env, True)
        self.basic_db_client = DBClient(region_name, 'abc_cis_mixed', 'abc_cis_basic', 'prod', True)

    def run(self):
        try:
            self.updateGoodsClinicConfigIndepent()
            self.updateGoodsStockLog()
            self.updateGoodsMedicine();
            requests.get("""http://{rpcHost}/rpc/v3/goods/jenkins/clean-chain-redis-cache?chainId={chainId}""".format(chainId = self.chain_id,rpcHost=regionRpcHost(self.region_name)))
        except Exception as e:
            print("id:", self.chain_id, ' error:: ', str(e))
            raise e
        finally:
            self.goods_db_client.close()

    def updateGoodsMedicine(self):
        try:
            organViewMode = self.goods_db_client.fetchall(''' select clinic_id,independent_pricing,view_mode,node_type from v2_goods_clinic_config where chain_id = '{chainId}' '''.format(chainId=self.chain_id))
            # 遍历organViewMode
            for item in organViewMode:
                clinicId = item['clinic_id']
                independentPricing = item['independent_pricing']
                viewMode = item['view_mode']
                nodeType = item['node_type']
                # 子店 没有子店定价
                if viewMode == 0 and independentPricing == 0 and nodeType == 2:
                    # 查v2_goods_price表，如果这个表里面又clinicId的定价数据，就把这个clinicId的数据的independent_pricing改为2
                    count = self.goods_db_client.fetchone(''' select count(1) as c from v2_goods_price where organ_id = '{clinicId}' '''.format(clinicId=clinicId))
                    if count['c'] > 0:
                        sql = ''' update v2_goods_clinic_config set independent_pricing = 2 where clinic_id = '{clinicId}' and chain_id = '{chainId}' '''.format(clinicId=clinicId, chainId=self.chain_id)
                        self.goods_db_client.execute(sql)



        except Exception as e:
            print(e)
    def updateGoodsClinicConfigIndepent(self):
        try:
            sqls = [
                '''update v2_goods set medicine_nmpn = certificate_no where type in (2, 7) and organ_id = '{chainId}' and (medicine_nmpn is  null or medicine_nmpn ='') and certificate_no  is not null ;''',
                '''update v2_goods_clinic_config set clinic_external_flag = clinic_external_flag|504 where node_type = 2  and chain_id = '{chainId}';''',
                '''update v2_goods_chain_config as a inner join v2_goods_clinic_config c on a.chain_id = c.chain_id set c.independent_pricing =2 where a.sub_set_price = 1 and a.sub_set_price_all_clinics = 1 and a.chain_id = '{chainId}';'''
            ]
            for sql in sqls:
                self.goods_db_client.execute(sql.format(chainId=self.chain_id))
        except Exception as e:
            print(e)

    # 刷这个logBat有几条日志
    def updateGoodsStockLog(self):
        sql = """ select CONCAT('update v2_goods_clinic_config set independent_pricing=2 where clinic_id =\''', clinicId, '\'';') as exeSql 
from (
         select sub_set_price_clinics,
                t.n,
                JSON_UNQUOTE(json_extract(sub_set_price_clinics, CONCAT('$[', t.n, '].clinicId'))) as clinicId
         from v2_goods_chain_config
                  cross join (select (row_number() over () - 1) as n from information_schema.tables limit 200) as t
         where sub_set_price = 1
           and sub_set_price_all_clinics != 1
           and chain_id = '{chainId}'
           and JSON_LENGTH(sub_set_price_clinics) != 0) as t
where t.clinicId is not null; """.format(
            chainId=self.chain_id)
        goods_sql_res = self.goods_db_client.fetchall(sql)
        for stmt in goods_sql_res:
            sql = stmt['exeSql']
            if sql is None:
                continue
            print(sql)
            self.goods_db_client.execute(sql)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    updateData = UpdateData(args.region_name, args.chain_id)
    updateData.run()


if __name__ == '__main__':
    main()
