#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import logging
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.rpc import regionRpcHost
from multizone.db import DBClient
import argparse
import requests

default_id = '00000000000000000000000000000000'


def run(region_name, chain_id, env):
    goods_db_cli = DBClient(region_name, 'abc_cis_stock', 'abc_cis_goods', env, True)
    # 刷medical_stat
    goods_db_cli.execute(f"""
        update v2_goods g join v2_goods_medical_stat s on g.id = s.goods_id
        set s.extend_info = json_object('bizRelevantIds', json_array(g.biz_relevant_id))
        where g.organ_id = '{chain_id}' and g.type in (3, 27)
          and (g.sub_type = 1 or (g.sub_type = 2 and g.extend_spec = '20'));
    """)
    goods_db_cli.execute(f"""
    update abc_cis_goods.v2_goods
        set biz_relevant_id = 10
        where type = 21
            and biz_relevant_id is null and organ_id = '{chain_id}';
    """)



def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    run(args.region_name, args.chain_id, 'prod')


if __name__ == '__main__':
    main()
