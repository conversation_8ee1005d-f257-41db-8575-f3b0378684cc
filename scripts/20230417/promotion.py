#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

reload(sys)
sys.setdefaultencoding('utf-8')
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db
import argparse

default_id = '00000000000000000000000000000000'


def updateInVoiceType(chain_id):
    patient_db_client = db.DBClient('abc_cis_charge', 'abc_cis_promotion')
    patient_db_client.execute("""
            update v2_promotion_card_patient_charge_record
            set original_price = pay_money
            where charge_type in (1, 2)
             and original_price is  null
              and chain_id = '{chainId}';""".format(chainId=chain_id))


def run(chain_id):
    updateInVoiceType(chain_id)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)


if __name__ == '__main__':
    main()
