#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import logging
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

reload(sys)
sys.setdefaultencoding('utf-8')
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db
import argparse

default_id = '00000000000000000000000000000000'


def updateInVoiceType(chain_id):
    patient_db_client = db.DBClient('abc_cis_bill', 'abc_cis_invoice')
    patient_db_client.execute(""" update v2_invoice_record
                            set business_scene = 'registration'
                                where invoice_type = 10
                            and business_scene = 'charge' and chain_id = '{chainId}'""".format(chainId=chain_id))
def updateCharge(chain_id):
    ods_db_client = db.DBClient('ods', 'abc_cis_charge')
    updateSql = ods_db_client.fetchone(""" select o.parent_id as clinicId
      from abc_cis_basic.organ o
      where o.address_province_id = 450000
        and o.status < 90
        and node_type = 1
        and o.parent_id ='{chainId}'
        and o.id not in (select chain_id from abc_cis_charge.v2_charge_pay_mode_relation where pay_mode_config_id = 23);""".format(chainId=chain_id))
    if updateSql:
        sql ="""INSERT INTO abc_cis_charge.v2_charge_pay_mode_relation (
        id, clinic_id, chain_id, pay_mode_config_id, sort, is_enable, is_deleted, created, created_by, last_modified_by, last_modified) values (
        substr(uuid_short(), 4),'{clinicId}','{clinicId}',23,23,0,0,now(),'0000000000000000000000000000','0000000000000000000000000000',now());""".format(clinicId =updateSql['clinicId'])
        logging.warn(sql)
        charge_db_client = db.DBClient('abc_cis_charge', 'abc_cis_charge')
        charge_db_client.execute(sql)


def run(chain_id):
    updateInVoiceType(chain_id)
    updateCharge(chain_id)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)


if __name__ == '__main__':
    main()
