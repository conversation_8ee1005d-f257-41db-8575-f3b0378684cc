"""
刷新formIsDeleted 的数据
"""
import os
import sys
import argparse

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import requests
from multizone.rpc import regionRpcHost
from multizone.db import DBClient


# 刷进销存数据
class UpdateData:
    chain_id = None
    goods_db_client = None
    goods_log_db_client = None
    adb_client = None
    his_type = None
    old_physical_examination_fee_type_id = None

    def __init__(self, region_name, chain_id):
        self.env = 'prod'
        self.chain_id = chain_id
        self.region_name = region_name
        self.cis_charge_db_client = DBClient(region_name, 'abc_cis_charge', 'abc_cis_charge', self.env, True)
        self.goods_db_client = DBClient(self.region_name, 'abc_cis_stock', 'abc_cis_goods', self.env, True)
        self.medical_plan_db_client = DBClient(self.region_name, 'abc_cis_outpatient', 'abc_cis_medical_plan', self.env,True)
        self.outpatient_db_client = DBClient(self.region_name, 'abc_cis_outpatient', 'abc_cis_outpatient', self.env, True)
        self.registration_db_client = DBClient(self.region_name, 'abc_cis_outpatient', 'abc_cis_registration', self.env, True)
        self.shebao_db_client = DBClient(self.region_name, 'abc_cis_bill', 'abc_cis_shebao', self.env, True)
        self.advice_db_client = DBClient(self.region_name, 'scrm_hospital', 'abc_his_advice', self.env, True)
        self.his_charge_db_client = DBClient(self.region_name, 'scrm_hospital', 'abc_his_charge', self.env, True)
        self.pe_charge_db_client = DBClient(self.region_name, 'scrm_hospital', 'abc_pe_charge', self.env, True)
        self.pe_order_db_client = DBClient(self.region_name, 'scrm_hospital', 'abc_pe_order', self.env, True)

        self.adb_cis_charge_client = DBClient(self.region_name, 'adb', 'abc_cis_charge', self.env, True)
        self.adb_goods_client = DBClient(self.region_name, 'adb', 'abc_cis_goods', self.env, True)
        self.adb_outpatient_client = DBClient(self.region_name, 'adb', 'abc_cis_outpatient', self.env, True)
        self.adb_registration_client = DBClient(self.region_name, 'adb', 'abc_cis_registration', self.env, True)
        self.adb_shebao_client = DBClient(self.region_name, 'adb', 'abc_cis_shebao', self.env, True)
        self.adb_advice_client = DBClient(self.region_name, 'adb', 'abc_his_advice', self.env, True)
        self.adb_his_charge_client = DBClient(self.region_name, 'adb', 'abc_his_charge', self.env, True)
        self.adb_pe_charge_client = DBClient(self.region_name, 'adb', 'abc_pe_charge', self.env, True)
        self.adb_pe_order_client = DBClient(self.region_name, 'adb', 'abc_pe_order', self.env, True)

        self.basic_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', self.env, True)

        print("---------初始化数据库链接完成---------")

    def run(self):
        # 设置hisType
        self._set_his_type()
        # 内置体检费
        try:
            self._built_in_physical_examination_fee_type_id()
            print("_built_in_physical_examination_fee_type_id finish")
            #  清除redis 缓存
            requests.get("""http://{rpcHost}/rpc/v3/goods/jenkins/chain/{chainId}/fee-types/clean-cache"""
                         .format(chainId=self.chain_id, rpcHost=regionRpcHost(self.region_name, self.env)))
            print("clean redis cache finish")
        except Exception as e:
            print(e)
        # 清理goods数据
        try:
            self._clean_up_goods_data()
            print("_clean_up_goods_data finish")
        except Exception as e:
            print(e)
        # 如果老的内置体检费 feeTypeId为None或75，不需要刷数据
        if self.old_physical_examination_fee_type_id is None or self.old_physical_examination_fee_type_id == 75:
            print("old_physical_examination_fee_type_id is None or 75, only update goods, not need to update other data")
            return

        # 清理charge数据
        try:
            self._clean_up_charge_data()
            print("_clean_up_charge_data finish")
        except Exception as e:
            print(e)

        # 清理 abc_cis_medical_plan 数据
        try:
            self._clean_up_medical_plan_data()
            print("_clean_up_medical_plan_data finish")
        except Exception as e:
            print(e)

        # 清理 cis_outpatient 数据
        try:
            self._clean_cis_outpatient_data()
            print("_clean_cis_outpatient_data finish")
        except Exception as e:
            print(e)
        # 清理 cis_registration 数据
        try:
            self._clean_cis_registration_data()
            print("_clean_cis_registration_data finish")
        except Exception as e:
            print(e)
        # 清理 shebao 数据
        try:
            self._clean_shebao_data()
            print("_clean_shebao_data finish")
        except Exception as e:
            print(e)
        #  清理 advice 数据
        try:
            self._clean_advice_data()
            print("_clean_advice_data finish")
        except Exception as e:
            print(e)
        #  清理 his_charge 数据
        try:
            self._clean_his_charge_data()
            print("_clean_his_charge_data finish")
        except Exception as e:
            print(e)
        #  清理 pe_charge 数据
        try:
            self._clean_pe_charge_data()
            print("_clean_pe_charge_data finish")
        except Exception as e:
            print(e)
        #  清理 v1_pe_form_item 数据
        try:
            self._clean_pe_order_data()
            print("_clean_pe_order_data finish")
        except Exception as e:
            print(e)

    def _clean_pe_order_data(self):
        # 从adb查询 v1_pe_form_item
        needUpdatePeFormItems = self.adb_pe_order_client.fetchall(
            '''select id from v1_pe_form_item where chain_id = '{0}' and fee_type_id = {1} '''
            .format(self.chain_id, self.old_physical_examination_fee_type_id)
        )
        if needUpdatePeFormItems:
            self.pe_order_db_client.execute(
                '''update v1_pe_form_item set fee_type_id = 75 where id in ({0}) '''.format(
                    ','.join(str(item['id']) for item in needUpdatePeFormItems))
            )
    def _clean_pe_charge_data(self):
        # 从adb查询 v1_pe_charge_form_item
        needUpdatePeChargeItems = self.adb_pe_charge_client.fetchall(
            '''select id from v1_pe_charge_form_item where chain_id = '{0}' and fee_type_id = {1} '''
            .format(self.chain_id, self.old_physical_examination_fee_type_id)
        )
        if needUpdatePeChargeItems:
            self.pe_charge_db_client.execute(
                '''update v2_his_charge_form_item set fee_type_id = 75 where id in ({0}) '''.format(
                    ','.join(str(item['id']) for item in needUpdatePeChargeItems))
            )
        # 从adb查询 v1_pe_charge_sheet_transaction_record
        needUpdatePeTransactionRecord = self.adb_pe_charge_client.fetchall(
            '''select id from v1_pe_charge_sheet_transaction_record where chain_id = '{0}' and fee_type_id = {1} '''
            .format(self.chain_id, self.old_physical_examination_fee_type_id)
        )
        if needUpdatePeTransactionRecord:
            self.pe_charge_db_client.execute(
                '''update v1_pe_charge_sheet_transaction_record set fee_type_id = 75 where id in ({0}) '''.format(
                    ','.join(str(item['id']) for item in needUpdatePeTransactionRecord))
            )

    def _clean_his_charge_data(self):
        # 从adb查询 v2_his_charge_form_item
        needUpdateHisChargeItems = self.adb_his_charge_client.fetchall(
            '''select id from v2_his_charge_form_item where chain_id = '{0}' and fee_type_id = {1} '''
            .format(self.chain_id, self.old_physical_examination_fee_type_id)
        )
        if needUpdateHisChargeItems:
            self.his_charge_db_client.execute(
                '''update v2_his_charge_form_item set fee_type_id = 75 where id in ({0}) '''.format(
                    ','.join(str(item['id']) for item in needUpdateHisChargeItems))
            )

        # 从adb查询 v2_his_charge_settle_goods_fee
        needUpdateHisChargeSettleGoodsFees = self.adb_his_charge_client.fetchall(
            '''select id from v2_his_charge_settle_goods_fee where chain_id = '{0}' and fee_type_id = {1} '''
            .format(self.chain_id, self.old_physical_examination_fee_type_id)
        )
        if needUpdateHisChargeSettleGoodsFees:
            self.his_charge_db_client.execute(
                '''update v2_his_charge_settle_goods_fee set fee_type_id = 75 where id in ({0}) '''.format(
                    ','.join(str(item['id']) for item in needUpdateHisChargeSettleGoodsFees))
            )

        # 从adb查询 v2_his_charge_settle_transaction_record
        needUpdateHisChargeSettleTransactionRecords = self.adb_his_charge_client.fetchall(
            '''select id from v2_his_charge_settle_transaction_record where chain_id = '{0}' and fee_type_id = {1} '''
            .format(self.chain_id, self.old_physical_examination_fee_type_id)
        )
        if needUpdateHisChargeSettleTransactionRecords:
            self.his_charge_db_client.execute(
                '''update v2_his_charge_settle_transaction_record set fee_type_id = 75 where id in ({0}) '''.format(
                    ','.join(str(item['id']) for item in needUpdateHisChargeSettleTransactionRecords))
            )


    def _clean_advice_data(self):
        # 从adb查询 v1_advice_rule_item
        needUpdateAdviceRuleItems = self.adb_advice_client.fetchall(
            '''select id from v1_advice_rule_item where chain_id = '{0}' and fee_type_id = {1} '''
            .format(self.chain_id, self.old_physical_examination_fee_type_id)
        )
        if needUpdateAdviceRuleItems:
            self.advice_db_client.execute(
                '''update v1_advice_rule_item set fee_type_id = 75 where id in ({0}) '''.format(
                    ','.join(str(item['id']) for item in needUpdateAdviceRuleItems))
            )
        # 从adb查询 v1_advice_usage_rule_item
        needUpdateUsageRuleItems = self.adb_advice_client.fetchall(
            '''select id from v1_advice_usage_rule_item where chain_id = '{0}' and fee_type_id = {1} '''
            .format(self.chain_id, self.old_physical_examination_fee_type_id)
        )
        if needUpdateUsageRuleItems:
            self.advice_db_client.execute(
                '''update v1_advice_usage_rule_item set fee_type_id = 75 where id in ({0}) '''.format(
                    ','.join(str(item['id']) for item in needUpdateUsageRuleItems))
            )

    def _clean_shebao_data(self):
        # 从adb查询 shebao_national_payment_result_item
        needUpdatePaymentResultItems = self.adb_shebao_client.fetchall(
            '''select id from shebao_national_payment_result_item where chain_id = '{0}' and fee_type_id = {1} '''
            .format(self.chain_id, self.old_physical_examination_fee_type_id)
        )
        if needUpdatePaymentResultItems:
            self.shebao_db_client.execute(
                '''update shebao_national_payment_result_item set fee_type_id = 75 where id in ({0}) '''.format(
                    ','.join(str(item['id']) for item in needUpdatePaymentResultItems))
            )
        # 从adb查询 shebao_national_pre_payment_result_item
        needUpdatePrePaymentResultItems = self.adb_shebao_client.fetchall(
            '''select id from shebao_national_pre_payment_result_item where chain_id = '{0}' and fee_type_id = {1} '''
            .format(self.chain_id, self.old_physical_examination_fee_type_id)
        )
        if needUpdatePrePaymentResultItems:
            self.shebao_db_client.execute(
                '''update shebao_national_pre_payment_result_item set fee_type_id = 75 where id in ({0}) '''.format(
                    ','.join(str(item['id']) for item in needUpdatePrePaymentResultItems))
            )

    def _clean_cis_registration_data(self):
        # 从adb查询 v2_registration_fee_product
        needUpdateFeeProducts = self.adb_registration_client.fetchall(
            '''select id from v2_registration_fee_product where chain_id = '{0}' and fee_type_id = {1} '''
            .format(self.chain_id, self.old_physical_examination_fee_type_id)
        )
        if needUpdateFeeProducts:
            self.registration_db_client.execute(
                '''update v2_registration_fee_product set fee_type_id = 75 where id in ({0}) '''.format(
                    ','.join(str(item['id']) for item in needUpdateFeeProducts))
            )
        # 从adb查询 v2_registration_form_item_fee_detail
        needUpdateFeeDetails = self.adb_registration_client.fetchall(
            '''select id from v2_registration_form_item_fee_detail where chain_id = '{0}' and fee_type_id = {1} '''
            .format(self.chain_id, self.old_physical_examination_fee_type_id)
        )
        if needUpdateFeeDetails:
            self.registration_db_client.execute(
                '''update v2_registration_form_item_fee_detail set fee_type_id = 75 where id in ({0}) '''.format(
                    ','.join(str(item['id']) for item in needUpdateFeeDetails))
            )

    def _clean_cis_outpatient_data(self):
        # 从adb查询 v2_outpatient_product_form_item
        needUpdateProductFormItems = self.adb_outpatient_client.fetchall(
            '''select id from v2_outpatient_product_form_item where  fee_type_id = {0} '''
            .format(self.old_physical_examination_fee_type_id)
        )
        if needUpdateProductFormItems:
            self.outpatient_db_client.execute(
                '''update v2_outpatient_product_form_item set fee_type_id = 75 where id in ({0}) '''.format(
                    ','.join('\'' + item['id'] + '\'' for item in needUpdateProductFormItems))
            )

    def _clean_up_medical_plan_data(self):
        # v2_medical_plan_form_item 数据量小，直接修改
        self.medical_plan_db_client.execute(
            '''update v2_medical_plan_form_item set fee_type_id = 75 where chain_id = '{0}' and fee_type_id = {1} '''.format(
                self.chain_id, self.old_physical_examination_fee_type_id)
        )

    def _clean_up_charge_data(self):
        # 从adb查询v2_charge_form_item
        needUpdateChargeFormItems = self.adb_cis_charge_client.fetchall(
            '''select id from v2_charge_form_item where chain_id = '{0}' and fee_type_id = {1} '''
            .format(self.chain_id, self.old_physical_examination_fee_type_id)
        )
        if needUpdateChargeFormItems:
            self.cis_charge_db_client.execute(
                '''update v2_charge_form_item set fee_type_id = 75 where id in ({0}) '''.format(
                    ','.join('\'' + item['id'] + '\'' for item in needUpdateChargeFormItems))
            )

        # 从adb查询 v2_charge_owe_combine_transaction_record_detail
        needUpdateOweCombineRecordDetails = self.adb_cis_charge_client.fetchall(
            '''select id from v2_charge_owe_combine_transaction_record_detail where chain_id = '{0}' and fee_type_id = {1} '''
            .format(self.chain_id, self.old_physical_examination_fee_type_id)
        )
        if needUpdateOweCombineRecordDetails:
            self.cis_charge_db_client.execute(
                '''update needUpdateOweCombineRecordDetails set fee_type_id = 75 where id in ({0}) '''.format(
                    ','.join('\'' + item['id'] + '\'' for item in needUpdateOweCombineRecordDetails))
            )
        # 从adb查询 v2_charge_transaction_record
        needUpdateChargeTransactionRecord = self.adb_cis_charge_client.fetchall(
            '''select id from v2_charge_transaction_record where chain_id = '{0}' and fee_type_id = {1} '''
            .format(self.chain_id, self.old_physical_examination_fee_type_id)
        )
        if needUpdateChargeTransactionRecord:
            self.cis_charge_db_client.execute(
                '''update v2_charge_transaction_record set fee_type_id = 75 where id in ({0}) '''.format(
                    ','.join('\'' + item['id'] + '\'' for item in needUpdateChargeTransactionRecord))
            )

        # 修改 v2_charge_rule_process_usage、v2_charge_rule_express_delivery 数据量小，直接修改即可
        self.cis_charge_db_client.execute(
            '''update v2_charge_rule_express_delivery set fee_type_id = 75 where chain_id = '{0}' and fee_type_id = {1} '''.format(
                self.chain_id, self.old_physical_examination_fee_type_id)
        )
        self.cis_charge_db_client.execute(
            '''update v2_charge_rule_process_usage set fee_type_id = 75 where chain_id = '{0}' and fee_type_id = {1} '''.format(
                self.chain_id, self.old_physical_examination_fee_type_id)
        )

    def _clean_up_goods_data(self):
        # 从 goods_db_client 获取 体检项目 goods
        physicalExaminationGoodsIds = self.goods_db_client.fetchall(
            '''select id from v2_goods where organ_id = '{0}' and type = 27 and fee_type_id != 75 '''
            .format(self.chain_id)
        )
        if physicalExaminationGoodsIds:
            self.goods_db_client.execute(
                '''update v2_goods set fee_type_id = 75 where id in ({0}) '''.format(
                    ','.join('\'' + item['id'] + '\'' for item in physicalExaminationGoodsIds))
            )
            self.goods_db_client.execute(
                '''update v2_goods_medical_stat set fee_type_id = 75 where goods_id in ({0}) '''.format(
                    ','.join('\'' + item['id'] + '\'' for item in physicalExaminationGoodsIds))
            )

        if self.old_physical_examination_fee_type_id is None or self.old_physical_examination_fee_type_id == 75:
            return

        # 查询需要修改fee_type_id，使用adb
        needUpdateGoodsList = self.adb_goods_client.fetchall(
            '''select id from v2_goods where organ_id = '{chainId}' and fee_type_id = {feeTypeId} '''
            .format(chainId=self.chain_id, feeTypeId=self.old_physical_examination_fee_type_id)
        )
        # 如果needUpdateGoodsList为空
        if not needUpdateGoodsList:
            return
        # 修改goods对应的feeTypeId
        self.goods_db_client.execute(
            '''update v2_goods set fee_type_id = 75 where id in ({0}) '''.format(
                ','.join('\'' + item['id'] + '\'' for item in needUpdateGoodsList))
        )
        self.goods_db_client.execute(
            '''update v2_goods_medical_stat set fee_type_id = 75 where goods_id in ({0}) '''.format(
                ','.join('\'' + item['id'] + '\'' for item in needUpdateGoodsList))
        )

    def _built_in_physical_examination_fee_type_id(self):
        if self.his_type != 100:
            print("organHisType is not 100, not built in physical examination fee type id")
            return

        # 查询是否已存在体检费
        existFeeType = self.goods_db_client.fetchone(
            '''select id, fee_type_id, inner_flag from v2_goods_fee_type where chain_id = '{chainId}' and name = '体检费' and is_deleted = 0 limit 1'''.format(
                chainId=self.chain_id))
        # 如果已存在
        if existFeeType:
            # 如果是内置的，直接return
            if existFeeType['inner_flag'] == 1:
                return
            self.old_physical_examination_fee_type_id = existFeeType['fee_type_id']
            # 修改已存在的费用类型为内置
            self.goods_db_client.execute(
                '''update v2_goods_fee_type set fee_type_id = 75, inner_flag = 1 where id = {id} '''.format(
                    id=existFeeType['id']))
            return
        # 创建内置体检费
        self.goods_db_client.execute(
            '''insert into v2_goods_fee_type(id, chain_id, fee_type_id, scope_id, name, py, disable, inner_flag, is_deleted, sort,
                              created, created_by, last_modified, last_modified_by)
                VALUES (substr(uuid_short(),4),
                        '{chainId}',
                        75,
                        15,
                        '体检费',
                        '体检费|tijianfei|tjf',
                        0,
                        1,
                        0,
                        15,
                        now(),
                        null,
                        now(),
                        null)
            '''.format(chainId=self.chain_id)
        )

    def _set_his_type(self):
        organHisType = self.basic_db_client.fetchone(
            ''' select his_type from organ where id = '{chainId}' '''.format(chainId=self.chain_id))
        if organHisType:
            self.his_type = organHisType['his_type'];


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    updateData = UpdateData(args.region_name, args.chain_id)
    updateData.run()


if __name__ == '__main__':
    main()
