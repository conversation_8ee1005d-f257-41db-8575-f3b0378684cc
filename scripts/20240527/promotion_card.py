"""
刷新formIsDeleted 的数据
"""
import os
import sys
import argparse
import json
from datetime import date, datetime, time, timedelta

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import requests
from multizone.rpc import regionRpcHost
from multizone.db import DBClient


# 刷进销存数据
class UpdateData:
    chain_id = None
    goods_db_client = None
    goods_log_db_client = None
    adb_client = None


    def __init__(self, region_name, chain_id):
        self.env = 'prod'
        self.chain_id = chain_id
        self.region_name = region_name
        self.promotion_db_client = DBClient(self.region_name, 'abc_cis_charge', 'abc_cis_promotion', self.env, True)

    def run(self):
        try:
            self.updateGoodsClinicConfigIndepent()
        except Exception as e:
            print("id:", self.chain_id, ' error:: ', str(e))
            raise e
        finally:
            self.promotion_db_client.close()

    def updateGoodsClinicConfigIndepent(self):
        try:
            sqls = [
                '''
                update abc_cis_promotion.v2_promotion_card set switch_flag = 1  where chain_id ='{chainId}';
                '''
            ]
            for sql in sqls:
                self.promotion_db_client.execute(sql.format(chainId=self.chain_id))
        except Exception as e:
            print(e)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    updateData = UpdateData(args.region_name, args.chain_id)
    updateData.run()


if __name__ == '__main__':
    main()
