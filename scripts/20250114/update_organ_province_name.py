"""
腾讯地图机构地址过滤省市区
更新机构的省份名称
"""
import argparse
import os
import sys

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.db import DBClient


def execute_sql(client, sql):
    client.execute(sql)
    print(sql)
    pass


class UpdateOrganProvinceName:
    chain_id = None
    basic_wdb_client = None
    env = None

    def __init__(self, env, region_name, chain_id):
        self.env = env
        self.chain_id = chain_id
        self.region_name = region_name
        self.basic_wdb_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', self.env, True)

    def run(self):
        # 更新机构的省份名称
        sql = """
            update organ a 
            inner join address_region b on a.address_province_id = b.id and a.address_province_name <> b.name
            set a.address_province_name = b.name
            where a.parent_id = '{chainId}';
        """.format(chainId=self.chain_id)
        execute_sql(self.basic_wdb_client, sql)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    updater = UpdateOrganProvinceName(args.env, args.region_name, args.chain_id)
    updater.run()


if __name__ == '__main__':
    main()
