# -*- coding: utf-8 -*-
"""
@name: flush_return_out_order_flag.py
@author: <PERSON><PERSON><PERSON>
@email: <EMAIL>
@date: 2025-02-18 14:31:15
"""
import os
import sys
import argparse

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.db import DBClient

env = 'prod'

class UpdateData:
    chain_id = None
    goods_db_client = None

    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.goods_db_client = DBClient(self.region_name, 'abc_cis_stock', 'abc_cis_goods', env, True)

    def run(self):
        return_out_order_not_flush_count = self.goods_db_client.fetchone("""
        select count(1) as count from v2_goods_stock_in_order where type = 10 and external_flag&0x80=0 and chain_id = '{chainId}'
        """.format(chainId=self.chain_id))
        if return_out_order_not_flush_count is None or return_out_order_not_flush_count['count'] <= 0:
            return
        update_sql = """
        update v2_goods_stock_in_order set external_flag = external_flag|0x80 where chain_id = '{chainId}' and type = 10 and external_flag&0x80=0
        """.format(chainId=self.chain_id)
        # print(update_sql)
        self.goods_db_client.execute(update_sql)
        pass


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    update_data = UpdateData(args.region_name, args.chain_id)
    update_data.run()


if __name__ == '__main__':
    main()
