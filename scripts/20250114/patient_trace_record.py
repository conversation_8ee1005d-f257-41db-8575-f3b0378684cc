import argparse
import datetime
import json
import os
import sys
from _decimal import Decimal

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from idwork import IdWork
from multizone.db import DBClient

default_id = '00000000000000000000000000000000'

class CustomEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Decimal):
            return str(obj)
        elif isinstance(obj, datetime):
            return obj.isoformat()
        else:
            return super().default(obj)

class UpdateData:
    chain_id = None
    basic_db_client = None
    patient_cli = None
    patient_order_cli = None
    pe_order = None

    id_work = None
    type = None
    businessType = None

    def __init__(self, region_name, chain_id, env):
        self.chain_id = chain_id
        self.region_name = region_name
        self.basic_db_client = DBClient(self.region_name, 'ods', 'abc_cis_basic', env, True)
        self.patient_cli = DBClient(self.region_name, 'abc_cis_account_base', 'abc_cis_patient', env, True)
        self.patient_order_cli = DBClient(self.region_name, 'ods', 'abc_cis_patientorder', env, True)
        self.pe_order = DBClient(self.region_name, 'scrm_hospital', 'abc_pe_order', env, True)
        self.his_charge = DBClient(self.region_name, 'scrm_hospital', 'abc_his_charge', env, True)
        self.emr = DBClient(self.region_name, 'scrm_hospital', 'abc_his_emr', env, True)
    def run(self):
        self.id_work = IdWork(self.patient_cli, False)
        self.id_work.config()

        organHisType = self.basic_db_client.fetchone(
            ''' select his_type from organ where id = '{chainId}' '''.format(chainId=self.chain_id))
        if organHisType:
            if organHisType['his_type'] != 100:
                print("organHisType is not 100")
                return

        clinics = self.basic_db_client.fetchall(''' select id, name from organ where parent_id = '{chainId}' '''.format(chainId=self.chain_id))
        clinicMap = {}
        for clinic in clinics:
            clinicMap[clinic['id']] = clinic['name']

        self.add_patient_order_to_trace_record(clinicMap)

    def add_patient_order_to_trace_record(self, clinicMap):
        ### 查询住院单插入到patientTraceRecord里
        patientOrderList = self.patient_order_cli.fetchall(
            ''' select id, patient_id,chain_id, clinic_id,inpatient_time,department_id,status,doctor_id,discharge_time,last_modified from v2_patientorder_hospital_extend where status not in (90,0) and chain_id = '{chainId}' '''.format(
                chainId=self.chain_id))

        if patientOrderList is None or len(patientOrderList) == 0:
            return
        patientOrderMap = {}
        needSettleFeePatientOrderIds = []
        pateintTraceRecordMap = {}

        employeeList = []
        departmentList = []

        diagnosisList = self.emr.fetchall(
            '''select patient_id, patient_order_id, disease_code, disease_name, disease_category from v1_emr_diagnosis where chain_id = '{chainId}' and type = 20 and is_primary = 10 and is_deleted = 0;'''
            .format(chainId=self.chain_id))
        diagnosisMap = {}
        for diagnosis in diagnosisList:
            diagnosisMap[str(diagnosis['patient_order_id'])] = diagnosis

        for patientOrder in patientOrderList:
            patientOrderMap[str(patientOrder['id'])] = patientOrder
            departmentList.append(patientOrder['department_id'])
            employeeList.append(patientOrder['doctor_id'])
            patientTraceRecord = {
                'id': self.id_work.getUIDLong(),
                'patient_id': patientOrder['patient_id'],
                'chain_id': patientOrder['chain_id'],
                'clinic_id': patientOrder['clinic_id'],
                'action': 10,
                'action_id': patientOrder['id'],
                'record_created': datetime.datetime.now(),
                'record_happened': patientOrder['last_modified'],
                'isDeleted': 0,
                'action_abstract': {}
            }
            pateintTraceRecordMap[str(patientOrder['id'])] = patientTraceRecord;
            if patientOrder['status'] == 80:
                needSettleFeePatientOrderIds.append(patientOrder['id'])

        chargeSettleList = self.his_charge.fetchall(
            ''' select total_price,patient_order_id from v2_his_charge_settle where chain_id = '{chainId}' '''.format(
                chainId=self.chain_id))

        chargeSettleMap = {}
        for settle in chargeSettleList:
            # patientOrder = patientOrderMap.get(str(settle['id']))
            # patientOrder['totalPrice'] = settle['totalPrice']
            chargeSettleMap[str(settle['patient_order_id'])] = settle

        # 查询科室信息
        unique_departments = set(departmentList)
        departmentList = self.basic_db_client.fetchall(
            ''' select id,name from department where chain_id = '{chainId}' and id in ({departmentList}) '''.format(
                chainId=self.chain_id,
                departmentList=", ".join(["'{}'".format(dept_id) for dept_id in unique_departments])))

        departmentMap = {}
        for department in departmentList:
            departmentMap[str(department['id'])] = department

        # 查询人员信息
        unique_employeeList = set(employeeList)
        employeeList = self.basic_db_client.fetchall(
            ''' select id,name from employee where  id in ({employeeList}) '''.format(chainId=self.chain_id,
                                                                                      employeeList=", ".join(
                                                                                          ["'{}'".format(dept_id) for
                                                                                           dept_id in
                                                                                           unique_employeeList])))

        employeeMap = {}
        for employee in employeeList:
            employeeMap[str(employee['id'])] = employee

        # 将结算金额 科室名称 人员名称填充到    action_abstract
        for patientOrderId, patientTraceRecord in pateintTraceRecordMap.items():
            patientOrder = patientOrderMap.get(patientOrderId)
            if patientOrder:
                primaryDiagnosisInfos = self.emr_diagnosis_info_2_extend_diagnosis_infos(diagnosisMap.get(str(patientOrder['id'])))
                patientTraceRecord['action_abstract'] = {
                    'id': str(patientOrderId),
                    'totalPrice': chargeSettleMap.get(str(patientOrderId))['total_price'] if chargeSettleMap.get(
                        str(patientOrderId)) else 0,
                    'departmentName': departmentMap.get(str(patientOrder['department_id']))[
                        'name'] if departmentMap.get(str(patientOrder['department_id'])) else None,
                    'doctorName': employeeMap.get(str(patientOrder['doctor_id']))['name'] if employeeMap.get(
                        str(patientOrder['doctor_id'])) else None,
                    'inpatientTime': patientOrder['inpatient_time'],
                    'dischargeTime': patientOrder['discharge_time'],
                    'status': patientOrder['status'],
                    'doctorId': patientOrder['doctor_id'],
                    'departmentId': patientOrder['department_id'],
                    'clinicId': patientOrder['clinic_id'],
                    'clinicName': clinicMap.get(str(patientOrder['clinic_id'])) if clinicMap.get(str(patientOrder['clinic_id'])) else None,
                    'primaryDiagnosisInfos': primaryDiagnosisInfos if primaryDiagnosisInfos else None,
                }
                print(patientTraceRecord['action_abstract'])

        # 插入到patientTraceRecord
        for record in pateintTraceRecordMap.values():
            try:
                sql = self.create_sql(record)
                print(sql)
                self.patient_cli.execute(sql)
            except Exception as e:
                print(e)

    def create_sql(self, patientTraceRecord):
        action_abstract = patientTraceRecord['action_abstract']
        # 将日期时间格式化为可插入 JSON 对象的字符串格式
        if action_abstract.get('inpatientTime'):
            action_abstract['inpatientTime'] = action_abstract['inpatientTime'].isoformat()

        if action_abstract.get('dischargeTime'):
            action_abstract['dischargeTime'] = action_abstract['dischargeTime'].isoformat()

        # 转换 Decimal 类型为浮点数
        if action_abstract.get('totalPrice'):
            action_abstract['totalPrice'] = float(action_abstract['totalPrice'])

        if action_abstract.get('totalFee'):
            action_abstract['totalFee'] = float(action_abstract['totalFee'])

        # 转换字典为 JSON 字符串
        json_data = json.dumps(action_abstract, cls=CustomEncoder, ensure_ascii=False)
        # 构建 SQL 插入语句插入 JSON 字段
        sql_query = """
            INSERT INTO v2_patient_trace_record (id, patient_id, chain_id, clinic_id, action, action_id, record_created, record_happened, is_deleted, action_abstract)
            VALUES ('{id}', '{patient_id}', '{chain_id}', '{clinic_id}', {action}, '{action_id}', '{record_created}', '{record_happened}', {isDeleted}, '{action_abstract}')
            ON DUPLICATE KEY UPDATE action_abstract = values(action_abstract), record_happened = values(record_happened), is_deleted = values(is_deleted)
            """.format(
            id=patientTraceRecord['id'],
            patient_id=patientTraceRecord['patient_id'],
            chain_id=patientTraceRecord['chain_id'],
            clinic_id=patientTraceRecord['clinic_id'],
            action=patientTraceRecord['action'],
            action_id=patientTraceRecord['action_id'],
            record_created=patientTraceRecord['record_created'],
            record_happened=patientTraceRecord['record_happened'],
            isDeleted=patientTraceRecord['isDeleted'],
            action_abstract=json_data
        )

        return sql_query


    def emr_diagnosis_info_2_extend_diagnosis_infos(self, emr_diagnosis_info):
        if not emr_diagnosis_info:
            return None

        diagnosis_info = {
            'name': emr_diagnosis_info['disease_name'],
            'code': emr_diagnosis_info['disease_code'],
            'diseaseType': emr_diagnosis_info['disease_category']
        }
        extend_diagnosis_info = {
            "value": [diagnosis_info]
        }
        return [extend_diagnosis_info]

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    updateData = UpdateData(args.region_name, args.chain_id, args.env)
    updateData.run()

    # basic_db_client = DBClient('ShangHai', 'abc_cis_basic', 'abc_cis_basic', 'dev', True)
    # chainSql = '''
    #     select id
    #     from organ
    #     where node_type = 1 and his_type = 100
    # '''
    # chains = basic_db_client.fetchall(chainSql)
    # for chain in chains:
    #     updateData = UpdateData('ShangHai', chain['id'], 'dev')
    #     updateData.run()


    # region = 'ShangHai'
    # chain_id = 'ffffffff00000000347714011f71c000'
    # updateData = UpdateData(region, chain_id, 'test')
    # updateData.run()
if __name__ == '__main__':
    main()
