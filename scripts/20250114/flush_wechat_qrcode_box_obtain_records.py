"""
ABC支付开通优化-刷扫码盒领取记录
"""
import argparse
import os
import sys

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.db import DBClient


class UpdateWechatQRCodeBoxObtainRecords:
    chain_id = None

    def __init__(self, region_name, chain_id, env):
        self.env = env
        self.chain_id = chain_id
        self.region_name = region_name
        self.promotion_db_client = DBClient(self.region_name, 'abc_cis_bill', 'abc_cis_wechatpay', self.env, True)

    def run(self):
        try:
            self.updateWechatQRCodeBoxObtainRecords()
        except Exception as e:
            print("id:", self.chain_id, ' error:: ', str(e))
            raise e
        finally:
            self.promotion_db_client.close()

    def updateWechatQRCodeBoxObtainRecords(self):
        try:
            sql = """
                insert into v2_wechat_qr_code_box_obtain_records (id, chain_id, clinic_id, recipient_name, recipient_phone,
                                                                  address_province, address_city, address_district, address_detail,
                                                                  created_by, created)
                select substr(uuid_short(), 4),
                       a.chain_id,
                       a.clinic_id,
                       '',
                       '',
                       '',
                       '',
                       '',
                       '',
                       '00000000000000000000000000000000',
                       now()
                from v2_wechat_pay_config a left join v2_wechat_qr_code_box_obtain_records b on a.clinic_id = b.clinic_id
                where a.chain_id = '{chainId}'
                and b.id is null;
            """.format(chainId=self.chain_id)
            self.promotion_db_client.execute(sql)
            print(sql)
        except Exception as e:
            print(e)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    updateData = UpdateWechatQRCodeBoxObtainRecords(args.region_name, args.chain_id, args.env)
    updateData.run()


if __name__ == '__main__':
    main()