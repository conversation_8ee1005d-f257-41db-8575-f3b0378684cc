import argparse
import datetime
import json
import os
import sys
from _decimal import Decimal

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from idwork import IdWork
from multizone.db import DBClient

default_id = '00000000000000000000000000000000'

class CustomEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Decimal):
            return str(obj)
        elif isinstance(obj, datetime):
            return obj.isoformat()
        else:
            return super().default(obj)

class UpdateData:
    chain_id = None
    basic_db_client = None
    goods_db_client = None
    goods_log_db_client = None
    region_name = None

    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.basic_db_client = DBClient(self.region_name, 'ods', 'abc_cis_basic', 'prod', True)
        self.goods_db_client = DBClient(self.region_name, 'abc_cis_stock', 'abc_cis_goods', 'prod', True)
        self.goods_log_db_client = DBClient(self.region_name, 'abc_cis_stock_zip', 'abc_cis_goods_log', 'prod', True)
    def run(self):
        organHisType = self.basic_db_client.fetchone(
            ''' select his_type from organ where id = '{chainId}' '''.format(chainId=self.chain_id))
        if organHisType:
            if organHisType['his_type'] == 10:
                print("organHisType is  10 return ")
                return

        self.goods_db_client.execute('''update v2_goods_stat set last_stock_in_order_supplier ='初始化入库' where  last_stock_in_order_supplier = '盘点入库' and chain_id ='{chainId}'; '''.format(chainId=self.chain_id))
        self.goods_log_db_client.execute('''update v2_goods_stock_log as l set l.action ='采购入库' where  l.chain_id = '{chainId}' and l.created_date >='2025-01-01' and  l.action = '初始化入库'; '''.format(chainId=self.chain_id))


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    updateData = UpdateData(args.region_name, args.chain_id)
    updateData.run()

if __name__ == '__main__':
    main()
