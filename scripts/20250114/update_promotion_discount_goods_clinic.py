"""
设置商品会员价
更新会员商品clinic_id数据
"""
import argparse
import os
import sys

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.db import DBClient


class UpdateData:
    chain_id = None

    def __init__(self, region_name, chain_id, env):
        self.env = env
        self.chain_id = chain_id
        self.region_name = region_name
        self.promotion_db_client = DBClient(self.region_name, 'abc_cis_charge', 'abc_cis_promotion', self.env, True)

    def run(self):
        try:
            self.updatePromotionDiscountGoodsClinic()
        except Exception as e:
            print("id:", self.chain_id, ' error:: ', str(e))
            raise e
        finally:
            self.promotion_db_client.close()

    def updatePromotionDiscountGoodsClinic(self):
        try:
            sql = """
                update v2_promotion_discount_goods
                set clinic_id = chain_id
                where chain_id = '{chainId}'
                and clinic_id is null;
            """.format(chainId=self.chain_id)
            self.promotion_db_client.execute(sql)
            print(sql)
        except Exception as e:
            print(e)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    updateData = UpdateData(args.region_name, args.chain_id, args.env)
    updateData.run()


if __name__ == '__main__':
    main()
