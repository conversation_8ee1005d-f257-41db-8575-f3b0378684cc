"""
刷新住院随访模版
"""
import argparse
import os
import sys

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from idwork import IdWork

clinicSql = '''
    select id, name
    from organ
    where parent_id = '{chainId}';
'''

organSql = '''
    select id, name, his_type
    from organ
    where id = '{chainId}'
'''

catalogueSql = '''
insert into v2_short_url_catalogue (id, parent_id, chain_id, clinic_id, owner_id, owner_type,
                                                     is_folder, type, category, name, sort, is_deleted, created,
                                                     created_by, last_modified, last_modified_by, children_count, level,
                                                     source_id)
values ({newCatalogueId}, '{parentId}', '{chainId}',
        '{clinicId}', '{clinicId}', 1, 0, 23, 1, '出院后随访', 0, 0,
        now(), '00000000000000000000000000000000', now(),
        '00000000000000000000000000000000', 0, 1, null);
'''

fileSql = '''
insert into v2_patient_revisit_template (id, chain_id, clinic_id, name, content, is_deleted, created,
                                                         created_by, last_modified_by, last_modified, type, tasks)
values ({}, '{}', '{}', '出院后随访',
        '了解患者的病情变化，评估治疗效果，及时发现可能出现的并发症或病情恶化，为患者提供康复指导。',
        0, now(), '00000000000000000000000000000000', '00000000000000000000000000000000',
        now(), 0,
        '[{{"target": "了解患者的病情变化，评估治疗效果，及时发现可能出现的并发症或病情恶化，为患者提供康复指导。"}}]');
'''

catalogueName = '出院后随访'
content = '了解患者的病情变化，评估治疗效果，及时发现可能出现的并发症或病情恶化，为患者提供康复指导。'
tasks = '[{"target": "了解患者的病情变化，评估治疗效果，及时发现可能出现的并发症或病情恶化，为患者提供康复指导。"}]'


class UpdateData:
    def __init__(self, region_name, chain_id, env):
        self.chain_id = chain_id
        self.region_name = region_name
        self.base_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', env, True)
        self.patient_client = DBClient(self.region_name, 'abc_cis_account_base', 'abc_cis_patient', env, True)
        self.id_work = IdWork(self.base_client, False)
        self.id_work.config()

    def run(self):
        self.create()

    def create(self):
        try:
            organ = self.base_client.fetchone(organSql.format(chainId=self.chain_id))
            hisType = organ['his_type']
            if hisType != 100:
                return

            # 获取连锁下的子店
            clinics = self.base_client.fetchall(clinicSql.format(chainId=self.chain_id))
            for clinic in clinics:
                clinicId = clinic['id']
                # 模版门店，不做处理
                if clinicId == 'ffffffff00000000347a3895e21e0001':
                    continue

                # 找到常规目录
                catalogue1 = self.base_client.fetchone(
                    '''select id from v2_short_url_catalogue where type = 23 and is_folder = 0 and name = '出院后随访' and clinic_id = '{clinicId}' '''.format(
                        clinicId=clinicId))
                if catalogue1 is not None:
                    continue

                catalogue = self.base_client.fetchone(
                    '''select id from v2_short_url_catalogue where type = 23 and is_folder = 1 and name = '常规' and clinic_id = '{clinicId}' '''.format(
                        clinicId=clinicId))
                if catalogue is None or catalogue['id'] is None:
                    print("parent catalogue is null " + clinicId)
                parentId = catalogue['id']
                # 创建目录
                newCatalogueId = self.id_work.getUID()

                catalogueSql1 = catalogueSql.format(newCatalogueId=newCatalogueId, parentId=parentId,
                                                    chainId=self.chain_id,
                                                    clinicId=clinicId)
                self.base_client.execute(catalogueSql1)
                print(catalogueSql1)

                # 创建文件
                fileSql1 = fileSql.format(newCatalogueId, self.chain_id, clinicId)
                print(fileSql1)
                self.patient_client.execute(fileSql1)
        except Exception as e:
            print(self.chain_id)
            print(e)


def main():
    # basic_db_client = DBClient('ShangHai', 'abc_cis_basic', 'abc_cis_basic', 'test', True)
    # chainSql = '''
    #     select id
    #     from organ
    #     where node_type = 1;
    # '''
    # chains = basic_db_client.fetchall(chainSql)
    # for chain in chains:
    #     updateData = UpdateData('ShangHai', chain['id'], 'test')
    #     updateData.run()

    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    updateData = UpdateData(args.region_name, args.chain_id, args.env)
    updateData.run()

    # region = 'ShangHai'
    # chain_id = 'ffffffff00000000197c4e300fe84000'
    # updateData = UpdateData(region, chain_id, 'dev')
    # updateData.run()


if __name__ == '__main__':
    main()
