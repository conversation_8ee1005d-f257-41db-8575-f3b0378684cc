# -*- coding: utf-8 -*-
"""
@name: flush_multi_pharmacy_property_flag.py
@author: <PERSON><PERSON><PERSON>
@email: <EMAIL>
@date: 2025-01-22 10:02:06
"""
import os
import sys
import argparse

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.db import DBClient

DEFAULT_ID = '00000000000000000000000000000000'


class UpdateData:
    chain_id = None
    basic_db_client = None
    goods_db_client = None
    property_db_client = None

    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.goods_db_client = DBClient(self.region_name, 'abc_cis_stock', 'abc_cis_goods', 'prod', True)
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', 'prod', True)
        self.property_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_property', 'prod', True)

    def run(self):
        self.flush_multi_pharmacy_property_flag()

    def flush_multi_pharmacy_property_flag(self):
        organ = self.basic_db_client.fetchone("""
        select * from organ where id = '{chain_id}' and status = 1;
        """.format(chain_id=self.chain_id))
        if organ is None:
            return
        his_type = organ['his_type']
        if his_type is None or his_type == 10 or his_type == 100:
            # 药店医院不刷
            return
        clinic_list = self.goods_db_client.fetchall("""
        select * from v2_goods_clinic_config where chain_id = '{chain_id}';
        """.format(chain_id=self.chain_id))
        if clinic_list is None or len(clinic_list) == 0:
            return
        open_pharmacy_flag_list = []
        for clinic in clinic_list:
            open_pharmacy_flag = clinic['open_pharmacy_flag']
            if open_pharmacy_flag is None or open_pharmacy_flag != 20:
                continue
            open_pharmacy_flag_list.append(clinic['clinic_id'])
        if len(open_pharmacy_flag_list) == 0:
            # 没开多药房
            return
        edition_list = self.basic_db_client.fetchall("""
        select * from v2_clinic_current_edition where is_deleted = 0 and  chain_id = '{chain_id}'
        """.format(chain_id=self.chain_id))
        insert_clinic_list = []
        for edition in edition_list:
            edition_clinic = edition['clinic_id']
            edition = int(edition['edition_id'])
            if edition_clinic not in open_pharmacy_flag_list:
                continue
            if his_type == 0 or his_type == 1:
                # 诊所口腔
                if edition < 30:
                    continue
                insert_clinic_list.append(edition_clinic)
            else:
                # 眼科
                if edition < 230:
                    continue
                insert_clinic_list.append(edition_clinic)
        if len(insert_clinic_list) == 0:
            return
        for clinic_id in insert_clinic_list:
            sql = """
            INSERT INTO v2_property_config_item (id, `key`, value, scope, is_deleted, created_by, created, last_modified_by, last_modified, key_first, key_second, key_third, key_fourth, key_fifth, v2_scope_id) VALUES (substr(uuid_short(), 5), 'clinicBasic.multiPharmacy', '1', 'clinic', 0, '00000000000000000000000000000000', now(), '00000000000000000000000000000000', now(), 'clinicBasic', 'multiPharmacy', null, null, null, '{clinicId}');
            """.format(clinicId=clinic_id)
            # print(sql)
            self.property_db_client.execute(sql)

        pass


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    update_data = UpdateData(args.region_name, args.chain_id)
    update_data.run()


if __name__ == '__main__':
    main()