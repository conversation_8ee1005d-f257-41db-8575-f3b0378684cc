"""
    刷门店默认字段配置数据信息
"""

import argparse
import os
import sys
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient

"""
    chain_id: 门店id
    region_name: 分区
    env: 环境
"""
def run(chain_id_input, region_name, env):
    basic_db_client = DBClient(region_name, 'abc_cis_mixed', 'abc_cis_basic', env, True)
    property_db_client = DBClient(region_name, 'abc_cis_mixed', 'abc_cis_property' if env == 'prod' else 'abc_cis_basic', env, True)

    # 查询key[chainBasic.crm.requiredMobile | chainBasic.crm.requiredIdCard | chainBasic.crm.requiredAddress]的配置文件
    keyList = ['chainBasic.crm.requiredMobile', 'chainBasic.crm.requiredIdCard', 'chainBasic.crm.requiredAddress']
    for key in keyList:
        chainIdList = get_property_config(property_db_client, key)
        insertSQLList = []
        for chainId in chainIdList:
            if chain_id_input != chainId['id']:
                continue
            # 查询连锁下的门店
            clinicList = get_clinic_list(basic_db_client, chainId['id'])
            if len(clinicList) == 0:
                continue
            for clinic in clinicList:
                clinicId = clinic['id']
                insertSQL = None
                if key == keyList[0]:
                    # 手机号必填
                    insertSQL = """
                        INSERT INTO v2_property_config_item (id, `key`, value, scope, scope_id, is_deleted, created_by, created, last_modified_by, last_modified, key_first, key_second, key_third, key_fourth, key_fifth, v2_scope_id) 
                        VALUES (uuid_short(), 'field.patient.create.mobile.required', '1', 'clinic', null, 0, '00000000000000000000000000000000', CURRENT_TIMESTAMP, '00000000000000000000000000000000', CURRENT_TIMESTAMP, 'field', 'patient', 'create', 'mobile', 'required', '{clinicId}')
                    """.format(clinicId=clinicId)
                elif key == keyList[1]:
                    # 证件必填
                    insertSQL = """
                        INSERT INTO v2_property_config_item (id, `key`, value, scope, scope_id, is_deleted, created_by, created, last_modified_by, last_modified, key_first, key_second, key_third, key_fourth, key_fifth, v2_scope_id) 
                        VALUES (uuid_short(), 'field.patient.create.certificates.required', '1', 'clinic', null, 0, '00000000000000000000000000000000', CURRENT_TIMESTAMP, '00000000000000000000000000000000', CURRENT_TIMESTAMP, 'field', 'patient', 'create', 'certificates', 'required', '{clinicId}')
                    """.format(clinicId=clinicId)
                elif key == keyList[2]:
                    # 地址必填
                    insertSQL = """
                        INSERT INTO v2_property_config_item (id, `key`, value, scope, scope_id, is_deleted, created_by, created, last_modified_by, last_modified, key_first, key_second, key_third, key_fourth, key_fifth, v2_scope_id) 
                        VALUES (uuid_short(), 'field.patient.create.address.required', '1', 'clinic', null, 0, '00000000000000000000000000000000', CURRENT_TIMESTAMP, '00000000000000000000000000000000', CURRENT_TIMESTAMP, 'field', 'patient', 'create', 'address', 'required', '{clinicId}')
                    """.format(clinicId=clinicId)
                if insertSQL is None:
                    continue
                print(insertSQL)
                insertSQLList.append(insertSQL)
        property_db_client.executemany(insertSQLList)

"""
    查询连锁下的门店
"""
def get_clinic_list(db_client, chainId):
    sql = """
        select distinct id from organ where parent_id = '{chainId}' and status = 1
    """.format(chainId = chainId)
    clinicList = db_client.fetchall(sql)
    return clinicList

"""
    查询Key必填的连锁id
"""
def get_property_config(db_client, configKey):
    sql = """
        select distinct v2_scope_id as id from v2_property_config_item where `key` = '{configKey}' and value = '1'
    """.format(configKey = configKey)
    chainIdList = db_client.fetchall(sql)
    return chainIdList

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.env:
        parser.print_help()
        sys.exit(-1)
    run(args.chain_id, args.region_name, args.env)
    # run('ffffffff00000000347714011f71c000', 'ShangHai', 'test')

if __name__ == '__main__':
    main()