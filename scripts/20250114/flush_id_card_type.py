"""
    根据idCard刷新idCardType字段
"""

import argparse
import os
import sys
import re
import base64
import hashlib

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad

# AES 加密的Key
AES_KEY = 'd081bc67-b1a0-4d64-b58c-65e4dea69bfb'

# 香港身份证
PATTERN_HK = re.compile(r'^[A-Z]\d{6}[A-Z0-9]$')
# 澳门身份证
PATTERN_MACAO = re.compile(r'^(?:[A-Z0-9]/[0-9]{6}/[A-Z0-9]|[A-Z0-9][0-9]{6}[A-Z0-9])$')
# 台湾身份证
PATTERN_TAIWAN = re.compile(r'^[A-Z][0-9]{9}$')

class AESUtils:
    ENCRYPT_ALGORITHM = "AES"
    DEFAULT_CIPHER_TRANSFORMATION = "AES/ECB/PKCS5Padding"

    @staticmethod
    def generate_key(key_str: str) -> bytes:
        return AESUtils.sign(key_str).encode()

    @staticmethod
    def decrypt_from_base64_string(data: str, key: str) -> str:
        if data and key:
            try:
                key_bytes = AESUtils.generate_key(key)
                decrypted_data = AESUtils.decrypt(base64.b64decode(data), key_bytes)
                return decrypted_data.decode('utf-8')
            except Exception as e:
                print(f"Decryption error: {e}")
                return data
        return data

    @staticmethod
    def decrypt(data: bytes, secret_key: bytes, cipher_transformation: str = DEFAULT_CIPHER_TRANSFORMATION) -> bytes:
        if data and secret_key and cipher_transformation:
            cipher = AES.new(secret_key, AES.MODE_ECB)
            decrypted_data = cipher.decrypt(data)
            return unpad(decrypted_data, AES.block_size)
        else:
            raise ValueError("AES decrypt meet invalid argument, check it")

    @staticmethod
    def sign(data: str) -> str:
        if not data:
            return ""
        return hashlib.md5(data.encode()).hexdigest()

"""
    chain_id: 门店id
    region_name: 分区
    env: 环境
"""
def run(chain_id, region_name, env):
    ob_client = DBClient(region_name, 'ob', 'abc_cis_patient', env, True)
    db_client = DBClient(region_name, 'abc_cis_account_base', 'abc_cis_patient', env, True)

    # 查询连锁id对应的人员信息
    cursor = None
    while True:
        patientList = get_patient_list(ob_client, chain_id, cursor)
        if not patientList:
            break
        else:
            # 获取最后一条数据
            cursor = patientList[-1]['id']
            updateSqlList = []
            for patient in patientList:
                id_card_cipher = patient['id_card_cipher']
                id_card_type = None
                # 如果id_card不为空或者空字符串且长度是18位,则认为是身份证,否则判断是不是港澳台的证件号
                if id_card_cipher:
                    # 使用AES解密加密数据信息
                    id_card = AESUtils.decrypt_from_base64_string(id_card_cipher, AES_KEY)
                    id_card_type = get_id_card_type_by_id_card(id_card)
                    print("解密后的证件号码数据: {CardValue}".format(CardValue=id_card))
                if not id_card_type and id_card_type == '身份证':
                    continue

                updateSql = """update v2_patient set id_card_type='{idCardType}' where id='{id}'""".format(id=patient['id'], idCardType=id_card_type)
                updateSqlList.append(updateSql)

            if updateSqlList and len(updateSqlList) > 0:
                db_client.executemany(updateSqlList)
        print("{chainId} 更新idCardType完成")

def get_patient_list(ob_client, chain_id, cursor = None):
    patientList = None
    if not cursor:
        sql = """
                    select 
                        p.id,
                        p.id_card_cipher 
                    from v2_patient p
                    where p.chain_id = '{chainId}' and p.id_card_cipher is not null and p.id_card_cipher != ''
                    order by p.id
                    limit 1000
                """.format(chainId=chain_id)
        patientList = ob_client.fetchall(sql)
    else:
        sql = """
                    select 
                        p.id,
                        p.id_card_cipher 
                    from v2_patient p
                    where p.chain_id = '{chainId}' and p.id > '{cursor}' and p.id_card_cipher is not null and p.id_card_cipher != ''
                    order by p.id
                    limit 1000
                """.format(chainId=chain_id, cursor=cursor)
        patientList = ob_client.fetchall(sql)
    return patientList

"""
    根据证件号得到证件类型
"""
def get_id_card_type_by_id_card(id_card):
    if not id_card:
        return ''
    id_card = id_card.replace("(", "").replace("（", "").replace(")", "").replace("）", "")
    if len(id_card) >= 15:
        return '身份证'
    if PATTERN_HK.match(id_card):
        return '香港居民身份证'
    if PATTERN_MACAO.match(id_card):
        return '澳门居民身份证'
    if PATTERN_TAIWAN.match(id_card):
        return '台湾居民身份证'
    else:
        return ''

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.env:
        parser.print_help()
        sys.exit(-1)
    run(args.chain_id, args.region_name, args.env)

if __name__ == '__main__':
    main()