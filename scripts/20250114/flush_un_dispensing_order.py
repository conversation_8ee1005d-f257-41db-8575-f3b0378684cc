# -*- coding: utf-8 -*-
"""
@name: flush_un_dispensing_order.py
@author: <PERSON><PERSON><PERSON>
@email: <EMAIL>
@date: 2025-02-11 14:48:29
"""
import os
import sys
import argparse

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from scripts.common.utils.lists import ListUtils
from scripts.common.utils.sqls import SqlUtils

from multizone.db import DBClient

DEFAULT_ID = 'sys'

env = 'prod'


class UpdateData:
    chain_id = None
    basic_db_client = None
    dispensing_db_client = None

    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', env, True)
        self.dispensing_db_client = DBClient(self.region_name, 'abc_cis_stock_zip', 'abc_cis_dispensing', env, True)
        self.ob_dispensing_db_client = DBClient(self.region_name, 'ob', 'abc_cis_dispensing', env, True)

    def run(self):
        self.flush_un_dispensing_order()

    def flush_un_dispensing_order(self):
        organ_list = self.basic_db_client.fetchall("""
        select id, view_mode, his_type from organ where parent_id = '{chainId}' and status = 1 and node_type = 2 and his_type = 100;
        """.format(chainId=self.chain_id))
        if organ_list is None or len(organ_list) == 0:
            return
        for organ in organ_list:
            his_type = organ['his_type']
            clinic_id = organ['id']
            if his_type != 100:
                continue
            print(organ['id'])
            offset = 0
            limit = 200
            all_sheet_list = []
            while True:
                sheet_list = self.ob_dispensing_db_client.fetchall("""
                    select * from v2_dispensing_sheet force index (ix_clinic_id_order_by_date) where chain_id = '{chainId}' and clinic_id = '{clinicId}' and status = 2 and is_deleted = 0
                    and plan_execute_time is not null
                    and dispensing_method = 0 and type = 10 and order_by_date >= '2023-06-01 00:00:00' limit {offset}, {limit}
                    """.format(chainId=self.chain_id, clinicId=clinic_id, offset=offset, limit=limit))
                if sheet_list is None or len(sheet_list) == 0:
                    break
                offset += limit
                all_sheet_list.extend(sheet_list)
            if len(all_sheet_list) == 0:
                continue
            sheet_id_list = []
            for sheet in all_sheet_list:
                # print(sheet)
                if sheet['id'] in sheet_id_list:
                    continue
                sheet_id_list.append(sheet['id'])
            if len(sheet_id_list) == 0:
                continue
            sheet_id_sql_in = f"dispense_sheet_id in ({SqlUtils.to_in_value(sheet_id_list)}) and" if sheet_id_list else ""
            # print(sheet_id_sql_in)
            order_rel_list = self.dispensing_db_client.fetchall("""
            select * from v2_dispensing_order_rel_sheet where {sheet_id} chain_id = '{chainId}'
            and is_deleted = 0 and apply_type = 0;
            """.format(sheet_id=sheet_id_sql_in, chainId=self.chain_id))
            if order_rel_list is None or len(order_rel_list) == 0:
                continue
            order_id_to_rel_list = ListUtils.group_by(order_rel_list, lambda a: a['order_id'])
            # print(order_id_to_rel_list)
            # 将 order_id_to_rel_list 中的 order_id 按照组成列表
            order_id_list = []
            for order_id in order_id_to_rel_list:
                order_id_list.append(order_id)
            # print(order_id_list)
            order_id_sql_in = f"id in ({SqlUtils.to_in_value(order_id_list)}) and" if order_id_list else ""
            # print(order_id_sql_in)
            order_list = self.dispensing_db_client.fetchall("""
            select * from v2_dispensing_order where {order_id} chain_id = '{chainId}' and is_deleted = 0
            and apply_type = 0;
            """.format(order_id=order_id_sql_in, chainId=self.chain_id))
            insert_list = []
            for order in order_list:
                id = order['id']
                rel_sheet_list = order_id_to_rel_list.get(id)
                if rel_sheet_list is None or len(rel_sheet_list) == 0:
                    print(f"order id:{id} has no rel sheet")
                    continue
                undispense_time = order['apply_undispense_time']
                name = None
                if undispense_time is not None:
                    name = undispense_time.strftime('%H:%M')
                #     print(name)
                # print(undispense_time)
                if name is None:
                    name = order['name']
                order_id = self.dispensing_db_client.fetchone("""select substr(uuid_short(), 4) as uuid""")['uuid']
                order_sql = ("""
                insert into v2_dispensing_order(id, chain_id, clinic_id, department_id, ward_area_id, emergency_status, pharmacy_type, pharmacy_no, status, name,
                is_deleted, created, created_by, last_modified, last_modified_by,
                source_order_id, apply_dispense_time, apply_undispense_time, apply_type)
                values ({id}, '{chainId}', '{clinicId}', '{departmentId}', '{wardAreaId}',
                '{emergencyStatus}', '{pharmacyType}', '{pharmacyNo}', 40, '{name}', 0, '{created}', '{createdBy}',
                '{lastModified}', '{lastModifiedBy}',  null, null, '{applyUndispenseTime}', 1)"""
                             .format(id=order_id, chainId=self.chain_id, clinicId=clinic_id, departmentId=order['department_id'],
                                     wardAreaId=order['ward_area_id'],
                                     emergencyStatus=order['emergency_status'], pharmacyType=order['pharmacy_type'],
                                     pharmacyNo=order['pharmacy_no'],
                                     name=name, created=order['created'], createdBy=f'{DEFAULT_ID}',
                                     lastModified=order['last_modified'],
                                     lastModifiedBy=order['last_modified_by'],
                                     applyUndispenseTime=order['apply_undispense_time']))
                # print(order_sql)
                insert_list.append(order_sql)
                for rel_sheet in rel_sheet_list:
                    rel_sql = ("""
                    insert into v2_dispensing_order_rel_sheet(id, chain_id, clinic_id, order_id, dispense_sheet_id,
                    is_deleted, created, created_by, last_modified, last_modified_by, apply_type)
                    values (substr(uuid_short(), 5), '{chainId}', '{clinicId}', '{orderId}', '{dispenseSheetId}', 0,
                    '{created}', '{createdBy}', '{lastModified}', '{lastModifiedBy}', 1)"""
                               .format(chainId=self.chain_id, clinicId=clinic_id,
                                       orderId=order_id,
                                       dispenseSheetId=rel_sheet['dispense_sheet_id'],
                                       created=rel_sheet['created'],
                                       createdBy=f'{DEFAULT_ID}',
                                       lastModified=rel_sheet['last_modified'],
                                       lastModifiedBy=rel_sheet['last_modified_by']))
                    insert_list.append(rel_sql)
                    # print(rel_sql)
                # break
            # break
            if len(insert_list) == 0:
                continue
            for sql in insert_list:
                # print(sql)
                self.dispensing_db_client.execute(sql)
        pass

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    update_data = UpdateData(args.region_name, args.chain_id)
    update_data.run()


if __name__ == '__main__':
    main()
