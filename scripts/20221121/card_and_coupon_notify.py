#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON>g <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db
import argparse
import requests

def add_message_switch_config(chain_id):
    message_db_client = db.DBClient('abc_cis_mixed', 'abc_cis_message')

    # 执行刷数据脚本
    chain_message_switch_config_count = message_db_client.fetchone(
        '''
            select count(*) as count
            from v2_message_switch_config
            where chain_id = '{chainId}'
        '''.format(chainId=chain_id))

    # 如果从来没有配置过，则不需要插入
    if chain_message_switch_config_count['count'] == 0:
        return

    # 如果配置过，则需要新增两条配置
    message_db_client.execute(
        '''
                    INSERT INTO v2_message_switch_config (id, chain_id, `group`, `key`, name, notify_desc, notify_minutes_before, sms_template, wx_template,
                             weapp_template, sms_switch, wx_switch, weapp_switch, is_deleted, created_by, created,
                             last_modified_by, last_modified)
                    VALUES (substr(uuid_short(), 4), '{chainId}', '其他', 'others.promotion-patient-card', '卡项发放/消费/过期提醒',
                            '卡项发放、消费时，立即通知患者，卡项到期前%s自动提醒患者到店消费', 1,
                            '{{"types":[100010,100011,100012,100013],"templates":[{{"name":"开卡提醒","content":"【ABC诊所管家】您的「卡名称」已成功开卡，余额x元，共x次（剩余x次），有效期至xxxx-xx-xx，请及时使用。"}},{{"name":"消费提醒","content":"【ABC诊所管家】您的「卡名称」于xxxx-xx-xx xx:xx消费50.00元。"}},{{"name":"过期提醒","content":"【ABC诊所管家】您的「卡名称」将于xxxx-xx-xx xx:xx到期，余额x元，「项目名称」共x次（剩x次），请及时到店使用"}}]}}',
                            null, null, 0, 0, 0, 0, '00000000000000000000000000000000', '2022-12-06 08:43:32',
                            '00000000000000000000000000000000', '2022-12-06 08:43:32'),
                            (substr(uuid_short(), 4), '{chainId}', '其他', 'others.promotion-patient-coupon', '优惠券发放/过期提醒',
                            '优惠券发放后，立即通知患者，到期前%s自动提醒患者到店消费', 1,
                            '{{"types":[100020,100021],"templates":[{{"name":"发放提醒","content":"【ABC诊所管家】您的「券名称」（x张）已到账，满xx减xx，有效期至xxxx-xx-xx，请及时使用。"}},{{"name":"过期提醒","content":"【ABC诊所管家】您的「券名称」（x张）将于xxxx-xx-xx xx:xx到期，满xx减xx，请及时使用。"}}]}}',
                            null, null, 0, 0, 0, 0, '00000000000000000000000000000000', '2022-12-06 08:43:32',
                            '00000000000000000000000000000000', '2022-12-06 08:43:32')
                '''.format(chainId=chain_id)
    )

def run(chain_id):
    add_message_switch_config(chain_id)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)

if __name__ == '__main__':
    main()
