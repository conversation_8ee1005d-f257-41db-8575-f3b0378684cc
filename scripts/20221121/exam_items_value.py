#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

reload(sys)
sys.setdefaultencoding('utf-8')
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db
import json
import argparse
from idwork import IdWork
from manage import ScriptRecordExecutor

db_client = db.DBClient('abc_cis_outpatient', 'abc_cis_examination')
id_work = IdWork(db_client, False)
id_work.config()

default_id = '00000000000000000000000000000000'


def examinationSheetExamTrans(chainId):
    examination_sheets = db_client.fetchall(
        """select * from v2_examination_sheet where chain_id = '{chainId}' and type = 1 and status != 2 and items_value is not null""".format(
            chainId=chainId))
    oldItemIds = []
    # 收集goodsid为null的itemid
    for sheet in examination_sheets:
        items_value = json.loads(sheet['items_value'])
        if (items_value == None):
            continue
        if (len(items_value) <= 0):
            continue
        for item_value in items_value:
            if ('goodsId' not in item_value):
                oldItemIds.append(item_value['id'])

    # 构建itemid->goodsid
    itemIdToGoodsId = {}
    if (len((oldItemIds)) > 0):

        ids = ','.join([''' '{0}' '''.format(item) for item in list(set(oldItemIds))])
        items = db_client.fetchall(
            """select * from v2_examination_item where chain_id = '{chainId}' and id in ({ids})""".format(
                chainId=chainId, ids=ids))
        if (len(items) > 0):
            for item in items:
                itemIdToGoodsId[item['id']] = item['goods_id']

    for row in examination_sheets:
        examSheetId = row['id']
        examName = row['examination_name']
        created = row['created']
        createdBy = default_id
        lastModified = row['last_modified']
        lastModifiedBy = default_id
        items_value = json.loads(row['items_value'])
        if (items_value == None):
            continue
        if (len(items_value) <= 0):
            continue
        for item_value in items_value:
            if ('chainId' in item_value):
                del item_value['chainId']
            if ('clinicId' in item_value):
                del item_value['clinicId']
            if ('itemCode' in item_value):
                del item_value['itemCode']
            if ('ref' in item_value):
                if type(item_value['ref']) != dict:
                    try:
                        ref = json.loads(item_value['ref'])
                        if type(ref) == dict:
                            item_value['ref'] = ref
                    except Exception as e:
                        pass
                        # print("""json.loads("{ref}") error;""".format(ref=item_value['ref']), e)
                if (type(item_value['ref']) == dict):
                    if ('valid' in item_value['ref']):
                        del item_value['ref']['valid']
            item_value['examinationSheetId'] = examSheetId
            item_value['groupBy'] = examName
            examItemId = item_value['id']
            if ('goodsId' in item_value):
                goodsId = item_value['goodsId']
            else:
                goodsId = itemIdToGoodsId.get(examItemId, "")
                item_value['goodsId'] = goodsId
                # print("""appendGoodsId = {goodsId}""".format(goodsId=goodsId))

            for key in list(item_value.keys()):
                if item_value[key] is None:
                    del item_value[key]
                else:
                    value = item_value[key]
                    if isinstance(value, unicode) or isinstance(value, str):
                        if value.__contains__('\\'):
                            value = value.replace('\\', '\\\\')
                        if value.__contains__('\t'):
                            value = value.replace('\t', '\\t')
                        if value.__contains__('\"'):
                            value = value.replace('\"', '\\"')
                        if value.__contains__('\r'):
                            value = value.replace('\r', '\\r')
                        if value.__contains__('\n'):
                            value = value.replace('\n', '\\n')
                        item_value[key] = value

            sql = """insert into v2_examination_sheet_result(
                    id, 
                    examination_sheet_id, 
                    goods_id,
                    examination_item_id,
                    examination_value, 
                    created, 
                    created_by,
                    last_modified,
                    last_modified_by)
                    VALUES ({id},
                            '{examSheetId}',
                            '{goodsId}',
                            '{examItemId}',
                            '{item}',
                            '{created}',
                            '{createdBy}',
                            '{lastModified}',
                            '{lastModifiedBy}');
                    """.format(id=id_work.getUIDLong(),
                               examSheetId=examSheetId,
                               goodsId=goodsId,
                               examItemId=examItemId,
                               item=json.dumps(item_value, ensure_ascii=False),
                               created=created,
                               createdBy=createdBy,
                               lastModified=lastModified,
                               lastModifiedBy=lastModifiedBy)

            try:
                db_client.execute(sql)
            except Exception as ex:
                print("db_client.execute type=1.result error", ex)


def examinationSheetEyeInspectTrans(chainId):
    basic_db_client = db.DBClient('abc_cis_mixed', 'abc_cis_basic')
    hisType = basic_db_client.fetchone(
        ''' select his_type from organ where id = '{chainId}' '''.format(chainId=chainId))
    print "hisType = {hisType}".format(hisType=hisType)
    if hisType:
        if hisType['his_type'] != 2:
            return

    examination_sheets = db_client.fetchall(
        """select * from v2_examination_sheet where chain_id = '{chainId}' and type = 2 and status != 2 and items_value is not null""".format(
            chainId=chainId))

    item_ids = []
    for sheet in examination_sheets:
        items_value = json.loads(sheet['items_value'])
        if items_value is None:
            continue
        if len(items_value) <= 0:
            continue
        for item_value in items_value:
            if 'children' not in item_value:
                continue
            children1 = item_value['children']
            if len(children1) <= 0:
                continue
            for child1 in children1:
                if 'children' not in child1:
                    continue
                children2 = child1['children']
                if len(children2) <= 0:
                    continue
                for child2 in children2:
                    if 'children' not in child2:
                        continue
                    children3 = child2['children']
                    if len(children3) <= 0:
                        continue
                    for child3 in children3:
                        if 'children' not in child3:
                            continue
                        children4 = child3['children']
                        if len(children4) <= 0:
                            continue
                        for child4 in children4:
                            if 'items' not in child4:
                                continue
                            items = child4['items']
                            if len(items) <= 0:
                                continue
                            for item in items:
                                if 'additional' not in item or 'tag' not in item:
                                    item_ids.append(item['id'])

    ids = ','.join([''' '{0}' '''.format(item) for item in list(set(item_ids))])
    items = db_client.fetchall("""select * from v2_examination_item where chain_id = '{0}' and id in ({1});""".format(chainId, ids))
    item_id_to_map = {item['id']: item for item in items}
    for row in examination_sheets:
        examSheetId = row['id']
        created = row['created']
        createdBy = default_id
        lastModified = row['last_modified']
        lastModifiedBy = default_id
        items_value = json.loads(row['items_value'])
        if (items_value == None):
            continue
        if (len(items_value) <= 0):
            continue
        for item_value in items_value:
            goodsId = item_value['goodsId']
            goodsName = item_value['name']
            if 'checker' in item_value:
                checker = item_value['checker']
                checker_sql = """insert into v2_examination_sheet_extend(
                                                id, 
                                                examination_sheet_id, 
                                                goods_id,
                                                checker,
                                                created, 
                                                created_by,
                                                last_modified,
                                                last_modified_by)
                                                VALUES ({id},
                                                        '{examSheetId}',
                                                        '{goodsId}',
                                                        '{checker}',
                                                        '{created}',
                                                        '{createdBy}',
                                                        '{lastModified}',
                                                        '{lastModifiedBy}');
                                                """.format(id=id_work.getUIDLong(),
                                                           examSheetId=examSheetId,
                                                           goodsId=goodsId,
                                                           checker=json.dumps(checker, ensure_ascii=False),
                                                           created=created,
                                                           createdBy=createdBy,
                                                           lastModified=lastModified,
                                                           lastModifiedBy=lastModifiedBy)

                try:
                    db_client.execute(checker_sql)
                except Exception as ex:
                    print("db_client.execute type=2.extend error", ex)
            if ('children' not in item_value):
                continue
            children1 = item_value['children']

            if (len(children1) <= 0):
                continue
            for child1 in children1:
                if ('children' not in child1):
                    continue
                children2 = child1['children']
                if (len(children2) <= 0):
                    continue
                for child2 in children2:
                    if ('children' not in child2):
                        continue
                    children3 = child2['children']
                    if (len(children3) <= 0):
                        continue
                    for child3 in children3:
                        if ('children' not in child3):
                            continue
                        children4 = child3['children']
                        if (len(children4) <= 0):
                            continue
                        for child4 in children4:
                            if ('items' not in child4):
                                continue
                            items = child4['items']
                            if (len(items) <= 0):
                                continue
                            for item in items:
                                if ('chainId' in item):
                                    del item['chainId']
                                if ('clinicId' in item):
                                    del item['clinicId']
                                if ('itemCode' in item):
                                    del item['itemCode']
                                if ('refDetails' in item):
                                    del item['refDetails']
                                if ('searchText' in item):
                                    del item['searchText']
                                if ('valueQuantity' in item):
                                    del item['valueQuantity']
                                if ('ref' in item):
                                    if type(item['ref']) != dict:
                                        try:
                                            ref = json.loads(item['ref'])
                                            if type(ref) == dict:
                                                item['ref'] = ref
                                        except Exception as e:
                                            pass
                                            # print("""json.loads("{ref}") error;""".format(ref=item['ref']), e)
                                    if (type(item['ref']) == dict):
                                        if ('valid' in item['ref']):
                                            del item['ref']['valid']
                                item['goodsName'] = goodsName
                                examItemId = item['id']
                                if 'additional' not in item or item['additional'] is None:
                                    if examItemId in item_id_to_map:
                                        itemM = item_id_to_map[examItemId]
                                        if 'additional' in itemM:
                                            additional = itemM['additional']
                                            if additional is not None:
                                                if isinstance(additional, str) or isinstance(additional, unicode):
                                                    try:
                                                        additional = json.loads(additional)
                                                    except Exception as eex:
                                                        pass
                                                item['additional'] = additional
                                if 'tag' not in item or item['tag'] is None:
                                    if examItemId in item_id_to_map:
                                        itemM = item_id_to_map[examItemId]
                                        if 'tag' in itemM:
                                            tag = itemM['tag']
                                            if tag is not None:
                                                if isinstance(tag, str) or isinstance(tag, unicode):
                                                    try:
                                                        tag = json.loads(tag)
                                                    except Exception as eex:
                                                        pass
                                                item['tag'] = tag
                                for key in list(item.keys()):
                                    if item[key] is None:
                                        del item[key]
                                    else:
                                        value = item[key]
                                        if isinstance(value, unicode) or isinstance(value, str):
                                            if value.__contains__('\\'):
                                                value = value.replace('\\', '\\\\')
                                            if value.__contains__('\t'):
                                                value = value.replace('\t', '\\t')
                                            if value.__contains__('\"'):
                                                value = value.replace('\"', '\\"')
                                            if value.__contains__('\r'):
                                                value = value.replace('\r', '\\r')
                                            if value.__contains__('\n'):
                                                value = value.replace('\n', '\\n')
                                            item[key] = value

                                sql = """insert into v2_examination_sheet_result(
                                                        id, 
                                                        examination_sheet_id, 
                                                        goods_id,
                                                        examination_item_id,
                                                        examination_value, 
                                                        created, 
                                                        created_by,
                                                        last_modified,
                                                        last_modified_by)
                                                        VALUES ({id},
                                                                '{examSheetId}',
                                                                '{goodsId}',
                                                                '{examItemId}',
                                                                '{item}',
                                                                '{created}',
                                                                '{createdBy}',
                                                                '{lastModified}',
                                                                '{lastModifiedBy}');
                                                        """.format(id=id_work.getUIDLong(),
                                                                   examSheetId=examSheetId,
                                                                   goodsId=goodsId,
                                                                   examItemId=examItemId,
                                                                   item=json.dumps(item, ensure_ascii=False),
                                                                   created=created,
                                                                   createdBy=createdBy,
                                                                   lastModified=lastModified,
                                                                   lastModifiedBy=lastModifiedBy)

                                try:
                                    db_client.execute(sql)
                                except Exception as ex:
                                    print("db_client.execute type=2.result error", ex)


def run(script_id, chain_ids):
    executor = ScriptRecordExecutor()
    for chain_id in chain_ids:
        print "run script_id {0} for chain {1}".format(script_id, chain_id)
        try:
            examinationSheetExamTrans(chain_id)
            examinationSheetEyeInspectTrans(chain_id)
            executor.execute(script_id, chain_id)
        except Exception as e:
            print e


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--script-id', help='脚本id')
    parser.add_argument('--chain-ids', nargs='*', type=str, default=[], help='连锁id')
    args = parser.parse_args()
    if not args.script_id or not args.chain_ids:
        parser.print_help()
        sys.exit(-1)

    run(args.script_id, args.chain_ids)


if __name__ == '__main__':
    main()
