#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

reload(sys)
sys.setdefaultencoding('utf-8')
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db
import json
import argparse
from idwork import IdWork
from manage import ScriptRecordExecutor

db_client = db.DBClient('abc_cis_outpatient', 'abc_cis_examination')
id_work = IdWork(db_client, False)
id_work.config()

default_id = '00000000000000000000000000000000'


def examinationSheetTrans(chainId):
    sheets = db_client.fetchall("""
    select s.id,
       count(r.id)                as co,
       json_length(s.items_value) as ic,
       group_concat(r.examination_item_id) as itemIds,
       s.items_value,
       s.examination_name,
       s.created,
       s.created_by,
       s.last_modified,
       s.last_modified_by
from v2_examination_sheet s
         left join v2_examination_sheet_result r on r.examination_sheet_id = s.id
where type = 1
  and status != 2
  and items_value is not null
  and json_valid(items_value) = 1
  and chain_id = '{chainId}'
group by s.id
having co != ic;""".format(chainId=chainId))

    oldItemIds = []
    # 收集goodsid为null的itemid
    for sheet in sheets:
        items_value = json.loads(sheet['items_value'])
        if items_value is None:
            continue
        if len(items_value) <= 0:
            continue
        for item_value in items_value:
            if 'goodsId' not in item_value:
                oldItemIds.append(item_value['id'])

    # 构建itemid->goodsid
    itemIdToGoodsId = {}
    if len(oldItemIds) > 0:
        ids = ','.join([''' '{0}' '''.format(item) for item in list(set(oldItemIds))])
        items = db_client.fetchall(
            """select * from v2_examination_item where id in ({ids})""".format(ids=ids))
        if len(items) > 0:
            for item in items:
                itemIdToGoodsId[item['id']] = item['goods_id']

    for row in sheets:
        examSheetId = row['id']
        examName = row['examination_name']
        resultItemIds = []
        if row['itemIds'] is not None:
            resultItemIds = row['itemIds'].split(',')
        created = row['created']
        createdBy = default_id
        lastModified = row['last_modified']
        lastModifiedBy = default_id
        items_value = json.loads(row['items_value'])
        itemIdToMap = {item_value['id']: item_value for item_value in items_value}

        for examItemId in list(itemIdToMap.keys()):
            if resultItemIds.__contains__(examItemId):
                continue
            item_value = itemIdToMap.get(examItemId)
            if 'chainId' in item_value:
                del item_value['chainId']
            if 'clinicId' in item_value:
                del item_value['clinicId']
            if 'itemCode' in item_value:
                del item_value['itemCode']

            if 'ref' in item_value:
                if type(item_value['ref']) != dict:
                    try:
                        ref = json.loads(item_value['ref'])
                        if type(ref) == dict:
                            item_value['ref'] = ref
                    except Exception as e:
                        pass
                        # print("""json.loads("{ref}") error;""".format(ref=item_value['ref']), e)
                if type(item_value['ref']) == dict:
                    if 'valid' in item_value['ref']:
                        del item_value['ref']['valid']
            item_value['examinationSheetId'] = examSheetId
            item_value['groupBy'] = examName
            examItemId = item_value['id']
            if 'goodsId' in item_value:
                goodsId = item_value['goodsId']
            else:
                goodsId = itemIdToGoodsId.get(examItemId, "")
                item_value['goodsId'] = goodsId

            for key in list(item_value.keys()):
                if item_value[key] is None:
                    del item_value[key]
                else:
                    value = item_value[key]
                    if isinstance(value, unicode) or isinstance(value, str):
                        if value.__contains__('\\'):
                            value = value.replace('\\', '\\\\')
                        if value.__contains__('\t'):
                            value = value.replace('\t', '\\t')
                        if value.__contains__('\"'):
                            value = value.replace('\"', '\\"')
                        if value.__contains__('\r'):
                            value = value.replace('\r', '\\r')
                        if value.__contains__('\n'):
                            value = value.replace('\n', '\\n')
                        item_value[key] = value

            sql = """insert into v2_examination_sheet_result(
            id, 
            examination_sheet_id, 
            goods_id,
            examination_item_id,
            examination_value, 
            created, 
            created_by,
            last_modified,
            last_modified_by)
            VALUES ({id},
                    '{examSheetId}',
                    '{goodsId}',
                    '{examItemId}',
                    '{item}',
                    '{created}',
                    '{createdBy}',
                    '{lastModified}',
                    '{lastModifiedBy}');
            """.format(id=id_work.getUIDLong(),
                       examSheetId=examSheetId,
                       goodsId=goodsId,
                       examItemId=examItemId,
                       item=json.dumps(item_value, ensure_ascii=False),
                       created=created,
                       createdBy=createdBy,
                       lastModified=lastModified, lastModifiedBy=lastModifiedBy)

            try:
                db_client.execute(sql)
            except Exception as ex:
                print("db_client.execute type=1.result error", ex)


def run(script_id, chain_ids):
    executor = ScriptRecordExecutor()
    db_client.execute("""SET SESSION group_concat_max_len = 10240000;""")
    for chain_id in chain_ids:
        print "run script_id {0} for chain {1}".format(script_id, chain_id)
        try:
            examinationSheetTrans(chain_id)
            executor.execute(script_id, chain_id)
        except Exception as e:
            print e


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--script-id', help='脚本id')
    parser.add_argument('--chain-ids', nargs='*', type=str, default=[], help='连锁id')
    args = parser.parse_args()
    if not args.script_id or not args.chain_ids:
        parser.print_help()
        sys.exit(-1)

    run(args.script_id, args.chain_ids)


if __name__ == '__main__':
    main()
