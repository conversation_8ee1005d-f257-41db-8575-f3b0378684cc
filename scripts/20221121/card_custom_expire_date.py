#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db
import argparse
import requests

def update_promotion_card_unit(chain_id):
    promotion_db_client = db.DBClient('abc_cis_charge', 'abc_cis_promotion')

    # 执行刷数据脚本
    promotion_db_client.execute(
        '''
            update v2_promotion_card
            set validity_type        = CASE validity_period
                                           WHEN 0 THEN 0
                                           ELSE 1 END,
                validity_period_unit = CASE validity_period
                                           WHEN 1 /*一个月*/ THEN 2 /*月*/
                                           WHEN 2 /*三个月*/ THEN 2 /*月*/
                                           WHEN 3 /*半年*/ THEN 2 /*月*/
                                           WHEN 4 /*一年*/ THEN 3 /*年*/
                                           WHEN 5 /*两年*/ THEN 3 /*年*/
                                           WHEN 6 /*三年*/ THEN 3 /*年*/
                                           WHEN 7 /*一周*/ THEN 1 /*周*/
                                           WHEN 8 /*两个月*/ THEN 2 /*月*/
                    END,
                validity_period      = CASE validity_period
                                           WHEN 1 /*一个月*/ THEN 1 /*月*/
                                           WHEN 2 /*三个月*/ THEN 3 /*月*/
                                           WHEN 3 /*半年*/ THEN 6 /*月*/
                                           WHEN 4 /*一年*/ THEN 1 /*年*/
                                           WHEN 5 /*两年*/ THEN 2 /*年*/
                                           WHEN 6 /*三年*/ THEN 3 /*年*/
                                           WHEN 7 /*一周*/ THEN 1 /*周*/
                                           WHEN 8 /*两个月*/ THEN 2 /*月*/
                                           ELSE validity_period
                    END
            where chain_id = '{chainId}'
        '''.format(chainId=chain_id))

def run(chain_id):
    update_promotion_card_unit(chain_id)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)

if __name__ == '__main__':
    main()
