#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import json
import db
import argparse
import logging

logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)


class UpdateExecutor(object):

    def __init__(self):
        self._db_client_cache = {}

    def _get_db_client(self, cluster, database):
        k = '{0}-{1}'.format(cluster, database)
        if k in self._db_client_cache.keys():
            return self._db_client_cache[k]

        db_client = db.DBClient(cluster, database)
        self._db_client_cache[k] = db_client
        return db_client

    def insertVirtualGoods(self, chain_id):
        try:
            goods_db_client = self._get_db_client('abc_cis_stock', 'abc_cis_goods')

            organs = goods_db_client.fetchall('''
            select chain_id,clinic_id,no
            from v2_goods_pharmacy  where type = 2 and chain_id = '{chainId}';
            '''.format(chainId=chain_id))


            for organ in organs:
                if organ['chain_id'] == organ['clinic_id']:
                    insertStatClinicId = ''
                    insertStatPharmacyNo = '-1'
                else:
                    insertStatClinicId = organ['clinic_id']
                    insertStatPharmacyNo = organ['no']

                sql = '''insert into v2_goods_stat(_id, chain_id, clinic_id, goods_id, pharmacy_type, pharmacy_no)
                            select substr(uuid_short(), 5), organ_id, '{insertStatClinicId}', id, 2, {insertStatPharmacyNo}
                            from v2_goods
                            where organ_id = '{chainId}'
                                    and type_id in (14,15)
                                    and status = 1
                                    and id not in (select s.goods_id from v2_goods_stat as s  where chain_id = '{chainId}' and clinic_id =  '{insertStatClinicId}' and pharmacy_type = 2);
                '''.format(chainId=chain_id,
                    insertStatClinicId=insertStatClinicId,
                           insertStatPharmacyNo=insertStatPharmacyNo
                )
                goods_db_client.execute(sql)
        except Exception as e:
            print(e)

    #多药房刷goods
    def updateGoods(self, chain_id):
        try:
            sqls = [
                # 刷入库单上 代煎代配的药房类型
                '''
                update v2_goods_clinic_config as c inner join v2_goods_pharmacy v2gp on c.chain_id = v2gp.chain_id and
                    c.clinic_id = v2gp.clinic_id and v2gp.type = 2 and c.chain_id = '{chainId}'
                set c.virtual_open_pharmacy_flag = 10
                ''',
                # 刷入库条目上 代煎代配的药房类型
                '''
                update v2_goods_chain_config chain inner join v2_goods_clinic_config v2gcc on chain.chain_id = v2gcc.chain_id and
                    v2gcc.virtual_open_pharmacy_flag = 10 and chain.chain_id = '{chainId}'
                set chain.virtual_open_pharmacy_flag = 10;
                ''',
                #刷GoodsStat的 goods类型Id
                '''
                    update
                    v2_goods_stat gs
                    inner join v2_goods as g on gs.chain_id = g.organ_id and gs.goods_id = g.id
                    set gs.type_id       = g.type_id,
                    gs.goods_type    = g.type,
                    gs.goods_sub_type    = g.sub_type,
                    gs.goods_created = g.created_date,
                    gs.piece_num     = g.piece_num,
                    gs.short_id      =g.short_id
                    where g.organ_id = '{chainId}'; 
                ''',
                '''
                update v2_goods_stat as gs
                inner join v2_goods as g on gs.goods_id = g.id and gs.chain_id = g.organ_id
                left join v2_goods_extend as v2ge on gs.chain_id = v2ge.chain_id and gs.clinic_id = v2ge.organ_id
                and gs.goods_id = v2ge.goods_id
                left join v2_goods_clinic_config cfg on gs.chain_id = cfg.chain_id and cfg.clinic_id = gs.clinic_id
                set gs.shebao_code_national_matched_status        = IFNULL(v2ge.shebao_code_national_matched_status, 0),
        gs.shebao_code_national_current_price_limited = v2ge.shebao_code_national_current_price_limited,
        gs.shebao_code_national_current_end_date      = v2ge.shebao_code_national_current_end_date,
        gs.shebao_code_national_future_price_limited  = v2ge.shebao_code_national_future_price_limited,
        gs.shebao_code_national_future_start_date     = v2ge.shebao_code_national_future_start_date,
        gs.shebao_code_national_code                  = v2ge.shebao_code_national_code,
        gs.shebao_code_national_code_id               = v2ge.shebao_code_national_code_id,
        gs.medical_fee_grade_national                 = IFNULL(v2ge.medical_fee_grade_national, 0),
        gs.stock_capacity_max_limit                   = IF(
        v2ge.stock_capacity_max_limit_enable = 1 and v2ge.stock_capacity_max_limit is not null,
        v2ge.stock_capacity_max_limit, 0),
        gs.shebao_pay_mode                            = v2ge.shebao_pay_mode,
        gs.purchase_cycle_days                        = IFNULL(v2ge.purchase_cycle_days, 0),
        gs.warn_stock_count                           = IFNULL(v2ge.warn_stock_count, 0),
        gs.config_turnover_days                       = IF(v2ge.stock_warn_customize = 1 and v2ge.turnover_days is not null,
        v2ge.turnover_days,
        IFNULL(cfg.stock_warn_goods_turnover_days, 30)),
        gs.days_of_day_avg_sell                       = IF(
        v2ge.stock_warn_customize = 1 and v2ge.days_of_day_avg_sell is not null, v2ge.days_of_day_avg_sell,
        IFNULL(cfg.stock_days_of_day_avg_sell, 7)),
        gs.expired_warn_months                        = IF(
        v2ge.stock_warn_customize = 1 and v2ge.expired_warn_months is not null, v2ge.expired_warn_months,
        IFNULL(cfg.stock_warn_goods_will_expired_month, 3)),
        gs.disable                                    = IF(g.disable = 1, g.disable, IFNULL(v2ge.disable, 0)),
        gs.sell_config                                = IF(g.sell_config != 0, g.sell_config, IFNULL(v2ge.sell_config, 0)),
        gs.inorder_config                             = IF(g.inorder_config != 0, g.inorder_config,
        IFNULL(v2ge.inorder_config, 0))
        where gs.chain_id = '{chainId}'; 
                ''',
                '''
                update v2_goods_stat as gs
    inner join v2_goods as g on gs.goods_id = g.id and gs.chain_id = g.organ_id
    left join v2_goods_extend as v2ge on gs.chain_id = v2ge.chain_id and gs.clinic_id = '' and
    v2ge.organ_id = gs.chain_id
    and gs.goods_id = v2ge.goods_id
    left join v2_goods_clinic_config cfg on gs.chain_id = cfg.chain_id and cfg.clinic_id = gs.clinic_id
    set gs.shebao_code_national_matched_status        = IFNULL(v2ge.shebao_code_national_matched_status, 0),
        gs.shebao_code_national_current_price_limited = v2ge.shebao_code_national_current_price_limited,
        gs.shebao_code_national_current_end_date      = v2ge.shebao_code_national_current_end_date,
        gs.shebao_code_national_future_price_limited  = v2ge.shebao_code_national_future_price_limited,
        gs.shebao_code_national_future_start_date     = v2ge.shebao_code_national_future_start_date,
        gs.medical_fee_grade_national                 = IFNULL(v2ge.medical_fee_grade_national, 0),
        gs.shebao_code_national_code                  = v2ge.shebao_code_national_code,
        gs.shebao_code_national_code_id               = v2ge.shebao_code_national_code_id,
        gs.stock_capacity_max_limit                   = IF(
        v2ge.stock_capacity_max_limit_enable = 1 and v2ge.stock_capacity_max_limit is not null,
        v2ge.stock_capacity_max_limit, 0),
        gs.shebao_pay_mode                            = v2ge.shebao_pay_mode,
        gs.purchase_cycle_days                        = IFNULL(v2ge.purchase_cycle_days, 0),
        gs.warn_stock_count                           = IFNULL(v2ge.warn_stock_count, 0),
        gs.config_turnover_days                       = IF(v2ge.stock_warn_customize = 1 and v2ge.turnover_days is not null,
        v2ge.turnover_days,
        IFNULL(cfg.stock_warn_goods_turnover_days, 30)),
        gs.days_of_day_avg_sell                       = IF(
        v2ge.stock_warn_customize = 1 and v2ge.days_of_day_avg_sell is not null, v2ge.days_of_day_avg_sell,
        IFNULL(cfg.stock_days_of_day_avg_sell, 7)),
        gs.expired_warn_months                        = IF(
        v2ge.stock_warn_customize = 1 and v2ge.expired_warn_months is not null, v2ge.expired_warn_months,
        IFNULL(cfg.stock_warn_goods_will_expired_month, 3)),
        gs.disable                                    = IF(g.disable = 1, g.disable, IFNULL(v2ge.disable, 0)),
        gs.sell_config                                = IF(g.sell_config != 0, g.sell_config, IFNULL(v2ge.sell_config, 0)),
        gs.inorder_config                             = IF(g.inorder_config != 0, g.inorder_config,
        IFNULL(v2ge.inorder_config, 0))
where gs.clinic_id = '' and gs.chain_id = '{chainId}'; 

                '''

            ]
            goods_db_client = self._get_db_client('abc_cis_stock', 'abc_cis_goods')

            for sql in sqls:
                goods_db_client.execute(sql.format(chainId=chain_id))
        except Exception as e:
            print(e)


    def execute_chain(self, chain_id):
        #先刷Goods数据
        self.insertVirtualGoods(chain_id)
        self.updateGoods(chain_id)

def run(chain_id):
    rgt = UpdateExecutor()
    rgt.execute_chain(chain_id)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)


if __name__ == '__main__':
    main()
