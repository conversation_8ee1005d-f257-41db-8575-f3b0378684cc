
import argparse
import os
import sys

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.db import DBClient


class UpdateData:
    chain_id = None
    nurse_wdb_client = None
    env = None

    def __init__(self, env, region_name, chain_id):
        self.env = env
        self.chain_id = chain_id
        self.region_name = region_name
        self.nurse_wdb_client = DBClient(self.region_name, 'abc_cis_outpatient', 'abc_cis_nurse', self.env, True)

    def run(self):

        self.nurse_wdb_client.execute("""
            update v2_nurse_physical_record
            set height_v2 = height
            where chain_id = '{chainId}' and height_v2 is null;
        """.format(chainId=self.chain_id))
        self.nurse_wdb_client.execute("""
            update v2_nurse_physical_record
            set weight_v2 = weight
            where chain_id = '{chainId}' and weight_v2 is null;
        """.format(chainId=self.chain_id))




def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    updateData = UpdateData(args.env, args.region_name, args.chain_id)
    updateData.run()


if __name__ == '__main__':
    main()
