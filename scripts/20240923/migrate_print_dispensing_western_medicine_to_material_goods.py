# -*- coding: utf-8 -*-
"""
@name: flush_advice_support_nid.py
@author: <PERSON><PERSON><PERSON>
@email: <EMAIL>
@date: 2023-11-21 22:41:44
"""
import argparse
import sys
import requests
import os
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient


DEFAULT_ID = '00000000000000000000000000000000'


class MigratePrintDispensingWesternMedicineToMaterialGoods:
    chain_id = None

    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', 'prod', True)
        self.property_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_property', 'prod', True)

    def run(self):
        organ_ids_result = self.basic_db_client.fetchall(
            ''' select id from organ where parent_id = '{chainId}' '''.format(chainId=self.chain_id))
        if organ_ids_result:
            # 保存发药打印材料商品配置
            self.property_db_client.execute(
                '''
                insert ignore into v2_property_config_item (id, `key`, `value`, scope, created_by, last_modified_by, key_first, key_second,
                                     key_third, key_fourth, key_fifth, v2_scope_id)
                select substr(uuid_short(), 4),
                       replace(`key`, 'westernMedicine', 'materialGoods'),
                       value,
                       scope,
                       created_by,
                       last_modified_by,
                       key_first,
                       key_second,
                       'materialGoods',
                       key_fourth,
                       key_fifth,
                       ifnull(v2_scope_id, scope_id)
                from v2_property_config_item
                where `key` in (
                                'print.dispensing.westernMedicine.batchNo',
                                'print.dispensing.westernMedicine.manufacturer',
                                'print.dispensing.westernMedicine.position',
                                'print.dispensing.westernMedicine.spec'
                    )
                  and ifnull(v2_scope_id, scope_id) in ({0})
                  on duplicate key update `value` = VALUES(`value`)
                '''.format(','.join(['\'' + organ_id_result['id'] + '\'' for organ_id_result in organ_ids_result]))
            )

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    update_data = MigratePrintDispensingWesternMedicineToMaterialGoods(args.region_name, args.chain_id)
    update_data.run()




if __name__ == '__main__':
    main()

