# -*- coding: utf-8 -*-
"""
@name: flush_goods_extend_data.py
@author: <PERSON><PERSON><PERSON>
@email: <EMAIL>
@date: 2024-07-04 23:21:52
"""
import argparse
import json
import sys
import requests
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.rediscli import RedisClient
from multizone.rpc import regionRpcHost

from multizone.db import DBClient


# 定义位标志
NORMAL = 1 << 0  # 0 常温
COOL = 1 << 1    # 阴凉
COLD = 1 << 2    # 冷藏
FROZEN = 1 << 3  # 冷冻
DARK = 1 << 4    # 避光
SEAL = 1 << 5    # 密封

# 定义标志对应的名称
FLAGS = {
    NORMAL: '常温',
    COOL: '阴凉',
    COLD: '冷藏',
    FROZEN: '冷冻',
    DARK: '避光',
    SEAL: '密封'
}

# 定义一个函数来检查并转换标志
def convert_flags(value):
    result = []
    for flag, name in FLAGS.items():
        if value & flag:
            result.append(name)
    return '，'.join(result)


class UpdateData:
    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.goods_db_client = DBClient(self.region_name, 'abc_cis_stock', 'abc_cis_goods', 'prod', True)
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', 'prod', True)

    def mv_trace_code_1_to_2(self):
        self.goods_db_client.execute("""
                    insert into v2_goods_identification_code_relation(id, chain_id, goods_id, drug_identification_code, type,is_deleted,
                                                  created_by, created, last_modified_by, last_modified)
                    select substr(uuid_short(), 4), organ_id, id, drug_identification_code, 0,0,
                            last_modified_user_id,last_modified_date,last_modified_user_id,last_modified_date
                    from v2_goods where drug_identification_code != '' and organ_id = '{chainId}'
        """.format(chainId=self.chain_id))
        self.goods_db_client.execute("""
                update v2_goods set compose_flag = compose_flag|0x1000 where  drug_identification_code != '' and organ_id = '{chainId}'
        """.format(chainId=self.chain_id))

    def flush_clinic_scope(self):
        sql="""
        select id,address_city_id,address_province_id,address_district_id
        from  organ where parent_id = '{chainId}' 
        """.format(chainId=self.chain_id)
        organ_his_type = self.basic_db_client.fetchall(sql)
        for stmt in organ_his_type:
            self.goods_db_client.execute("""
                update v2_goods_clinic_config set address_city_id = '{address_city_id}',address_province_id='{address_province_id}',address_district_id='{address_district_id}' where  clinic_id = '{clinicId}'
            """.format(address_city_id=stmt['address_city_id'],
                       address_province_id=stmt['address_province_id'],
                       address_district_id=stmt['address_district_id'],
                       clinicId=stmt['id']))

    def run(self):
        # self.mv_trace_code_1_to_2()
        self.flush_clinic_scope()
        requests.get("""http://{rpcHost}/rpc/v3/goods/jenkins/clean-chain-redis-cache?chainId={chainId}""".format(chainId = self.chain_id,rpcHost=regionRpcHost(self.region_name)))

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    update_data = UpdateData(args.region_name, args.chain_id)
    update_data.run()


if __name__ == '__main__':
    main()
