"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
import argparse

def run(region_name, chain_id, env):
    abc_cis_basic_client = DBClient(region_name, 'abc_cis_mixed', 'abc_cis_basic', env, True)
    updateSql = '''
        update clinic_employee set module_ids = concat_ws(',', module_ids, '14')
        where find_in_set('7', module_ids) and !find_in_set('14', module_ids) and !find_in_set('0', module_ids) and chain_id = '{chainId}'
    '''.format(chainId=chain_id)
    abc_cis_basic_client.execute(updateSql)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    run(args.region_name, args.chain_id, args.env)

if __name__ == '__main__':
    main()
