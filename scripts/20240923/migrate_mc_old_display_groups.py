# -*- coding: utf-8 -*-
"""
@name: flush_advice_support_nid.py
@author: <PERSON><PERSON><PERSON>
@email: <EMAIL>
@date: 2023-11-21 22:41:44
"""
import argparse
import sys
import requests
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys
import logging

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.rediscli import RedisClient

from multizone.db import DBClient
from multizone.rpc import regionRpcHost

DEFAULT_ID = '00000000000000000000000000000000'


class MigrateMcOldDisplayGroups:
    chain_id = None

    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name

    def run(self):
        rsp = requests.put("""http://{rpcHost}/rpc/mc/inner/chain/{chainId}/old-display-groups/migrate"""
            .format(chainId=self.chain_id, rpcHost=regionRpcHost(self.region_name)))
        logging.info('migrate old display group, chainId:{0}, rsp:{1}'.format(self.chain_id, rsp.content))

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    update_data = MigrateMcOldDisplayGroups(args.region_name, args.chain_id)
    update_data.run()


if __name__ == '__main__':
    main()
