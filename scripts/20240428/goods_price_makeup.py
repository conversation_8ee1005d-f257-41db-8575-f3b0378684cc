"""
刷新formIsDeleted 的数据
"""
import os
import sys
import argparse
import json
from datetime import date, datetime, time, timedelta

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import requests
from multizone.rpc import regionRpcHost
from multizone.db import DBClient


# 刷进销存数据
class UpdateData:
    chain_id = None
    goods_db_client = None
    goods_log_db_client = None
    adb_client = None


    def __init__(self, region_name, chain_id):
        self.env = 'prod'
        self.chain_id = chain_id
        self.region_name = region_name
        self.goods_db_client = DBClient(self.region_name, 'abc_cis_stock', 'abc_cis_goods', self.env, True)
        self.basic_db_client = DBClient(region_name, 'abc_cis_mixed', 'abc_cis_basic', 'prod', True)

    def run(self):
        try:
            self.updateGoodsClinicConfigIndepent()
            requests.get("""http://{rpcHost}/rpc/v3/goods/jenkins/clean-chain-redis-cache?chainId={chainId}""".format(chainId = self.chain_id,rpcHost=regionRpcHost(self.region_name)))
        except Exception as e:
            print("id:", self.chain_id, ' error:: ', str(e))
            raise e
        finally:
            self.goods_db_client.close()

    def updateGoodsClinicConfigIndepent(self):
        try:
            sqls = [
                '''update v2_goods set price_type = 1 where price_type = 0 and  organ_id = '{chainId}';''',
                '''update v2_goods_price set price_type = 1 where price_type = 0 and  chain_id = '{chainId}';''',
                '''delete from v2_goods_stock_locking where  chain_id ='{chainId}' and type < 40 ;'''
            ]
            for sql in sqls:
                self.goods_db_client.execute(sql.format(chainId=self.chain_id))
            organ_his_type = self.basic_db_client.fetchone(
                ''' select id from organ where id = '{chainId}' and address_city_id in( '370100' ,'371100' ,'370600' ,'370900' ,'371600' ,'120100' ); '''.format(chainId=self.chain_id))
            if organ_his_type is not None:
                self.goods_db_client.execute(''' update v2_goods_chain_config set chain_external_flag = chain_external_flag | 8 where chain_id ='{chainId}' and open_pharmacy_flag > 0;'''.format(chainId=self.chain_id))
                return
            self.goods_db_client.execute(''' update v2_goods_chain_config set chain_external_flag = chain_external_flag | 8 where chain_id ='{chainId}';'''.format(chainId=self.chain_id))
        except Exception as e:
            print(e)



def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    updateData = UpdateData(args.region_name, args.chain_id)
    updateData.run()


if __name__ == '__main__':
    main()
