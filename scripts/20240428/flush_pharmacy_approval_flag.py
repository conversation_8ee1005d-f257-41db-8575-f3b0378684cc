# -*- coding: utf-8 -*-
"""
@name: flush_pharmacy_approval_flag.py
@author: <PERSON><PERSON><PERSON>
@email: <EMAIL>
@date: 2024-04-30 10:01:48
"""
import argparse
import sys
import requests
import os
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.rpc import regionRpcHost


DEFAULT_ID = '00000000000000000000000000000000'


class UpdateData:
    chain_id = None
    basic_db_client = None
    goods_db_client = None
    his_advice_client = None

    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', 'prod', True)
        self.goods_db_client = DBClient(self.region_name, 'abc_cis_stock', 'abc_cis_goods', 'prod', True)
        self.pe_client = DBClient(region_name, 'scrm_hospital', 'abc_pe_order', 'prod', True)


    def run(self):
        organ_his_type = self.basic_db_client.fetchone(
            ''' select his_type from organ where id = '{chainId}' '''.format(chainId=self.chain_id))
        if organ_his_type is None:
            return
        if organ_his_type['his_type'] != 10:
            return
        print("organ_his_type is 10")
        self.flush_pharmacy_approval_flag()
        requests.get("""http://{rpcHost}/rpc/v3/goods/jenkins/clean-chain-redis-cache?chainId={chainId}"""
                     .format(chainId=self.chain_id, rpcHost=regionRpcHost(self.region_name)))

    def flush_pharmacy_approval_flag(self):
        sql = """
            update v2_goods_chain_config set chain_external_flag = chain_external_flag | 6, stock_trans_chain_review = 1 where chain_id = '{chainId}'
        """.format(chainId=self.chain_id)
        # print(sql)
        self.goods_db_client.execute(sql)
        self.pe_client.execute("""update abc_pe_order.v1_pe_sheet
                                        set we_clinic_release_status = 1
                                        where status = 90 and chain_id ='{chainId}'""".format(chainId=self.chain_id))


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    update_data = UpdateData(args.region_name, args.chain_id)
    update_data.run()


if __name__ == '__main__':
    main()
