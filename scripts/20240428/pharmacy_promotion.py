import argparse
import os
import sys

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.db import DBClient


class UpdateData:
    chain_id = None
    goods_db_client = None
    goods_log_db_client = None
    adb_client = None
    promotion_db_client = None

    def __init__(self, region_name, chain_id):
        self.env = 'prod'
        self.chain_id = chain_id
        self.region_name = region_name
        self.promotion_db_client = DBClient(self.region_name, 'abc_cis_charge', 'abc_cis_promotion', self.env, True)

    def run(self):
        self.insert_promotion_main()

    def insert_promotion_main(self):
        # 折扣活动
        self.promotion_db_client.execute("""
            INSERT INTO v2_promotion_main (id, chain_id, name, is_forever, begin_date, end_date, is_all_clinic, is_all_patient, type, status, is_deleted, publish_time, created, created_by,
                                           last_modified, last_modified_by, period_rule)
            select a.id,
                   a.chain_id,
                   a.name,
                   a.is_forever,
                   a.begin_date,
                   a.end_date,
                   if(count(b.promotion_id) = 0, 1, 0) as is_all_clinic,
                   if(count(c.promotion_id) = 0, 1, 0) as is_all_patient,
                   0                                   as type,
                   a.status,
                   if(a.status > 90, 1, 0)             as is_deleted,
                   a.created                           as publish_time,
                   a.created,
                   a.created_by,
                   a.last_modified,
                   a.last_modified_by,
                   null                                as period_rule
            from v2_promotion_discount a
                     left join v2_promotion_discount_clinic b on (a.id = b.promotion_id and b.is_deleted = 0)
                     left join v2_promotion_discount_member c on (a.id = c.promotion_id and c.is_deleted = 0)
            where a.type = 1
              and a.main_id is null
              and a.chain_id = '{chainId}'
            group by a.id;
        """.format(chainId=self.chain_id))

        self.promotion_db_client.execute("""
            update v2_promotion_discount a inner join v2_promotion_main b on (a.id = b.id)
            set a.main_id = b.id
            where a.main_id is null and a.chain_id = '{chainId}';
        """.format(chainId=self.chain_id))

        self.promotion_db_client.execute("""
            INSERT INTO v2_promotion_main (id, chain_id, name, is_forever, begin_date, end_date, is_all_clinic, is_all_patient, type, status, is_deleted, publish_time, created, created_by,
                               last_modified, last_modified_by, period_rule)
            select a.id,
                   a.chain_id,
                   a.name,
                   a.is_forever,
                   a.begin_date,
                   a.end_date,
                   if(count(b.promotion_id) = 0, 1, 0) as is_all_clinic,
                   if(count(c.promotion_id) = 0, 1, 0) as is_all_patient,
                   0                                   as type,
                   a.status,
                   if(a.status > 90, 1, 0)             as is_deleted,
                   a.created                           as publish_time,
                   a.created,
                   a.created_by,
                   a.last_modified,
                   a.last_modified_by,
                   null                                as period_rule
            from v2_promotion a
                     left join v2_promotion_clinic b on (a.id = b.promotion_id and b.is_deleted = 0)
                     left join v2_promotion_member c on (a.id = c.promotion_id and c.is_deleted = 0)
            where a.type = 2
              and a.main_id is null
              and a.chain_id = '{chainId}'
            group by a.id;
        """.format(chainId=self.chain_id))

        self.promotion_db_client.execute("""
            update v2_promotion a inner join v2_promotion_main b on (a.id = b.id)
            set a.main_id = b.id
            where a.main_id is null and a.chain_id = '{chainId}';
        """.format(chainId=self.chain_id))


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    updateData = UpdateData(args.region_name, args.chain_id)
    updateData.run()


if __name__ == '__main__':
    main()
