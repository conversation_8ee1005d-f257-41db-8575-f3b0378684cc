#! /usr/bin/env python3
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
import json
import argparse
import logging
import pypinyin
from decimal import Decimal, getcontext

logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)

default_id = '00000000000000000000000000000000'
standard_chain_id_map = {
    'dev': {
        'normal_template_chain_id': 'ffffffff000000000c5a1308069aa000',
        'eye_template_chain_id': 'ffffffff0000000025ba072801ec0000'
    },
    'test': {
        'normal_template_chain_id': 'ffffffff000000001cdb3b8007024000',
        'eye_template_chain_id': 'ffffffff00000000266aac780890e000'
    },
    'prod': {
        'normal_template_chain_id': 'ffffffff000000001f2d1f400a1e6000',
        'eye_template_chain_id': 'ffffffff0000000026f573b00acb4000'
    }
}

# 0-radio 1-checkbox 2-autocomplete 3-input 4-upload 5textarea 6-select 7-选择眼镜度数 8-选择视力 9-多行文本框
default_component_type = 3
component_type_map = {
    "单选框": 0,
    "复选框": 1,
    "自动补全输入框": 2,
    "输入框": 3,
    "上传组件": 4,
    "文本域": 5,
    "下拉框": 6,
    "选择眼镜度数": 7,
    "选择视力": 8,
    "多行文本框": 9
}

component_config_type_map = {
    0: "radio",
    1: "checkbox",
    2: "autocomplete",
    3: "input",
    4: "upload",
    5: "textarea",
    6: "select",
    7: "select_glasses_degree",
    8: "select_vision",
    9: "textarea"
}

"""
1：小数，小数点前1位后2位
2：正负数，2位小数，小数点前2位后2位
3：0-180的正整数，
4：2位小数，小数点前2位后2位，正数
5：2位正整数，
6：数字，2位小数，小数点前3位后2位
7：2位正整数 0-10
8：2位小数，1以内
9：正数，整数，4位数
10：数字，小数前5位后2位
11：100以内，2位小数，百分数
"""

inspect_type_map = {
    0: "L",
    1: "R",
    2: "B"
}

f = open('eye_examination_item.sql', "w+")

items_json = """[
    {
        "name": "AC/A",
        "en_name": "AC/A",
        "unit": "△/D",
        "ref": {
            "min": "3",
            "max": "5"
        },
        "component_type": "输入框",
        "column": 4,
        "constraints": [
            {
                "value": "3",
                "min_value": "0",
                "validate_type": 1,
                "component_config_type": "number"
            }
        ],
        "eye_type": "双眼"
    },
    {
        "name": "BCC",
        "en_name": "BCC",
        "unit": "D",
        "ref": {
            "min": "+0.25",
            "max": "+0.75"
        },
        "component_type": "输入框",
        "column": 4,
        "constraints": [
            {
                "value": "0.25",
                "min_value": null,
                "validate_type": 1,
                "component_config_type": "number"
            }
        ],
        "eye_type": "双眼"
    },
    {
        "name": "AMP",
        "en_name": "AMP",
        "unit": "D",
        "ref": {
            "min": "",
            "max": ""
        },
        "component_type": "输入框",
        "column": 2,
        "constraints": [
            {
                "value": "11.25",
                "min_value": "0",
                "validate_type": 1,
                "component_config_type": "number"
            }
        ],
        "eye_type": "单眼"
    },
    {
        "name": "flipper",
        "en_name": "flipper",
        "unit": "cpm",
        "ref": {
            "min": "",
            "max": ""
        },
        "component_type": "输入框",
        "column": 2,
        "constraints": [
            {
                "value": "1",
                "min_value": "0",
                "validate_type": 1,
                "component_config_type": "number"
            }
        ],
        "eye_type": "单眼"
    },
    {
        "name": "WORTH 4DOT",
        "en_name": "WORTH4DOT",
        "unit": "dots",
        "ref": {
            "min": "",
            "max": ""
        },
        "component_type": "下拉框",
        "column": 4,
        "constraints": [
            
        ],
        "options": ["2", "3", "4", "5"],
        "eye_type": "双眼"
    }
]"""

items = json.loads(items_json)


def generate_constraint(value, validate_type, component_config_type, min_value):
    # 将字符串转换为Decimal对象
    number_decimal = Decimal(value)

    # 获取小数的精度
    precision = len(number_decimal.as_tuple().digits)

    # 获取小数的标度
    scale = -number_decimal.as_tuple().exponent

    # 获取小数的最大值（根据当前上下文的精度）
    max_value = int(9 * (precision - scale))
    if scale == 0:
        # 整数
        if min_value is None:
            # 正负数
            message = f"正负数，最大值{max_value}"
        else:
            # 正数
            message = f"正整数，0-{max_value}"
    else:
        # 小数
        if min_value is None:
            # 正负数
            message = f"正负数，小数点前{precision - scale}位后{scale}位"
        else:
            # 正小数
            message = f"正小数，小数点前{precision - scale}位后{scale}位"
    constraint = {
        "max": max_value,
        "min": min_value,
        "size": None,
        "unit": "",
        "scale": scale,
        "message": message,
        "required": False,
        "precision": precision,
        "componentConfig": {
            "type": component_config_type
        }
    }
    return constraint


def run(env):
    db_client = DBClient('ShangHai', 'ob', 'abc_cis_examination', env, True)
    group_id_base = db_client.fetchone("select max(group_id) as group_id from v2_examination_item where is_standard = 1 and inspect_type is not null")['group_id']
    for i in range(0, len(items)):
        item = items[i]
        group_id = group_id_base + i + 1
        eye_type = item['eye_type']
        if eye_type == '双眼':
            inspect_types = [2]
        else:
            inspect_types = [0, 1]
        for inspect_type in inspect_types:
            item_id = db_client.fetchone("select replace(uuid(), '-', '') as id")['id']
            sql = f"""INSERT INTO v2_examination_item (id, clinic_id, chain_id, type, name, en_name, search_text, unit,
                                         ref, result_display_scale, is_deleted, created_user_id,
                                         created_date, last_modified_user_id, last_modified_date, item_code,
                                         goods_id, source_id, inspect_type, component_type, options,
                                         constraints, group_id, value_quantity, display_name, is_standard,
                                         tag, sort, additional, item_type, goods_type, device_type)
            VALUES ('{item_id}', '{standard_chain_id_map[env]['eye_template_chain_id']}', '{standard_chain_id_map[env]['eye_template_chain_id']}', 1, '{item['name']}',
                    '{inspect_type_map[inspect_type]}:{item['en_name']}', '{f"{item['name']}_{''.join([p[0][0] for p in pypinyin.pinyin(item['name'], style=pypinyin.Style.NORMAL)])}_{item['en_name'].lower()}"}', '{item['unit']}', '{json.dumps(item['ref'], ensure_ascii=False)}', 2, 0, '{default_id}',
                    now(), '{default_id}', now(), null, '', null, {inspect_type}, {component_type_map.get(item['component_type'], default_component_type)}, '{item.get('options', '[]')}', '{json.dumps([generate_constraint(constraint['value'], constraint['validate_type'], constraint['component_config_type'], constraint.get('min_value', None)) for constraint in item['constraints']], ensure_ascii=False)}', {group_id}, 1, '{item.get('display_name', item['name'])}', 1, null, 0, '{{
            "column": {item['column']}
              }}', 2, 3, null);"""
            f.write(sql + os.linesep)
            if item['ref']['min'] != '' or item['ref']['max'] != '':
                ref_detail_sql = f"""INSERT INTO v2_examination_item_ref_detail(id, item_id, sex, start_age, end_age, age_unit, ref, is_deleted, created,
                                               created_by, last_modified, last_modified_by, sample_type)
                VALUES (substr(uuid_short(), 4), '{item_id}', null, null, null, null, '{json.dumps(item['ref'], ensure_ascii=False)}', 0, now(), '{default_id}', now(), '{default_id}', null);
                    """
                f.write(ref_detail_sql + os.linesep)


if __name__ == '__main__':
    run('dev')
