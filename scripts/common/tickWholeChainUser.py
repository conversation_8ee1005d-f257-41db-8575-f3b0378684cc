#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import logging
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.rediscli import RedisClient,redisHost
import argparse

default_id = '00000000000000000000000000000000'

# 2024 发现有一部分用户的rediskey没在 v2_clinic_login_token表里面
#这里通过开启redis的keys * 功能 清理logintoken
def tickChainLoginUserByFix(abcRegion,chainId):
    try:
        # 连HARedis
        haRedisCli = RedisClient(abcRegion, 'abc-ha-redis', 0, 'prod', True)
        basic_db_client = DBClient(abcRegion, 'abc_cis_mixed', 'abc_cis_basic', 'prod', True)
        #查连锁员工
        sql = '''select id,
                redis_token_key as redisTokenKey
            from v2_clinic_login_token
            where chain_id = '{chainId}'  and (is_deleted = 1 or is_valid = 0); '''
        employeeIdList = basic_db_client.fetchall(sql.format(chainId=chainId))
        if employeeIdList:
            for employeeId in employeeIdList:
                key_str = employeeId["redisTokenKey"]
                result = haRedisCli.client.delete(key_str)
        sql = '''select id,
                redis_token_key as redisTokenKey
            from v2_clinic_login_token_deleted
            where chain_id = '{chainId}' and created >='2023-06-01'; '''
        employeeIdList = basic_db_client.fetchall(sql.format(chainId=chainId))
        if employeeIdList:
            for employeeId in employeeIdList:
                key_str = employeeId["redisTokenKey"]
                result = haRedisCli.client.delete(key_str)
    except Exception as e:
        print('清理token时出错:', e)
def tickChainLoginUser(abcRegion, chain_id):
    sql = '''select id,
                redis_token_key as redisTokenKey
            from v2_clinic_login_token
            where chain_id = '{chainId}' 
            and is_valid = 1
            and is_deleted = 0
            and expired > now(); '''
    basic_db_client = DBClient(abcRegion, 'abc_cis_mixed', 'abc_cis_basic', 'prod', True)
    basic_ob_db_client = DBClient(abcRegion, 'ob', 'abc_cis_basic', 'prod', True)
    redisTokenKeyList = basic_ob_db_client.fetchall(sql.format(chainId=chain_id))
    haRedisCli = RedisClient(abcRegion, 'abc-ha-redis', 0, 'prod', True)
    if redisTokenKeyList is None or len(redisTokenKeyList) == 0:
        return
    # file_name = '''./tickUser/{chainId}_redis.txt'''.format(chainId=chain_id)
    # f = open(file_name.format(chainId=chain_id), 'w')
    for redisTokenKey in redisTokenKeyList:
        redisKey = redisTokenKey['redisTokenKey']
        result = haRedisCli.client.delete(redisKey)
        if result < 0:
            logging.error('===清理token:'+redisKey+'失败:'+str(result))
        # 把踢人的redis命令写入文件
        # python3 写入文件换行要 加\n，否则会写在一行
        # f.write('''del {redisTokenKey}\n'''.format(redisTokenKey=redisTokenKey['redisTokenKey']))

    # 关闭文件
    # f.close()
    # redisCmd ='''cat {fileName} | redis-cli -h {redisHostUrl} -n 0 -p 6379'''.format(
    #     fileName=file_name,redisHostUrl=redisHost(abcRegion,'abcHaRedis'))
    # logging.info(redisCmd)
    # os.system(redisCmd)
    # 删除文件
    # os.remove(file_name)

    # 修改db token 为无效
    idList = ','.join([''' '{0}' '''.format(record['id']) for record in redisTokenKeyList])
    sql = '''update v2_clinic_login_token set is_deleted = 1,expired =  now() where chain_id = '{chainId}' and id in( {idList} ) ; '''.format(
        chainId=chain_id, idList=idList)
    basic_db_client.execute(sql)


def run(abcRegion, chain_id):
    tickChainLoginUser(abcRegion, chain_id)
    # tickChainLoginUserByFix(abcRegion, chain_id)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.region_name, args.chain_id)


if __name__ == '__main__':
    main()
