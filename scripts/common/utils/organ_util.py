class OrganUtils:
    @staticmethod
    def is_head_clinic_by_organ(organ) -> bool:
        """
        判断是否是连锁总部或者单店

        :param organ: 门店信息(v2_clinic表)
        :return: 是否为连锁总部或者单店
        """
        return OrganUtils.is_head_clinic(organ['node_type'], organ['view_mode'])

    @staticmethod
    def is_head_clinic(node_type, view_mode) -> bool:
        """
        判断是否是连锁总部或者单店

        :param node_type:
        :param view_mode:
        :return:
        """

        if node_type == 1:
            return True
        return view_mode == 1

    @staticmethod
    def is_single_clinic_by_organ(organ) -> bool:
        return OrganUtils.is_single_clinic(organ['view_mode'])

    @staticmethod
    def is_single_clinic(view_mode) -> bool:
        return view_mode == 1
