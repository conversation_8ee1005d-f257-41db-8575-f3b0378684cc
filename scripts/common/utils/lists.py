class ListUtils:
    @staticmethod
    def group_by(_list: list, key_fun):
        if not _list:
            return {}

        key_to_values = {}
        for e in _list:
            key = key_fun(e)
            values = key_to_values.get(key)
            if not values:
                values = []
            values.append(e)
            key_to_values[key] = values

        return key_to_values

    @staticmethod
    def to_map(_list: list, key_fun, value_fun=lambda e: e):
        if not _list:
            return {}

        key_to_value = {}
        for e in _list:
            key = key_fun(e)
            key_to_value[key] = value_fun(e)

        return key_to_value

    @staticmethod
    def dist_mapping(_list: list, key_fun):
        if not _list:
            return []

        seen = set()
        result = []
        for item in _list:
            key = key_fun(item)
            if key not in seen:
                seen.add(key)
                result.append(key)

        return list(result)
