class AttrHelper:
    @staticmethod
    def getattr(obj, attributes: str):
        # 将属性访问链拆分成列表
        attrs = attributes.split('.')
        # 逐级使用 getattr() 获取属性值
        value = obj
        for attr in attrs:
            if value is None:
                value = None
            elif isinstance(value, dict):
                value = value.get(attr)
            else:
                value = getattr(value, attr)
        return value
