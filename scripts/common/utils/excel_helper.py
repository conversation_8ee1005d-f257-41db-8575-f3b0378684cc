from .attr_helper import AttrHelper as attr_helper
import openpyxl.worksheet.worksheet as worksheet


class ExcelHelper:
    @staticmethod
    def sheet_to_json(sheet: worksheet, mapper: dict, filter: dict = None):
        arrays = []
        headers = {}
        for row_index, row in enumerate(sheet.rows, start=1):
            if row_index == 1:
                headers.update({col.col_idx: col.value.strip() for col in row if col.value is not None})
                continue
            if filter is not None and attr_helper.getattr(row[filter['column_index']], filter['attrs']) != filter['value']:
                continue
            arrays.append({mapper[headers[col.col_idx]]: col.value.strip() if isinstance(col.value, str) else col.value if col.value is not None else col.value for col in row if hasattr(col, 'col_idx') and col.col_idx in headers})
        return arrays
