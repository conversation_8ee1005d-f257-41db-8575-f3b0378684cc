from typing import Iterable
from multizone.db import escape_string
from datetime import date, time, datetime, timedelta


class SqlUtils:
    @staticmethod
    def to_in_value(_list: Iterable):
        if not _list:
            return

        return ','.join([f"'{e}'" for e in _list])

    @staticmethod
    def trans_data(rows, client_source, client_target, table_name_source, table_name_target, custom_update_dict):
        """
        数据迁移
        :param rows: 数据行
        :param client_source: 源数据库客户端
        :param client_target: 目标数据库客户端
        :param table_name_source: 源表名
        :param table_name_target: 目标表名
        :param custom_update_dict: 自定义更新字段e.g. {'clinic_id': 'xxx'} 会将clinic_id的值更新为xxx
        :return:
        """
        columns = client_source.show_columns(table_name_source)
        column_names = [column['Field'] for column in columns]

        update_values = []
        for column in columns:
            column_name = column['Field']
            column_key = column['Key']
            if column_key != 'PRI':
                update_values.append(f"""`{column_name}` = VALUES(`{column_name}`)""")

        # rows 拆分1组1000条
        rows_1000 = [rows[i:i + 1000] for i in range(0, len(rows), 1000)]
        for rows_list in rows_1000:
            insert_values = []
            for row in rows_list:
                values = []
                for column in columns:
                    column_name = column['Field']
                    meta_value = row[column_name]
                    value = row[column_name] if row[column_name] is not None else 'null'
                    if isinstance(meta_value, str):
                        value = escape_string(value)
                    if custom_update_dict is not None and custom_update_dict.__contains__(column_name):
                        value = custom_update_dict[column_name]
                    if isinstance(meta_value, str) or isinstance(meta_value, date) or isinstance(meta_value,time) or isinstance(meta_value, datetime):
                        values.append("""'{}'""".format(value))
                    else:
                        values.append("""{}""".format(value))
                insert_values.append('({0})'.format(', '.join(values)))
            insert_sql = """
        INSERT INTO {0}
        VALUES {1}
        ON DUPLICATE KEY UPDATE {2};
                    """.format('{0} ({1})'.format(table_name_target, ', '.join([f"`{column_name}`" for column_name in column_names])),
                                '{}'.format(',\n '.join(insert_values)),
                                '{}'.format(', '.join(update_values)))
            client_target.execute(insert_sql)
