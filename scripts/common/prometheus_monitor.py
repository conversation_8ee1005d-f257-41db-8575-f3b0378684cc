#! /usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Prometheus业务监控报表
获取最近2天每天9点到11点的process_cpu_usage平均值
输出格式：服务名，日期，cpu平均值
支持邮件发送功能
"""

import argparse
import logging
import os
import sys
import tempfile
from datetime import datetime, timedelta
from io import BytesIO
from base64 import b64encode

import requests
import yagmail
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from collections import defaultdict

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 核心接口列表：[服务名, 接口功能, 接口路径]
core_interfaces = [
    ["abc-cis-sc-clinic-service", "批量查询employee", "/rpc/v3/clinics/employees/query-by-ids", "POST"],
    ["abc-cis-sc-clinic-service", "条件批量查询employee", "/api/v3/clinics/employees/list-by-condition", "POST"],
    ["abc-cis-sc-clinic-service", "批量查询门店信息", "/rpc/v3/clinics/departments/query-by-ids", "POST"],
    ["abc-cis-sc-clinic-service", "批量查询科室信息", "/rpc/v3/clinics/organs/list-by-ids", "POST"],
    ["abc-cis-sc-clinic-service", "登陆人信息", "/api/v3/clinics/employees/me", "GET"],
    ["abc-cis-registration-service", "挂号", "/api/v2/registrations/manage", "POST"],
    ["abc-cis-registration-service", "挂号QL", " /api/v2/registrations/itemlist", "GET"],
    ["abc-cis-registration-service", "查询挂号单", " /api/v2/registrations/{registrationSheetId}", "GET"],
    ["abc-cis-registration-service", "rpc拉取挂号单", "/rpc/registrations/patientorders/{patientOrderId}", "GET"],
    ["abc-cis-outpatient-service", "门诊QL", "/api/v2/outpatients", "GET"],
    ["abc-cis-outpatient-service", "查询门诊单", "/api/v2/outpatients/{id}/", "GET"],
    ["abc-cis-outpatient-service", "提交门诊单", "/api/v2/outpatients/{id}/", "PUT"],
    ["abc-cis-charge-service", "收费QL", "/api/v2/charges", "GET"],
    ["abc-cis-charge-service", "查询收费单", "/api/v2/charges/{id}", "GET"],
    ["abc-cis-charge-service", "提交收费", "/api/v2/charges/{id}/paid", "PUT"],
    ["abc-cis-charge-service", "算费", "/api/v2/charges/calculate", "POST"],
    ["abc-cis-patient-order-service", "查询就诊单", "/rpc/patientorders/{id}", "GET"],
    ["abc-cis-patient-order-service", "批量查询就诊单", "/rpc/patientorders/list", "POST"],
    ["abc-cis-patient-order-service", "创建就诊单", "/rpc/patientorders/", "POST"],
    ["abc-cis-sc-goods-service", "rpc查询goods", "/rpc/v3/goods/find-goods-by-pharmacy-v2", "POST"],
    ["abc-cis-sc-goods-service", "goods搜索", "/api/v3/goods/search", "POST"],
    ["abc-cis-sc-goods-service", "rpc查询goods-根据药房", "/rpc/v3/goods/find-goods-by-pharmacy", "POST"],
    ["abc-cis-sc-goods-service", "rpc查询goods-根据药房v2", "/rpc/v3/goods/find-goods-by-pharmacy-v2", "POST"],
    ["abc-cis-crm-service", "患者搜索", "/api/v2/crm/patients/query", "GET"],
    ["abc-cis-crm-service", "查询患者", "/api/v2/crm/patients/{patientId}", "GET"],
    ["abc-cis-crm-service", "rpc查询患者", "/rpc/crm/patients/basic/{patientId}", "GET"],
    ["abc-cis-crm-service", "rpc查询患者详情", "/rpc/crm/patients/{patientId}", "GET"],
    ["abc-cis-crm-service", "rpc批量查询患者", "/rpc/crm/patients", "POST"],
    ["abc-cis-crm-service", "rpc创建更新患者", "/rpc/crm/patients/basic", "POST"],
    ["abc-cis-crm-service", "rpc轻量级查询患者信息", "/rpc/crm/patients/mini/basic/list", "POST"],
    ["abc-cis-examination-service", "检查检验QL", "/api/v2/examinations", "GET"],
    ["abc-cis-examination-service", "查询检查检验单", "/api/v2/examinations/{id}", "GET"],
    ["abc-cis-shebao-service", "医保收费", "/api/v2/shebao/national/charge/tasks/{id}/paid", "PUT"],
    ["abc-cis-shebao-service", "查询医保收费单", "/api/v2/shebao/national/charge/tasks/{id}", "GET"],
    ["abc-cis-shebao-service", "医保对码", "/api/v2/shebao/code/get_matched_codes", "POST"],
    # 可以根据需要添加更多核心接口
]


class PrometheusMonitor:
    def __init__(self, prometheus_url, token):
        self.prometheus_url = prometheus_url.rstrip('/')
        self.token = token

    def query_prometheus(self, query, start_time, end_time, step='60s'):
        """
        查询Prometheus数据
        """
        url = f"{self.prometheus_url}/api/v1/query_range"
        params = {
            'query': query,
            'start': start_time,
            'end': end_time,
            'step': step
        }

        headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': self.token
        }

        try:
            response = requests.get(url, params=params, headers=headers, timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"查询Prometheus失败: {e}")
            return None

    def get_cpu_usage_report(self, days=2):
        """
        获取最近指定天数每天9-11点的CPU使用率平均值、最大值和最小值
        按服务名和日期维度聚合数据
        """
        # 用于存储所有原始数据的字典，key为(service_name, date)
        raw_data = {}

        # 计算日期范围
        today = datetime.now().date()

        for i in range(days):
            target_date = today - timedelta(days=i)

            # 设置时间范围：9:00-11:00
            start_datetime = datetime.combine(target_date, datetime.min.time().replace(hour=9))
            end_datetime = datetime.combine(target_date, datetime.min.time().replace(hour=11))

            # 转换为Unix时间戳
            start_timestamp = int(start_datetime.timestamp())
            end_timestamp = int(end_datetime.timestamp())

            logger.info(f"查询日期: {target_date}, 时间范围: {start_datetime} - {end_datetime}")

            # 查询CPU使用率数据
            query = 'process_cpu_usage'
            data = self.query_prometheus(query, start_timestamp, end_timestamp, '60s')

            if not data or data.get('status') != 'success':
                logger.warning(f"查询 {target_date} 数据失败")
                continue

            # 处理查询结果，收集所有原始数据
            for result in data['data']['result']:
                metric = result['metric']
                values = result['values']

                # 获取服务名
                service_name = metric.get('application') or metric.get('job', 'unknown')
                date_str = target_date.strftime('%Y-%m-%d')

                if not values:
                    logger.warning(f"服务 {service_name} 在 {target_date} 无数据")
                    continue

                # 提取有效的CPU值
                cpu_values = [float(value[1]) for value in values if value[1] != 'NaN']

                if cpu_values:
                    # 使用(服务名, 日期)作为key来聚合数据
                    key = (service_name, date_str)
                    if key not in raw_data:
                        raw_data[key] = []
                    raw_data[key].extend(cpu_values)

        # 对聚合后的数据进行统计计算
        results = []
        for (service_name, date_str), all_cpu_values in raw_data.items():
            if all_cpu_values:
                avg_cpu = sum(all_cpu_values) / len(all_cpu_values)
                max_cpu = max(all_cpu_values)
                min_cpu = min(all_cpu_values)

                results.append({
                    'service_name': service_name,
                    'date': date_str,
                    'avg_cpu_usage': round(avg_cpu, 6),
                    'max_cpu_usage': round(max_cpu, 6),
                    'min_cpu_usage': round(min_cpu, 6),
                    'data_points': len(all_cpu_values)  # 添加数据点数量用于调试
                })
                logger.info(f"服务: {service_name}, 日期: {date_str}, 数据点: {len(all_cpu_values)}, CPU平均值: {avg_cpu:.6f}, 最大值: {max_cpu:.6f}, 最小值: {min_cpu:.6f}")
            else:
                logger.warning(f"服务 {service_name} 在 {date_str} 无有效CPU数据")

        return results

    def print_report(self, results):
        """
        打印报表结果
        """
        if not results:
            print("\n❌ 没有找到任何数据")
            return

        print("\n" + "=" * 80)
        print("📊 Prometheus CPU使用率监控报表")
        print("=" * 80)
        print(f"📅 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)

        # 计算最大服务名长度以动态调整列宽
        max_service_name_len = max(len(result['service_name']) for result in results)
        service_col_width = max(20, min(max_service_name_len + 2, 50))

        # 打印表头
        header = f"{'服务名':<{service_col_width}} {'日期':<12} {'CPU平均值':<12} {'CPU最大值':<12} {'CPU最小值':<12} {'数据点':<8}"
        print(header)
        print("-" * len(header))

        # 按服务名和日期排序
        sorted_results = sorted(results, key=lambda x: (x['service_name'], x['date']))

        # 按服务分组显示
        current_service = None
        for result in sorted_results:
            # 如果是新的服务，添加分隔线
            if current_service != result['service_name']:
                if current_service is not None:
                    print("-" * len(header))
                current_service = result['service_name']

            # 格式化CPU值显示
            avg_cpu_str = f"{result['avg_cpu_usage']:.4f}"
            max_cpu_str = f"{result['max_cpu_usage']:.4f}"
            min_cpu_str = f"{result['min_cpu_usage']:.4f}"
            data_points = result.get('data_points', 0)

            # 添加颜色标识（使用emoji）
            if result['avg_cpu_usage'] > 0.8:
                status_icon = "🔴"  # 高CPU使用率
            elif result['avg_cpu_usage'] > 0.5:
                status_icon = "🟡"  # 中等CPU使用率
            else:
                status_icon = "🟢"  # 低CPU使用率

            service_display = f"{status_icon} {result['service_name']}"

            print(f"{service_display:<{service_col_width+2}} {result['date']:<12} {avg_cpu_str:<12} {max_cpu_str:<12} {min_cpu_str:<12} {data_points:<8}")

        print("=" * len(header))
        print(f"📈 总计: {len(results)} 条记录")
        print(f"🔴 高CPU(>0.8)  🟡 中等CPU(>0.5)  🟢 低CPU(≤0.5)")
        print("=" * 80)

    def export_to_csv(self, results, filename=None):
        """
        导出结果到CSV文件
        """
        if not filename:
            filename = f"prometheus_cpu_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"

        try:
            import csv
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['service_name', 'date', 'avg_cpu_usage']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()
                # 按服务名和日期排序
                sorted_results = sorted(results, key=lambda x: (x['service_name'], x['date']))
                for result in sorted_results:
                    writer.writerow(result)

            logger.info(f"报表已导出到: {filename}")
            return filename
        except Exception as e:
            logger.error(f"导出CSV失败: {e}")
            return None

    def generate_combined_html_report(self, cpu_results, response_time_results):
        """
        生成包含CPU和响应时间的综合HTML报表
        """
        if not cpu_results and not response_time_results:
            return "<p>没有找到任何数据</p>"

        # 获取所有日期
        all_dates = sorted(set(result['date'] for result in cpu_results))

        # 按服务名和日期组织CPU数据
        cpu_by_service = {}
        for result in cpu_results:
            service_name = result['service_name']
            if service_name not in cpu_by_service:
                cpu_by_service[service_name] = {}
            cpu_by_service[service_name][result['date']] = result

        # 处理响应时间数据
        response_time_by_interface = {}
        if response_time_results:
            all_dates = sorted(set(date for dates in [set(result['date'] for result in cpu_results),
                                                     set(result['date'] for result in response_time_results)]
                                  for date in dates))

            for result in response_time_results:
                # 使用(服务名, 接口名)作为键
                key = (result['service_name'], result['interface_name'])
                if key not in response_time_by_interface:
                    response_time_by_interface[key] = {'service_name': result['service_name'],
                                                      'interface_name': result['interface_name'],
                                                      'dates': {}}
                # 存储每个日期的响应时间
                response_time_by_interface[key]['dates'][result['date']] = result

        # 生成CSS样式
        css_styles = (
            "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background-color: #f5f5f5; } "
            ".container { max-width: 1400px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); } "
            "h1 { color: #333; text-align: center; margin-bottom: 10px; } "
            "h2 { color: #555; margin-top: 30px; margin-bottom: 15px; border-bottom: 2px solid #4CAF50; padding-bottom: 5px; } "
            ".report-info { text-align: center; color: #666; margin-bottom: 20px; } "
            "table { border-collapse: collapse; width: 100%; margin-top: 20px; } "
            "th, td { border: 1px solid #ddd; padding: 8px 6px; text-align: center; font-size: 12px; } "
            "th { background-color: #4CAF50; color: white; font-weight: bold; } "
            "tr:nth-child(even) { background-color: #f9f9f9; } "
            "tr:hover { background-color: #f5f5f5; } "
            ".cpu-high { background-color: #ffebee; color: #d32f2f; font-weight: bold; } "
            ".cpu-medium { background-color: #fff3e0; color: #f57c00; font-weight: bold; } "
            ".cpu-low { background-color: #e8f5e8; color: #388e3c; } "
            ".rt-slow { background-color: #ffebee; color: #d32f2f; font-weight: bold; } "
            ".rt-medium { background-color: #fff3e0; color: #f57c00; font-weight: bold; } "
            ".rt-fast { background-color: #e8f5e8; color: #388e3c; } "
            ".rt-improved { background-color: #e8f5e8; color: #388e3c; font-weight: bold; } "
            ".rt-worse { background-color: #ffebee; color: #d32f2f; font-weight: bold; } "
            ".service-name { text-align: left; font-weight: bold; min-width: 150px; } "
            ".interface-name { text-align: left; min-width: 200px; } "
            ".cpu-avg { font-size: 14px; font-weight: bold; } "  # 新增：平均值样式
            ".cpu-minmax { font-size: 11px; color: #666; } "  # 新增：最大值/最小值样式
            ".chart-section { margin: 30px 0; } "
            ".chart-container { margin-bottom: 30px; overflow: hidden; } "
            ".chart-item { display: inline-block; width: 48%; min-width: 400px; text-align: center; vertical-align: top; margin: 1%; } "
            ".chart-item img { width: 100%; max-width: 600px; height: auto; border: 1px solid #ddd; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); } "
            "h3 { color: #444; margin-top: 25px; margin-bottom: 15px; font-size: 16px; } "
            ".legend { margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px; font-size: 11px; color: #666; } "
            ".legend-item { display: inline-block; margin-right: 15px; margin-bottom: 5px; } "
        )

        html_content = f"""<!DOCTYPE html><html><head><meta charset="UTF-8"><title>业务监控</title><style>{css_styles}</style></head>"""

        # CPU使用率表格 - 修改为新格式
        if cpu_results:
            html_content += "<h2>🖥️ CPU使用率</h2><table><tr><th class='service-name'>服务名</th>"

            # 添加日期列 - 修改表头格式
            for date_str in all_dates:
                html_content += f"<th>{date_str}<br><span class='cpu-minmax'>平均值（最大值/最小值）</span></th>"

            # 添加变化幅度列
            if len(all_dates) >= 2:
                html_content += "<th>变化幅度</th>"

            html_content += "</tr>"

            # 按服务名排序显示数据
            for service_name in sorted(cpu_by_service.keys()):
                html_content += f"<tr><td class='service-name'>{service_name}</td>"

                # 添加每个日期的CPU数据 - 修改显示格式
                for date_str in all_dates:
                    if date_str in cpu_by_service[service_name]:
                        cpu_data = cpu_by_service[service_name][date_str]
                        avg_cpu = cpu_data['avg_cpu_usage']
                        max_cpu = cpu_data['max_cpu_usage']
                        min_cpu = cpu_data['min_cpu_usage']

                        css_class = "cpu-low"
                        if avg_cpu > 0.8:
                            css_class = "cpu-high"
                        elif avg_cpu > 0.5:
                            css_class = "cpu-medium"

                        # 新格式：平均值(最大值/最小值) - 使用HTML标签区分字体大小
                        cpu_display = f"<span class='cpu-avg'>{avg_cpu:.4f}</span><br><span class='cpu-minmax'>({max_cpu:.4f}/{min_cpu:.4f})</span>"
                        html_content += f"<td class='{css_class}'>{cpu_display}</td>"
                    else:
                        html_content += "<td>-</td>"

                # 添加变化幅度
                if len(all_dates) >= 2 and all_dates[0] in cpu_by_service[service_name] and all_dates[-1] in cpu_by_service[service_name]:
                    first_value = cpu_by_service[service_name][all_dates[0]]['avg_cpu_usage']
                    last_value = cpu_by_service[service_name][all_dates[-1]]['avg_cpu_usage']

                    if first_value > 0:
                        change_rate = (last_value - first_value) / first_value * 100

                        css_class = "rt-improved" if change_rate < 0 else "rt-worse" if change_rate > 0 else ""
                        change_icon = "⬇️" if change_rate < 0 else "⬆️" if change_rate > 0 else "➡️"

                        html_content += f"<td class='{css_class}'>{change_icon} {abs(change_rate):.2f}%</td>"
                    else:
                        html_content += "<td>-</td>"
                else:
                    html_content += "<td>-</td>"

                html_content += "</tr>"

            html_content += "</table>"

        # 响应时间表格 - 新格式
        if response_time_results and len(all_dates) >= 2:
            html_content += "<h2>⚡ 响应时间</h2><table><tr><th class='service-name'>服务名</th><th class='interface-name'>接口功能</th>"

            # 添加日期列
            for date_str in all_dates:
                html_content += f"<th>{date_str}<br>平均值(ms)</th>"

            # 添加变化幅度列
            if len(all_dates) >= 2:
                html_content += "<th>变化幅度</th>"

            html_content += "</tr>"

            # 按服务名排序
            for key in sorted(response_time_by_interface.keys()):
                service_name, interface_name = key
                interface_data = response_time_by_interface[key]

                html_content += f"<tr><td class='service-name'>{service_name}</td><td class='interface-name'>{interface_name}</td>"

                # 添加每个日期的响应时间
                prev_value = None
                for date_str in all_dates:
                    if date_str in interface_data['dates']:
                        rt_data = interface_data['dates'][date_str]
                        avg_rt = rt_data['avg_response_time_ms']

                        css_class = "rt-fast"
                        if avg_rt > 1000:  # 大于1秒
                            css_class = "rt-slow"
                        elif avg_rt > 500:  # 大于500ms
                            css_class = "rt-medium"

                        html_content += f"<td class='{css_class}' title='平均: {avg_rt:.2f}ms, 最大: {rt_data['max_response_time_ms']:.2f}ms, 最小: {rt_data['min_response_time_ms']:.2f}ms'>{avg_rt:.2f}</td>"

                        if prev_value is None:
                            prev_value = avg_rt
                    else:
                        html_content += "<td>-</td>"

                # 添加变化幅度
                if len(all_dates) >= 2 and all_dates[-1] in interface_data['dates'] and all_dates[0] in interface_data['dates']:
                    first_value = interface_data['dates'][all_dates[0]]['avg_response_time_ms']
                    last_value = interface_data['dates'][all_dates[-1]]['avg_response_time_ms']

                    if first_value > 0:
                        change_rate = (last_value - first_value) / first_value * 100

                        css_class = "rt-improved" if change_rate < 0 else "rt-worse" if change_rate > 0 else ""
                        change_icon = "⬇️" if change_rate < 0 else "⬆️" if change_rate > 0 else "➡️"

                        html_content += f"<td class='{css_class}'>{change_icon} {abs(change_rate):.2f}%</td>"
                    else:
                        html_content += "<td>-</td>"
                else:
                    html_content += "<td>-</td>"

                html_content += "</tr>"

            html_content += "</table>"

        # 添加监控曲线图部分
        html_content += "<div class='chart-section'><h2>📈 监控曲线图</h2>"

        # 获取所有服务名
        all_services = set()
        if cpu_results:
            all_services.update(result['service_name'] for result in cpu_results)
        if response_time_results:
            all_services.update(result['service_name'] for result in response_time_results)

        # 为每个服务生成图表
        for service_name in sorted(all_services):
            html_content += f"<h3>🔧 {service_name}</h3>"
            html_content += "<div class='chart-container'>"

            # CPU使用率图表
            if cpu_results:
                cpu_chart = self.generate_cpu_chart(cpu_results, service_name)
                if cpu_chart:
                    html_content += f"<div class='chart-item'><img src='data:image/png;base64,{cpu_chart}' alt='CPU使用率趋势图'/></div>"

            # 响应时间图表
            if response_time_results:
                rt_chart = self.generate_response_time_chart(response_time_results, service_name)
                if rt_chart:
                    html_content += f"<div class='chart-item'><img src='data:image/png;base64,{rt_chart}' alt='响应时间趋势图'/></div>"

            html_content += "</div>"

        html_content += "</div>"

        # 图例说明
        html_content += (
            "<div class='legend'>"
            "<div><strong>📋 说明:</strong></div>"
            "<div class='legend-item'><span class='cpu-high'>🔴 高CPU使用率(>0.8)</span></div>"
            "<div class='legend-item'><span class='cpu-medium'>🟡 中等CPU使用率(>0.5)</span></div>"
            "<div class='legend-item'><span class='cpu-low'>🟢 低CPU使用率(≤0.5)</span></div><br>"
            "<div class='legend-item'><span class='rt-slow'>🔴 慢响应(>1s)</span></div>"
            "<div class='legend-item'><span class='rt-medium'>🟡 中等响应(>500ms)</span></div>"
            "<div class='legend-item'><span class='rt-fast'>🟢 快响应(≤500ms)</span></div><br>"
            "<div class='legend-item'><span class='rt-improved'>⬇️ 性能改善</span></div>"
            "<div class='legend-item'><span class='rt-worse'>⬆️ 性能下降</span></div><br>"
            "<div><strong>CPU格式:</strong> 平均值(最大值/最小值)</div>"
            "<div><strong>统计时间段:</strong> 上午9点-11点</div>"
            "<div><strong>曲线图说明:</strong> 蓝色线为CPU平均值，紫色线为最大值，橙色线为最小值，阴影区域为波动范围</div>"
            "</div></div></body></html>"
        )

        return html_content

    def send_combined_email_report(self, cpu_results, response_time_results, email_config):
        """
        发送包含CPU和响应时间的综合邮件报表
        """
        if not yagmail:
            logger.error("yagmail未安装，无法发送邮件")
            return False

        if not cpu_results and not response_time_results:
            logger.warning("没有数据，跳过邮件发送")
            return False

        try:
            # 生成综合HTML报表
            html_content = self.generate_combined_html_report(cpu_results, response_time_results)

            # 生成CSV附件
            attachments = []
            if email_config.get('attach_csv', True):
                # CPU数据CSV
                if cpu_results:
                    with tempfile.NamedTemporaryFile(mode='w', suffix='_cpu.csv', delete=False, encoding='utf-8') as f:
                        cpu_csv_filename = f.name
                        import csv
                        fieldnames = ['service_name', 'date', 'avg_cpu_usage', 'max_cpu_usage', 'min_cpu_usage']
                        writer = csv.DictWriter(f, fieldnames=fieldnames)
                        writer.writeheader()
                        sorted_results = sorted(cpu_results, key=lambda x: (x['service_name'], x['date']))
                        for result in sorted_results:
                            writer.writerow(result)
                        attachments.append(cpu_csv_filename)

                # 响应时间数据CSV
                if response_time_results:
                    with tempfile.NamedTemporaryFile(mode='w', suffix='_response_time.csv', delete=False, encoding='utf-8') as f:
                        rt_csv_filename = f.name
                        import csv
                        fieldnames = ['service_name', 'date', 'avg_response_time_ms', 'max_response_time_ms', 'min_response_time_ms']
                        writer = csv.DictWriter(f, fieldnames=fieldnames)
                        writer.writeheader()
                        sorted_results = sorted(response_time_results, key=lambda x: (x['service_name'], x['date']))
                        for result in sorted_results:
                            writer.writerow(result)
                        attachments.append(rt_csv_filename)

            # 配置邮件
            yag = yagmail.SMTP(
                user=email_config['smtp_user'],
                password=email_config['smtp_password'],
                smtp_ssl=True,
                host=email_config.get('smtp_host', 'smtp.exmail.qq.com'),
                port=email_config.get('smtp_port', 465)
            )

            # 生成邮件主题
            today = datetime.now().strftime('%Y-%m-%d')
            subject = f"📊 Prometheus 业务监控报表 - {today}"

            # 发送邮件
            yag.send(
                to=email_config['to_emails'],
                cc=email_config.get('cc_emails', []),
                subject=subject,
                contents=html_content,
                attachments=attachments if attachments else None
            )

            logger.info(f"综合邮件发送成功，收件人: {email_config['to_emails']}")

            # 清理临时文件
            for filename in attachments:
                if os.path.exists(filename):
                    os.unlink(filename)

            return True

        except Exception as e:
            logger.error(f"综合邮件发送失败: {e}")
            return False

    def get_response_time_report(self, days=2):
        """
        获取最近指定天数每天9-11点的核心服务接口平均响应时间
        基于Spring Boot Micrometer指标，只查询核心业务接口
        """

        # 用于存储所有原始数据的字典，key为(service_name, interface_name, date)
        raw_data = {}

        # 计算日期范围
        today = datetime.now().date()

        for i in range(days):
            target_date = today - timedelta(days=i)

            # 设置时间范围：9:00-11:00
            start_datetime = datetime.combine(target_date, datetime.min.time().replace(hour=9))
            end_datetime = datetime.combine(target_date, datetime.min.time().replace(hour=11))

            # 转换为Unix时间戳
            start_timestamp = int(start_datetime.timestamp())
            end_timestamp = int(end_datetime.timestamp())

            logger.info(f"查询核心接口响应时间 - 日期: {target_date}, 时间范围: {start_datetime} - {end_datetime}")

            # 为每个核心接口构建查询
            for service_name, interface_desc, uri_path, method in core_interfaces:
                # 构建针对特定服务和URI的查询
                query = f'(sum(rate(http_server_requests_seconds_sum{{service="{service_name}", exception="None", uri="{uri_path}", method="{method}", status="200"}}[5m])) / sum(rate(http_server_requests_seconds_count{{service="{service_name}", exception="None", uri="{uri_path}", method="{method}", status="200"}}[5m]))) * 1000'

                logger.info(f"查询接口: {service_name} - {interface_desc} ({uri_path})")
                data = self.query_prometheus(query, start_timestamp, end_timestamp, '60s')

                if not data or data.get('status') != 'success':
                    logger.warning(f"查询接口 {service_name}:{uri_path} 在 {target_date} 数据失败")
                    continue

                # 处理查询结果
                for result in data['data']['result']:
                    metric = result['metric']
                    values = result['values']

                    date_str = target_date.strftime('%Y-%m-%d')
                    interface_key = f"{interface_desc}({uri_path})"

                    if not values:
                        logger.warning(f"接口 {service_name}:{interface_desc} 在 {target_date} 无响应时间数据")
                        continue

                    # 提取有效的响应时间值（毫秒）
                    response_times = [float(value[1]) for value in values if value[1] != 'NaN' and float(value[1]) > 0]

                    if response_times:
                        key = (service_name, interface_key, date_str)
                        if key not in raw_data:
                            raw_data[key] = []
                        raw_data[key].extend(response_times)

        # 统计计算
        results = []
        for (service_name, interface_key, date_str), all_response_times in raw_data.items():
            if all_response_times:
                avg_response_time = sum(all_response_times) / len(all_response_times)
                max_response_time = max(all_response_times)
                min_response_time = min(all_response_times)

                results.append({
                    'service_name': service_name,
                    'interface_name': interface_key,
                    'date': date_str,
                    'avg_response_time_ms': round(avg_response_time, 2),
                    'max_response_time_ms': round(max_response_time, 2),
                    'min_response_time_ms': round(min_response_time, 2),
                    'data_points': len(all_response_times)
                })
                logger.info(f"核心接口响应时间 - 服务: {service_name}, 接口: {interface_key}, 日期: {date_str}, 平均: {avg_response_time:.2f}ms")

        return results

    def generate_cpu_chart(self, cpu_results, service_name):
        """
        为指定服务生成CPU使用率趋势图
        """
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

        # 筛选指定服务的数据
        service_data = [result for result in cpu_results if result['service_name'] == service_name]
        if not service_data:
            return None

        # 按日期排序
        service_data.sort(key=lambda x: x['date'])

        # 提取数据
        dates = [datetime.strptime(result['date'], '%Y-%m-%d').date() for result in service_data]
        avg_cpu = [result['avg_cpu_usage'] for result in service_data]
        max_cpu = [result['max_cpu_usage'] for result in service_data]
        min_cpu = [result['min_cpu_usage'] for result in service_data]

        # 创建图表
        plt.figure(figsize=(10, 6))

        # 绘制线条
        plt.plot(dates, avg_cpu, marker='o', linewidth=2, label='平均值', color='#2E86AB')
        plt.plot(dates, max_cpu, marker='^', linewidth=1, label='最大值', color='#A23B72', alpha=0.7)
        plt.plot(dates, min_cpu, marker='v', linewidth=1, label='最小值', color='#F18F01', alpha=0.7)

        # 填充区域
        plt.fill_between(dates, min_cpu, max_cpu, alpha=0.2, color='#2E86AB')

        # 设置标题和标签
        plt.title(f'{service_name} - CPU使用率趋势 (7天)', fontsize=14, fontweight='bold')
        plt.xlabel('日期', fontsize=12)
        plt.ylabel('CPU使用率', fontsize=12)

        # 设置日期格式
        date_format = mdates.DateFormatter('%m-%d')
        plt.gca().xaxis.set_major_formatter(date_format)
        plt.gca().xaxis.set_major_locator(mdates.DayLocator(interval=1))

        # 添加网格
        plt.grid(True, alpha=0.3)

        # 添加图例
        plt.legend()

        # 旋转日期标签
        plt.xticks(rotation=45)
        plt.tight_layout()

        # 转换为base64
        buffer = BytesIO()
        plt.savefig(buffer, format='png', dpi=100, bbox_inches='tight')
        buffer.seek(0)
        img_base64 = b64encode(buffer.read()).decode('utf-8')
        plt.close()

        return img_base64

    def generate_response_time_chart(self, response_time_results, service_name):
        """
        为指定服务生成响应时间趋势图
        """
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

        # 筛选指定服务的数据
        service_data = [result for result in response_time_results if result['service_name'] == service_name]
        if not service_data:
            return None

        # 按接口和日期分组
        interface_data = defaultdict(list)
        for result in service_data:
            interface_data[result['interface_name']].append(result)

        # 如果接口太多，只显示前5个
        interfaces = list(interface_data.keys())[:5]

        if not interfaces:
            return None

        # 创建图表
        plt.figure(figsize=(12, 8))

        colors = ['#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#6A994E']

        for i, interface_name in enumerate(interfaces):
            data = interface_data[interface_name]
            data.sort(key=lambda x: x['date'])

            dates = [datetime.strptime(result['date'], '%Y-%m-%d').date() for result in data]
            avg_times = [result['avg_response_time_ms'] for result in data]

            plt.plot(dates, avg_times, marker='o', linewidth=2,
                    label=interface_name[:30] + '...' if len(interface_name) > 30 else interface_name,
                    color=colors[i % len(colors)])

        # 设置标题和标签
        plt.title(f'{service_name} - 响应时间趋势 (7天)', fontsize=14, fontweight='bold')
        plt.xlabel('日期', fontsize=12)
        plt.ylabel('响应时间 (ms)', fontsize=12)

        # 设置日期格式
        date_format = mdates.DateFormatter('%m-%d')
        plt.gca().xaxis.set_major_formatter(date_format)
        plt.gca().xaxis.set_major_locator(mdates.DayLocator(interval=1))

        # 添加网格
        plt.grid(True, alpha=0.3)

        # 添加图例
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')

        # 旋转日期标签
        plt.xticks(rotation=45)
        plt.tight_layout()

        # 转换为base64
        buffer = BytesIO()
        plt.savefig(buffer, format='png', dpi=100, bbox_inches='tight')
        buffer.seek(0)
        img_base64 = b64encode(buffer.read()).decode('utf-8')
        plt.close()

        return img_base64

    def print_response_time_report(self, results):
        """
        打印核心接口响应时间报表
        """
        if not results:
            print("\n❌ 没有找到任何核心接口响应时间数据")
            return

        print("\n" + "=" * 100)
        print("📊 Prometheus 核心接口响应时间监控报表")
        print("=" * 100)
        print(f"📅 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 100)

        # 计算列宽
        max_service_name_len = max(len(result['service_name']) for result in results)
        max_interface_name_len = max(len(result['interface_name']) for result in results)
        service_col_width = max(20, min(max_service_name_len + 2, 40))
        interface_col_width = max(30, min(max_interface_name_len + 2, 60))

        # 打印表头
        header = f"{'服务名':<{service_col_width}} {'接口':<{interface_col_width}} {'日期':<12} {'平均响应时间(ms)':<16} {'最大响应时间(ms)':<16} {'最小响应时间(ms)':<16} {'数据点':<8}"
        print(header)
        print("-" * len(header))

        # 排序并显示
        sorted_results = sorted(results, key=lambda x: (x['service_name'], x['interface_name'], x['date']))

        current_service = None
        for result in sorted_results:
            if current_service != result['service_name']:
                if current_service is not None:
                    print("-" * len(header))
                current_service = result['service_name']

            # 响应时间状态标识
            if result['avg_response_time_ms'] > 1000:  # 大于1秒
                status_icon = "🔴"
            elif result['avg_response_time_ms'] > 500:  # 大于500ms
                status_icon = "🟡"
            else:
                status_icon = "🟢"

            service_display = f"{status_icon} {result['service_name']}"

            print(f"{service_display:<{service_col_width+2}} {result['interface_name']:<{interface_col_width}} {result['date']:<12} {result['avg_response_time_ms']:<16.2f} {result['max_response_time_ms']:<16.2f} {result['min_response_time_ms']:<16.2f} {result['data_points']:<8}")

        print("=" * len(header))
        print(f"📈 总计: {len(results)} 条记录")
        print(f"🔴 慢响应(>1s)  🟡 中等响应(>500ms)  🟢 快响应(≤500ms)")
        print("=" * 100)

def main():
    parser = argparse.ArgumentParser(description='业务监控报表')
    parser.add_argument('--prometheus-url', required=False,
                       default="https://cn-shanghai.arms.aliyuncs.com:9443/api/v1/prometheus/cc9088bc80fa0109f85996cb49ce9ff/1858502702438156/ced5f20a675da434bab4501993f588fa1/cn-shanghai",
                       help='Prometheus服务器地址 (例如: http://localhost:9090)')
    parser.add_argument('--token', required=False,
                       help='Prometheus服务器token',
                       default="eyJhbGciOiJIUzI1NiJ9.eyJleHAiOjIwNjQ0MDIyNjYsImlzcyI6Imh0dHA6Ly9hbGliYWJhY2xvdWQuY29tIiwiaWF0IjoxNzQ5MDQyMjY2LCJqdGkiOiJlZWI0NDM5NC0wZDk1LTQ3YjktYTIwYi1lYmQyZDJiN2RjZjkifQ.cOBqt8p3ifZM5f5Kf0D687tDv9UpNn1eKnPCjPgTvm4")
    # 添加新的命令行参数
    parser.add_argument('--days', type=int, default=7,
    #       help='查询最近几天的数据 (默认: 7)')
    #       help='报表展示天数 (默认: 3)')
    #       help='监控图标展示天数 (默认: 7)')
                       help='查询最近几天的数据 (默认: 7)')
                       help='报表展示天数 (默认: 3)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)')
                       help='监控图标展示天数 (默认: 7)