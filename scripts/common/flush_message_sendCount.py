"""
刷新formIsDeleted 的数据
"""
import argparse
import os
import sys
from datetime import date, timedelta, datetime

from multizone.config import region_name

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient


class UpdateData:
    chain_id = None
    short_url_db_client = None
    emr_db_client = None
    scform_db_client = None
    basic_db_client = None
    id_work = None
    patient_tag_template_chain_id = None
    type = None
    businessType = None

    def __init__(self, region_name, chain_id, env, begin_date, end_date):
        self.chain_id = chain_id
        self.region_name = region_name
        self.adb_client = DBClient(self.region_name, 'abc_cis_account_base', 'abc_cis_message', env, True)
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_account_base', 'abc_cis_message', env, True)
        self.beginTime = datetime.strptime(begin_date, "%Y-%m-%d").date()
        self.endTime = datetime.strptime(end_date, "%Y-%m-%d").date() + timedelta(days=1)

    def run(self):
        self.updateMessageSendCount()

    def updateMessageSendCount(self):
        try:
            sql = '''
                select DATE_FORMAT(created, '%Y%m%d') as day, sum(message_count) as total
                from v2_message_log
                where chain_id = '{chainId}'
                  and created >= '{beginDate}'
                  and created <= '{endDate}'
                  and message_channel = '{messageChannel}'
                  and is_bill = 0
                group by day;
            '''

            insertSql = '''
            insert into v2_message_send_stat (chain_id, `date`, sms_count, wx_count) values ('{chainId}', '{day}', '{smsCount}', '{wxCount}')
            on duplicate key update sms_count = '{smsCount}', wx_count = '{wxCount}',last_modified = now()
            '''

            # 一个月一个月的刷
            beginDate = self.beginTime
            while beginDate < self.endTime:
                endDate = beginDate + timedelta(days=90)
                if endDate > self.endTime:
                    endDate = self.endTime

                smsMessageSendStatList = self.adb_client.fetchall(
                    sql.format(chainId=self.chain_id, beginDate=beginDate, endDate=endDate, messageChannel=0))
                wxMessageSendStatList = self.adb_client.fetchall(
                    sql.format(chainId=self.chain_id, beginDate=beginDate, endDate=endDate, messageChannel=1))
                smsMsgStatMap = {smsMsgSendStat['day']: smsMsgSendStat['total'] for smsMsgSendStat in
                                 smsMessageSendStatList}
                wxMsgStatMap = {wxMsgSendStat['day']: wxMsgSendStat['total'] for wxMsgSendStat in wxMessageSendStatList}

                currentDate = beginDate
                print(self.chain_id + ' ' + currentDate.strftime("%Y-%m-%d") + ' ' + endDate.strftime("%Y-%m-%d"))
                while currentDate <= endDate:
                    day = currentDate.strftime("%Y%m%d")
                    smsCount = smsMsgStatMap.get(day)
                    if smsCount is None:
                        smsCount = 0
                    wxCount = wxMsgStatMap.get(day)
                    if wxCount is None:
                        wxCount = 0
                    try:
                        if wxCount != 0 or smsCount != 0:
                            self.basic_db_client.execute(
                                insertSql.format(chainId=self.chain_id, day=day, smsCount=smsCount, wxCount=wxCount))
                    except Exception as e:
                        print(self.chain_id + " 处理失败" + day)
                        print(e)

                    currentDate += timedelta(days=1)

                beginDate = endDate

        except Exception as e:
            print(self.chain_id)
            print(e)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--env', help='环境 dev/test/prod')
    parser.add_argument('--region-id', help='region-id ShangHai:1/HangZhou:2')
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--beginDate', help='开始时间 2023-01-01')
    parser.add_argument('--endDate', help='结束时间 2024-01-01')
    args = parser.parse_args()
    if not args.env or not args.region_id or not args.chain_id or not args.clinic_id or not args.beginDate or not args.endDate:
        parser.print_help()
        sys.exit(-1)

    updateData = UpdateData(region_name(args.region_id), args.chain_id, args.env, args.beginDate, args.endDate)
    updateData.run()


if __name__ == '__main__':
    main()
