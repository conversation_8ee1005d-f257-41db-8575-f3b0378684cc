import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.config import region_name
import argparse

from idwork import IdWork

default_id = '00000000000000000000000000000000'

from openpyxl import load_workbook
import re

typeMap = {
    "未知分类": 0,
    "CT": 1,
    "DR": 2,
    "CR": 3,
    "透视": 4,
    "心电图": 5,
    "骨密度": 6,
    "试光类": 7,
    "彩超": 8,
    "超声检查室": 8,
    "MR": 9,
    "胃肠镜": 10,
    "B超": 11,
    "脑电图": 12,
    "其他": 13,
    "一般检查": 14,
    "内科检查": 15,
    "外科检查": 16,
    "耳鼻喉检查": 17,
    "耳鼻咽喉科": 17,
    "口腔检查": 18,
    "口腔科": 18,
    "眼科": 19,
    "检验": 10001,
    "动脉硬化": 21,
    "妇科检查": 22
}


def getDeviceTypeByTypeStr(typeStr):
    if typeStr is None or typeStr == '':
        return None

    deviceType = typeMap.get(typeStr)
    if deviceType:
        return deviceType

    for key, value in typeMap.items():
        if typeStr in key or key in typeStr:
            return value
    return None


def importData(chainId, clinicId, env, region_name):
    exam_client = DBClient(region_name, 'abc_cis_outpatient', 'abc_cis_examination', env, True)
    id_work_exam = IdWork(exam_client, False)
    id_work_exam.config()

    pe_client = DBClient(region_name, 'scrm_hospital', 'abc_pe_order', env, True)
    id_work_pe = IdWork(pe_client, False)
    id_work_pe.config()

    # 读取 Excel 文件
    workbook = load_workbook('./uploads/peTemplateData.xlsx')
    # 获取工作表
    sheet = workbook.worksheets[0]

    # 遍历行和列，获取单元格数据
    for row in sheet.iter_rows(values_only=True):
        typeStr = row[1]
        name = row[2]
        commonReason = row[3]
        explain = row[4]
        advice = row[5]

        if typeStr:
            typeStr = re.sub(r'^[\s\r\n]+|[\s\r\n]+$', '', typeStr)

        if name:
            name = re.sub(r'^[\s\r\n]+|[\s\r\n]+$', '', name)
            name = name.replace("'", "''")

        if commonReason:
            commonReason = re.sub(r'^[\s\r\n]+|[\s\r\n]+$', '', commonReason)
            commonReason = commonReason.replace("'", "''")

        if explain:
            explain = re.sub(r'^[\s\r\n]+|[\s\r\n]+$', '', explain)
            explain = explain.replace("'", "''")

        if advice:
            advice = re.sub(r'^[\s\r\n]+|[\s\r\n]+$', '', advice)
            advice = advice.replace("'", "''")
        deviceType = getDeviceTypeByTypeStr(typeStr)

        # print(typeStr, '-----', deviceType, name, commonReason, explain, advice)

        if deviceType is None:
            print("invalid row", row)
            continue

        type = 2
        if deviceType == 10001:
            type = 1
            deviceType = 0

        # 导入总评建议
        reportAdviceTemplateSql = '''insert into v1_pe_report_advice_template (id, clinic_id, chain_id, device_type, title, common_reason, medical_explain, doctor_advice, type, created_by, last_modified_by) 
        values ('{id}', '{clinicId}', '{chainId}', '{deviceType}', '{title}', '{commonReason}', '{medicalExplain}', '{doctorAdvice}', '{type}', '00000000000000000000000000000000', '00000000000000000000000000000000');'''

        # 导入诊断意见词条
        diagnosisEntrySql = '''insert into v2_examination_diagnosis_entry (id, chain_id, clinic_id, type, device_type, name, created_by, last_modified_by, abnormal_flag) 
        values ('{id}', '{chainId}', '{clinicId}', '{type}', '{deviceType}', '{name}','00000000000000000000000000000000','00000000000000000000000000000000', 10);'''

        relateSql = '''insert into v2_examination_diagnosis_entry_relation (id, chain_id, clinic_id,diagnosis_entry_id, relation_id,relation_type, created_by, last_modified_by) 
        VALUES (substr(uuid_short(), 4), '{chainId}', '{clinicId}', '{diagnosisEntryId}', '{adviceTemplateId}', 10, '00000000000000000000000000000000', '00000000000000000000000000000000');
        '''

        adviceTemplateId = id_work_pe.getUIDLong()
        diagnosisEntryId = id_work_exam.getUIDLong()

        # 将内容写入文件
        pe_client.execute(
            reportAdviceTemplateSql.format(id=adviceTemplateId, clinicId=clinicId, chainId=chainId,
                                           deviceType=deviceType,
                                           title=name,
                                           commonReason=commonReason, medicalExplain=explain, doctorAdvice=advice,
                                           type=type))

        # 将内容写入文件
        exam_client.execute(
            diagnosisEntrySql.format(id=diagnosisEntryId, chainId=chainId, clinicId=clinicId, type=2,
                                     deviceType=deviceType,
                                     name=name))

        exam_client.execute(
            relateSql.format(chainId=chainId, clinicId=clinicId, diagnosisEntryId=diagnosisEntryId,
                             adviceTemplateId=adviceTemplateId))


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--env', help='环境 dev/test/prod')
    parser.add_argument('--region-id', help='region-id ShangHai:1/HangZhou:2')
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--clinic-id', help='诊所id')
    args = parser.parse_args()
    if not args.env or not args.region_id or not args.chain_id or not args.clinic_id:
        parser.print_help()
        sys.exit(-1)

    importData(args.chain_id, args.clinic_id, args.env, region_name(args.region_id))

    # importData('ffffffff0000000034a5082756030000', 'ffffffff0000000034a5082756030002', 'dev', 'ShangHai')

if __name__ == '__main__':
    main()
