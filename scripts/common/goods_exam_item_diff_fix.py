#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.config import region_name
import json
import argparse
import logging

logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)

default_id = '00000000000000000000000000000000'
standard_chain_id_map = {
    'dev': {
        'template_chain_id': 'ffffffff000000000c5a1308069aa000'
    },
    'test': {
        'template_chain_id': 'ffffffff000000001cdb3b8007024000'
    },
    'prod': {
        'template_chain_id': 'ffffffff000000001f2d1f400a1e6000'
    }
}

rpc_config = {
    'local': {
        'host': 'dev.abczs.cn',
        'headers': {
            'abc-rpc': 'we-will-win'
        }
    },
    'dev': {
        'host': 'dev.rpc.abczs.cn',
    },
    'test': {
        'host': 'test.rpc.abczs.cn',
    },
    'prod': {
        'host': 'pre.rpc.abczs.cn',
    }
}

region_id_name_map = {
    '1': "ShangHai",
    '2': 'HangZhou'
}

f = open('./abc_cis_examination_v2_examination_item.sql', "w+")


def run(region_name, chain_id, goods_sub_type, env):
    goods_dbcli = DBClient(region_name, 'ods', 'abc_cis_goods', env, True)
    exam_dbcli = DBClient(region_name, 'ods', 'abc_cis_examination', env, True)
    goods_list = goods_dbcli.fetchall(
        f"select * from v2_goods where organ_id = '{chain_id}' and type in(3, 27) and sub_type = {goods_sub_type} and status = 1 and combine_type = 0;")
    if len(goods_list) == 0:
        print('exam_goods is empty')
        return
    goods_ids = [goods['id'] for goods in goods_list]
    biz_relevant_ids = [goods['biz_relevant_id'] for goods in goods_list]
    standard_goods_list = []
    if len(biz_relevant_ids) > 0:
        standard_goods_list.extend(
            goods_dbcli.fetchall(
                f"""select * from v2_goods where organ_id = '{standard_chain_id_map[env]['template_chain_id']}' and type in (3, 27) and sub_type = {goods_sub_type} and status = 1 and combine_type = 0 and biz_relevant_id in ({', '.join([f"'{biz_relevant_id}'" for biz_relevant_id in biz_relevant_ids])});""")
        )
    standard_goods_ids = [standard_goods['id'] for standard_goods in standard_goods_list]
    biz_relevant_id_name_map = {}
    for standard_goods in standard_goods_list:
        biz_relevant_id_name_map[f"{standard_goods['biz_relevant_id']}_{standard_goods['name']}"] = standard_goods

    standard_exam_list = []
    if len(standard_goods_ids) > 0:
        standard_exam_list.extend(
            exam_dbcli.fetchall(
                f"""select * from v2_examination_item where chain_id = '{standard_chain_id_map[env]['template_chain_id']}' and is_deleted = 0 and goods_id in ({', '.join([f"'{goods_id}'" for goods_id in standard_goods_ids])});""")
        )

    standard_goods_id_exam_list = {}
    for exam in standard_exam_list:
        if exam['goods_id'] not in standard_goods_id_exam_list:
            standard_goods_id_exam_list[exam['goods_id']] = []
        standard_goods_id_exam_list[exam['goods_id']].append(exam)

    standard_exam_item_ids = [standard_exam['id'] for standard_exam in standard_exam_list]
    standard_exam_ref_list = []
    if len(standard_exam_item_ids) > 0:
        standard_exam_ref_list.extend(
            exam_dbcli.fetchall(
                f"""select * from v2_examination_item_ref_detail where item_id in ({', '.join([f"'{exam_item_id}'" for exam_item_id in standard_exam_item_ids])}) and is_deleted = 0;""")
        )
    standard_exam_item_id_ref_list_map = {}
    for standard_exam_ref in standard_exam_ref_list:
        if standard_exam_ref['item_id'] not in standard_exam_item_id_ref_list_map:
            standard_exam_item_id_ref_list_map[standard_exam_ref['item_id']] = []
        standard_exam_item_id_ref_list_map[standard_exam_ref['item_id']].append(standard_exam_ref)

    exam_list = []
    if len(goods_ids) > 0:
        exam_list.extend(
            exam_dbcli.fetchall(
                f"""select * from v2_examination_item where chain_id = '{chain_id}' and is_deleted = 0 and goods_id in ({', '.join([f"'{goods_id}'" for goods_id in goods_ids])});""")
        )
    goods_id_exam_list = {}
    for exam in exam_list:
        if exam['goods_id'] not in goods_id_exam_list:
            goods_id_exam_list[exam['goods_id']] = []
        goods_id_exam_list[exam['goods_id']].append(exam)

    need_supplement_goods_ids = []
    for goods in goods_list:
        exam_items = goods_id_exam_list.get(goods['id'])
        if exam_items is not None and len(exam_items) > 0:
            print(f"无需处理examination_item goods_id = {goods['id']}, exam_item_id = {[exam_item['id'] for exam_item in exam_items]}")
            exam_item_ids = [exam_item['id'] for exam_item in exam_items]
            if len(exam_item_ids) > 1:
                for item_id in exam_item_ids:
                    if item_id.__contains__('fffff'):
                        continue
                    logging.info(f"item_id = {item_id}")
                    # f.write(f"'{item_id}', ")
                # f.write(os.linesep)
            continue
        standard_g = biz_relevant_id_name_map.get(f"{goods['biz_relevant_id']}_{goods['name']}")
        standard_exam_items = None
        if standard_g is not None:
            standard_exam_items = standard_goods_id_exam_list.get(standard_g['id'])

        item_id = exam_dbcli.fetchone("select replace(uuid(), '-', '') as item_id")['item_id']
        if standard_g is None or standard_exam_items is None or len(standard_exam_items) == 0:
            insert_exam_sql = f"""INSERT INTO v2_examination_item (id, clinic_id, chain_id, type, name, en_name, search_text, unit,
                                                     ref, result_display_scale, is_deleted, created_user_id,
                                                     created_date, last_modified_user_id, last_modified_date, item_code,
                                                     goods_id, source_id, inspect_type, component_type, options,
                                                     constraints, group_id, value_quantity, display_name, is_standard,
                                                     tag, sort, additional, item_type, goods_type)
VALUES ('{item_id}', '{chain_id}', '{chain_id}', 1,
        '{goods['name']}', {f"'{goods['en_name']}'" if goods['en_name'] is not None else 'null'}, '{f"{goods['name']}_{goods['py'].split('|')[0].lower()}_{goods['en_name'].lower() if goods['en_name'] is not None else ''}"}', '', '{{"min":"","max":""}}', 2, 0,
        '{default_id}', now(), '{default_id}',
        now(), null, '{goods['id']}', null, null, null,
        null, null, null, 1, null, 0, null, 0, null, {goods_sub_type}, 3);"""

            insert_exam_ref_sql = f"""INSERT INTO v2_examination_item_ref_detail (id, item_id, sex, start_age, end_age, age_unit, ref,
                                                                is_deleted, created, created_by, last_modified,
                                                                last_modified_by, sample_type)
VALUES (substr(uuid_short(), 4), '{item_id}', null, null, null, null, '{{"min":"","max":""}}', 0,
        now(), '{default_id}', now(),
        '{default_id}', null);"""

            logging.info(f"insert_exam_sql = {insert_exam_sql}")
            logging.info(f"insert_exam_ref_sql = {insert_exam_ref_sql}")
            # f.write(insert_exam_sql + os.linesep)
            # f.write(insert_exam_ref_sql + os.linesep)

        else:
            exam_item = standard_exam_items[0]
            exam_item_ref_list = standard_exam_item_id_ref_list_map.get(exam_item['id'])
            insert_exam_sql = f"""INSERT INTO v2_examination_item (id, clinic_id, chain_id, type, name, en_name, search_text, unit,
                                                                 ref, result_display_scale, is_deleted, created_user_id,
                                                                 created_date, last_modified_user_id, last_modified_date, item_code,
                                                                 goods_id, source_id, inspect_type, component_type, options,
                                                                 constraints, group_id, value_quantity, display_name, is_standard,
                                                                 tag, sort, additional, item_type, goods_type)
            VALUES ('{item_id}', '{chain_id}', '{chain_id}', {exam_item['type']},
                    '{exam_item['name']}', {f"'{exam_item['en_name']}'" if exam_item['en_name'] is not None else 'null'}, '{exam_item['search_text']}', '{exam_item['unit']}', '{exam_item['ref']}', {exam_item['result_display_scale']}, 0,
                    '{default_id}', now(), '{default_id}',
                    now(), null, '{goods['id']}', '{exam_item['id']}', null, null,
                    null, null, null, 1, null, 0, null, 0, null, {goods_sub_type}, 3);"""

            logging.info(f"insert_exam_sql = {insert_exam_sql}")
            # f.write(insert_exam_sql + os.linesep)

            if exam_item_ref_list is None or len(exam_item_ref_list) == 0:
                insert_exam_ref_sql = f"""INSERT INTO v2_examination_item_ref_detail (id, item_id, sex, start_age, end_age, age_unit, ref,
                                                                                is_deleted, created, created_by, last_modified,
                                                                                last_modified_by, sample_type)
                VALUES (substr(uuid_short(), 4), '{item_id}', null, null, null, null, '{{"min":"","max":""}}', 0,
                        now(), '{default_id}', now(),
                        '{default_id}', null);"""

                logging.info(f"insert_exam_ref_sql = {insert_exam_ref_sql}")
                # f.write(insert_exam_ref_sql + os.linesep)
            else:
                for exam_item_ref in exam_item_ref_list:
                    insert_exam_ref_sql = f"""INSERT INTO v2_examination_item_ref_detail (id, item_id, sex, start_age, end_age, age_unit, ref,
                                                                                            is_deleted, created, created_by, last_modified,
                                                                                            last_modified_by, sample_type)
                            VALUES (substr(uuid_short(), 4), '{item_id}', {f"'{exam_item_ref['sex']}'" if exam_item_ref['sex'] is not None else 'null'}, {f"'{exam_item_ref['start_age']}'" if exam_item_ref['start_age'] is not None else 'null'}, {f"'{exam_item_ref['end_age']}'" if exam_item_ref['end_age'] is not None else 'null'}, {f"'{exam_item_ref['age_unit']}'" if exam_item_ref['age_unit'] is not None else 'null'}, {f"'{exam_item_ref['ref']}'" if exam_item_ref['ref'] is not None else 'null'}, 0,
                                    now(), '{default_id}', now(),
                                    '{default_id}', {f"'{exam_item_ref['sample_type']}'" if exam_item_ref['sample_type'] is not None else 'null'});"""
                    logging.info(f"insert_exam_ref_sql = {insert_exam_ref_sql}")
                    # f.write(insert_exam_ref_sql + os.linesep)

        need_supplement_goods_ids.append(goods['id'])
    logging.info(f"need_supplement_goods_ids: {need_supplement_goods_ids}")



def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--region-id', help='分区id', required=False)
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--goods-sub-type', type=int, help='连锁id')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.region_id or not args.chain_id or not args.goods_sub_type or not args.env:
        parser.print_help()
        sys.exit(-1)

    run(region_name(args.region_id), args.chain_id, args.goods_sub_type, args.env)


if __name__ == '__main__':
    main()
