#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.rpc import regionRpcHost

import argparse
import requests
import logging

logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)


def run(abcRegion, chain_id):
    url = '''http://{rpcHost}/rpc/mc/weapp-code/commit-submit-audit'''.format(rpcHost=regionRpcHost(abcRegion))
    rsp = requests.post(url, json={
        'chainIds': [chain_id]
    })

    logging.info('commit submit weapp audit:{0}, rsp:{1}'.format(chain_id, rsp.content))


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.region_name, args.chain_id)


if __name__ == '__main__':
    main()
