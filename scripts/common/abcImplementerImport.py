import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.config import region_name
import argparse

from idwork import IdWork

default_id = '00000000000000000000000000000000'

from openpyxl import load_workbook
import re

from datetime import datetime



def importData(env):
    oa_client = DBClient("ShangHai", 'internal_system', 'abc_oa', env, True)
    id_work_schedule = IdWork(oa_client, False)
    id_work_schedule.config()

    # 读取 Excel 文件
    workbook = load_workbook('./uploads/scheduleTemplateData.xlsx')
    # 获取工作表
    sheet = workbook.worksheets[0]

    # 查企微员工
    sql = '''select id, name, mobile
             from corp_user
             where status = 1; '''
    userList = oa_client.fetchall(sql)

    # 查询今天及今日之后的人员排班
    sql = '''select user_id, schedule_date
                from support_schedule
                where schedule_date >= curdate()
                group by user_id, schedule_date; '''
    scheduleList = oa_client.fetchall(sql)
    # 按照 user_id 和 schedule_date 生成map, key 为 user_id + schedule_date, value 为 1
    scheduleMap = {}
    for schedule in scheduleList:
        scheduleMap[str(schedule['user_id']) + str(schedule['schedule_date'])] = 1

    # userList 按照 mobile 生成map, key 为 mobile, value 为 id
    userMap = {}
    for user in userList:
        userMap[user['mobile']] = user['id']

    # 遍历行和列，获取单元格数据
    i = 0
    for row in sheet.iter_rows(values_only=True):
        # 忽略表头，忽略第一行
        if i == 0:
            i += 1
            continue

        print(row)

        userMobile = row[1]
        scheduleDate = row[2]
        workBeginTime = row[3]
        workEndTime = row[4]

        if userMobile is None:
            print("invalid row", row)
            continue

        # 当日时间
        today = datetime.now().date()

        # 如果scheduleDate小于今天，则不再插入
        if scheduleDate.date() < today:
            print("history date", row)
            continue

        workBeginTimeList = []
        workEndTimeList = []

        if workBeginTime:
            workBeginTime = re.sub(r'^[\s\r\n]+|[\s\r\n]+$', '', workBeginTime)
            workBeginTime = workBeginTime.replace("'", "''")
            workBeginTimeList.append(workBeginTime)
        else:
            workBeginTimeList.append(scheduleDate.strftime('%Y-%m-%d') + ' 09:00:00')
            workBeginTimeList.append(scheduleDate.strftime('%Y-%m-%d') + ' 13:00:00')


        if workEndTime:
            workEndTime = re.sub(r'^[\s\r\n]+|[\s\r\n]+$', '', workEndTime)
            workEndTime = workEndTime.replace("'", "''")
            workEndTimeList.append(workEndTime)
        else:
            workEndTimeList.append(scheduleDate.strftime('%Y-%m-%d') + ' 12:00:00')
            workEndTimeList.append(scheduleDate.strftime('%Y-%m-%d') + ' 18:00:00')

        # print(typeStr, '-----', deviceType, name, commonReason, explain, advice)

        userId = userMap.get(userMobile)
        if userId is None:
            print("invalid row", row)
            continue

        # 如果人员排班已经存在，则不再插入
        if scheduleMap.get(str(userId) + str(scheduleDate.date())):
            print("schedule already exist", row)
            continue

        # fori遍历workBeginTimeList
        for i in range(len(workBeginTimeList)):
            workBeginTime = workBeginTimeList[i]
            workEndTime = workEndTimeList[i]
            scheduleTemplateSql = '''insert into support_schedule (id, user_id, schedule_date, work_begin_time, work_end_time)
            values ('{id}', '{userId}', '{scheduleDate}', '{workBeginTime}', '{workEndTime}');'''

            scheduleId = id_work_schedule.getUIDLong()

            # 将内容写入文件
            oa_client.execute(
                scheduleTemplateSql.format(id=scheduleId,
                                           userId=userId,
                                           scheduleDate=scheduleDate,
                                           workBeginTime=workBeginTime,
                                           workEndTime=workEndTime)
            )


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.env:
        parser.print_help()
        sys.exit(-1)

    importData(args.env)

    # importData('ffffffff0000000034a5082756030000', 'ffffffff0000000034a5082756030002', 'dev', 'ShangHai')


if __name__ == '__main__':
    main()
