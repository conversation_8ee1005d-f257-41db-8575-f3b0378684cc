#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import logging
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
import json
import datetime

# from idwork import IdWork
#
# db_client = db.DBClient('abc_cis_outpatient', 'abc_cis_examination')
# id_work = IdWork(db_client, False)
# id_work.config()

# {
#     "query": {
#         "term": {
#             "patientId": ""
#         }
#     },
#     "script": {
#         "source": "ctx._source.patientSn = 'new_value'"
#     }
# }

default_id = '00000000000000000000000000000000'


def update_json(region_name, chain_id, cluster, db_name, table_name, where_sql, json_columns_prop, env):
    # try:
    ob_client = DBClient(region_name, 'ob', db_name, env, True)
    db_client = DBClient(region_name, cluster, db_name, env, True)
    date_stat = ob_client.fetchone(
        """select /*+ max_execution_time(600000)*/ date(min(created)) as startDate, date(max(created)) as endDate from {0} where chain_id = '{1}';""".format(table_name, chain_id))
    start_date = date_stat['startDate']
    end_date = date_stat['endDate']
    print(f'{start_date}~{end_date}')
    if start_date is None:
        print('start_date is None')
        return
    # today = datetime.date.today()
    # 计算时间间隔
    days = (end_date - start_date).days
    gap = 30
    # f = open(f'{table_name}_json_remove.sql', 'w')
    for i in range(0, days, gap):
        sql = f"""select /*+ max_execution_time(600000)*/ id, {', '.join(json_columns_prop.keys())} from {table_name}
             where {where_sql if where_sql else ''}
             {'and' if where_sql else ''} created >= '{start_date + datetime.timedelta(days=i + (0 if i == 0 else 1))} 00:00:00' 
             and created <= '{start_date + datetime.timedelta(days=i + gap)} 23:59:59'
             and chain_id = '{chain_id}';"""
        print(sql)
        rows = ob_client.fetchall(sql)
        if len(rows) == 0:
            print('rows is empty')
            continue
        # rows拆分 一组2000个
        rows2000 = [rows[i:i + 2000] for i in range(0, len(rows), 2000)]
        for rows2 in rows2000:
            json_update_sql_to_ids = {}
            for row in rows2:
                json_update_sql = []
                for json_column in json_columns_prop:
                    json_value_str = row[json_column]
                    if json_value_str is None:
                        continue
                    json_value = json.loads(json_value_str)
                    json_remove_columns = []
                    json_array_remove_columns = []
                    for key in json_value.keys():
                        if json_value[key] is None or json_value[key] == '':
                            json_remove_columns.append(key)
                            continue
                        if json_columns_prop[json_column] is not None and json_columns_prop[json_column].__contains__(key) and (json_value[key] == 0 or json_value[key] == False):
                            # logging.debug(f'{json_column}.{key}={json_value[key]}')
                            json_remove_columns.append(key)
                            continue
                        if isinstance(json_value[key], list) or isinstance(json_value[key], set):
                            if len(json_value[key]) == 0:
                                json_remove_columns.append(key)
                                continue
                            length = 0
                            for index, item in enumerate(json_value[key]):
                                if item is None:
                                    json_array_remove_columns.append(f'{key}[{index}]')
                                    length += 1
                            if length == len(json_value[key]):
                                json_remove_columns.append(key)
                            continue
                    if len(json_remove_columns) == 0:
                        continue
                    if len(json_remove_columns) == len(json_value.keys()):
                        json_update_sql.append(f"""{json_column} = NULL""")
                    else:
                        if len(json_array_remove_columns) > 0:
                            json_remove_columns.extend(json_array_remove_columns)
                        json_update_sql.append(
                            f"""{json_column} = JSON_REMOVE({json_column}, {', '.join([f"'$.{key}'" for key in json_remove_columns])})""")
                if len(json_update_sql) == 0:
                    continue
                json_update_sql_joined = ', '.join(json_update_sql)
                if json_update_sql_joined not in json_update_sql_to_ids:
                    json_update_sql_to_ids[json_update_sql_joined] = [row['id']]
                json_update_sql_to_ids[json_update_sql_joined].append(row['id'])
            # 批量更新
            if len(json_update_sql_to_ids) == 0:
                print('req_body is empty')
                continue
            # f.write(os.linesep.join(values))
            values = []
            for json_update_sql_j, ids in json_update_sql_to_ids.items():
                values.append(f"""update {table_name} set {json_update_sql_j} where id in ({f', '.join([f"'{id}'" for id in ids])});""")
            if len(values) == 0:
                continue
            db_client.executemany(values)