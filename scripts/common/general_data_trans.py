import os
import sys
import time
from typing import List

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.db import DBClient
from utils.sqls import SqlUtils as sql_utils
from datetime import date, datetime, timedelta
import argparse
import logging
import requests


class TransDateNested:
    def __init__(self, region_name, cluster, db_name, table_name, table_name_bak, column_in_mappings, env, where):
        self.region_name = region_name
        self.cluster = cluster
        self.db_name = db_name
        self.table_name = table_name
        self.table_name_bak = table_name_bak
        self.column_in_mappings = column_in_mappings
        self.env = env
        self.where = where


def trans_data(region_name, cluster, db_name, table_name, table_name_bak, max_column_sql, env, where,
               nested: List[TransDateNested] = None, limit=10000):
    """
    :param limit:
    :param region_name: 分区名称
    :param cluster: 集群名称
    :param db_name: 数据库名称
    :param table_name: 主表名称
    :param table_name_bak: 主表备份名称
    :param sql sql语句
    :param env: env
    :param date_column: 时间列
    :param nested: List[TransDateNested]
    :return:
    """
    ob_client = DBClient(region_name, 'ob', db_name, env, True)
    db_client = DBClient(region_name, cluster, db_name, env, True)
    index = 0
    url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=be9dbb00-0d2e-4cba-b16f-fe2ceb849c46'
    requests.post(
        url=url,
        json={
            "msgtype": "markdown",
            "markdown": {
                "content": f"""「{region_name}」开始执行「迁移/清理」任务，[<font color=\"warning\">{table_name}</font>] -> [<font color=\"warning\">{table_name_bak}</font>], rows: [<font color=\"red\">1000 * {limit}</font>]，请关注数据同步延迟情况"""
            }
        }
    )
    column_value_pair = ob_client.fetchone(max_column_sql)
    if not column_value_pair or len(column_value_pair) != 1:
        logging.warning(f'column_value_pair is empty, return')
        return
    column = list(column_value_pair.keys())[0]
    value = column_value_pair[column]
    start_time = datetime.now()
    while True:
        if index > 1000:
            logging.warning(f'index > 1000, break')
            break
        query_sql = f"select * from {table_name} where {column} <= '{value}' {f'and {where}' if where else ''} limit {limit};"
        rows = ob_client.fetchall(query_sql)
        logging.info(f"query_sql: {query_sql}")
        if len(rows) <= 0:
            logging.warning(f'rows is empty, break')
            break
        if nested is not None and len(nested) > 0:
            for n in nested:
                # rows 拆分1组1000条
                rows_1000 = [rows[i:i + 1000] for i in range(0, len(rows), 1000)]
                for rows_list in rows_1000:
                    column_in = [row[n.column_in_mappings['key']] for row in rows_list]
                    nested_rows = ob_client.fetchall(f'''
                        select * from {n.table_name} where {n.column_in_mappings['value']} in ({', '.join([f"""'{c}'""" for c in column_in])}) {f'and {n.where}' if n.where else ''};
                    ''')
                    if not do_tarns_data(db_client, nested_rows, n.table_name, n.table_name_bak):
                        logging.warning(f'do_tarns_data affected_rows is 0, break')
                        break

        if not do_tarns_data(db_client, rows, table_name, table_name_bak):
            logging.warning(f'do_tarns_data affected_rows is 0, break')
            break
        index += 1
    end_time = datetime.now()
    requests.post(
        url=url,
        json={
            "msgtype": "markdown",
            "markdown": {
                "content": f"""「{region_name}」「迁移/清理」任务执行结束，[<font color=\"warning\">{table_name}</font>] -> [<font color=\"warning\">{table_name_bak}</font>], rows: [<font color=\"red\">{index} * {limit}</font>]，耗时: [<font color=\"red\">{end_time - start_time}</font>]"""
            }
        }
    )


def do_tarns_data(db_client, rows, table_name, table_name_bak):
    if table_name_bak is not None:
        sql_utils.trans_data(rows=rows, client_source=db_client, client_target=db_client,
                             table_name_source=table_name, table_name_target=table_name_bak,
                             custom_update_dict=None)
    # rows 拆分1组1000条
    rows_1000 = [rows[i:i + 1000] for i in range(0, len(rows), 1000)]
    for rows_list in rows_1000:
        affected_rows = db_client.execute(
            f'''delete from {table_name} where id in ({','.join([f"""'{row['id']}'""" for row in rows_list])});''')
        if affected_rows == 0:
            return False
    time.sleep(2)
    return True


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--region-name', help='分区名字')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    region_name = args.region_name
    env = args.env
    if not env:
        parser.print_help()
        sys.exit(-1)

    # region_name = 'ShangHai'
    # env = 'dev'

    if region_name == 'ShangHai':
        # 迁移token
        trans_data(
            region_name=region_name,
            cluster='abc_cis_mixed',
            db_name='abc_cis_basic',
            table_name='v2_clinic_login_token',
            table_name_bak='v2_clinic_login_token_bak',
            max_column_sql=f"select max(id) as id from v2_clinic_login_token where created <= '{date.today() - timedelta(days=90)} 00:00:00';",
            where='(is_deleted = 1 or is_valid = 0 or expired <= now())',
            env=env
        )

        # 迁移oa device登录记录
        trans_data(
            region_name=region_name,
            cluster='internal_system',
            db_name='abc_oa',
            table_name='client_device_login',
            table_name_bak='client_device_login_1112',
            max_column_sql=f"select max(id) as id from client_device_login where created <= '{date.today() - timedelta(days=7)} 00:00:00';",
            where='(is_deleted = 1)',
            env=env
        )

        # 迁移login_log
        trans_data(
            region_name=region_name,
            cluster='abc_cis_mixed',
            db_name='abc_cis_basic',
            table_name='login_log',
            table_name_bak='login_log_bak',
            max_column_sql=f"select '{date.today() - timedelta(days=180)} 00:00:00' as created;",
            where=None,
            env=env
        )

    # 迁移outpatient_draft
    trans_data(
        region_name=region_name,
        cluster='abc_cis_outpatient',
        db_name='abc_cis_outpatient',
        table_name='v2_outpatient_sheet_draft',
        table_name_bak='v2_outpatient_sheet_draft_bak',
        max_column_sql=f"select max(id) as id from v2_outpatient_sheet_draft where created <= '{date.today() - timedelta(days=60)} 00:00:00';",
        where='(is_deleted = 1)',
        env=env
    )

    # 迁移goods_locking
    trans_data(
        region_name=region_name,
        cluster='abc_cis_stock',
        db_name='abc_cis_goods',
        table_name='v2_goods_stock_locking',
        table_name_bak=None,
        max_column_sql=f"select max(id) as id from v2_goods_stock_locking where created <= '{date.today() - timedelta(days=90)} 00:00:00';",
        where='(status >= 20)',
        env=env,
        nested=[
            TransDateNested(
                region_name=region_name,
                cluster='abc_cis_stock',
                db_name='abc_cis_goods',
                table_name='v2_goods_stock_locking_log',
                table_name_bak=None,
                column_in_mappings={'key': 'lock_id', 'value': 'lock_id'},
                where=None,
                env=env
            ),
            TransDateNested(
                region_name=region_name,
                cluster='abc_cis_stock',
                db_name='abc_cis_goods',
                table_name='v2_goods_locking_relation',
                table_name_bak=None,
                column_in_mappings={'key': 'lock_id', 'value': 'lock_id'},
                where=None,
                env=env
            )
        ]
    )

    # 迁移shebao_national_signin_info
    trans_data(
        region_name=region_name,
        cluster='abc_cis_bill',
        db_name='abc_cis_shebao',
        table_name='shebao_national_signin_info',
        table_name_bak=None,
        max_column_sql=f"select max(id) as id from shebao_national_signin_info where created <= '{date.today() - timedelta(days=10)} 00:00:00';",
        where=None,
        env=env
    )
