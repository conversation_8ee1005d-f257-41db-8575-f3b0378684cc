import logging
import os

import requests

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.config import region_name
from multizone.rpc import regionRpcHost
import argparse
import json
from openpyxl import load_workbook
from utils.excel_helper import ExcelHelper as excel_helper
import re

default_id = '00000000000000000000000000000000'

type_name_map = {
    "CT": 7,
    "DR": 8,
    "CR": 9,
    "透视": 10,
    "心电图": 11,
    "骨密度": 12,
    "试光类": 13,
    "彩超": 14,
    "MR": 15,
    "胃肠镜": 16,
    "内窥镜": 16,
    "B超": 17,
    "脑电图": 18,
    "其他": 19,
}


def importInspectionReportTemplate(region_name, chain_id, clinic_id, env):
    exam_client = DBClient(region_name, 'abc_cis_outpatient', 'abc_cis_examination', env, True)
    short_utl_client = DBClient(region_name, 'abc_cis_mixed', 'abc_cis_shorturl' if env == 'prod' else 'abc_cis_basic', env, True)

    template_contents = exam_client.fetchall(f"""select * from v2_examination_report_template_content where chain_id = '{chain_id}' and clinic_id = '{clinic_id}' and is_deleted = 0""")
    key_to_template_content = {template_content['template_id']: template_content for template_content in template_contents}
    catalogues = short_utl_client.fetchall(f"""select * from v2_short_url_catalogue where chain_id = '{chain_id}' and clinic_id = '{clinic_id}' and is_deleted = 0""")
    key_to_catalogue = {f"{catalog['parent_id'] if catalog['parent_id'] is not None and catalog['parent_id'] != '' else 'none'}_{catalog['owner_id']}_{catalog['owner_type']}_{catalog['is_folder']}_{catalog['type']}_{catalog['category']}_{catalog['name']}": catalog for catalog in catalogues}

    logging.info('--------------------start parse excel--------------------')
    workbook = load_workbook('./uploads/uploadedFile.xlsx')
    # workbook = load_workbook('/Users/<USER>/PycharmProjects/pythonProjectFor2.7/parse/DX.xlsx')
    # 获取工作表
    sheet = workbook.worksheets[0]
    list = excel_helper.sheet_to_json(sheet, {
        '类型': 'type',
        '目录名称': 'folderName',
        '项目名称': 'fileName',
        '影像描述': 'videoDescription',
        '影像诊断': 'advice'
    })
    logging.info(f'--------------------parse excel {len(list)} row--------------------')
    type_folder_file_map = {}
    for item in list:
        type = type_name_map[item['type']]
        folder_name = item['folderName']
        folder_file_map = type_folder_file_map.get(type)
        if folder_file_map is None:
            folder_file_map = {}
            type_folder_file_map[type] = folder_file_map

        if folder_name not in folder_file_map:
            folder_file_map[folder_name] = []
        folder_file_map.get(folder_name).append({
            "fileName": item['fileName'],
            "videoDescription": item['videoDescription'],
            "advice": item['advice']
        })

    file_rows = 0
    insert_folder_rows = 0
    insert_file_rows = 0
    insert_file_content_rows = 0
    for type in type_folder_file_map.keys():
        folder_file_map = type_folder_file_map.get(type)
        for folder_name in folder_file_map.keys():
            try:
                # 创建目录
                folder = key_to_catalogue.get(f"none_{clinic_id}_{1}_{1}_{type}_{1}_{folder_name}")
                if folder is None:
                    folder = json.loads(requests.post(url=f'http://{regionRpcHost(region_name, env)}/rpc/catalogue', json={
                        "ownerId": clinic_id,
                        "parentId": None,
                        "isFolder": 1,
                        "name": folder_name,
                        "category": 1,
                        "type": type,
                        "ownerType": 1,
                        "chainId": chain_id,
                        "clinicId": clinic_id,
                        "operatorId": default_id
                    }).content.decode('utf-8'))['data']
                    key_to_catalogue[f"none_{clinic_id}_{1}_{1}_{type}_{1}_{folder_name}"] = folder
                    insert_folder_rows = insert_folder_rows + 1
                    logging.info(f'folder = {folder}')
                else:
                    logging.warning(f'folder: {folder["name"]} exists')
                rows = folder_file_map.get(folder_name)
                file_rows = file_rows + len(rows)
                for row in rows:
                    try:
                        # 创建文件
                        file = key_to_catalogue.get(f"{folder['id']}_{clinic_id}_{1}_{0}_{type}_{1}_{row['fileName']}")
                        if file is None:
                            file = json.loads(requests.post(url=f'http://{regionRpcHost(region_name, env)}/rpc/catalogue', json={
                                "ownerId": clinic_id,
                                "parentId": folder['id'],
                                "isFolder": 0,
                                "name": row['fileName'],
                                "category": 1,
                                "type": type,
                                "ownerType": 1,
                                "chainId": chain_id,
                                "clinicId": clinic_id,
                                "operatorId": default_id,
                                "file": {
                                    "videoDescription": row['videoDescription'],
                                    "advice": row['advice']
                                }
                            }).content.decode('utf-8'))['data']
                            key_to_catalogue[f"{folder['id']}_{clinic_id}_{1}_{0}_{type}_{1}_{row['fileName']}"] = file
                            key_to_template_content[str(file['id'])] = file['file']
                            insert_file_rows = insert_file_rows + 1
                            insert_file_content_rows = insert_file_content_rows + 1
                            logging.info(f'file = {file}')
                        else:
                            logging.warning(f'file: {file["name"]} exists')
                            # 查文件内容
                            template_content = key_to_template_content.get(str(file['id']))
                            if template_content is None:
                                # 创建模版内容
                                create_template_content_rsp = json.loads(requests.post(
                                    url=f'http://{regionRpcHost(region_name, env)}/rpc/examinations/report-template-content',
                                    json={
                                        'chainId': chain_id,
                                        'clinicId': clinic_id,
                                        'operatorId': default_id,
                                        'type': file['type'],
                                        'id': file['id'],
                                        'method': 'POST',
                                        'file': {
                                            "videoDescription": row['videoDescription'],
                                            "advice": row['advice']
                                        }
                                    }).content.decode('utf-8'))
                                key_to_template_content[str(file['id'])] = create_template_content_rsp
                                insert_file_content_rows = insert_file_content_rows + 1
                                logging.info(f'create_template_content_rsp = {create_template_content_rsp}')
                            else:
                                logging.warning(f'file_content exists templateId: {file["id"]}')
                    except Exception as e:
                        logging.error(e)
            except Exception as e:
                logging.error(e)
        logging.info(f'--------------------parse excel folder: {len(folder_file_map)} row, insert: {insert_folder_rows} row; file: {file_rows} row, insert: {insert_file_rows} row; insert_file_content_rows: {insert_file_content_rows} row--------------------')


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--env', help='环境 dev/test/prod')
    parser.add_argument('--region-id', help='region-id ShangHai:1/HangZhou:2')
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--clinic-id', help='诊所id')
    args = parser.parse_args()
    if not args.env or not args.region_id or not args.chain_id or not args.clinic_id:
        parser.print_help()
        sys.exit(-1)

    importInspectionReportTemplate(region_name(args.region_id), args.chain_id, args.clinic_id, args.env)
    # importInspectionReportTemplate('ShangHai', 'ffffffff000000003483b76b3ce84000', 'ffffffff000000003483b76b3ce84001', 'dev')


if __name__ == '__main__':
    main()
