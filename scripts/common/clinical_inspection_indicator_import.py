import logging
import os
import requests

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.config import region_name
from multizone.rpc import regionRpcHost
from decimal import Decimal
import argparse
import json
import pypinyin
from openpyxl import load_workbook
from utils.excel_helper import ExcelHelper as excel_helper
import re

default_id = '00000000000000000000000000000000'

device_type_name_map = {
    u'CT': 1,
    u'DR': 2,
    u'彩超': 8,
    u'MR': 9,
    u'其他': 13,
    u'一般检查': 14,
    u'内科检查': 15,
    u'外科检查': 16,
    u'耳鼻喉检查': 17,
    u'口腔检查': 18,
    u'眼科检查': 19,
    u'公卫检查': 20,
    u'妇科检查': 22
}

group_id_map = {
    '空腹血糖': 200,
    '血常规': 201,
    '尿常规': 202,
    '尿微量白蛋白': 203,
    '大便隐血': 204,
    '肝功能': 205,
    '肾功能': 206,
    '血脂': 207,
    '糖化血红蛋白': 208,
    '乙型肝炎表面抗原': 209,
}

def indicator_import():
    adb_cli = DBClient('ShangHai', 'adb', 'abc_cis_examination', 'dev')
    logging.info('--------------------start parse excel--------------------')
    workbook = load_workbook('/Users/<USER>/Desktop/检查项目指标.xlsx')
    # 获取工作表
    sheet = workbook.worksheets[0]
    rows = excel_helper.sheet_to_json(sheet=sheet, mapper={
        '分类': 'category',
        '分组': 'groupBy',
        '指标名称': 'name',
        '检查类型': 'deviceTypeName',
        '代码': 'enName',
        '单位': 'unit',
        '参考范围': 'ref',
        '结果录入': 'result',
        '异常提示': 'abnormality',
        '判断条件': 'conditional'
    }, filter={
        'column_index': 2,
        'attrs': 'font.color.value',
        'value': 'FFC00000'
    })
    logging.info(f'--------------------parse excel {len(rows)} row--------------------')
    logging.info(json.dumps(rows, ensure_ascii=False))
    sql_file = open('新增检查内置指标.sql', "w+")
    for row in rows:
        if len(row) == 0:
            logging.error("row is empty")
            continue
        deviceTypeName = row.get('deviceTypeName')
        if deviceTypeName is None:
            logging.error("deviceTypeName is None")
            continue
        device_type = device_type_name_map[deviceTypeName]
        if device_type != 20:
            continue
        item_id = adb_cli.fetchone("select replace(uuid(), '-', '') as id")['id']
        name = row['name']
        en_name = row['enName']
        search_text = f"{name}_{''.join([p[0][0] for p in pypinyin.pinyin(name, style=pypinyin.Style.NORMAL)])}_{en_name.lower()}"
        unit = row['unit'].lower() if row['unit'] is not None and row['unit'] != '-' else None

        ref_json = {"min": "", "max": ""}
        ref_arr = row['ref'].split('-') if row['ref'] is not None and re.match(r'^\d+(\.\d+)?[-~]{1}\d+(\.\d+)?$', row['ref']) else None
        if ref_arr is not None:
            if len(ref_arr) > 0:
                ref_json['min'] = ref_arr[0]
            if len(ref_arr) > 1:
                ref_json['max'] = ref_arr[1]
        if ref_json['min'] != '' and ref_json['max'] != '':
            if Decimal(ref_json['min']) > Decimal(ref_json['max']):
                raise Exception('min must lte max')

        constraints = None
        additional = None
        result = row['result']
        if result == '输入框：限200个字，支持快速选择“未见异常”' or result == '输入框：限200个字,默认未见异常' or result == '输入框：限200个字，默认“未见异常”':
            constraints = '[{"size":200,"message":"限200个字","required":false}]'
            additional = '{"defaultValue": "未见异常"}'
        if result == '输入框：限数字、20个字':
            constraints = '[{"size":20,"message":"限数字、20个字","required":false}]'
        if result == '输入框：限200个字':
            constraints = '[{"size":200,"message":"限200个字","required":false}]'

        component_type = 3 if device_type != 20 else None
        item_type = 2
        goods_type = 27 if device_type == 20 else 3
        options = []
        sql = f"""
            INSERT INTO v2_examination_item (id, clinic_id, chain_id, type, name, en_name, search_text, unit,
                                             ref, result_display_scale, is_deleted, created_user_id,
                                             created_date, last_modified_user_id, last_modified_date, item_code,
                                             goods_id, source_id, inspect_type, component_type, options,
                                             constraints, group_id, value_quantity, display_name, is_standard,
                                             tag, sort, additional, item_type, goods_type, device_type)
            VALUES ('{item_id}', '{default_id}', '{default_id}', 1, '{name}',
                                '{en_name}', '{search_text}', '{unit}', '{json.dumps(ref_json, ensure_ascii=False)}', 2, 0, '{default_id}',
                                now(), '{default_id}', now(), null, '', null, null, {component_type}, '{json.dumps(options, ensure_ascii=False)}', 
                                '{constraints}', null, null, null, 1, null, 0, '{additional}', {item_type}, {goods_type}, {device_type});
        """.replace("'None'", "null")
        sql_file.write(sql + os.linesep)
        if ref_json['min'] != '' or ref_json['max'] != '':
            ref_detail_sql = f"""
                    INSERT INTO v2_examination_item_ref_detail(id, item_id, sex, start_age, end_age, age_unit, ref, is_deleted, created,
                                               created_by, last_modified, last_modified_by, sample_type)
                    VALUES (substr(uuid_short(), 4), '{item_id}', null, null, null, null, '{json.dumps(ref_json, ensure_ascii=False)}', 0, now(), '{default_id}', now(), '{default_id}', null);
                    """
            sql_file.write(ref_detail_sql + os.linesep)


def main():
    indicator_import()


if __name__ == '__main__':
    main()
