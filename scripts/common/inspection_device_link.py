#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.rediscli import RedisClient
from multizone.rpc import regionRpcHost
from multizone.config import region_name
import json
import argparse
import requests
import logging

logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)

default_id = '00000000000000000000000000000000'


def run(chain_id, clinic_id, device_name, match_mode, link_count, env, region_name):
    db_client = DBClient(region_name, 'abc_cis_stock', 'abc_cis_goods', env, True)
    basic_db_client = DBClient(region_name, 'abc_cis_mixed', 'abc_cis_basic', env, True)
    property_db_client = DBClient(region_name, 'abc_cis_mixed', 'abc_cis_property' if env == 'prod' else 'abc_cis_basic', env, True)
    redis_client = RedisClient(region_name, 'abc-ha-redis', 13, env, True)
    match_where = ''
    if match_mode == 1:
        match_where = f"name like '%{device_name}%' or model like '%{device_name}%' or device_uuid like '%{device_name}%'"
    elif match_mode == 0:
        match_where = f"id = '{device_name}'"
    query_device_sql = f"select * from v2_goods_examination_device_model where ({match_where}) and  goods_type = 3 and goods_sub_type = 2"
    logging.info(f"query_device_sql = {query_device_sql}")
    device_models = db_client.fetchall(query_device_sql)
    if len(device_models) == 0 or len(device_models) > 1:
        raise Exception("device_model is None or device_model is non unique")
    device_model = device_models[0]

    property = property_db_client.fetchone(
        f"""select * from v2_property_config_item where v2_scope_id = '{clinic_id}' and `key` = 'examination.settings.inspect.pacsSettings.deviceList';""")
    if property is None:
        device_list = []
    else:
        device_list = json.loads(property['value'])
    incrementLinkCount = False
    for device in device_list:
        if str(device['deviceModelId']).__eq__(str(device_model['id'])):
            incrementLinkCount = True
            device['linkCount'] = link_count
            device['deviceType'] = device_model['device_type']
            logging.info(f"device['linkCount']+1, device_model_id = {device_model['id']}")
            break
    if not incrementLinkCount:
        device_list.append(
            {
                "deviceModelId": str(device_model['id']),
                "deviceType": device_model['device_type'],
                "linkCount": link_count
            }
        )

    try:
        property_db_client.execute(f"""
        INSERT INTO v2_property_config_item (id, `key`, value, scope, is_deleted, created_by, created,
                                                       last_modified_by, last_modified, key_first, key_second, key_third,
                                                       key_fourth, key_fifth, v2_scope_id)
                    values (
                            substr(uuid_short(),5),
                            'examination.settings.inspect.pacsSettings.deviceList',
                            '{json.dumps(device_list, ensure_ascii=False)}',
                            'clinic',
                            0,
                            '00000000000000000000000000000000',
                            now(),
                            '00000000000000000000000000000000',
                            now(),
                            if('examination' = 'null', null, 'examination'),
                            if('settings' = 'null', null, 'settings'),
                            if('inspect' = 'null', null, 'inspect'),
                            if('pacsSettings' = 'null', null, 'pacsSettings'),
                            if('deviceList' = 'null', null, 'deviceList'),
                            '{clinic_id}'
                           )
                           on duplicate key update value = '{json.dumps(device_list, ensure_ascii=False)}';""")
        clean_redis_key = 'examination.settings.inspect.pacsSettings'
        while len(clean_redis_key) > 0:
            logging.info(f"clean_redis_key = {clean_redis_key}")
            redis_client.client.delete(f'property.v3.config.item.clinic.{clinic_id}.{clean_redis_key}')
            redis_client.client.delete(f'property.v3.config.item.clinic.{clinic_id}.{clean_redis_key}.v1')
            if clean_redis_key.rfind('.') == -1:
                break
            clean_redis_key = clean_redis_key[:clean_redis_key.rfind('.')]

        # organ = basic_db_client.fetchone(''' select * from organ where id = '{clinic_id}' '''.format(clinic_id=clinic_id))
        # if organ is None:
        #     logging.error("organ is None")
        #     return
        # logging.info("node_type = {0}, view_mode = {1}".format(organ['node_type'], organ['view_mode']))
        # clinic_employees = basic_db_client.fetchall(
        #     ''' select * from clinic_employee where clinic_id = '{}' and role_id = 1; '''.format(clinic_id))
        # # logging.info("clinic_employees = {}".format(json.dumps(clinic_employees)))
        # employee_id = default_id
        # if len(clinic_employees) > 0:
        #     employee_id = clinic_employees[0]['employee_id']
        # headers = {
        #     'cis-chain-id': '{}'.format(chain_id),
        #     'cis-clinic-id': '{}'.format(clinic_id),
        #     'cis-employee-id': '{}'.format(employee_id),
        #     'cis-clinic-type': '{}'.format(organ['node_type']),
        #     'cis-view-mode': '{}'.format(organ['view_mode'])
        # }
        # resp = requests.put(
        #     url='http://{host}/api/v3/goods/exam/assay/devices/link/{deviceModelId}?1677468128021'.format(
        #         host=regionRpcHost(region_name, env), deviceModelId=device_model['id']),
        #     headers=headers)
        # if resp is not None:
        #     logging.info(resp.content.decode("utf-8"))
    except Exception as e:
        raise Exception(f"transaction rollback {e}")


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--env', help='环境 dev/test/prod')
    parser.add_argument('--region-id', help='region-id ShangHai:1/HangZhou:2')
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--clinic-id', help='诊所id')
    parser.add_argument('--device-name', help='设备名称/型号/UUID')
    parser.add_argument('--match-mode', type=int, help='匹配模式 0-精确匹配 1-模糊匹配')
    parser.add_argument('--link-count', type=int, help='联机数量')
    args = parser.parse_args()
    if not args.env or not args.region_id or not args.chain_id or not args.clinic_id or not args.device_name or not args.link_count:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id, args.clinic_id, args.device_name, args.match_mode, args.link_count, args.env, region_name(args.region_id))
    # run('6a869c22abee4ffbaef3e527bbb70aeb', 'fff730ccc5ee45d783d82a85b8a0e52d', '航卫', 'dev', 'ShangHai')


if __name__ == '__main__':
    main()
