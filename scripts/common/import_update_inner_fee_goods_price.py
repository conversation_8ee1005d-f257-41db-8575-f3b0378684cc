"""

"""
import json
import logging
import os

import requests

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.config import region_id, region_name
from multizone.rpc.RpcClient import regionRpcHost
import time
from openpyxl import load_workbook
from scripts.common.utils.excel_helper import ExcelHelper as excel_helper
from scripts.common.utils.lists import ListUtils as list_utils
import argparse

default_id = '00000000000000000000000000000000'


def run(region_name, chain_id, clinic_id, env):
    goods_cli = DBClient(region_name, 'ob', 'abc_cis_goods', env, True)
    basic_cli = DBClient(region_name, 'ob', 'abc_cis_basic', env, True)

    clinic = basic_cli.fetchone(f'''select * from organ where id = '{clinic_id}';''')
    if clinic['his_type'] != 100 and clinic['node_type'] != 2:
        logging.warning(f'not supported clinic_type: {clinic["node_type"]}')
        return

    logging.info('--------------------start parse excel--------------------')
    workbook = load_workbook('./uploads/uploadedFile.xlsx')
    # 获取工作表
    sheet = workbook.worksheets[0]
    list = excel_helper.sheet_to_json(sheet, {
        '医保编码': 'national_code',
        '成本价': 'package_cost_price',
        '售价': 'package_price'
    })
    logging.info(f'--------------------parse excel {len(list)} row--------------------')
    national_code_to_price = list_utils.to_map([l for l in list if l['national_code'] is not None],
                                               lambda x: x['national_code'].strip())
    national_codes = national_code_to_price.keys()
    goods_extends = goods_cli.fetchall(f"""
        select * from v2_goods_extend where chain_id = '{chain_id}' and organ_id = '{clinic_id}' and shebao_code_national_code in ({', '.join([f"'{nation_code}'" for nation_code in national_codes])});
    """)
    for goods_extend in goods_extends:
        request_uri = f'''http://{regionRpcHost(region_name, env)}/api/v3/goods/{goods_extend["goods_id"]}?{int(time.time() * 1000)}'''
        headers = {
            'cis-chain-id': chain_id,
            'cis-clinic-id': clinic_id,
            'cis-employee-id': default_id,
            'cis-clinic-type': str(clinic['node_type']),
            'cis-view-mode': str(clinic['view_mode']),
            'cis-his-type': str(clinic['his_type']),
            '__region_id__': region_id(region_name)
        }
        logging.info(f"GET goods national_code: {goods_extend['shebao_code_national_code']} request_uri: {request_uri}")
        goods_rsp = json.loads(
            requests.get(
                url=request_uri,
                headers=headers
            ).content.decode('utf-8')
        )
        if 'data' in goods_rsp:
            goods = goods_rsp['data']
        else:
            logging.error(f'fetch goods error: {json.dumps(goods_rsp, ensure_ascii=False)}')
            continue
        if goods['type'] != 19 or goods['innerFlag'] != 1:
            logging.warning('only allowed update inner fee_goods')
            continue
        # logging.info(f'goods: {json.dumps(goods, ensure_ascii=False)}')
        national_code = goods_extend['shebao_code_national_code']
        if national_code is None or national_code == '':
            logging.warning('national_code is null')
            continue
        price_obj = national_code_to_price.get(national_code)
        if price_obj is None:
            logging.warning(f'national_code: {national_code} => price is null')
            continue

        if price_obj.get("package_price") is None or price_obj.get("package_price") < 0:
            logging.warning("package_price is null or < 0")
            continue

        logging.info(f"PUT goods request_uri: {request_uri}")
        goods['packagePrice'] = price_obj.get('package_price')
        if price_obj.get('package_cost_price') is not None:
            goods['packageCostPrice'] = price_obj.get('package_cost_price')
        put_goods_rsp = json.loads(
            requests.put(
                url=request_uri,
                headers=headers,
                json=goods
            ).content.decode('utf-8')
        )
        logging.info(f'PUT goods rsp: {json.dumps(put_goods_rsp, ensure_ascii=False)}')
        time.sleep(0.5)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--region-id', help='region-id ShangHai:1/HangZhou:2')
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--clinic-id', help='诊所id')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.env or not args.region_id or not args.chain_id or not args.clinic_id:
        parser.print_help()
        sys.exit(-1)
    run(region_name(args.region_id), args.chain_id, args.clinic_id, args.env)


if __name__ == '__main__':
    main()
