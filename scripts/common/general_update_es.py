#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.es import ESClient
import json
import datetime

# from idwork import IdWork
#
# db_client = db.DBClient('abc_cis_outpatient', 'abc_cis_examination')
# id_work = IdWork(db_client, False)
# id_work.config()

# {
#     "query": {
#         "term": {
#             "patientId": ""
#         }
#     },
#     "script": {
#         "source": "ctx._source.patientSn = 'new_value'"
#     }
# }

default_id = '00000000000000000000000000000000'


def updateEs(region_name, chain_id, db_name, table_name, select_sql, index_name, env, isNormal=True):
    # try:
    db_client = DBClient(region_name, 'adb', db_name, env, True)
    es_cli = ESClient(region_name, env, isNormal, True)
    start_date = db_client.fetchone(
        """select date(min(created)) as startDate from {0} where chain_id = '{1}';""".format(table_name, chain_id))['startDate']
    if start_date is None:
        print('start_date is None')
        return
    today = datetime.date.today()
    # 计算时间间隔
    days = (today - start_date).days
    gap = 30
    for i in range(0, days, gap):
        sql = f"""{select_sql}
             {'and' if select_sql.__contains__('where') else 'where'} created >= '{start_date + datetime.timedelta(days=i + (0 if i == 0 else 1))} 00:00:00' 
             and created <= '{start_date + datetime.timedelta(days=i + gap)} 23:59:59' 
             and chain_id = '{chain_id}';"""
        print(sql)
        rows = db_client.fetchall(sql)
        # rows拆分 一组2000个
        rows2000 = [rows[i:i + 2000] for i in range(0, len(rows), 2000)]
        for rows2 in rows2000:
            values = []
            for row in rows2:
                body = {
                    "doc": {
                        k: v for k, v in row.items() if k != 'id'
                    }
                }
                values.append({'update': {'_index': '{0}-{1}'.format(index_name, env), '_id': str(row['id'])}})
                values.append(json.dumps(body, ensure_ascii=False))
            # 批量更新
            if len(values) == 0:
                print('req_body is empty')
                continue
            print(values)
            try:
                es_cli.bulkInsert('{0}-{1}'.format(index_name, env), values)
            except Exception as e:
                print("index_name: {} to-es error".format(index_name), e)

# except Exception as e:
#     print("update {} to-es error".format(table_name), e)
