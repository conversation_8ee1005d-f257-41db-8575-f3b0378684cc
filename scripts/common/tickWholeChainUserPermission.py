#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import logging
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.rediscli import redisHost
import argparse

default_id = '00000000000000000000000000000000'


def tickChainUserPermission(abcRegion, chain_id):
    sql = '''select concat("_permissionInfo", ":", clinic_id, ":", employee_id) as redisTokenKey
            from clinic_employee
            where chain_id = '{chainId}' 
            '''
    basic_db_client = DBClient(abcRegion, 'abc_cis_mixed', 'abc_cis_basic', 'prod', True)
    redisTokenKeyList = basic_db_client.fetchall(sql.format(chainId=chain_id))
    if redisTokenKeyList is None or len(redisTokenKeyList) == 0:
        return
    file_name = '''./tickUser/{chainId}_permission_redis.txt'''.format(chainId=chain_id)
    f = open(file_name.format(chainId=chain_id), 'w')
    for redisTokenKey in redisTokenKeyList:
        # 把踢人的redis命令写入文件
        # python3 写入文件换行要 加\n，否则会写在一行
        f.write('''del {redisTokenKey}\n'''.format(redisTokenKey=redisTokenKey['redisTokenKey']))

    # 关闭文件
    f.close()
    redisCmd ='''cat {fileName} | redis-cli -h {redisHostUrl} -n 0 -p 6379'''.format(
        fileName=file_name,redisHostUrl=redisHost(abcRegion,'abcHaRedis'))
    logging.info(redisCmd)
    os.system(redisCmd)
    # 删除文件
    os.remove(file_name)


def run(abcRegion, chain_id):
    tickChainUserPermission(abcRegion, chain_id)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.region_name, args.chain_id)


if __name__ == '__main__':
    main()
