#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
import time

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.rediscli import RedisClient
from multizone.rpc import regionRpcHost
from multizone.config import region_name
import json
import argparse
import requests
import logging
import hashlib
from base64 import b64encode

logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)

default_id = '00000000000000000000000000000000'

supplier_config_map = {
    1: {
        "app_id": "abc_coop_hongan",
        "app_secret": "HA28H6B16",
        "callback_url": {
            "dev": "http://test.honganyun.com/api/third/sync/abc/deviceInfo",
            "test": "http://test.honganyun.com/api/third/sync/abc/deviceInfo",
            "prod": "https://www.honganyun.com/api/third/sync/abc/deviceInfo"
        }
    }
}

pay_rule_map = {
    'HAJYABC001': {
        'receivableFee': 14.5,
        'depositDeductionFee': 1,
        "packageCostPrice": 7,
        "salesCommission": 1
    },
    'HAJYABC002': {
        'receivableFee': 24.5,
        'depositDeductionFee': 2,
        "packageCostPrice": 9,
        "salesCommission": 2
    },
    'HAJYABC003': {
        'receivableFee': 34.5,
        'depositDeductionFee': 3,
        "packageCostPrice": 12,
        "salesCommission": 3
    },
    'HAJYABC004': {
        'receivableFee': 44.5,
        'depositDeductionFee': 4,
        "packageCostPrice": 15,
        "salesCommission": 4
    },
    'HAJYABC005': {
        'receivableFee': 54.5,
        'depositDeductionFee': 5,
        "packageCostPrice": 18,
        "salesCommission": 5
    },
    'HAJYABC006': {
        'receivableFee': 64.5,
        'depositDeductionFee': 6,
        "packageCostPrice": 21,
        "salesCommission": 6
    },
    'HAJYABC007': {
        'receivableFee': 74.5,
        'depositDeductionFee': 7,
        "packageCostPrice": 25,
        "salesCommission": 7
    },
    'HAJYABC009': {
        'receivableFee': 94.5,
        'depositDeductionFee': 9,
        "packageCostPrice": 30,
        "salesCommission": 9
    }
}

def run(chain_id, clinic_id, device_name, device_no, match_mode, op_type, env, region_name):
    db_client = DBClient(region_name, 'abc_cis_stock', 'abc_cis_goods', env, True)
    basic_db_client = DBClient(region_name, 'abc_cis_mixed', 'abc_cis_basic', env, True)
    property_db_client = DBClient(region_name, 'abc_cis_mixed', 'abc_cis_property' if env == 'prod' else 'abc_cis_basic', env, True)
    redis_client = RedisClient(region_name, 'abc-ha-redis', 13, env, True)
    match_where = ''
    if match_mode == 1:
        match_where = f"name like '%{device_name}%' or model like '%{device_name}%' or device_uuid like '%{device_name}%'"
    elif match_mode == 0:
        if not device_name.isdigit():
            raise Exception("device_name is not digit")
        match_where = f"id = '{device_name}'"
    query_device_sql = f"select * from v2_goods_examination_device_model where ({match_where}) and  goods_type = 3 and goods_sub_type = 1"
    logging.info(f"query_device_sql = {query_device_sql}")
    device_models = db_client.fetchall(query_device_sql)
    if len(device_models) == 0 or len(device_models) > 1:
        raise Exception("device_model is None or device_model is non unique")
    device_model = device_models[0]
    if env == 'prod' and device_model.get('supplier_id', 0) != 0:
        raise Exception("云检设备不允许通过jenkins绑定")
    device_model_id = device_model['id']

    organ = basic_db_client.fetchone(''' select * from organ where id = '{clinic_id}' '''.format(clinic_id=clinic_id))
    if organ is None:
        raise Exception("organ is None")
    logging.info("node_type = {0}, view_mode = {1}".format(organ['node_type'], organ['view_mode']))
    clinic_employees = basic_db_client.fetchall(
        ''' select * from clinic_employee where clinic_id = '{}' and role_id = 1; '''.format(clinic_id))
    # logging.info("clinic_employees = {}".format(json.dumps(clinic_employees)))
    employee_id = default_id
    if len(clinic_employees) > 0:
        employee_id = clinic_employees[0]['employee_id']
    headers = {
        'cis-chain-id': '{}'.format(chain_id),
        'cis-clinic-id': '{}'.format(clinic_id),
        'cis-employee-id': '{}'.format(employee_id),
        'cis-clinic-type': '{}'.format(organ['node_type']),
        'cis-view-mode': '{}'.format(organ['view_mode']),
        'cis-his-type': '{}'.format(organ['his_type'])
    }
    # 查short_id
    if device_no is None or device_no == '':
        if op_type == 0:
            max_sid = db_client.fetchone(f"""
                        SELECT ifnull(max(short_id), 0) as max_sid FROM v2_goods_examination_device WHERE short_id regexp '^\\\\d+$' and chain_id = '{chain_id}' and goods_sub_type = {device_model['goods_sub_type']};
                    """)
            short_id = "000001"
            if max_sid is not None:
                short_id_num = int(max_sid['max_sid']) + 1
                if short_id_num < 100000:
                    short_id = str(short_id_num).zfill(6)
                else:
                    short_id = str(short_id_num)
            device_id = db_client.fetchone("""select substr(uuid_short(), 4) as uuid""")['uuid']
            db_client.execute("""
                    INSERT INTO v2_goods_examination_device (id, device_model_id, chain_id, clinic_id, short_id, goods_type,
                                                                       goods_sub_type, device_type, name, inspect_device_model,
                                                                       is_virtual, inner_flag, status, sort, created_by, created,
                                                                       last_modified_by, last_modified, py, device_parameters)
                    VALUES ({device_id}, '{device_model_id}', '{chain_id}',
                        '{clinic_id}', '{short_id}', 3, 1, 1, null, null, 0, 0, 10, 0,
                        '{default_id}', current_timestamp(), '{default_id}',
                        current_timestamp(), null, null)
                    """.format(device_id=device_id, device_model_id=device_model_id, chain_id=chain_id, clinic_id=clinic_id, short_id=short_id, default_id=default_id))
        elif op_type == 1:
            raise Exception("device_no is None, unsupported unbind operation")
    else:
        # 手动输入的设备编号必须保证全局唯一
        devices = db_client.fetchall("select * from v2_goods_examination_device where short_id = '{}' and status != 99".format(device_no))
        if op_type == 0:
            if len(devices) > 1:
                raise Exception("device is not unique")
            if len(devices) == 1:
                device = devices[0]
                if device['chain_id'] != chain_id or device['clinic_id'] != clinic_id:
                    raise Exception("device chain_id or clinic_id is not match")
                device_id = device['id']
            else:
                device_id = db_client.fetchone("""select substr(uuid_short(), 4) as uuid""")['uuid']
                db_client.execute("""
                                INSERT INTO v2_goods_examination_device (id, device_model_id, chain_id, clinic_id, short_id, goods_type,
                                                                                   goods_sub_type, device_type, name, inspect_device_model,
                                                                                   is_virtual, inner_flag, status, sort, created_by, created,
                                                                                   last_modified_by, last_modified, py, device_parameters)
                                VALUES ({device_id}, '{device_model_id}', '{chain_id}',
                                    '{clinic_id}', '{short_id}', 3, 1, 1, null, null, 0, 0, 10, 0,
                                    '{default_id}', current_timestamp(), '{default_id}',
                                    current_timestamp(), null, null)
                                """.format(device_id=device_id, device_model_id=device_model_id, chain_id=chain_id, clinic_id=clinic_id,
                                           short_id=device_no,
                                           default_id=default_id))
        elif op_type == 1:
            clinic_devices = [device for device in devices if device['chain_id'] == chain_id and device['clinic_id'] == clinic_id]
            if len(clinic_devices) == 1:
                device_id = clinic_devices[0]['id']
                db_client.execute("update v2_goods_examination_device set status = 99 where id = '{}'".format(device_id))
            else:
                logging.warning("device_no = {} not found, ignore delete".format(device_no))

    # 查询设备型号
    url = f'http://{regionRpcHost(region_name, env)}/api/v3/goods/exam/assay/device-models/{device_model_id}?{int(time.time() * 1000)}&combineType=&onlyStandardGoods=0'
    logging.info("url = {}".format(url))
    query_device_model_rsp = json.loads(
        requests.get(
            url=url,
            headers=headers
        ).content.decode('utf-8')
    )
    logging.info("query_device_model_rsp = {}".format(json.dumps(query_device_model_rsp, ensure_ascii=False)))
    if query_device_model_rsp is None:
        raise Exception("query_device_model_rsp is None")
    unInstallStandardGoodsList = query_device_model_rsp.get('data', {}).get('unInstallStandardGoodsList', [])
    installStandardGoodsList = query_device_model_rsp.get('data', {}).get('installedLocalGoodsList', [])
    goodsList = unInstallStandardGoodsList if unInstallStandardGoodsList is not None and len(unInstallStandardGoodsList) > 0 else installStandardGoodsList
    if op_type == 0 and len(goodsList) > 0:
        batch_create_req_body = {
            "combineItemGoodsIds": [
                {
                    "children": [child['id'] for child in goods['children']],
                    "id": goods['id']
                }
                for goods in goodsList if goods['combineType'] == 1
            ],
            "singleItemGoodsIds": [goods['id'] for goods in goodsList if goods['combineType'] == 0]
        }
        logging.info("batch_create_req_body = {}".format(json.dumps(batch_create_req_body, ensure_ascii=False, indent=2)))
        url = f'http://{regionRpcHost(region_name, env)}/api/v2/examination-goods/examinations/batch-create/{device_model_id}?{int(time.time() * 1000)}'
        logging.info("url = {}".format(url))
        batch_create_rsp = json.loads(
            requests.post(
                url=url,
                json=batch_create_req_body,
                headers=headers
            ).content.decode('utf-8')
        )
        logging.info("batch_create_rsp = {}".format(json.dumps(batch_create_rsp, ensure_ascii=False, indent=2)))
        taskId = batch_create_rsp.get('data', {}).get('taskId')
        if taskId is None or taskId == '':
            raise Exception("taskId is Required")
        # 轮巡联机进度
        while True:
            async_task_rsp = json.loads(
                requests.get(
                    url=f'http://{regionRpcHost(region_name, env)}/api/v2/examination-goods/examinations/async-task-progress/batch-create-examination-goods/{taskId}?{int(time.time() * 1000)}',
                    headers=headers
                ).content.decode('utf-8')
            )
            logging.info("async_task_rsp = {}".format(json.dumps(async_task_rsp, ensure_ascii=False, indent=2)))
            time.sleep(0.5)
            if async_task_rsp.get('data', {}).get('taskStatus') == 2:
                break

    elif op_type == 1 and len(installStandardGoodsList) > 0:
        # 先删组合
        for installGoods in installStandardGoodsList:
            if installGoods['combineType'] == 1:
                delete_exam_goods(env, headers, installGoods, region_name)

        # 删除单品
        for installGoods in installStandardGoodsList:
            if installGoods['combineType'] == 0:
                delete_exam_goods(env, headers, installGoods, region_name)


    if device_no is not None and device_no != '' and device_model.get('supplier_id', 0) != 0:
        # 绑定device
        goods_sub_type = device_model['goods_sub_type']
        device_type = None
        if goods_sub_type == 1:
            device_type = 4
        elif goods_sub_type == 2:
            device_type = 5
        if device_type is not None:
            if op_type == 0:
                # 开通云检配置
                switch_cloud_supplier_settings(property_db_client, redis_client, clinic_id, '1')
                # 绑定设备
                url = f'http://{regionRpcHost(region_name, env)}/rpc/v3/clinics/device/bind?{int(time.time() * 1000)}'
                logging.info("url = {}".format(url))
                bind_device_rsp = json.loads(
                    requests.post(
                        url=url,
                        headers=headers,
                        json={
                            "chainId": chain_id,
                            "clinicId": clinic_id,
                            "operatorId": employee_id,
                            "deviceId": device_no,
                            "deviceType": device_type,
                        }
                    ).content.decode('utf-8')
                )
                logging.info("bind_device_rsp = {}".format(json.dumps(bind_device_rsp, ensure_ascii=False, indent=2)))
                device_secret = bind_device_rsp.get('data', {}).get('bindInfo')

                # 同步密钥给第三方
                app = supplier_config_map.get(device_model.get('supplier_id'))
                if app is not None and device_secret is not None:
                    app_id = app.get("app_id")
                    app_secret = app.get("app_secret")
                    callback_url = app.get("callback_url", {}).get(env)

                    ts = int(time.time() * 1000)
                    version = "1.0"

                    if app_id is not None and app_secret is not None and callback_url is not None:
                        md5 = hashlib.md5(f'{device_no}{ts}{version}:{app_secret}'.encode('utf-8')).hexdigest()
                        # sha1 = hashlib.sha1(md5.encode('utf-8')).hexdigest()
                        # sign = b64encode(sha1.encode('utf-8')).decode('utf-8')
                        third_sync_rsp = json.loads(
                            requests.post(
                                url=callback_url,
                                headers={
                                    "appId": app_id,
                                    "ts": str(ts),
                                    "deviceId": device_no,
                                    "version": version,
                                    "sign": md5
                                },
                                json={
                                    "deviceId": device_no,
                                    "appSecret": device_secret
                                }
                            ).content.decode('utf-8')
                        )
                        logging.info("third_sync_rsp = {}".format(json.dumps(third_sync_rsp, ensure_ascii=False, indent=2)))

                # 查押金
                deposit_list_rsp = json.loads(
                    requests.post(
                        url=f'http://{regionRpcHost(region_name, env)}/rpc/wallet/deposit/list-deposits?{int(time.time() * 1000)}',
                        json={
                            "ownerId": clinic_id,
                            "ownerType": 1,
                            "businessIds": [device_id],
                            "businessType": 1
                        }
                    ).content.decode('utf-8')
                )
                logging.info("deposit_list_rsp = {}".format(json.dumps(deposit_list_rsp, ensure_ascii=False, indent=2)))
                if deposit_list_rsp is None or len(deposit_list_rsp.get('data', {}).get("rows", [])) == 0:
                    # 冲押金
                    recharge_deposit_rsp = json.loads(
                        requests.post(
                            url=f'http://{regionRpcHost(region_name, env)}/rpc/wallet/deposit/recharge-offline?{int(time.time() * 1000)}',
                            json={
                                "totalFee": 5000,
                                "ownerId": clinic_id,
                                "ownerType": 1,
                                "businessId": device_id,
                                "businessType": 1,
                                "remarks": f"{device_model['name']}({device_no})押金充值备注"
                            }
                        ).content.decode('utf-8')
                    )
                    logging.info("recharge_deposit_rsp = {}".format(json.dumps(recharge_deposit_rsp, ensure_ascii=False, indent=2)))
                # 支付规则
                pay_rule_rsp = json.loads(
                    requests.get(
                        url=f'http://{regionRpcHost(region_name, env)}/api/v2/examinations/pay-rule/device-model/{device_model_id}?{int(time.time() * 1000)}',
                        headers=headers
                    ).content.decode('utf-8')
                )
                logging.info("pay_rule_rsp = {}".format(json.dumps(pay_rule_rsp, ensure_ascii=False, indent=2)))
                pay_rule_items = pay_rule_rsp.get('data', {}).get('items', [])
                if len(pay_rule_items) > 0:
                    # 修改或更新支付规则
                    update_pay_rule_rsp = json.loads(
                        requests.put(
                            url=f'http://{regionRpcHost(region_name, env)}/api/v2/examinations/pay-rule/device-model/{device_model_id}?{int(time.time() * 1000)}',
                            headers=headers,
                            json={
                                "items": [{
                                    "id": item.get('id'),
                                    "deviceModelId": item.get('deviceModelId'),
                                    "goodsId": item.get('goodsId'),
                                    "receivableFee": pay_rule_map[item['goods']['shortId']]['receivableFee'],
                                    "depositDeductionFee": pay_rule_map[item['goods']['shortId']]['depositDeductionFee'],
                                    "packageCostPrice": pay_rule_map[item['goods']['shortId']]['packageCostPrice'],
                                    "salesCommission": pay_rule_map[item['goods']['shortId']]['salesCommission']
                                    } for item in pay_rule_items if item['goods']['shortId'] in pay_rule_map.keys()
                                ]
                            }
                        ).content.decode('utf-8')
                    )
                    logging.info("update_pay_rule_rsp = {}".format(json.dumps(update_pay_rule_rsp, ensure_ascii=False, indent=2)))

            elif op_type == 1:
                clinic_device = basic_db_client.fetchone(f"select * from v2_clinic_device where chain_id = '{chain_id}' and clinic_id = '{clinic_id}' and device_no = '{device_no}';")
                if clinic_device is None:
                    raise Exception(f"设备{device_no}不存在")
                switch_cloud_supplier_settings(property_db_client, redis_client, clinic_id, '0')
                # 解绑设备
                url = f'http://{regionRpcHost(region_name, env)}/rpc/v3/clinics/device/unbind/{clinic_device["id"]}?{int(time.time() * 1000)}'
                logging.info("url = {}".format(url))
                un_bind_device_rsp = json.loads(
                    requests.put(
                        url=url,
                        headers=headers,
                        json={
                            "chainId": chain_id,
                            "clinicId": clinic_id,
                            "operatorId": employee_id
                        }
                    ).content.decode('utf-8')
                )
                logging.info("un_bind_device_rsp = {}".format(json.dumps(un_bind_device_rsp, ensure_ascii=False, indent=2)))

                # 删除押金
                logging.info("delete_deposit_rsp = {}".format("删除押金 -> TODO"))

                # 删除支付规则
                delete_pay_rule_rsp = json.loads(
                    requests.delete(
                        url=f'http://{regionRpcHost(region_name, env)}/api/v2/examinations/pay-rule/device-model/{device_model_id}?{int(time.time() * 1000)}',
                        headers=headers
                    ).content.decode('utf-8')
                )
                logging.info("delete_pay_rule_rsp = {}".format(json.dumps(delete_pay_rule_rsp, ensure_ascii=False, indent=2)))


def switch_cloud_supplier_settings(property_db_client, redis_client, clinic_id, value):
    property_db_client.execute(f"""
                        INSERT INTO v2_property_config_item (id, `key`, value, scope, is_deleted, created_by, created,
                                                                       last_modified_by, last_modified, key_first, key_second, key_third,
                                                                       key_fourth, key_fifth, v2_scope_id)
                                    values (
                                            substr(uuid_short(),5),
                                            'examination.settings.examine.cloudSupplierSettings.isEnable',
                                            '{value}',
                                            'clinic',
                                            0,
                                            '00000000000000000000000000000000',
                                            now(),
                                            '00000000000000000000000000000000',
                                            now(),
                                            if('examination' = 'null', null, 'examination'),
                                            if('settings' = 'null', null, 'settings'),
                                            if('examine' = 'null', null, 'examine'),
                                            if('cloudSupplierSettings' = 'null', null, 'cloudSupplierSettings'),
                                            if('isEnable' = 'null', null, 'isEnable'),
                                            '{clinic_id}'
                                           )
                                           on duplicate key update value = '{value}';""")
    clean_redis_key = 'examination.settings.examine.cloudSupplierSettings'
    while len(clean_redis_key) > 0:
        logging.info(f"clean_redis_key = {clean_redis_key}")
        redis_client.client.delete(f'property.v3.config.item.clinic.{clinic_id}.{clean_redis_key}')
        redis_client.client.delete(f'property.v3.config.item.clinic.{clinic_id}.{clean_redis_key}.v1')
        if clean_redis_key.rfind('.') == -1:
            break
        clean_redis_key = clean_redis_key[:clean_redis_key.rfind('.')]


def delete_exam_goods(env, headers, installGoods, region_name):
    url = f'http://{regionRpcHost(region_name, env)}/api/v2/examination-goods/examinations/{installGoods["id"]}?{int(time.time() * 1000)}'
    logging.info("url = {}".format(url))
    delete_goods__rsp = json.loads(
        requests.delete(
            url=url,
            headers={
                'cis-chain-id': headers['cis-chain-id'],
                'cis-clinic-id': headers['cis-clinic-id'] if headers['cis-view-mode'] == '1' else headers['cis-chain-id'],
                'cis-employee-id': headers['cis-employee-id'],
                'cis-clinic-type': headers['cis-clinic-type'] if headers['cis-view-mode'] == '1' else '1',
                'cis-view-mode': headers['cis-view-mode'],
                'cis-his-type': headers['cis-his-type'],
            }
        ).content.decode('utf-8')
    )
    logging.info("delete_goods_rsp = {}".format(json.dumps(delete_goods__rsp, ensure_ascii=False, indent=2)))


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--env', help='环境 dev/test/prod')
    parser.add_argument('--region-id', help='region-id ShangHai:1/HangZhou:2')
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--clinic-id', help='诊所id')
    parser.add_argument('--device-name', help='设备名称/型号/UUID')
    parser.add_argument('--device-no', help='设备编号')
    parser.add_argument('--match-mode', type=int, help='匹配模式 0-精确匹配 1-模糊匹配')
    parser.add_argument('--op-type', type=int, help='操作 0-绑定 1-解绑')
    args = parser.parse_args()
    if not args.env or not args.region_id or not args.chain_id or not args.clinic_id or not args.device_name:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id, args.clinic_id, args.device_name, args.device_no, args.match_mode, args.op_type, args.env, region_name(args.region_id))


if __name__ == '__main__':
    main()
