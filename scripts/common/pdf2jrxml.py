import logging
import uuid
import math
from base64 import b64encode
from collections import Iterable

from pdfminer.high_level import extract_pages
from pdfminer.layout import LTTextBox, LTLine, LTRect, LTCurve, LTFigure, LTImage

logging.getLogger().setLevel(logging.DEBUG)

"""
https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff00000000146808c695534004/inspect/cat_sOPxPgz6MJZ2.jpeg
https://cis-images-dev.oss-cn-shanghai.aliyuncs.com/prescription-sign/EbTZLqepFwzQPfjkNhhEFA9FCATJuYsC_1716536781474
https://cis-images-dev.oss-cn-shanghai.aliyuncs.com/prescription-sign/EbTZLqepFwzQPfjkNhhEFA9FCATJuYsC_1716536781474
"""

jrxml_template = f"""<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.21.3.final using JasperReports Library version 6.21.3-4a3078d20785ebe464f18037d738d12fc98c13cf  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="pdf2jrxml" pageWidth="595" pageHeight="842" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="{uuid.uuid4()}">
	{{jrxml_template_parameter}}
	<detail>
		<band height="842" splitType="Stretch">
			{{jrxml_template_detail}}
		</band>
	</detail>
</jasperReport>
"""


def font_color(ncolor):
    return '#{:02x}{:02x}{:02x}'.format(math.ceil(ncolor[0] * 255), math.ceil(ncolor[1] * 255), math.ceil(ncolor[2] * 255))


def get_font(element):
    if hasattr(element, 'fontname') and hasattr(element, 'graphicstate'):
        ncolor = element.graphicstate.ncolor
        return {'font_name': element.fontname.split('+')[1], 'font_size': math.ceil(element.size), 'font_color': font_color(ncolor)}
    if isinstance(element, Iterable):
        font_items = []
        for e in element:
            font_items.append(get_font(e))
        font_names = set([font_item['font_name'] for font_item in font_items if font_item is not None])
        font_sizes = set([font_item['font_size'] for font_item in font_items if font_item is not None])
        font_colors = set([font_item['font_color'] for font_item in font_items if font_item is not None])
        if len(font_names) == 1 and len(font_sizes) == 1 and len(font_colors) == 1:
            return font_items[0]
        # 计算平均字体大小
        return {'font_name': font_names.pop(), 'font_size': math.ceil(sum(font_sizes) / len(font_sizes)), 'font_color': font_colors.pop()}
    return None


def get_element_box(element, page_width, page_height):
    x = math.ceil(element.x0)
    y = math.ceil(page_height - element.y1)
    width = math.ceil(min(element.width, page_width - x))
    height = math.ceil(min(element.height, page_height - y))
    return x, y, width, height


if __name__ == '__main__':
    pdf_file_path = '/Users/<USER>/IdeaProjects/PreGrayDataMigrate/scripts/common/HospitalInspection2.pdf'

    jrxml_template_parameter = []
    jrxml_template_detail = []
    page_width = 595
    page_height = 842
    for page in extract_pages(pdf_file_path):
        for element in page:
            element_box = get_element_box(element, page_width, page_height)
            if isinstance(element, LTTextBox):
                font = get_font(element)
                jrxml_template_detail.append(f"""
            <staticText>
                <reportElement x="{element_box[0]}" y="{element_box[1]}" width="{element_box[2]}" height="{element_box[3]}" forecolor="{font['font_color']}" uuid="{uuid.uuid4()}"/>
                <textElement>
                    <font fontName="{font['font_name']}" size="{font['font_size']}"/>
                </textElement>
                <text><![CDATA[{element.get_text().strip()}]]></text>
            </staticText>
                """)
            elif isinstance(element, LTLine) or isinstance(element, LTRect) or isinstance(element, LTCurve):
                if isinstance(element, LTCurve) and element.stroking_color is None:
                    logging.warning(f"Element {element_box} has no stroking color, skip it.")
                    continue
                body_data = f"""<reportElement x="{element_box[0]}" y="{element_box[1]}" width="{element_box[2]}" height="{element_box[3]}" backcolor="{font_color(element.non_stroking_color) if element.non_stroking_color else '#FFFFFF'}" uuid="{uuid.uuid4()}"/>
				<graphicElement>
					<pen lineWidth="{element.linewidth if element.linewidth > 0 else 0.1:.1f}" lineColor="{font_color(element.stroking_color) if element.stroking_color else '#FFFFFF'}"/>
				</graphicElement>"""
                if isinstance(element, LTLine):
                    jrxml_template_detail.append(f"""
            <line>
                {body_data}
            </line>
                """)
                elif isinstance(element, LTRect) or isinstance(element, LTCurve):
                    jrxml_template_detail.append(f"""
            <rectangle>
                {body_data}
            </rectangle>
                """)
            elif isinstance(element, LTFigure):
                for image in element:
                    image_url = f"image_url_{len(jrxml_template_parameter) + 1}"
                    jrxml_template_parameter.append(f"""<parameter name="{image_url}" class="java.lang.String"/>
    """)
                    logging.info(f"{image_url} = \n{b64encode(image.stream.get_rawdata()).decode('utf-8')}")
                    if isinstance(image, LTImage):
                        jrxml_template_detail.append(f"""
            <image>
                <reportElement x="{element_box[0]}" y="{element_box[1]}" width="{element_box[2]}" height="{element_box[3]}" uuid="{uuid.uuid4()}"/>
				<!-- <imageExpression><![CDATA[new ByteArrayInputStream(org.apache.commons.codec.binary.Base64.decodeBase64($P{{{image_url}}}.getBytes()))]]></imageExpression> -->
				<imageExpression><![CDATA[$P{{{image_url}}}]]></imageExpression>
            </image>
                        """)

    jrxml_template = jrxml_template.format(jrxml_template_parameter=''.join(jrxml_template_parameter), jrxml_template_detail=''.join(jrxml_template_detail))
    with open('/Users/<USER>/IdeaProjects/PreGrayDataMigrate/scripts/common/HospitalInspection2.jrxml', 'w') as f:
        f.write(jrxml_template)
