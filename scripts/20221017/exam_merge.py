#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import json
import db
import argparse
import logging

logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)


class UpdateExecutor(object):

    def __init__(self):
        self._db_client_cache = {}

    def _get_db_client(self, cluster, database):
        k = '{0}-{1}'.format(cluster, database)
        if k in self._db_client_cache.keys():
            return self._db_client_cache[k]

        db_client = db.DBClient(cluster, database)
        self._db_client_cache[k] = db_client
        return db_client

    # 1、补充samplePipeName
    def fillGoodsBizExtensionsSamplePipeName(self, chain_id):
        try:
            sql = """
            update v2_goods a
                inner join (select biz_extensions -> '$.samplePipe',
                                   md5(biz_extensions -> '$.samplePipe') as m5,
                                   biz_extensions -> '$.samplePipeName'  as samplePipeName
                            from v2_goods
                            where type = 3
                              and organ_id = '{chain_id}'
                              and sub_type = 1
                              and status = 1
                              and biz_extensions is not null
                              and json_type(biz_extensions) != 'NULL'
                              and biz_extensions -> '$.samplePipe' is not null
                              and biz_extensions -> '$.samplePipe' != ''
                              and json_type(biz_extensions -> '$.samplePipe') != 'NULL'
                              and biz_extensions -> '$.samplePipeName' is not null
                            group by md5(biz_extensions -> '$.samplePipe')) as b 
                on md5(a.biz_extensions -> '$.samplePipe') = b.m5 and a.organ_id = '{chain_id}'
            set biz_extensions = json_set(biz_extensions, '$.samplePipeName', b.samplePipeName)
            where type = 3
              and organ_id = '{chain_id}'
              and sub_type = 1
              and status = 1
              and biz_extensions is not null
              and json_type(biz_extensions) != 'NULL'
              and biz_extensions -> '$.samplePipe' is not null
              and biz_extensions -> '$.samplePipe' != ''
              and json_type(biz_extensions -> '$.samplePipe') != 'NULL'
              and biz_extensions -> '$.samplePipeName' is null;
            """.format(chain_id=chain_id)
            print(sql)
            sc_goods_client = db.DBClient('abc_cis_stock', 'abc_cis_goods')
            sc_goods_client.execute(sql)
            #sc_goods_client.commit()
        except Exception as e:
            print(e)
            #sc_goods_client.rollback()

    # # 2、x头管替换成x头管
    # def replaceGoodsBizExtensionsSamplePipeName(self, chain_id):
    #     try:
    #         sql = """
    #                 update v2_goods
    #                 set biz_extensions = json_set(biz_extensions, '$.samplePipeName',
    #                                               REPLACE(biz_extensions ->> '$.samplePipeName', '头', '头'))
    #                 where type = 3
    #                 and organ_id = '{chain_id}'
    #                   and sub_type = 1
    #                   and status = 1
    #                   and biz_extensions is not null
    #                   and json_type(biz_extensions) != 'NULL'
    #                   and biz_extensions -> '$.samplePipe' is not null
    #                   and biz_extensions -> '$.samplePipe' != ''
    #                   and json_type(biz_extensions -> '$.samplePipe') != 'NULL'
    #                   and biz_extensions -> '$.samplePipeName' is not null;
    #                 """.format(chain_id=chain_id)
    #         print(sql)
    #         sc_goods_client = db.DBClient('abc_cis_stock', 'abc_cis_goods')
    #         sc_goods_client.execute(sql)
    #         # sc_goods_client.commit()
    #     except Exception as e:
    #         print(e)
    #         # sc_goods_client.rollback()

    # 3、插入samplePipeId
    def insertGoodsBizExtensionsSamplePipeId(self, chain_id):
        try:
            sc_goods_client = db.DBClient('abc_cis_stock', 'abc_cis_goods')
            for samplePipeName in ['紫头管', '红头管', '黄头管', '蓝头管', '绿头管', '灰头管', '黑头管', '取样器', '拭子']:
                samplePipeId = sc_goods_client.fetchone("select substr(uuid_short(), 4) as uuid")['uuid']
                sql = """update v2_goods set biz_extensions = json_set(biz_extensions, '$.samplePipeId', '{samplePipeId}') where status = 1 and organ_id = '{organId}' and biz_extensions -> '$.samplePipeName' = '{samplePipeName}'
                                """.format(samplePipeId=samplePipeId, organId=chain_id, samplePipeName=samplePipeName)
                print(sql)
                sc_goods_client.execute(sql)
                
            # sc_goods_client.commit()
        except Exception as e:
            print(e)
            # sc_goods_client.rollback()

    def insertGoodsExaminationSamplePipe(self, chain_id):
        try:
            sql = """
                    insert into v2_goods_examination_sample_pipe(id,
                                                                           chain_id,
                                                                           code,
                                                                           name,
                                                                           color,
                                                                           additive,
                                                                           capacity,
                                                                           unit,
                                                                           item_category,
                                                                           is_deleted,
                                                                           created_by,
                                                                           created,
                                                                           last_modified_by, last_modified)
                    SELECT biz_extensions ->> '$.samplePipeId',
                           organ_id,
                           case biz_extensions ->> '$.samplePipeName'
                               when '紫头管' then '#1'
                               when '红头管' then '#2'
                               when '黄头管' then '#3'
                               when '蓝头管' then '#4'
                               when '绿头管' then '#5'
                               when '灰头管' then '#6'
                               when '黑头管' then '#7'
                               when '取样器' then '#8'
                               when '拭子' then '#9'
                               else ''
                               end,
                           case biz_extensions ->> '$.samplePipeName'
                               when '紫头管' then '紫色管'
                               when '红头管' then '红色管'
                               when '黄头管' then '黄色管'
                               when '蓝头管' then '蓝色管'
                               when '绿头管' then '绿色管'
                               when '灰头管' then '灰色管'
                               when '黑头管' then '黑色管'
                               when '取样器' then '取样器'
                               when '拭子' then '拭子'
                               else ''
                               end,
                           biz_extensions ->> '$.samplePipe',
                           case biz_extensions ->> '$.samplePipeName'
                               when '紫头管' then 'EDTA-K2'
                               when '红头管' then ''
                               when '黄头管' then '促凝剂'
                               when '蓝头管' then '3.2%枸橼酸钠'
                               when '绿头管' then '肝素锂'
                               when '灰头管' then '氟化钠+草酸钙'
                               when '黑头管' then '3.2%枸橼酸钠'
                               when '取样器' then ''
                               when '拭子' then ''
                               else ''
                               end,
                           case biz_extensions ->> '$.samplePipeName'
                               when '紫头管' then 2
                               when '红头管' then 5
                               when '黄头管' then 5
                               when '蓝头管' then 2
                               when '绿头管' then 5
                               when '灰头管' then 5
                               when '黑头管' then 5
                               when '取样器' then 10
                               when '拭子' then 5
                               else ''
                               end,
                           'ml',
                           case biz_extensions ->> '$.samplePipeName'
                               when '紫头管' then 1
                               when '红头管' then 2
                               when '黄头管' then 3
                               when '蓝头管' then 1
                               when '绿头管' then 1
                               when '灰头管' then 1
                               when '黑头管' then 1
                               when '取样器' then 6
                               when '拭子' then 4
                               else ''
                               end,
                           0,
                           '00000000000000000000000000000000',
                           CURRENT_TIMESTAMP(),
                           '00000000000000000000000000000000',
                           CURRENT_TIMESTAMP()
                    from v2_goods
                    where type = 3
                    and organ_id = '{chain_id}'
                      and sub_type = 1
                      and status = 1
                      and biz_extensions is not null
                      and json_type(biz_extensions) != 'NULL'
                      and biz_extensions -> '$.samplePipe' is not null
                      and biz_extensions -> '$.samplePipe' != ''
                      and json_type(biz_extensions -> '$.samplePipe') != 'NULL'
                    group by organ_id, biz_extensions -> '$.samplePipeId'
                    order by organ_id;
                    """.format(chain_id=chain_id)
            print(sql)
            sc_goods_client = db.DBClient('abc_cis_stock', 'abc_cis_goods')
            sc_goods_client.execute(sql)
            # sc_goods_client.commit()
        except Exception as e:
            print(e)
            # sc_goods_client.rollback()

    # 5、es同步
    def examinationSheetEsSync(self, chain_id):
        try:
            sql = """
                    update v2_examination_sheet set created = date_add(created, interval 1 SECOND ) where chain_id = '{chain_id}'
                    """.format(chain_id=chain_id)
            print(sql)
            sc_exam_client = db.DBClient('abc_cis_outpatient', 'abc_cis_examination')
            sc_exam_client.execute(sql)
            # sc_exam_client.commit()
        except Exception as e:
            print(e)
            # sc_exam_client.rollback()


    def execute_chain(self, chain_id):
        self.fillGoodsBizExtensionsSamplePipeName(chain_id)
        self.insertGoodsBizExtensionsSamplePipeId(chain_id)
        self.insertGoodsExaminationSamplePipe(chain_id)
        # self.examinationSheetEsSync(chain_id)


def run(chain_id):
    rgt = UpdateExecutor()
    rgt.execute_chain(chain_id)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)


if __name__ == '__main__':
    main()
