#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import json
import db
import argparse
import logging

logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)


class UpdateExecutor(object):

    def __init__(self):
        self._db_client_cache = {}

    def _get_db_client(self, cluster, database):
        k = '{0}-{1}'.format(cluster, database)
        if k in self._db_client_cache.keys():
            return self._db_client_cache[k]

        db_client = db.DBClient(cluster, database)
        self._db_client_cache[k] = db_client
        return db_client

    #多药房刷goods
    def updateGoods(self, chain_id):
        try:
            sqls = [
                # 刷入库单上 代煎代配的药房类型
                '''
                update v2_goods_stock_in_order
                set pharmacy_type = 2
                where pharmacy_no = 1 and chain_id = '{chainId}';
                ''',
                # 刷入库条目上 代煎代配的药房类型
                '''
                update v2_goods_stock_in_order as o inner join v2_goods_stock_in as i 
                    on o.id = i.order_id and o.pharmacy_no = 1
                set i.pharmacy_no   = 1,
                    i.pharmacy_type =2
                where o.pharmacy_no = 1 and o.chain_id = '{chainId}';
                ''',
                #刷GoodsStat的 goods类型Id
                '''
                update v2_goods_stat as gs inner join v2_goods as g 
                on gs.goods_id = g.id and gs.chain_id = g.organ_id
                set gs.type_id = g.type_id
                where g.organ_id = '{chainId}'; 
                ''',
                '''
                update v2_goods_stat as gs  
                set pharmacy_type = 2
                where chain_id = '{chainId}' and gs.pharmacy_no = 1; 
                ''',
                '''
                update v2_goods_stat as gs  
                set pharmacy_no = -1
                where chain_id = '{chainId}' and gs.clinic_id = '' and gs.pharmacy_no = 0; 
                '''

            ]
            goods_db_client = self._get_db_client('abc_cis_stock', 'abc_cis_goods')
            for sql in sqls:
                goods_db_client.execute(sql.format(chainId=chain_id))
        except Exception as e:
            print(e)


    #更新收费单
    def updateCharge(self,chain_id):
        try:
            adb_client = self._get_db_client('adb', 'abc_cis_charge')
            charge_db_client = self._get_db_client('abc_cis_charge', 'abc_cis_charge')

            sql = '''
                select id
                from v2_charge_form
                where chain_id = '{chainId}'
                and pharmacy_type = 2 and source_form_type = 6 
                ;
            '''.format(chainId = chain_id)
            formIdList = adb_client.fetchall(sql);

            if formIdList is not None and len(formIdList) > 0:
                formIdStr = ','.join([ ''' '{0}' '''.format(item['id']) for item in formIdList ])
                updateChargeFormItemsql ='''
                    update v2_charge_form_item
                        set  pharmacy_type = 2 ,  pharmacy_no = 1 
                        where chain_id = '{chainId}'
                          and charge_form_id in ({formIdStr});
                '''.format(chainId = chain_id,formIdStr = formIdStr)
                print updateChargeFormItemsql
                charge_db_client.execute(updateChargeFormItemsql)
                updateChargeFormSql ='''
                    update v2_charge_form
                        set  pharmacy_type = 2 
                        where chain_id = '{chainId}'
                          and id in ({formIdStr});
                '''.format(chainId = chain_id,formIdStr = formIdStr)
                print updateChargeFormSql
                charge_db_client.execute(updateChargeFormSql)
        except Exception as e:
            print(e)


    def updateChainOutPatient(self,chain_id):
        basic_db_client = self._get_db_client('abc_cis_mixed', 'abc_cis_basic')

        #拼clinicIdList
        organs = basic_db_client.fetchall(''' select id from organ where parent_id = '{chainId}' '''.format(chainId=chain_id))

        for organ in organs:
            self.updateOutpatient(organ['id'])


#刷门诊单数据
    def updateOutpatient(self,clinic_id):
        try:
            adb_client = self._get_db_client('adb', 'abc_cis_outpatient')
            outpatient_db_client = self._get_db_client('abc_cis_outpatient', 'abc_cis_outpatient')

            sql = '''
                select id
                from v2_outpatient_prescription_form
                where clinic_id = '{clinicId}'
                and pharmacy_type = 2 ;
            '''.format(clinicId=clinic_id)
            formIdList = adb_client.fetchall(sql);

            if formIdList is not None and len(formIdList) > 0:
                formIdStr = ','.join([ ''' '{0}' '''.format(item['id']) for item in formIdList ])
                updateFormItemsql ='''
                    update v2_outpatient_prescription_form_item
                        set  pharmacy_type = 2 ,  pharmacy_no = 1 
                        where  prescription_form_id in ({formIdStr});
                '''.format(formIdStr = formIdStr)
                outpatient_db_client.execute(updateFormItemsql)
        except Exception as e:
            print(e)

    #更新收费单
    def updateDispense(self,chain_id):
        try:
            adb_client = self._get_db_client('adb', 'abc_cis_dispensing')
            dispensing_db_client = self._get_db_client('abc_cis_stock_zip', 'abc_cis_dispensing')

            sql = '''
                select id
                from v2_dispensing_sheet
                where chain_id = '{chainId}'
                and pharmacy_type = 2 ;
            '''.format(chainId = chain_id)
            formIdList = adb_client.fetchall(sql);

            if formIdList is not None and len(formIdList) > 0:
                formIdStr = ','.join([ ''' '{0}' '''.format(item['id']) for item in formIdList ])
                updateChargeFormItemsql ='''
                    update v2_dispensing_form_item
                        set  pharmacy_type = 2 ,  pharmacy_no = 1 
                        where chain_id = '{chainId}'
                          and dispensing_sheet_id in ({formIdStr});
                '''.format(chainId = chain_id,formIdStr = formIdStr)
                dispensing_db_client.execute(updateChargeFormItemsql)
        except Exception as e:
            print(e)

    def execute_chain(self, chain_id):
        #先刷Goods数据
        self.updateGoods(chain_id)

        #检查开了代煎代配药房没有 总共128个连锁有开代煎代配
        goods_db_client = self._get_db_client('abc_cis_stock', 'abc_cis_goods')
        openVirtualChainId = goods_db_client.fetchall(''' select distinct  chain_id as id from v2_goods_pharmacy where chain_id = '{chainId}' and type = 2; '''.format(chainId = chain_id))
        if openVirtualChainId:
            #刷代煎代配要发个上 收费 门诊 发药的 药房号和药房类型
            self.updateCharge(chain_id)
            self.updateChainOutPatient(chain_id)
            self.updateDispense(chain_id)


def run(chain_id):
    rgt = UpdateExecutor()
    rgt.execute_chain(chain_id)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)


if __name__ == '__main__':
    main()
