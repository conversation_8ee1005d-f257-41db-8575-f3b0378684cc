#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys 
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db 
import argparse
import requests

def run_for_goods(chain_id):
    # call rpc
    basic_db_client = db.DBClient('abc_cis_mixed', 'abc_cis_basic')

    hisType = basic_db_client.fetchone(
        ''' select his_type from organ where id = '{chainId}' '''.format(chainId=chain_id))
    print(hisType)
    if hisType:
        if hisType['his_type'] == 1:
            url = """http://pre.rpc.abczs.cn/rpc/v3/goods/processing/init/{chainId}""".format(chainId=chain_id)
            rsp = requests.post(url)
            print(rsp.content)


def run(chain_id):
    run_for_goods(chain_id)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)

if __name__ == '__main__':
    main()
