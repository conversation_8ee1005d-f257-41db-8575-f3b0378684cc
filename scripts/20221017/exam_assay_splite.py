#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import json
import db
import argparse
import logging

logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)


class UpdateExecutor(object):

    def __init__(self):
        self._db_client_cache = {}

    def _get_db_client(self, cluster, database):
        k = '{0}-{1}'.format(cluster, database)
        if k in self._db_client_cache.keys():
            return self._db_client_cache[k]

        db_client = db.DBClient(cluster, database)
        self._db_client_cache[k] = db_client
        return db_client

    def updateClinicEmployeeModule(self, chain_id):
        try:
            sql = """
                update clinic_employee a
                set a.module_ids = concat(a.module_ids, ',', 17)
                where a.chain_id = '{chain_id}'
                    and !find_in_set(17, module_ids)
                    and (find_in_set(9, module_ids) or find_in_set(0, module_ids));
            """.format(chain_id=chain_id)
            print(sql)
            basic_goods_client = db.DBClient('abc_cis_mixed', 'abc_cis_basic')
            basic_goods_client.execute(sql)
            sql = """
            update clinic_employee
            set roles = json_array_append(roles, '$', 10)
            where json_contains(roles, '3')
                and !json_contains(roles, '10')
                and chain_id ='{chain_id}'
            """.format(chain_id=chain_id)
            print(sql)
            basic_goods_client = db.DBClient('abc_cis_mixed', 'abc_cis_basic')
            basic_goods_client.execute(sql)
        except Exception as e:
            print(e)



    def execute_chain(self, chain_id):
        self.updateClinicEmployeeModule(chain_id)


def run(chain_id):
    rgt = UpdateExecutor()
    rgt.execute_chain(chain_id)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)


if __name__ == '__main__':
    main()
