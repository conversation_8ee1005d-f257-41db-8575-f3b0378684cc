#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
import argparse

default_id = '00000000000000000000000000000000'


def update_roles(chain_id, region_name):
    basic_db_client = DBClient(region_name, 'abc_cis_mixed', 'abc_cis_basic', 'prod', True)

    # 初始化管理员权限
    basic_db_client.execute("""
    UPDATE clinic_employee
    SET roles            = JSON_ARRAY_APPEND(roles, '$', 0),
        last_modified_by = '{defaultId}',
        last_modified    = now()
    WHERE chain_id = '{chainId}'
      and role_id = 1
      and !JSON_CONTAINS(roles, '0')
      and roles is not null;
    """.format(chainId=chain_id, defaultId=default_id))


def run(chain_id, region_name):
    update_roles(chain_id, region_name)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id, args.region_name)


if __name__ == '__main__':
    main()
