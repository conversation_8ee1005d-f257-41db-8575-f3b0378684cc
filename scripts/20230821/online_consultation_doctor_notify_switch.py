#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
import argparse

default_id = '00000000000000000000000000000000'


def insert_switch(chain_id, region_name):
    basic_db_client = DBClient(region_name, 'abc_cis_account_base', 'abc_cis_message', 'prod', True)

    # 初始化管理员权限
    basic_db_client.execute("""
    INSERT INTO v2_message_switch_config (id, chain_id, `group`, `key`, name, notify_desc, notify_minutes_before, sms_template, wx_template,
                                              weapp_template, sms_switch, wx_switch, weapp_switch, is_deleted, created_by, created,
                                              last_modified_by, last_modified)
    SELECT
        substr(uuid_short(), 4),
        chain_id as chain_id,
        '网络问诊', 'consultation.begin',
        '医生接单通知',
        '患者发起在线问诊并成功支付后，提醒医生接单，并及时回复',
        1,
        '{{"types": [800001], "templates": [{{"name":"接单通知", "content": "【ABC数字医疗云】曾超医生晚上好！刚刚收到患者陆思源发起的一次在线问诊咨询（已支付）。患者正在线上等候，请您及时回复哦！"}}]}}',
        null,
        null,
        0,
        0,
        0,
        0,
        '00000000000000000000000000000000',
        current_timestamp,
        '00000000000000000000000000000000',
        current_timestamp
    FROM v2_message_switch_config
    WHERE is_deleted<>1 and chain_id = '{chainId}'
    GROUP BY chain_id;
    """.format(chainId=chain_id))


def run(chain_id, region_name):
    insert_switch(chain_id, region_name)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id, args.region_name)


if __name__ == '__main__':
    main()
