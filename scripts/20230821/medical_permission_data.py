#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
import sys
import argparse
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from itertools import groupby

from idwork import IdWork

from multizone.db import DBClient


default_id = '00000000000000000000000000000000'




class UpdateData:
    chain_id = None
    short_url_db_client = None
    emr_db_client = None
    scform_db_client = None
    id_work = None
    patient_tag_template_chain_id = None
    type = None
    businessType = None

    def __init__(self, region_name):

        self.region_name = region_name
        self.short_url_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_shorturl', 'prod', True)
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', 'prod', True)



    def run(self):
        self.flush_medicine_config()

    ###

    def flush_medicine_config(self):


        # organHisType = self.basic_db_client.fetchone(
        #     ''' select his_type from organ where id = '{chainId}' '''.format(chainId=self.chain_id))
        # if organHisType:
        #     if organHisType['his_type'] == 0:
        #         print("organHisType is 0")
        #         return

        self.id_work = IdWork(self.short_url_db_client, False)
        self.id_work.config()

        select_catalogue_sql = '''
        select id,name,clinic_id,chain_id,source_id,type from v2_short_url_catalogue where  level = 1 and is_deleted = 0 and type in (1,21)
        '''.format(chain_id = self.chain_id ,type=self.type)
        catalogues = self.short_url_db_client.fetchall(select_catalogue_sql)


        select_permission_sql = '''
        select business_id from v2_clinic_business_permission_data
        '''.format(chain_id = self.chain_id ,type=self.type)
        permissions = self.basic_db_client.fetchall(select_permission_sql)



        permissions_map = {}
        for permission in permissions:
            permissions_map[str(permission['business_id'])] = str(permission['business_id'])

        for catalogue in catalogues:
            permission = permissions_map.get(str(catalogue.get('id')))
            if permission is not None:
                continue

            medicalInsertSql = self.createInsertSql(catalogue["chain_id"],catalogue["clinic_id"],catalogue["id"])
            self.basic_db_client.execute(medicalInsertSql)



    def createInsertSql(self,chain_id, clinic_id, business_id):
        permission = "[{\"scopePath\": \"/clinic\", \"scopeValues\": [\"/" + clinic_id + "\"]}]"
        sql = '''
            INSERT INTO v2_clinic_business_permission_data (id, chain_id, clinic_id, permission_id, business_id, permission_value, is_deleted, created, created_by, last_modified, last_modified_by) 
            VALUES ({id}, '{chain_id}', '{clinic_id}', 1, '{business_id}', '{permission_value}', 0, now(), '0', now(), '0');

            '''.format(id=self.id_work.getUIDLong(), chain_id=chain_id, clinic_id=clinic_id,
                       business_id=business_id,
                       permission_value=permission)
        return sql





def main():
    # parser = argparse.ArgumentParser()
    # parser.add_argument('--chain-id', help='连锁id')
    # parser.add_argument('--region-name', help='分区名字可直接查配置')
    # args = parser.parse_args()
    # if not args.chain_id:
    #     parser.print_help()
    #     sys.exit(-1)


    region = 'ShangHai'
    # chain_id = 'ffffffff00000000347ca188a4614001'
    updateData = UpdateData(region)
    updateData.run()



if __name__ == '__main__':
    main()
