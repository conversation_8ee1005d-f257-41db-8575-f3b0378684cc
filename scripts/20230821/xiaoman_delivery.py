#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON>yon<PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
import sys
import argparse
import copy
from itertools import groupby


CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from idwork import IdWork
from multizone.db import DBClient



def update_data():
    db_client = DBClient('ShangHai', 'abc_cis_mixed', 'abc_bis', 'prod', True)
    id_work = IdWork(db_client, False)
    id_work.config()
    orderDeliveryRuleSql = '''
          select * from v1_order_delivery_rule where business_scope_id = 3 and is_deleted = 0;
        '''
    deliveryRules = db_client.fetchall(orderDeliveryRuleSql)
    idsSet = {str(deliveryRule['id']) for deliveryRule in deliveryRules}
    deliveryRulesIds = ','.join(idsSet)

    orderDeliveryRuleAddressSql = '''
                select * from v1_order_delivery_rule_address where is_deleted = 0 and delivery_rule_id in ({});
              '''.format(deliveryRulesIds)
    orderDeliveryRuleAddress = db_client.fetchall(orderDeliveryRuleAddressSql)
    addressGroups = groupby(orderDeliveryRuleAddress, groupByOrderDeliveryRuleAddress)
    addressGroupMap = {}
    for key, group in addressGroups:
        addressGroupMap[key] = list(group)


    for rule in deliveryRules:
        rule1 = copy.copy(rule)
        rule1['id'] = id_work.getUIDLong()
        rule1['business_scope_id'] = 9
        sql1 = createRuleSql(rule1)
        db_client.execute(sql1)

        rule2 = copy.copy(rule)
        rule2['id'] = id_work.getUIDLong()
        rule2['business_scope_id'] = 10
        sql2 = createRuleSql(rule2)
        db_client.execute(sql2)
        address = addressGroupMap.get(rule.get("id"))

        for add in address:
            add1 = copy.copy(add)
            add1["id"] = id_work.getUIDLong()
            add1["delivery_rule_id"] = rule1['id']
            addSql1 = createAddressSql(add1)
            db_client.execute(addSql1)
            add2 = copy.copy(add)
            add2["id"] = id_work.getUIDLong()
            add2["delivery_rule_id"] = rule2['id']
            addSql2 = createAddressSql(add2)
            db_client.execute(addSql2)
            print()

def groupByOrderDeliveryRuleAddress(address):
    return address.get("delivery_rule_id")

def createRuleSql(rule):
    sql = '''
    INSERT INTO v1_order_delivery_rule 
    (id, vendor_id, name, company_id, business_scope_id, type, permanent_price, free_postage_type, free_postage_unit_count, free_postage_unit, description, is_deleted, created, created_by, last_modified, last_modified_by, payer_types) 
    VALUES ({id}, {vendor_id}, '{name}', {company_id}, {business_scope_id}, {type}, {permanent_price}, {free_postage_type}, {free_postage_unit_count}, {free_postage_unit},{description}, 0, now(), 0, now(), 0, {payer_types});

    '''.format(id=rule['id'], vendor_id=rule['vendor_id'], name=rule['name'], company_id=rule['company_id'],
               business_scope_id=rule['business_scope_id'], type=rule['type'], permanent_price=rule['permanent_price'] if rule['permanent_price'] is not None else 'NULL',
               free_postage_type=rule['free_postage_type'] if rule['free_postage_type'] is not None else 'NULL', free_postage_unit_count=rule['free_postage_unit_count']  if rule['free_postage_unit_count'] is not None else 'NULL', free_postage_unit='\''+rule['free_postage_unit']+'\'' if rule['free_postage_unit'] is not None else 'NULL' ,description='\'' + rule['description'] +'\'' if rule['description'] is not None else 'NULL',payer_types='\'' + rule['payer_types'] +'\'' if rule['payer_types'] is not None else 'NULL')
    return sql


def createAddressSql(rule):
    sql = '''
    INSERT INTO v1_order_delivery_rule_address 
    (id, vendor_id, delivery_rule_id, node_type, address_province_id, address_province_name, address_city_id, address_city_name, address_district_id, address_district_name, is_deleted, created, created_by, last_modified, last_modified_by) 
    VALUES ({id}, {vendor_id}, {delivery_rule_id}, {node_type}, {address_province_id}, {address_province_name}, {address_city_id}, {address_city_name}, {address_district_id}, {address_district_name}, 0, now(), 1, now(), 1);


    '''.format(id=rule['id'], vendor_id=rule['vendor_id'], delivery_rule_id=rule['delivery_rule_id'],
               node_type=rule['node_type'],
               address_province_id='\'' + rule['address_province_id'] + '\'' if rule[
                                                                                'address_province_id'] is not None else 'NULL',
               address_province_name='\'' + rule['address_province_name'] + '\'' if rule['address_province_name'] is not None else 'NULL',
               address_city_id='\'' + rule['address_city_id'] + '\'' if rule['address_city_id'] is not None else 'NULL',
               address_city_name='\'' + rule['address_city_name'] + '\'' if rule['address_city_name'] is not None else 'NULL',
               address_district_id='\'' + rule['address_district_id'] + '\'' if rule['address_district_id'] is not None else 'NULL',
               address_district_name='\'' + rule['address_district_name'] + '\'' if rule['address_district_name'] is not None else 'NULL')
    return sql


def main():
    # parser = argparse.ArgumentParser()
    # parser.add_argument('--chain-id', help='连锁id')
    # args = parser.parse_args()
    # if not args.chain_id:
    #     parser.print_help()
    #     sys.exit(-1)

    update_data()


if __name__ == '__main__':
    main()
