#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
import sys
import argparse
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from itertools import groupby

from idwork import IdWork

from multizone.db import DBClient


default_id = '00000000000000000000000000000000'




class UpdateData:
    chain_id = None
    short_url_db_client = None
    emr_db_client = None
    scform_db_client = None
    id_work = None
    patient_tag_template_chain_id = None
    type = None
    businessType = None

    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.short_url_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_shorturl', 'prod', True)
        self.emr_db_client = DBClient(self.region_name, 'scrm_hospital', 'abc_his_emr', 'prod', True)
        self.scform_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_form', 'prod', True)
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', 'prod', True)



    def run(self):
        self.flush_medicine_config()

    ###

    def flush_medicine_config(self):


        organHisType = self.basic_db_client.fetchone(
            ''' select his_type from organ where id = '{chainId}' '''.format(chainId=self.chain_id))
        if organHisType:
            if organHisType['his_type'] == 0:
                print("organHisType is 0")
                return

        self.id_work = IdWork(self.emr_db_client, False)
        self.id_work.config()

        select_catalogue_sql = '''
        select id,name,clinic_id,chain_id,source_id,type from v2_short_url_catalogue where chain_id = '{chain_id}'  and level = 1 and is_deleted = 0 and type in (1,21)
        '''.format(chain_id = self.chain_id ,type=self.type)
        catalogues = self.short_url_db_client.fetchall(select_catalogue_sql)
        for catalogue in catalogues:
            medicalInsertSql = self.createMedicalInsertSql(catalogue)
            self.emr_db_client.execute(medicalInsertSql)

        #更新文书记录的文书id
        updateMedicalDocSql = self.updateMedicalDoc(self.chain_id)
        self.emr_db_client.execute(updateMedicalDocSql)

        select_template_catalogue_sql = '''
                select id,name,clinic_id,chain_id,source_id,parent_id,owner_type, owner_id from v2_short_url_catalogue where chain_id = '{chain_id}' and level = 2 and is_deleted = 0  and type in (1,21)
                '''.format(chain_id = self.chain_id, type=self.type)
        template_catalogues = self.short_url_db_client.fetchall(select_template_catalogue_sql)

        template_catalogues_map = {}
 #更新模版文书id  更新文书记录id
        for template in template_catalogues:
            template_catalogues_map[str(template['id'])] = template
            updateMedicalTemplateSql = self.updateMedicalTemplate(template['id'],template['parent_id'], template['owner_type'])
            self.emr_db_client.execute(updateMedicalTemplateSql)



        select_template_emr_sql = '''
        select id,catalogue_id,owner_type, owner_id, business_type,chain_id,clinic_id,medical_id from v1_emr_medical_doc_template  where chain_id = '{chain_id}' and is_deleted = 0 order by medical_id, created desc
        '''.format(chain_id = self.chain_id)
        templates = self.emr_db_client.fetchall(select_template_emr_sql)
        #设置新版本母版数据
        templateGroups = groupby(templates, lambda template: template['medical_id'])

        self.emr_db_client.execute('''
        update v1_emr_medical_doc_template set  is_default = 0  where chain_id = '{chain_id}'

        '''.format(chain_id=self.chain_id))
        for key, group in templateGroups:

            templateList = list(group)

            if len(templateList) !=0 :
                templateFirst = templateList[0]
                update_parent_template_sql = '''
                 update v1_emr_medical_doc_template set catalogue_id = medical_id, is_default = 1, last_modified = now() where id = {id}
                '''.format(id = templateFirst['id'])
                self.emr_db_client.execute(update_parent_template_sql)

                #删除母版对应目录数据
                update_parent_template_sql = ''' 
                                update v2_short_url_catalogue set  is_deleted = 1,last_modified = now()  where id = '{id}' and  chain_id = '{chain_id}' and type in (1,21)
                               '''.format(id = templateFirst['catalogue_id'], chain_id = self.chain_id)
                self.short_url_db_client.execute(update_parent_template_sql)


        #模版权限刷新，将现有的模版权限ownerType更新到 clinic的权限结构中
        for template in templates:
            catalogue = template_catalogues_map.get(template.get('catalogue_id'))
            if catalogue is None:
                continue

            template['owner_id'] = catalogue['owner_id']
            template['owner_type'] = catalogue['owner_type']
            if  template['owner_id'] is None or len(template['owner_id']) == 0:
                continue
            insertClinicPermissionSql = self.insertClinicPermission(template)
            self.basic_db_client.execute(insertClinicPermissionSql)

        self.basic_db_client.execute(self.updatePermission(self.chain_id))


        #给没有模版的文书设置一个


    def createMedicalInsertSql(self,catalogue):
        sql = '''
       INSERT INTO v1_emr_medical (id, clinic_id, chain_id, name, source_id, catalogue_id, business_type, is_deleted, created, created_by, last_modified, last_modified_by) 
VALUES ({id}, '{clinic_id}', '{chain_id}', '{name}', {source_id}, '{id}', {type}, 0, now(), '0', now(), '0');

        '''.format(id=catalogue['id'], clinic_id=catalogue['clinic_id'], chain_id=catalogue['chain_id'], name=catalogue['name'],
                   source_id= catalogue['source_id']  if catalogue['source_id'] is not None else 'NULL',
                   catalogue_id=catalogue['id'], type= 0 if catalogue['type'] == 1 else 1)
        return sql;

    def updateMedicalTemplate(self,catalogue_id,catalogue_parent_id, owner_type):
        sql = '''
            update v1_emr_medical_doc_template set medical_id = {medical_id}, owner_type = {owner_type} where catalogue_id = '{catalogue_id}'
        '''.format(medical_id=catalogue_parent_id, catalogue_id = catalogue_id, owner_type = owner_type)
        return  sql

    def updateMedicalDoc(self, chain_id):
        sql = '''
             update v1_emr_medical_doc set medical_id = catalogue_id where chain_id = '{chain_id}'
         '''.format(chain_id=chain_id)
        return sql

    def insertClinicPermission(self,template):


        ownerType = '/clinic'
        if template['owner_type'] == 2:
           ownerType = '/employee'
        elif template['owner_type'] == 3:
           ownerType = '/department'

        permission = "[{\"scopePath\": \"" + ownerType + "\", \"scopeValues\": [\"/"+ template['owner_id']+"\"]}]"

        sql = '''
        INSERT INTO v2_clinic_business_permission_data (id, chain_id, clinic_id, permission_id, business_id, permission_value, is_deleted, created, created_by, last_modified, last_modified_by) 
        VALUES ({id}, '{chain_id}', '{clinic_id}', 3, '{business_id}', '{permission_value}', 0, now(), '0', now(), '0');

        '''.format(id = self.id_work.getUIDLong(), chain_id = template['chain_id'], clinic_id = template['clinic_id'], business_id = template['id'],
                   permission_value = permission)
        return sql

    def updatePermission(self, chain_id):
        sql = '''
        update v2_clinic_business_permission_data set permission_id = 1 where permission_id = 2 and chain_id = '{chain_id}'
        '''.format(chain_id = chain_id)
        return sql




def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    #
    # region = 'ShangHai'
    # chain_id = 'ffffffff00000000347ca188a4614001'
    updateData = UpdateData(args.region_name, args.chain_id)
    updateData.run()



if __name__ == '__main__':
    main()
