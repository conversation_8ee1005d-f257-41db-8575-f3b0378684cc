#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
import sys
import argparse
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))



from multizone.db import DBClient


default_id = '00000000000000000000000000000000'




class UpdateData:
    chain_id = None
    patient_db_client = None
    basic_db_client = None
    id_work = None
    patient_tag_template_chain_id = None

    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_property', 'prod', True)

    def run(self):
        self.flush_medicine_config()

    def flush_medicine_config(self):
        # 口腔数据初始化-患者标签初始化
        self.basic_db_client.execute("""
            insert into abc_cis_property.v2_property_config_item(id, `key`, value, scope, scope_id, created_by, last_modified_by, key_first,
                                    key_second, key_third, key_fourth, key_fifth, v2_scope_id)
            select substr(uuid_short(), 5),
                   replace(`key`, 'print.medicineTag', 'print.medicineTag.common'),
                   value,
                   scope,
                   scope_id,
                   created_by,
                   last_modified_by,
                   key_first,
                   key_second,
                   'common',
                   key_third,
                   key_fifth,
                   v2_scope_id
            from v2_property_config_item v2pci
            where `key` in ('print.medicineTag.chineseRemark', 'print.medicineTag.mobile', 'print.medicineTag.mobileType',
                            'print.medicineTag.printDate', 'print.medicineTag.title', 'print.medicineTag.westernDays')
              and scope = 'clinic'
              and v2_scope_id collate utf8mb4_0900_ai_ci in (select id
                                                             from abc_cis_basic.organ
                                                             where parent_id = '{chainId}')
            on duplicate key update value = values(value);
                """.format(chainId=self.chain_id))

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    updateData = UpdateData(args.region_name, args.chain_id)
    updateData.run()



if __name__ == '__main__':
    main()
