#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import logging
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.rediscli import RedisClient
import argparse
import requests

default_id = '00000000000000000000000000000000'

def run(region_name, chain_id, env):
    adb_client = DBClient(region_name, 'ods', 'abc_cis_examination', env, True)
    exam_client = DBClient(region_name, 'abc_cis_outpatient', 'abc_cis_examination', env, True)
    stmts = adb_client.fetchall(f"""
    select CONCAT('update v2_examination_item set item_type = ', g.sub_type, ' where id = \\'', i.id, '\\';') as stmt
    from v2_examination_item i
             inner join abc_cis_goods.v2_goods g on i.goods_id = g.id
    where chain_id = '{chain_id}' and i.is_deleted = 0 and sub_type != 1;
    """)
    if len(stmts) == 0:
        logging.info('stmts is empty')
        return
    for stmt in stmts:
        exam_client.execute(stmt['stmt'])



def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.region_name, args.chain_id, 'prod')
    # run('ShangHai', 'ffffffff00000000146808c695534000', 'dev')


if __name__ == '__main__':
    main()
