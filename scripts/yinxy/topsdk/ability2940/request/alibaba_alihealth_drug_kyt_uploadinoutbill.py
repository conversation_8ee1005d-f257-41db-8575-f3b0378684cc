from typing import List
from topsdk.client import BaseRequest
from topsdk.util import convert_struct_list, convert_basic_list, convert_struct, convert_basic
from datetime import datetime


class AlibabaAlihealthDrugKytUploadinoutbillRequest(BaseRequest):

    def __init__(
            self,
            bill_code: str = None,
            bill_time: str = None,
            bill_type: int = None,
            physic_type: int = None,
            ref_user_id: str = None,
            agent_ref_user_id: str = None,
            from_user_id: str = None,
            to_user_id: str = None,
            dest_user_id: str = None,
            oper_ic_code: str = None,
            oper_ic_name: str = None,
            warehouse_id: str = None,
            drug_id: str = None,
            trace_codes: str = None,
            client_type: str = None,
            return_reason_code: str = None,
            return_reason_des: str = None,
            cancel_reason_code: str = None,
            cancel_reason_des: str = None,
            executer_name: str = None,
            executer_code: str = None,
            superviser_name: str = None,
            superviser_code: str = None,
            from_address: str = None,
            to_address: str = None,
            from_bill_code: str = None,
            order_code: str = None,
            from_person: str = None,
            to_person: str = None,
            dis_ref_ent_id: str = None,
            dis_ent_id: str = None,
            qu_receivable: float = None,
            xt_is_check: str = None,
            xt_check_code: str = None,
            xt_check_code_desc: str = None,
            drug_list_json: str = None,
            ass_ref_ent_id: str = None,
            ass_ent_id: str = None
    ):
        self._bill_code = bill_code
        self._bill_time = bill_time
        self._bill_type = bill_type
        self._physic_type = physic_type
        self._ref_user_id = ref_user_id
        self._agent_ref_user_id = agent_ref_user_id
        self._from_user_id = from_user_id
        self._to_user_id = to_user_id
        self._dest_user_id = dest_user_id
        self._oper_ic_code = oper_ic_code
        self._oper_ic_name = oper_ic_name
        self._warehouse_id = warehouse_id
        self._drug_id = drug_id
        self._trace_codes = trace_codes
        self._client_type = client_type
        self._return_reason_code = return_reason_code
        self._return_reason_des = return_reason_des
        self._cancel_reason_code = cancel_reason_code
        self._cancel_reason_des = cancel_reason_des
        self._executer_name = executer_name
        self._executer_code = executer_code
        self._superviser_name = superviser_name
        self._superviser_code = superviser_code
        self._from_address = from_address
        self._to_address = to_address
        self._from_bill_code = from_bill_code
        self._order_code = order_code
        self._from_person = from_person
        self._to_person = to_person
        self._dis_ref_ent_id = dis_ref_ent_id
        self._dis_ent_id = dis_ent_id
        self._qu_receivable = qu_receivable
        self._xt_is_check = xt_is_check
        self._xt_check_code = xt_check_code
        self._xt_check_code_desc = xt_check_code_desc
        self._drug_list_json = drug_list_json
        self._ass_ref_ent_id = ass_ref_ent_id
        self._ass_ent_id = ass_ent_id

    def get_api_name(self):
        return "alibaba.alihealth.drug.kyt.uploadinoutbill"

    def to_dict(self):
        request_dict = {}
        if self._bill_code is not None:
            request_dict["bill_code"] = convert_basic(self._bill_code)

        if self._bill_time is not None:
            request_dict["bill_time"] = convert_basic(self._bill_time)

        if self._bill_type is not None:
            request_dict["bill_type"] = convert_basic(self._bill_type)

        if self._physic_type is not None:
            request_dict["physic_type"] = convert_basic(self._physic_type)

        if self._ref_user_id is not None:
            request_dict["ref_user_id"] = convert_basic(self._ref_user_id)

        if self._agent_ref_user_id is not None:
            request_dict["agent_ref_user_id"] = convert_basic(self._agent_ref_user_id)

        if self._from_user_id is not None:
            request_dict["from_user_id"] = convert_basic(self._from_user_id)

        if self._to_user_id is not None:
            request_dict["to_user_id"] = convert_basic(self._to_user_id)

        if self._dest_user_id is not None:
            request_dict["dest_user_id"] = convert_basic(self._dest_user_id)

        if self._oper_ic_code is not None:
            request_dict["oper_ic_code"] = convert_basic(self._oper_ic_code)

        if self._oper_ic_name is not None:
            request_dict["oper_ic_name"] = convert_basic(self._oper_ic_name)

        if self._warehouse_id is not None:
            request_dict["warehouse_id"] = convert_basic(self._warehouse_id)

        if self._drug_id is not None:
            request_dict["drug_id"] = convert_basic(self._drug_id)

        if self._trace_codes is not None:
            request_dict["trace_codes"] = convert_basic(self._trace_codes)

        if self._client_type is not None:
            request_dict["client_type"] = convert_basic(self._client_type)

        if self._return_reason_code is not None:
            request_dict["return_reason_code"] = convert_basic(self._return_reason_code)

        if self._return_reason_des is not None:
            request_dict["return_reason_des"] = convert_basic(self._return_reason_des)

        if self._cancel_reason_code is not None:
            request_dict["cancel_reason_code"] = convert_basic(self._cancel_reason_code)

        if self._cancel_reason_des is not None:
            request_dict["cancel_reason_des"] = convert_basic(self._cancel_reason_des)

        if self._executer_name is not None:
            request_dict["executer_name"] = convert_basic(self._executer_name)

        if self._executer_code is not None:
            request_dict["executer_code"] = convert_basic(self._executer_code)

        if self._superviser_name is not None:
            request_dict["superviser_name"] = convert_basic(self._superviser_name)

        if self._superviser_code is not None:
            request_dict["superviser_code"] = convert_basic(self._superviser_code)

        if self._from_address is not None:
            request_dict["from_address"] = convert_basic(self._from_address)

        if self._to_address is not None:
            request_dict["to_address"] = convert_basic(self._to_address)

        if self._from_bill_code is not None:
            request_dict["from_bill_code"] = convert_basic(self._from_bill_code)

        if self._order_code is not None:
            request_dict["order_code"] = convert_basic(self._order_code)

        if self._from_person is not None:
            request_dict["from_person"] = convert_basic(self._from_person)

        if self._to_person is not None:
            request_dict["to_person"] = convert_basic(self._to_person)

        if self._dis_ref_ent_id is not None:
            request_dict["dis_ref_ent_id"] = convert_basic(self._dis_ref_ent_id)

        if self._dis_ent_id is not None:
            request_dict["dis_ent_id"] = convert_basic(self._dis_ent_id)

        if self._qu_receivable is not None:
            request_dict["qu_receivable"] = convert_basic(self._qu_receivable)

        if self._xt_is_check is not None:
            request_dict["xt_is_check"] = convert_basic(self._xt_is_check)

        if self._xt_check_code is not None:
            request_dict["xt_check_code"] = convert_basic(self._xt_check_code)

        if self._xt_check_code_desc is not None:
            request_dict["xt_check_code_desc"] = convert_basic(self._xt_check_code_desc)

        if self._drug_list_json is not None:
            request_dict["drug_list_json"] = convert_basic(self._drug_list_json)

        if self._ass_ref_ent_id is not None:
            request_dict["ass_ref_ent_id"] = convert_basic(self._ass_ref_ent_id)

        if self._ass_ent_id is not None:
            request_dict["ass_ent_id"] = convert_basic(self._ass_ent_id)

        return request_dict

    def get_file_param_dict(self):
        file_param_dict = {}
        return file_param_dict
