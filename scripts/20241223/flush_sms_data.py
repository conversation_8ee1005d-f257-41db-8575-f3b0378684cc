"""
刷新sms data
"""
import argparse
import os
import sys
from datetime import date, timedelta

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient

chainSql = '''
    select id, address_province_id, name
    from organ
    where id = '{chainId}'
'''

GANG_AO_TAI_IDS = ["710000", "810000", "820000"]

PRICE_CHINA = 0.06
PRICE_GANG_AO_TAI = 0.5

GANG_AO_TAI_SMS_AREA_TYPES = '[0,1]'

updateMessageConfigSql = '''
update v2_message_config set sms_balance = sms_quota * {unitPirce}, sms_area_types = '{smsAreaTypes}' where chain_id = '{chainId}';
'''

updateMessageConfigSql1 = '''
update v2_message_config set sms_area_types = '{smsAreaTypes}' where chain_id = '{chainId}';
'''

updateMessageLogSql = '''
update v2_message_single_log set fee = message_count * {unitPirce} where chain_id = '{chainId}' and created >= '{beginDate}' and message_channel = 0;
'''

updateMessageSendStatSql = '''
update v2_message_send_stat set sms_amount = sms_count * {unitPirce} where chain_id = '{chainId}';
'''

getMessageConfigSql = '''
select sms_quota, sms_balance, chain_id from v2_message_config where chain_id = '{chainId}' limit 1;
'''


class UpdateData:
    def __init__(self, region_name, chain_id, env):
        self.chain_id = chain_id
        self.region_name = region_name
        self.message_client = DBClient(self.region_name, 'abc_cis_account_base', 'abc_cis_message', env, True)
        self.basic_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', env, True)

        self.beginTime = date.today() - timedelta(days=180)
        self.endTime = date.today() + timedelta(days=1)

    def run(self):
        self.updateMessageSendCount()

    def updateMessageSendCount(self):
        try:
            # 刷短信余额信息
            organ = self.basic_client.fetchone(chainSql.format(chainId=self.chain_id))
            if organ is None:
                return

            smsAreaTypes = '[0]'
            smsUnitPrice = PRICE_CHINA
            organAddressProvinceId = organ['address_province_id']
            if (organAddressProvinceId is not None) and (organAddressProvinceId in GANG_AO_TAI_IDS):
                smsUnitPrice = PRICE_GANG_AO_TAI
                smsAreaTypes = GANG_AO_TAI_SMS_AREA_TYPES

            # 刷余额
            messageConfig = self.message_client.fetchone(getMessageConfigSql.format(chainId=self.chain_id))
            if messageConfig is None:
                return
            smsBalance = messageConfig['sms_balance']
            smsQuota = messageConfig['sms_quota']

            if (smsBalance <= 0) and (smsQuota > 0):
                rows = self.message_client.execute(
                    updateMessageConfigSql.format(unitPirce=smsUnitPrice, chainId=self.chain_id,
                                                  smsAreaTypes=smsAreaTypes))
                print("updateMessageConfigSql ", rows)
            else:
                rows = self.message_client.execute(
                    updateMessageConfigSql1.format(chainId=self.chain_id, smsAreaTypes=smsAreaTypes))
                print("updateMessageConfigSql1 ", rows)


            # 刷流水
            # beginDate = self.beginTime
            # while beginDate < self.endTime:
            #     endDate = beginDate + timedelta(days=61)
            #     if endDate > self.endTime:
            #         endDate = self.endTime
            #     rows = self.message_client.execute(updateMessageLogSql.format(unitPirce=smsUnitPrice,chainId=self.chain_id, beginDate=beginDate, endDate=endDate))
            #     currentDate = beginDate
            #     print(rows, ' ' + self.chain_id + ' ' + currentDate.strftime("%Y-%m-%d") + ' ' + endDate.strftime("%Y-%m-%d"))
            #     beginDate = endDate

            rows = self.message_client.execute(
                updateMessageLogSql.format(unitPirce=smsUnitPrice, chainId=self.chain_id, beginDate=self.beginTime))
            print(rows,
                  ' ' + self.chain_id + ' ' + self.beginTime.strftime("%Y-%m-%d"))

            # 刷统计
            rows = self.message_client.execute(
                updateMessageSendStatSql.format(unitPirce=smsUnitPrice, chainId=self.chain_id))
            print("updateMessageSendStatSql ", rows)

        except Exception as e:
            print(self.chain_id)
            print(e)


def main():
#     basic_db_client = DBClient('ShangHai', 'abc_cis_basic', 'abc_cis_basic', 'test', True)
#     chainSql = '''
#         select id
#         from organ
#         where node_type = 1;
#     '''
#     chains = basic_db_client.fetchall(chainSql)
#     for chain in chains:
#         updateData = UpdateData('ShangHai', chain['id'], 'test')
#         updateData.run()

    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    updateData = UpdateData(args.region_name, args.chain_id, args.env)
    updateData.run()

    # region = 'ShangHai'
    # chain_id = '6a869c22abee4ffbaef3e527bbb70aeb'
    # updateData = UpdateData(region, chain_id, 'dev')
    # updateData.run()


if __name__ == '__main__':
    main()
