import argparse
import os
import sys

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.db import DBClient


class UpdateData:
    chain_id = None
    his_emr_wdb_client = None
    outpatient_db_client = None
    env = None

    def __init__(self, env, region_name, chain_id):
        self.env = env
        self.chain_id = chain_id
        self.region_name = region_name
        self.his_emr_wdb_client = DBClient(self.region_name, 'scrm_hospital', 'abc_his_emr', 'prod', True)
        self.outpatient_db_client = DBClient(self.region_name, 'abc_cis_outpatient', 'abc_cis_outpatient', 'prod', True)


    def run(self):

        businessIds = self.his_emr_wdb_client.fetchall('''
        select distinct business_id from v1_emr_medical_doc where business_type = 1 and is_deleted = 0 and chain_id = '{chainId}'
        '''.format(chainId=self.chain_id))

        #如果businessIds为空，则直接返回
        if not businessIds or len(businessIds) == 0:
            return

        ids = ','.join([''' '{0}' '''.format(item['business_id']) for item in businessIds])
        update_sql= '''
                  update v2_outpatient_sheet set outpatient_flag = outpatient_flag | 4 where  chain_id = '{chainId}' and id in ({ids}) 
                '''.format(chainId=self.chain_id, ids=ids)
        print(update_sql);
        self.outpatient_db_client.execute(update_sql)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    updateData = UpdateData(args.env, args.region_name, args.chain_id)
    # updateData = UpdateData("args.env", "ShangHai", "ffffffff00000000146808c695534000")
    updateData.run()


if __name__ == '__main__':
    main()
