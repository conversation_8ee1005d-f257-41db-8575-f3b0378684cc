"""
【统计信息架构调整【二】】
https://www.tapd.cn/47644659/prong/stories/view/1147644659001060182

刷数据规则如下
左边是原来的 module_id， 右边是新的 module_id
1018 -> 1010
5250419 -> 1010
1017 -> 5250420
5250407 -> 5250420
5250408 -> 5250420
5250417 -> 5250420
5250418 -> 5250420
6002 -> 5250514
5250504 -> 5250514
6011 -> 5250514
2006 -> 5251301
5250305 -> 5251301
6003 -> 52510
5250505 -> 52510
6007 -> 52511
5250507 -> 52511
6004 -> 52512
5250511 -> 52512
206 -> 5250514,52510,52511,6005,6010,52512
202 -> 2001,5251301,2012,2003,2013,209,2009,2008,207
52503 -> 5251301,5250301,5250303
"""
import argparse
import os
import sys
from datetime import date, timedelta

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient

class UpdateData:
    def __init__(self, region_name, chain_id, env):
        self.chain_id = chain_id
        self.region_name = region_name
        self.basic_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', env, True)


    def run(self):
        self.updateEmployeeModule()

    def updateEmployeeModule(self):
        # 只处理医院
        organ = self.basic_client.fetchone("""
            select *
            from organ
            where id = '{chainId}' and his_type = 100;
        """.format(chainId=self.chain_id))

        if not organ:
            return

        # 查询门店下所有的员工
        clinic_employees = self.basic_client.fetchall("""
            select id, module_ids
            from clinic_employee
            where chain_id = '{chainId}'
        """.format(chainId=self.chain_id))

        if not clinic_employees:
            return

        for clinic_employee in clinic_employees:
            before_module_ids = clinic_employee['module_ids'].split(',')
            after_module_ids = list(before_module_ids)
            has_change = False
            for module_id in after_module_ids:
                if module_id == '1018':
                    # 统计分析-营收统计-住院结算统计 -> 统计分析-营收统计-收费明细
                    after_module_ids.remove('1018')
                    after_module_ids.append('1010')
                    has_change = True
                elif module_id == '5250419':
                    # 统计分析-营收统计-体检收费统计 -> 统计分析-营收统计-收费明细
                    after_module_ids.remove('5250419')
                    after_module_ids.append('1010')
                    has_change = True
                elif module_id == '1017':
                    # 统计分析-营收统计-门诊患者费用查询 -> 统计分析-营收统计-收费单据
                    after_module_ids.remove('1017')
                    after_module_ids.append('5250420')
                    has_change = True
                elif module_id == '5250407':
                    # 统计分析-营收统计-住院患者费用查询 -> 统计分析-营收统计-收费单据
                    after_module_ids.remove('5250407')
                    after_module_ids.append('5250420')
                    has_change = True
                elif module_id == '5250408':
                    # 统计分析-营收统计-门诊欠还款统计 -> 统计分析-营收统计-收费单据
                    after_module_ids.remove('5250408')
                    after_module_ids.append('5250420')
                    has_change = True
                elif module_id == '5250417':
                    #统计分析-营收统计-住院计费统计 -> 统计分析-营收统计-收费单据
                    after_module_ids.remove('5250417')
                    after_module_ids.append('5250420')
                    has_change = True
                elif module_id == '5250418':
                    #统计分析-营收统计-住院押金统计 -> 统计分析-营收统计-收费单据
                    after_module_ids.remove('5250418')
                    after_module_ids.append('5250420')
                    has_change = True
                elif module_id == '6002':
                    # 统计分析-业绩统计-门诊开单业绩 -> 刷统计分析-业绩统计-医生业绩
                    after_module_ids.remove('6002')
                    after_module_ids.append('5250514')
                    has_change = True
                elif module_id == '5250504':
                    # 统计分析-业绩统计-住院开单业绩 -> 刷统计分析-业绩统计-医生业绩
                    after_module_ids.remove('5250504')
                    after_module_ids.append('5250514')
                    has_change = True
                elif module_id == '6011':
                    # 统计分析-业绩统计-转诊业绩 -> 刷统计分析-业绩统计-医生业绩
                    after_module_ids.remove('6011')
                    after_module_ids.append('5250514')
                    has_change = True
                elif module_id == '2006':
                    # 统计分析-门诊医务统计-门诊日志 -> 统计分析-医务统计-就诊日志
                    after_module_ids.remove('2006')
                    after_module_ids.append('5251301')
                    has_change = True
                elif module_id == '5250305':
                    # 统计分析-住院医务统计-住院日志 -> 统计分析-医务统计-就诊日志 
                    after_module_ids.remove('5250305')
                    after_module_ids.append('5251301')
                    has_change = True
                elif module_id == '6003':
                    # 统计分析-业绩统计-门诊执行业绩 -> 统计分析-营收统计-护士业绩
                    after_module_ids.remove('6003')
                    after_module_ids.append('52510')
                    has_change = True
                elif module_id == '5250505':
                    # 统计分析-业绩统计-住院护理业绩 -> 统计分析-营收统计-护士业绩
                    after_module_ids.remove('5250505')
                    after_module_ids.append('52510')
                    has_change = True
                elif module_id == '6007':
                    # 统计分析-业绩统计-门诊药房业绩 -> 统计分析-业绩统计-药房业绩
                    after_module_ids.remove('6007')
                    after_module_ids.append('52511')
                    has_change = True
                elif module_id == '5250507':
                    # 统计分析-业绩统计-住院药房业绩 -> 统计分析-业绩统计-药房业绩
                    after_module_ids.remove('5250507')
                    after_module_ids.append('52511')
                    has_change = True
                elif module_id == '6004':
                    # 统计分析-业绩统计-会员充值业绩 -> 统计分析-营收统计-充值业绩
                    after_module_ids.remove('6004')
                    after_module_ids.append('52512')
                    has_change = True
                elif module_id == '5250511':
                    # 统计分析-业绩统计-开卡充值业绩 -> 统计分析-营收统计-充值业绩
                    after_module_ids.remove('5250511')
                    after_module_ids.append('52512')
                    has_change = True
                elif module_id == '206':
                    # 统计分析-业绩统计 -> 统计分析-业绩统计-医生业绩,统计分析-营收统计-护士业绩,统计分析-业绩统计-药房业绩,统计分析-业绩统计-检验业绩,统计分析-业绩统计-检查业绩,统计分析-营收统计-充值业绩
                    after_module_ids.remove('206')
                    after_module_ids.append('5250514')
                    after_module_ids.append('52510')
                    after_module_ids.append('52511')
                    after_module_ids.append('6005')
                    after_module_ids.append('6010')
                    after_module_ids.append('52512')
                    has_change = True
                elif module_id == '202':
                    # 统计分析-门诊医务统计 -> 统计分析-医务统计-运营概况,统计分析-医务统计-就诊日志,统计分析-医务统计-医嘱执行统计,统计分析-医务统计-复诊分析,统计分析-医务统计-来源分析,统计分析-医务统计-慢特病分析,统计分析-医务统计-家庭医生分析,统计分析-医务统计-病种分析,统计分析-医务统计-随访分析
                    after_module_ids.remove('202')
                    after_module_ids.append('2001')
                    after_module_ids.append('5251301')
                    after_module_ids.append('2012')
                    after_module_ids.append('2003')
                    after_module_ids.append('2013')
                    after_module_ids.append('209')
                    after_module_ids.append('2009')
                    after_module_ids.append('2008')
                    after_module_ids.append('207')
                    has_change = True
                elif module_id == '52503':
                    # 统计分析-住院医务统计 -> 统计分析-医务统计-就诊日志,统计分析-医务统计-科室工作分析,统计分析-医务统计-医嘱分析
                    after_module_ids.remove('52503')
                    after_module_ids.append('5251301')
                    after_module_ids.append('5250301')
                    after_module_ids.append('5250303')
                    has_change = True
            
            if not has_change:
                continue

            # 去重
            after_module_ids = list(set(after_module_ids))

            # 对比时候，需要对比内容，一个一个对比是否真的有，不用保证顺序
            if sorted(before_module_ids) == sorted(after_module_ids):
                continue
            
            # 更新前备份原来的权限
            print(f"bak: update clinic_employee set module_ids = '{clinic_employee['module_ids']}' where id = '{clinic_employee['id']}'")
            self.basic_client.execute("""
                update clinic_employee
                set module_ids = '{moduleIds}'
                where id = '{employeeId}'
            """.format(moduleIds=','.join(after_module_ids), employeeId=clinic_employee['id']))



def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    updateData = UpdateData(args.region_name, args.chain_id, args.env)
    updateData.run()

if __name__ == '__main__':
    main()
