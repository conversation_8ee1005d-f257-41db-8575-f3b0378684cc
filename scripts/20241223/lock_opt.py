# -*- coding: utf-8 -*-
"""
@name: flush_goods_extend_data.py
@author: <PERSON><PERSON><PERSON>
@email: <EMAIL>
@date: 2024-07-04 23:21:52
"""
import argparse
import json
import sys
import requests
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import requests
from multizone.db import DBClient
from multizone.rediscli import RedisClient
from multizone.rpc import regionRpcHost



class UpdateData:
    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.goods_db_client = DBClient(self.region_name, 'abc_cis_stock', 'abc_cis_goods', 'prod', True)

    def flush_goods_storage_to_extend_data(self):
        sqls=[
            '''update v2_goods_stock_locking set opt_type = 1 where chain_id ='{chainId}' and stock_id is not null; ''',
                 '''
                 update v2_goods_stock set inner_has_stock = 1 where chain_id ='{chainId}' and (piece_count != 0 or package_count != 0); '''
        ]
        for sql in sqls:
            self.goods_db_client.execute(sql.format(chainId=self.chain_id))
        requests.get("""http://{rpcHost}/rpc/v3/goods/jenkins/clean-chain-redis-cache?chainId={chainId}""".format(chainId = self.chain_id,rpcHost=regionRpcHost(self.region_name)))


    def run(self):
        self.flush_goods_storage_to_extend_data()

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    update_data = UpdateData(args.region_name, args.chain_id)
    update_data.run()


if __name__ == '__main__':
    main()
