#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import logging
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.rediscli import RedisClient
import argparse
import requests

default_id = '00000000000000000000000000000000'


def mobileShebaoProperty(region_name,chain_id):
    property_db_client = DBClient(region_name, 'abc_cis_mixed', 'abc_cis_property', 'prod', True)
    adb_db_client =DBClient(region_name, 'ods', 'abc_cis_property', 'prod', True)

    # 老数据订单自动推送开关迁移
    property_sql = """
            select concat('insert ignore into v2_property_config_item(id, `key`, value, scope, is_deleted, created_by, created,',
              'last_modified_by, last_modified, key_first, key_second, key_third,key_fourth,key_fifth, v2_scope_id) values (substr(uuid_short(), 5),\''print.medicalDocuments.treatment.content.diagnosisTreatmentPrintUsage\'',',
              '\''1\'',',
              '\''clinic\'',',
              '0,', 
              '\''', v2pci.created_by, '\'',',
              'now(),',
              '\''',v2pci.last_modified_by, '\'',',
              'now(),',
              '\''print\'',',
              '\''medicalDocuments\'',',
              '\''treatment\'',',
              '\''content\'',',
              '\''diagnosisTreatmentPrintUsage\'',',
              '\''', v2pci.v2_scope_id, '\'');') as sql_item
            from v2_property_config_item v2pci
                     inner join abc_cis_basic.organ o
                                on ifnull(v2pci.v2_scope_id, v2pci.scope_id) = o.id  COLLATE utf8mb4_0900_ai_ci and
                                   v2pci.`key` = 'print.medicalDocuments.treatment.header.signPosition'
                                   and v2pci.value != '1'
            where o.status = 1
              and v2pci.is_deleted = 0
              and o.parent_id = '{chainId}';
    """.format(chainId = chain_id)
    property_insert_sql_res = adb_db_client.fetchall(property_sql)
    for stmt in property_insert_sql_res:
        sql = stmt['sql_item']
        if sql is None:
            continue
        print(sql)
        property_db_client.execute(sql)



def run(region_name,chain_id):
    mobileShebaoProperty(region_name,chain_id)

def main():

    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.region_name,args.chain_id)


if __name__ == '__main__':
    main()
