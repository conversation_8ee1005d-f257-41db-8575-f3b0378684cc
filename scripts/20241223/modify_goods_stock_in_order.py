# -*- coding: utf-8 -*-
"""
@name: flush_goods_extend_data.py
@author: <PERSON><PERSON><PERSON>
@email: <EMAIL>
@date: 2024-07-04 23:21:52
"""
import argparse
import json
import sys
import requests
import os
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import logging


logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.rediscli import RedisClient

from multizone.db import DBClient



class UpdateData:
    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.goods_db_client = DBClient(self.region_name, 'abc_cis_stock', 'abc_cis_goods', 'prod', True)
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', 'prod', True)

    def flush_goods_storage_to_extend_data(self):
        # 先查分页查处 v2_goods_stock_in_order的ID
        # 再根据ID更新v2_goods_stock_in
        selectIdSql = '''select id from v2_goods_stock_in_order where chain_id = '{chainId}' and original_in_order_id is null  limit 500'''.format(chainId=self.chain_id)
        #循环，知道没有500个
        while True:
            ids = self.goods_db_client.fetchall(selectIdSql)
            if len(ids) == 0:
                break
            #把ids拼接成sql in 的 字符串
            idsStr = ','.join([str(id['id']) for id in ids])
            self.goods_db_client.execute('''update v2_goods_stock_in
                                                                   set original_in_id = id,
                                                                           original_in_order_id = order_id
                                                                   where  chain_id ='{chainId}' and order_id in({idsStr});'''.format(chainId=self.chain_id, idsStr=idsStr))

            self.goods_db_client.execute('''update v2_goods_stock_in_order as o
                                                           set o.original_in_order_id = o.id,
                                                               o.external_flag        = IFNULL(o.import_flag, 0),
                                                               o.import_flag          = IF(IFNULL(o.import_flag, 0) & 0x01, 1, 0)
                                                           where o.original_in_order_id is null
                                                             and o.chain_id = '{chainId}' and id in({idsStr});'''.format(chainId=self.chain_id, idsStr=idsStr))




        sqls=[
            '''update v2_goods_stock_check_order as o
               set o.original_check_order_id = o.id
               where  o.chain_id = '{chainId}';''',
            '''update v2_goods_stock_out_order as o
               set o.original_out_order_id = o.id
               where o.original_out_order_id is null
                 and o.chain_id = '{chainId}';''',
            '''update v2_goods_stock_trans_order as o
               set o.original_trans_order_id = o.id
               where o.original_trans_order_id is null
                 and o.chain_id = '{chainId}';'''
        ]
        for sql in sqls:
            executeSql = sql.format(chainId=self.chain_id)
            logging.info(executeSql)
            self.goods_db_client.execute(executeSql)
            logging.info('execute sql success ')
        pass


    def run(self):
        self.flush_goods_storage_to_extend_data()

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    update_data = UpdateData(args.region_name, args.chain_id)
    update_data.run()


if __name__ == '__main__':
    main()
