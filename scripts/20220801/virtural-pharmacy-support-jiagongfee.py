#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys 
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db 
import argparse
import logging
logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)
import datetime
from dateutil.relativedelta import relativedelta


class UpdateGoodsType(object):

    def __init__(self):
        self._db_client_cache = {}


    def _get_db_client(self, cluster, database):
        k = '{0}-{1}'.format(cluster, database)
        if k in self._db_client_cache.keys():
            return self._db_client_cache[k]
        
        db_client = db.DBClient(cluster, database)
        self._db_client_cache[k] = db_client
        return db_client

    def update_promotion(self, chain_id):
        sqls = [
            #  挂号费
            '''
        update v2_promotion_discount_goods
        set pharmacy_type = is_air_pharmacy
        where chain_id = '{0}';
            ''',
            '''
            update v2_promotion_goods a inner join v2_promotion b on a.promotion_id = b.id and b.chain_id = '{0}'
            set pharmacy_type = is_air_pharmacy
            where b.chain_id = '{0}';
            ''',

            '''
            update v2_promotion_card_goods
            set pharmacy_type = is_air_pharmacy
            where chain_id = '{0}';
            '''
        ]

        promotion_db_client = self._get_db_client('abc_cis_charge', 'abc_cis_promotion')

        for sql in sqls:
            sql_f = sql.format(chain_id)
            print sql_f
            promotion_db_client.execute(sql_f)


    def execute_chain(self, chain_id):
        self.update_promotion(chain_id)


def run(chain_id):
    rgt = UpdateGoodsType()
    rgt.execute_chain(chain_id)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)

if __name__ == '__main__':
    main()
