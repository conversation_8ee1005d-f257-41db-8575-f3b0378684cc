#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys 
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db
import argparse
import requests
import logging

logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)


def run(chain_id):

    #检查未知设备刷成未知仪器
    db_client = db.DBClient('abc_cis_stock', 'abc_cis_goods')
    sql = '''
        update v2_goods_examination_device set name='未知仪器'  where chain_id ='{0}' and goods_sub_type = 2 and inner_flag = 1 and name ='未知设备' ;
    '''.format(chain_id)
    db_client.execute(sql)

    #检查单orderNo刷数据
    exam_db_client = db.DBClient('abc_cis_outpatient', 'abc_cis_examination')
    exam_sql = '''
        update v2_examination_sheet set order_no = bar_code where order_no is null and chain_id = '{0}';
    '''.format(chain_id)
    exam_db_client.execute(exam_sql)


    #调用rpc为检查设备的名字生成pinyin字段
    url = 'http://pre.rpc.abczs.cn/rpc/v3/goods/jenkins/refresh-exam-device-name-py/{0}'.format(chain_id)
    rsp = requests.put(url)
    logging.info('init device pinyin:{0}, rsp:{1}'.format(chain_id, rsp.content))


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)

if __name__ == '__main__':
    main()
