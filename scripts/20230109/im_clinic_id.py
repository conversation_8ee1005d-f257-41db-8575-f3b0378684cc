#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import json
import db
import argparse
import logging

logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)


class UpdateExecutor(object):

    def __init__(self):
        self._db_client_cache = {}

    def _get_db_client(self, cluster, database):
        k = '{0}-{1}'.format(cluster, database)
        if k in self._db_client_cache.keys():
            return self._db_client_cache[k]

        db_client = db.DBClient(cluster, database)
        self._db_client_cache[k] = db_client
        return db_client

    #多药房刷goods
    def updateGoods(self, chain_id):
        try:
            sqls = [
                # 刷入库单上 代煎代配的药房类型
                '''
                update abc_cis_im.v2_im_participant_message_sync a
    inner join abc_cis_im.v2_im_conversation b on a.conversation_id = b.id
set a.chain_id = b.chain_id
where b.chain_id='{chainId}';
                '''
            ]
            basic_db_client = self._get_db_client('abc_cis_account_base', 'abc_cis_im')

            for sql in sqls:
                basic_db_client.execute(sql.format(chainId=chain_id))
        except Exception as e:
            print(e)


    def execute_chain(self, chain_id):
        #先刷Goods数据
        self.updateGoods(chain_id)

def run(chain_id):
    rgt = UpdateExecutor()
    rgt.execute_chain(chain_id)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)


if __name__ == '__main__':
    main()
