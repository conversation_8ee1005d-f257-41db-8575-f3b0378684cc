"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.rpc import regionRpcHost
import argparse
import requests

default_id = '00000000000000000000000000000000'

def run(region_name, chain_id, env):
    abc_cis_invoice_client = DBClient(region_name, 'abc_cis_bill', 'abc_cis_invoice', env, True)
    invoice_sql = '''
        select id, invoice_area_code from v2_invoice_config
        where chain_id = '{chainId}' and invoice_supplier_id = 2 and invoice_area_code != 'neimenggu';
    '''.format( chainId = chain_id)
    invoiceList = abc_cis_invoice_client.fetchall(invoice_sql)

    for item in invoiceList:
        id = item['id']
        invoice = item['invoice_area_code']
        update_sql = None
        if invoice == 'anhui':
            # 安徽发票更新
            update_sql = '''
                update v2_invoice_config
                set mapping_config = '{{"outpatients": [{{"code": "9000111", "name": "挂号费", "feeTypeId": "5"}},{{"code": "9000107", "name": "西药费", "feeTypeId": "12"}}, {{"code": "9000108", "name": "中药费", "feeTypeId": "13"}}, {{"code": "9000108", "name": "中药费", "feeTypeId": "14"}}, {{"code": "9000108", "name": "中药费", "feeTypeId": "15"}}, {{"code": "9000109", "name": "中成药费", "feeTypeId": "16"}}, {{"code": "9000104", "name": "化验费", "feeTypeId": "20"}}, {{"code": "9000102", "name": "检查费", "feeTypeId": "21"}}, {{"code": "9000103", "name": "治疗费", "feeTypeId": "22"}}, {{"code": "9000103", "name": "治疗费", "feeTypeId": "23"}}, {{"code": "9000101", "name": "诊察费", "feeTypeId": "1001"}}, {{"code": "9000105", "name": "手术费", "feeTypeId": "1002"}},{{"code": "9000110", "name": "一般诊疗费", "feeTypeId": "1003"}},{{"code": "9000106", "name": "卫生材料费", "feeTypeId": "1004"}}], "defaultOutpatientMapping": {{"code": "9000112", "name": "其他门诊收费", "feeTypeId": "02"}}}}'
                where id = '{id}';
            '''.format( id = id )
        elif invoice == 'guangxi':
            # 广西发票更新
            update_sql = '''
                update v2_invoice_config
                set mapping_config = '{{"outpatients": [{{"code": "5010701", "name": "西药费", "feeTypeId": "12"}}, {{"code": "5010702", "name": "中药费", "feeTypeId": "13"}}, {{"code": "5010702", "name": "中药费", "feeTypeId": "14"}}, {{"code": "5010702", "name": "中药费", "feeTypeId": "15"}}, {{"code": "5010703", "name": "中成药费", "feeTypeId": "16"}}, {{"code": "50103", "name": "化验费", "feeTypeId": "20"}}, {{"code": "50102", "name": "检查费", "feeTypeId": "21"}}, {{"code": "50104", "name": "治疗费", "feeTypeId": "22"}}, {{"code": "50104", "name": "治疗费", "feeTypeId": "23"}}, {{"code": "50108", "name": "药事服务费", "feeTypeId": "34"}}, {{"code": "501111", "name": "门/急诊留观床位费", "feeTypeId": "58"}}, {{"code": "50101", "name": "诊查费", "feeTypeId": "1001"}}, {{"code": "50105", "name": "手术费", "feeTypeId": "1002"}}, {{"code": "50106", "name": "卫生材料费", "feeTypeId": "1004"}}], "defaultOutpatientMapping": {{"code": "50110", "name": "其他门诊收费", "feeTypeId": "02"}}}}'
                where id = '{id}';
            '''.format( id = id )
        elif invoice == 'chongqing':
            # 重庆发票更新
            update_sql = '''
                update v2_invoice_config
                set mapping_config = '{{"outpatients": [{{"code": "999090107", "name": "西药费", "feeTypeId": "12"}}, {{"code": "999090115", "name": "中药费", "feeTypeId": "13"}}, {{"code": "999090115", "name": "中药费", "feeTypeId": "14"}}, {{"code": "999090115", "name": "中药费", "feeTypeId": "15"}}, {{"code": "999090109", "name": "中成药费", "feeTypeId": "16"}}, {{"code": "999090103", "name": "化验费", "feeTypeId": "20"}}, {{"code": "999090102", "name": "检查费", "feeTypeId": "21"}}, {{"code": "999090104", "name": "治疗费", "feeTypeId": "22"}}, {{"code": "999090104", "name": "治疗费", "feeTypeId": "23"}}, {{"code": "999090101", "name": "诊察费", "feeTypeId": "1001"}}, {{"code": "999090105", "name": "手术费", "feeTypeId": "1002"}}, {{"code": "999090106", "name": "卫生材料费", "feeTypeId": "1004"}}], "defaultOutpatientMapping": {{"code": "999090111", "name": "一般诊疗费", "feeTypeId": "02"}}}}'
                where id = '{id}';
            '''.format(id)
        elif invoice == 'guizhou':
            # 贵州发票更新
            update_sql = '''
                update v2_invoice_config
                set mapping_config = '{{"outpatients": [{{"code": "90301011", "name": "挂号费", "feeTypeId": "5"}},{{"code": "90301007", "name": "西药费", "feeTypeId": "12"}}, {{"code": "90301008", "name": "中药费", "feeTypeId": "13"}}, {{"code": "90301008", "name": "中药费", "feeTypeId": "14"}}, {{"code": "90301008", "name": "中药费", "feeTypeId": "15"}}, {{"code": "90301009", "name": "中成药费", "feeTypeId": "16"}}, {{"code": "90301003", "name": "化验费", "feeTypeId": "20"}}, {{"code": "90301002", "name": "检查费", "feeTypeId": "21"}}, {{"code": "90301004", "name": "治疗费", "feeTypeId": "22"}}, {{"code": "90301004", "name": "治疗费", "feeTypeId": "23"}}, {{"code": "90301001", "name": "诊察费", "feeTypeId": "1001"}}, {{"code": "90301005", "name": "手术费", "feeTypeId": "1002"}},{{"code": "90301010", "name": "一般诊疗费", "feeTypeId": "1003"}},{{"code": "90301006", "name": "卫生材料费", "feeTypeId": "1004"}}], "defaultOutpatientMapping": {{"code": "90301099", "name": "其他门诊收费", "feeTypeId": "02"}}}}'
                where id = '{id}';
            '''.format( id = id )
        elif invoice == 'jiangsu':
            # 江苏发票更新
            update_sql = '''
                update v2_invoice_config
                set mapping_config = '{{"outpatients": [{{"code": "32080101", "name": "挂号费", "feeTypeId": "5"}},{{"code": "32080108", "name": "西药费", "feeTypeId": "12"}}, {{"code": "32080110", "name": "中药费", "feeTypeId": "13"}}, {{"code": "32080110", "name": "中药费", "feeTypeId": "14"}}, {{"code": "32080110", "name": "中药费", "feeTypeId": "15"}}, {{"code": "32080111", "name": "中成药费", "feeTypeId": "16"}}, {{"code": "32080104", "name": "化验费", "feeTypeId": "20"}}, {{"code": "32080103", "name": "检查费", "feeTypeId": "21"}}, {{"code": "32080105", "name": "治疗费", "feeTypeId": "22"}}, {{"code": "32080105", "name": "治疗费", "feeTypeId": "23"}}, {{"code": "32080102", "name": "诊察费", "feeTypeId": "1001"}}, {{"code": "32080106", "name": "手术费", "feeTypeId": "1002"}},{{"code": "32080112", "name": "一般诊疗费", "feeTypeId": "1003"}},{{"code": "32080107", "name": "卫生材料费", "feeTypeId": "1004"}}], "defaultOutpatientMapping": {{"code": "32080199", "name": "其他门诊收费", "feeTypeId": "02"}}}}'
                where id = '{id}';
            '''.format( id = id )
        elif invoice == 'liaoning':
            # 辽宁发票更新
            update_sql = '''
                update v2_invoice_config
                set mapping_config = '{{"outpatients": [{{"code": "C160", "name": "挂号费", "feeTypeId": "5"}},{{"code": "C101", "name": "西药费", "feeTypeId": "12"}}, {{"code": "C102", "name": "中药费", "feeTypeId": "13"}}, {{"code": "C102", "name": "中药费", "feeTypeId": "14"}}, {{"code": "C102", "name": "中药费", "feeTypeId": "15"}}, {{"code": "C103", "name": "中成药费", "feeTypeId": "16"}}, {{"code": "C106", "name": "化验费", "feeTypeId": "20"}}, {{"code": "C105", "name": "检查费", "feeTypeId": "21"}}, {{"code": "C107", "name": "治疗费", "feeTypeId": "22"}}, {{"code": "C107", "name": "治疗费", "feeTypeId": "23"}}, {{"code": "C129", "name": "床位费", "feeTypeId": "58"}},{{"code": "C166", "name": "诊察费", "feeTypeId": "1001"}}, {{"code": "C118", "name": "手术费", "feeTypeId": "1002"}},{{"code": "C108", "name": "卫生材料费", "feeTypeId": "1004"}}], "defaultOutpatientMapping": {{"code": "C187", "name": "其他费", "feeTypeId": "02"}}}}'
                where id = '{id}';
            '''.format( id = id )
        else:
            continue
        if update_sql is not None:
            print(update_sql)
            abc_cis_invoice_client.execute(update_sql)

    abc_cis_basic_client = DBClient(region_name, 'abc_cis_mixed', 'abc_cis_basic', env, True)
    region_organ_sql = '''
        select region_id from v2_clinic_region_organ where chain_id = '{chainId}'
    '''.format(chainId = chain_id)
    result = abc_cis_basic_client.fetchone(region_organ_sql)

    if result:
        abc_cis_goods_client = DBClient(region_name, 'abc_cis_stock', 'abc_cis_goods', env, True)
        region_id = result['region_id']
        goods_config_sql = '''
            update v2_goods_chain_config set region_id = '{regionId}' where chain_id = '{chainId}' and region_id = 0
        '''.format( regionId = region_id, chainId = chain_id )
        print(goods_config_sql)
        abc_cis_goods_client.execute(goods_config_sql)
        requests.get("""http://{rpcHost}/rpc/v3/goods/jenkins/clean-chain-redis-cache?chainId={chainId}"""
                     .format(chainId=chain_id, rpcHost=regionRpcHost(region_name)))

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    run(args.region_name, args.chain_id, args.env)

if __name__ == '__main__':
    main()
