"""
新增内置的患者来源-首次
"""
import argparse
import os
import sys

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.db import DBClient
from idwork import IdWork


class UpdateData:
    chain_id = None
    patient_wdb_client = None
    env = None
    id_work = None

    def __init__(self, env, region_name, chain_id):
        self.env = env
        self.chain_id = chain_id
        self.region_name = region_name
        self.patient_wdb_client = DBClient(region_name, 'abc_cis_account_base', 'abc_cis_patient', env, True)
        self.id_work = IdWork(self.patient_wdb_client, False)

    def run(self):
        # 查询是否存在【首次】来源，如果没有则插入一条
        source_types = self.patient_wdb_client.fetchall("""
            select *
            from v2_patient_source_type
            where chain_id = '{chainId}'
              and name = '首次'
              and parent_id is null
              and is_deleted = 0
              and status = 1
        """.format(chainId=self.chain_id))

        if source_types:
            return

        self.id_work.config()
        source_type_id = self.id_work.getUUID()
        self.patient_wdb_client.execute("""
            insert into v2_patient_source_type (id, chain_id, name, status, created_by, created, last_modified_by, last_modified, is_deleted, parent_id, level, related_type, related_id)
            values ('{id}', '{chainId}', '首次', 1, '00000000000000000000000000000000', current_timestamp, '00000000000000000000000000000000' , current_timestamp, 0, null, 1, 0, null);
        """.format(id=source_type_id, chainId=self.chain_id))


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    updateData = UpdateData(args.env, args.region_name, args.chain_id)
    updateData.run()


if __name__ == '__main__':
    main()
