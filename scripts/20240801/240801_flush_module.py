"""
出库统计展示科室情况权限调整
"""
import argparse
import os
import sys

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.db import DBClient


def execute_sql(client, sql):
    client.execute(sql)
    print(sql)
    pass


class UpdateData:
    chain_id = None
    patient_wdb_client = None
    patient_ods_client = None
    outpatient_ods_client = None
    charge_ods_client = None
    env = None

    def __init__(self, env, region_name, chain_id):
        self.env = env
        self.chain_id = chain_id
        self.region_name = region_name
        self.patient_wdb_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', self.env, True)

    def run(self):
        ###刷数据
        # 诊所+医院 有领用统计的 -> 赋予给报损出库统计
        # +---------+----------------+
        # |module_id|parent_module_id|
        # +---------+----------------+
        # |3006     |203             | 6
        # +---------+----------------+
        sql = """
            update
                clinic_employee a
                    inner join organ b on a.chain_id = b.id and b.id = b.parent_id and b.id = '{chainId}' and
                                          b.his_type in (0, 1, 2, 100)
            set a.module_ids = concat(module_ids, ',3012')
            where find_in_set('3006', a.module_ids)
              and !find_in_set('6', a.module_ids)
              and !find_in_set('0', a.module_ids)
              and !find_in_set('3012', a.module_ids)
              and a.chain_id = '{chainId}';
        """.format(chainId=self.chain_id)
        execute_sql(self.patient_wdb_client, sql)

        # 医院 有领用统计的 -> 赋予消耗出库统计，其他出库统计
        # |5250607  |消耗出库统计|
        # +---------+------+
        # |5250609  |其他出库统计|
        sql = """
            update
                clinic_employee a
                    inner join organ b on a.chain_id = b.id and b.id = b.parent_id and b.id = '{chainId}' and b.his_type = 100
            set a.module_ids = concat(module_ids, ',5250607')
            where find_in_set('3006', a.module_ids)
              and !find_in_set('6', a.module_ids)
              and !find_in_set('0', a.module_ids)
              and !find_in_set('5250607', a.module_ids)
              and a.chain_id = '{chainId}';
        """.format(chainId=self.chain_id)
        execute_sql(self.patient_wdb_client, sql)

        sql = """
            update
                clinic_employee a
                    inner join organ b on a.chain_id = b.id and b.id = b.parent_id and b.id = '{chainId}' and b.his_type = 100
            set a.module_ids = concat(module_ids, ',5250609')
            where find_in_set('3006', a.module_ids)
              and !find_in_set('6', a.module_ids)
              and !find_in_set('0', a.module_ids)
              and !find_in_set('5250609', a.module_ids)
              and a.chain_id = '{chainId}';
        """.format(chainId=self.chain_id)
        execute_sql(self.patient_wdb_client, sql)

        # 有入库统计的 -> 赋予领用统计
        # +---------+----------------+
        # |module_id|parent_module_id|
        # +---------+----------------+
        # |3004     |203             |
        # +---------+----------------+

        sql = """
            update
                clinic_employee a
                    inner join organ b on a.chain_id = b.id and b.id = b.parent_id and b.id = '{chainId}' and
                                          b.his_type in (0, 1, 2, 100)
            set a.module_ids = concat(module_ids, ',3006')
            where find_in_set('3004', a.module_ids)
              and !find_in_set('6', a.module_ids)
              and !find_in_set('0', a.module_ids)
              and !find_in_set('3006', a.module_ids)
              and a.chain_id = '{chainId}';
        """.format(chainId=self.chain_id)
        execute_sql(self.patient_wdb_client, sql)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    updateData = UpdateData(args.env, args.region_name, args.chain_id)
    updateData.run()


if __name__ == '__main__':
    main()
