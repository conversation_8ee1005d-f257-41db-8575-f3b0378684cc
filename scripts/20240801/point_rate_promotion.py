"""
多倍积分
1. 新增 v2_promotion_main_clinic 将同一个 main_id 下的 v2_promotion_clinic 或 v2_promotion_discount_clinic 的数据插入
2. 新增 v2_promotion_main_member 将同一个 main_id 下的 v2_promotion_member 或 v2_promotion_discount_member 的数据插入
3. 在 v2_promotion_main 表新增 recent_begin_date 和 recent_end_date 字段，将最近的 begin_date 和 end_date 写入
"""
import argparse
import os
import sys

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from scripts.common.utils.lists import ListUtils
from scripts.common.utils.sqls import SqlUtils
from multizone.db import DBClient
from idwork import IdWork


class UpdateData:
    chain_id = None
    promotion_wdb_client = None
    env = None
    id_work = None

    def __init__(self, env, region_name, chain_id):
        self.env = env
        self.chain_id = chain_id
        self.region_name = region_name
        self.promotion_wdb_client = DBClient(self.region_name, 'abc_cis_charge', 'abc_cis_promotion', self.env, True)
        self.id_work = IdWork(self.promotion_wdb_client, False)

    def run(self):
        # 查询连锁下所有的 v2_promotion_main
        promotion_mains = self.promotion_wdb_client.fetchall("""
            select *
            from v2_promotion_main
            where chain_id = '{chainId}'
              and is_deleted = 0
        """.format(chainId=self.chain_id))

        if not promotion_mains:
            return
        self.id_work.config()

        # 查询连锁下所有 v2_promotion_main_clinic
        promotion_main_clinics = self.promotion_wdb_client.fetchall("""
            select *
            from v2_promotion_main_clinic
            where chain_id = '{chainId}'
              and is_deleted = 0
        """.format(chainId=self.chain_id))
        main_id_to_clinics = ListUtils.group_by(promotion_main_clinics, lambda clinic: str(clinic['promotion_main_id']))

        # 查询连锁下所有 v2_promotion_main_member
        promotion_main_members = self.promotion_wdb_client.fetchall("""
            select *
            from v2_promotion_main_member
            where chain_id = '{chainId}'
              and is_deleted = 0
        """.format(chainId=self.chain_id))
        main_id_to_members = ListUtils.group_by(promotion_main_members, lambda member: str(member['promotion_main_id']))

        for promotion_main in promotion_mains:
            promotion_main['clinics'] = main_id_to_clinics.get(promotion_main['id'], [])
            promotion_main['members'] = main_id_to_members.get(promotion_main['id'], [])

        self.upsert_promotion_main_clinic(promotion_mains)

        self.upsert_promotion_main_member(promotion_mains)

        self.update_recent_date(promotion_mains)

    def upsert_promotion_main_clinic(self, promotion_mains):
        if not promotion_mains:
            return

        # 查询连锁下所有 v2_promotion_clinic
        main_ids = ListUtils.dist_mapping(promotion_mains, lambda main: main['id'])
        all_promotion_clinics = []
        all_promotion_clinics.extend(self.promotion_wdb_client.fetchall("""
            select b.*, a.main_id
            from v2_promotion a
                     inner join v2_promotion_clinic b on (a.id = b.promotion_id)
            where a.status < 90
              and b.is_deleted = 0
              and a.main_id in ({mainIds});        
        """.format(mainIds=SqlUtils.to_in_value(main_ids))))

        all_promotion_clinics.extend(self.promotion_wdb_client.fetchall("""
            select b.*, a.main_id
            from v2_promotion_discount a
                     inner join v2_promotion_discount_clinic b on (a.id = b.promotion_id)
            where a.status < 90
              and b.is_deleted = 0
              and a.main_id in ({mainIds});        
        """.format(mainIds=SqlUtils.to_in_value(main_ids))))

        if not all_promotion_clinics:
            return

        main_id_to_clinics = ListUtils.group_by(all_promotion_clinics, lambda clinic: clinic['main_id'])
        for promotion_main in promotion_mains:
            main_clinics = promotion_main['clinics']
            clinics = main_id_to_clinics.get(promotion_main['id'], [])
            if main_clinics or not clinics:
                continue

            # 拼接批量插入的 SQL
            sql = """
                insert into v2_promotion_main_clinic (id, promotion_main_id, chain_id, clinic_id, is_deleted, created, created_by, last_modified, last_modified_by)
                values
            """

            clinic_ids = set()
            for clinic in clinics:
                if clinic['clinic_id'] in clinic_ids:
                    continue
                clinic_ids.add(clinic['clinic_id'])
                sql += ("""('{id}', '{promotion_main_id}', '{chain_id}', '{clinic_id}', 0, now(), '00000000000000000000000000000000', now(), '00000000000000000000000000000000'),"""
                        .format(id=self.id_work.getUIDLong(), promotion_main_id=promotion_main['id'], chain_id=self.chain_id, clinic_id=clinic['clinic_id']))
            sql = sql[:-1]

            print(sql)
            self.promotion_wdb_client.execute(sql)

    def upsert_promotion_main_member(self, promotion_mains):
        if not promotion_mains:
            return

        # 查询连锁下所有 v2_promotion_member
        main_ids = ListUtils.dist_mapping(promotion_mains, lambda main: main['id'])
        all_promotion_members = []
        all_promotion_members.extend(self.promotion_wdb_client.fetchall("""
            select b.*, a.main_id
            from v2_promotion a
                inner join v2_promotion_member b on (a.id = b.promotion_id)
            where a.status < 90
                and b.is_deleted = 0
                and a.main_id in ({mainIds});        
        """.format(mainIds=SqlUtils.to_in_value(main_ids))))

        all_promotion_members.extend(self.promotion_wdb_client.fetchall("""
            select b.*, a.main_id
            from v2_promotion_discount a
                inner join v2_promotion_discount_member b on (a.id = b.promotion_id)
            where a.status < 90
                and b.is_deleted = 0
                and a.main_id in ({mainIds});
        """.format(mainIds=SqlUtils.to_in_value(main_ids))))
        if not all_promotion_members:
            return

        main_id_to_members = ListUtils.group_by(all_promotion_members, lambda member: member['main_id'])
        for promotion_main in promotion_mains:
            main_members = promotion_main['members']
            members = main_id_to_members.get(promotion_main['id'], [])
            if main_members or not members:
                continue

            # 拼接批量插入的 SQL
            sql = """
                insert into v2_promotion_main_member (id, promotion_main_id, chain_id, member_type_id, is_deleted, created, created_by, last_modified, last_modified_by)
                values
            """
            member_type_ids = set()
            for member in members:
                if member['member_type_id'] in member_type_ids:
                    continue
                member_type_ids.add(member['member_type_id'])
                sql += ("""('{id}', '{promotion_main_id}', '{chain_id}', '{member_type_id}', 0, now(), '00000000000000000000000000000000', now(), '00000000000000000000000000000000'),"""
                        .format(id=self.id_work.getUIDLong(), promotion_main_id=promotion_main['id'], chain_id=self.chain_id, member_type_id=member['member_type_id']))
            sql = sql[:-1]

            self.promotion_wdb_client.execute(sql)

    def update_recent_date(self, promotion_mains):
        if not promotion_mains:
            return

        normal_promotion_mains = list(filter(lambda main: main['type'] == 0 and main['is_forever'] == 0, promotion_mains))
        if normal_promotion_mains:
            for promotion_main in normal_promotion_mains:
                self.promotion_wdb_client.execute("""
                    update v2_promotion_main
                    set recent_begin_date = begin_date, recent_end_date = end_date
                    where id = '{mainId}'
                """.format(mainId=promotion_main['id']))

        member_promotion_mains = list(filter(lambda main: main['type'] == 1 and main['period_rule'], promotion_mains))

        if member_promotion_mains:
            recent_dates = self.promotion_wdb_client.fetchall("""
                select *
                from (select a.id, IF(b.begin_date is not null, b.begin_date, c.begin_date) as begin_date, IF(b.end_date is not null, b.end_date, c.end_date) as end_date
                      from v2_promotion_main a
                               left join v2_promotion_discount b on (a.id = b.main_id and b.status < 90)
                               left join v2_promotion c on (a.id = c.main_id and c.status < 90)
                      where a.is_deleted = 0
                        and a.type = 1
                        and a.period_rule is not null
                        and a.id in ({mainIds})) tmp
                where begin_date is not null
                  and end_date is not null
            """.format(mainIds=SqlUtils.to_in_value(ListUtils.dist_mapping(member_promotion_mains, lambda main: main['id']))))

            if not recent_dates:
                return

            for promotion_main in recent_dates:
                self.promotion_wdb_client.execute("""
                    update v2_promotion_main
                    set recent_begin_date = '{beginDate}', recent_end_date = '{endDate}'
                    where id = '{mainId}'
                """.format(beginDate=promotion_main['begin_date'], endDate=promotion_main['end_date'], mainId=promotion_main['id']))


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    updateData = UpdateData(args.env, args.region_name, args.chain_id)
    updateData.run()


if __name__ == '__main__':
    main()
