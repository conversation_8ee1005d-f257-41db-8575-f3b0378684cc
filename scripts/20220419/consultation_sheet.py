#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys 
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db 
import argparse

def run(chain_id):
    sql = '''
        update v2_outpatient_consultation_sheet
        set status = 70
        where status = 0
        and created <= date_sub(now(), interval 1 hour)
        and chain_id = '{0}';
    '''.format(chain_id)
    print sql
    db_client = db.DBClient('abc_cis_outpatient', 'abc_cis_outpatient')
    db_client.execute(sql)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)

if __name__ == '__main__':
    main()
