#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import sys
import argparse
import os
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import db


SQLS = [
    '''
INSERT INTO v2_property_config_item (id, `key`, value, scope, scope_id, is_deleted, created_by, created,
                                     last_modified_by, last_modified, key_first, key_second, key_third,
                                     key_fourth, key_fifth)
select substr(uuid_short(), 4),
       'chainBasic.weClinic.outpatientReport.showMedicalRecord',
       if(v2mc.diagnose = 1, '[0,1,2]', '[]'),
       'chain',
       v2mc.chain_id,
       0,
       v2mc.created_by,
       v2mc.created,
       v2mc.last_modified_by,
       v2mc.last_modified,
       'chainBasic',
       'weClinic',
       'outpatientReport',
       'showMedicalRecord',
       null
from abc_cis_mc.v2_mc_config v2mc
where v2mc.chain_id = '{0}';
''',

    '''
INSERT INTO v2_property_config_item (id, `key`, value, scope, scope_id, is_deleted, created_by, created,
                                     last_modified_by, last_modified, key_first, key_second, key_third,
                                     key_fourth, key_fifth)
select substr(uuid_short(), 4),
       'chainBasic.weClinic.outpatientReport.showExaminationItem',
       if(v2mc.examine_and_treat = 1, '1', '2'),
       'chain',
       v2mc.chain_id,
       0,
       v2mc.created_by,
       v2mc.created,
       v2mc.last_modified_by,
       v2mc.last_modified,
       'chainBasic',
       'weClinic',
       'outpatientReport',
       'showExaminationItem',
       null
from abc_cis_mc.v2_mc_config v2mc
where v2mc.chain_id  = '{0}';
''',
    '''
INSERT INTO v2_property_config_item (id, `key`, value, scope, scope_id, is_deleted, created_by, created,
                                     last_modified_by, last_modified, key_first, key_second, key_third,
                                     key_fourth, key_fifth)
select substr(uuid_short(), 4),
       'chainBasic.weClinic.outpatientReport.showTreatItem',
       if(v2mc.examine_and_treat = 1, '1', '2'),
       'chain',
       v2mc.chain_id,
       0,
       v2mc.created_by,
       v2mc.created,
       v2mc.last_modified_by,
       v2mc.last_modified,
       'chainBasic',
       'weClinic',
       'outpatientReport',
       'showTreatItem',
       null
from abc_cis_mc.v2_mc_config v2mc
where v2mc.chain_id = '{0}';
''',
    '''
INSERT INTO v2_property_config_item (id, `key`, value, scope, scope_id, is_deleted, created_by, created,
                                     last_modified_by, last_modified, key_first, key_second, key_third,
                                     key_fourth, key_fifth)
select substr(uuid_short(), 4),
       'chainBasic.weClinic.outpatientReport.showComposeItem',
       if(v2mc.examine_and_treat = 1, '1', '3'),
       'chain',
       v2mc.chain_id,
       0,
       v2mc.created_by,
       v2mc.created,
       v2mc.last_modified_by,
       v2mc.last_modified,
       'chainBasic',
       'weClinic',
       'outpatientReport',
       'showComposeItem',
       null
from abc_cis_mc.v2_mc_config v2mc
where v2mc.chain_id = '{0}';
''',
    '''
INSERT INTO v2_property_config_item (id, `key`, value, scope, scope_id, is_deleted, created_by, created,
                                     last_modified_by, last_modified, key_first, key_second, key_third,
                                     key_fourth, key_fifth)
select substr(uuid_short(), 4),
       'chainBasic.weClinic.outpatientReport.showDrugMaterialItem',
       if(v2mc.detail_goods = 1, '1', '2'),
       'chain',
       v2mc.chain_id,
       0,
       v2mc.created_by,
       v2mc.created,
       v2mc.last_modified_by,
       v2mc.last_modified,
       'chainBasic',
       'weClinic',
       'outpatientReport',
       'showDrugMaterialItem',
       null
from abc_cis_mc.v2_mc_config v2mc
where v2mc.chain_id = '{0}';
''',
    '''
INSERT INTO v2_property_config_item (id, `key`, value, scope, scope_id, is_deleted, created_by, created,
                                     last_modified_by, last_modified, key_first, key_second, key_third,
                                     key_fourth, key_fifth)
select substr(uuid_short(), 4),
       'chainBasic.weClinic.outpatientReport.showOtherItem',
       if(v2mc.detail_goods = 1, '1', '2'),
       'chain',
       v2mc.chain_id,
       0,
       v2mc.created_by,
       v2mc.created,
       v2mc.last_modified_by,
       v2mc.last_modified,
       'chainBasic',
       'weClinic',
       'outpatientReport',
       'showOtherItem',
       null
from abc_cis_mc.v2_mc_config v2mc
where v2mc.chain_id = '{0}';
''',
    '''
INSERT INTO v2_property_config_item (id, `key`, value, scope, scope_id, is_deleted, created_by, created,
                                     last_modified_by, last_modified, key_first, key_second, key_third,
                                     key_fourth, key_fifth)
select substr(uuid_short(), 4),
       'chainBasic.weClinic.outpatientReport.showWesternMedicine',
       if(v2mc.detail_goods = 1, '1', '2'),
       'chain',
       v2mc.chain_id,
       0,
       v2mc.created_by,
       v2mc.created,
       v2mc.last_modified_by,
       v2mc.last_modified,
       'chainBasic',
       'weClinic',
       'outpatientReport',
       'showWesternMedicine',
       null
from abc_cis_mc.v2_mc_config v2mc
where v2mc.chain_id = '{0}';
''',
    '''
INSERT INTO v2_property_config_item (id, `key`, value, scope, scope_id, is_deleted, created_by, created,
                                     last_modified_by, last_modified, key_first, key_second, key_third,
                                     key_fourth, key_fifth)
select substr(uuid_short(), 4),
       'chainBasic.weClinic.outpatientReport.showChinese',
       if(v2mc.detail_goods = 1, '1', '2'),
       'chain',
       v2mc.chain_id,
       0,
       v2mc.created_by,
       v2mc.created,
       v2mc.last_modified_by,
       v2mc.last_modified,
       'chainBasic',
       'weClinic',
       'outpatientReport',
       'showChinese',
       null
from abc_cis_mc.v2_mc_config v2mc
where v2mc.chain_id = '{0}';
''',
    '''
INSERT INTO v2_property_config_item (id, `key`, value, scope, scope_id, is_deleted, created_by, created,
                                     last_modified_by, last_modified, key_first, key_second, key_third,
                                     key_fourth, key_fifth)
select substr(uuid_short(), 4),
       'chainBasic.weClinic.outpatientReport.showInfusion',
       if(v2mc.detail_goods = 1, '1', '2'),
       'chain',
       v2mc.chain_id,
       0,
       v2mc.created_by,
       v2mc.created,
       v2mc.last_modified_by,
       v2mc.last_modified,
       'chainBasic',
       'weClinic',
       'outpatientReport',
       'showInfusion',
       null
from abc_cis_mc.v2_mc_config v2mc
where v2mc.chain_id = '{0}';
''',
    '''
INSERT INTO v2_property_config_item (id, `key`, value, scope, scope_id, is_deleted, created_by, created,
                                     last_modified_by, last_modified, key_first, key_second, key_third,
                                     key_fourth, key_fifth)
select substr(uuid_short(), 4),
       'chainBasic.weClinic.outpatientReport.showExternal',
       if(v2mc.detail_goods = 1, '1', '2'),
       'chain',
       v2mc.chain_id,
       0,
       v2mc.created_by,
       v2mc.created,
       v2mc.last_modified_by,
       v2mc.last_modified,
       'chainBasic',
       'weClinic',
       'outpatientReport',
       'showExternal',
       null
from abc_cis_mc.v2_mc_config v2mc
where v2mc.chain_id = '{0}';
''',
    '''
INSERT INTO v2_property_config_item (id, `key`, value, scope, scope_id, is_deleted, created_by, created,
                                     last_modified_by, last_modified, key_first, key_second, key_third,
                                     key_fourth, key_fifth)
select substr(uuid_short(), 4),
       'chainBasic.weClinic.outpatientReport.showFeeInfo',
       if(v2mc.detail_goods = 1, '[0,1]', '[]'),
       'chain',
       v2mc.chain_id,
       0,
       v2mc.created_by,
       v2mc.created,
       v2mc.last_modified_by,
       v2mc.last_modified,
       'chainBasic',
       'weClinic',
       'outpatientReport',
       'showFeeInfo',
       null
from abc_cis_mc.v2_mc_config v2mc
where v2mc.chain_id = '{0}';
'''

]


def run(chain_id):
    db_client = db.DBClient('abc_cis_mixed', 'abc_cis_basic')
    for sql in SQLS:
        sql_f = sql.format(chain_id)
        print sql_f
        db_client.execute(sql_f)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)


if __name__ == '__main__':
    main()
