#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys 
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db 
import argparse

def run(chain_id):
    db_client = db.DBClient('abc_cis_stock', 'abc_cis_goods')

    # 给历史供应商刷入shadowId
    sql = '''
        update v2_goods_supplier
        set shadow_id = substr(uuid_short(), 4)
        where chain_id = '{0}' and shadow_id = 0;
    '''.format(chain_id)
    print sql
    db_client.execute(sql)

    # 为现有虚拟药房供应商 刷入支持 饮片 和颗粒
    sql = '''
        update v2_goods_supplier
        set extend_info = '{{"restrictGoodsTypeIds":[14,15]}}'
        where chain_id = '{0}' and pharmacy_type= 2;
    '''.format(chain_id)
    print sql
    db_client.execute(sql)

    # 开了代煎代配药房的连锁 插入一条内置的合作供应商
    sql = '''
        INSERT INTO v2_goods_supplier (id, chain_id, name, status, license_id, contact, mobile, mark, created, created_by,
                                    last_modified, last_modified_by, pharmacy_type, extend_info, inner_flag, shadow_id)
        select CONCAT('0000000000000', uuid_short()),
            id,
            '代煎代配合作药房',
            1,
            null,
            null,
            null,
            null,
            now(),
            '00000000000000000000000000000000',
            now(),
            '00000000000000000000000000000000',
            2,
            '{{"restrictGoodsTypeIds":[14,15]}}',
            1,
            substr(uuid_short(), 4)
        from (select distinct chain_id as id
            from v2_goods_pharmacy
            where chain_id = '{0}') as a;
    '''.format(chain_id)
    print sql
    db_client.execute(sql)

    # 把虚拟药房现有的 goodsStock supplierId为0的刷到默认的待见代配供应商上
    sql = '''
        update
        v2_goods_stock stock
                inner join v2_goods_supplier v2gs
                            on stock.chain_id = v2gs.chain_id and stock.pharmacy_type = 2 and stock.supplier_id is null and
                            stock.`from` = 3 and v2gs.pharmacy_type = 2 and v2gs.inner_flag = 1

        set stock.supplier_id = v2gs.id
        where v2gs.chain_id = '{0}';
    '''.format(chain_id)
    print sql
    db_client.execute(sql)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)

if __name__ == '__main__':
    main()
