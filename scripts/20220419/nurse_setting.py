#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys 
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db 
import argparse

def run(chain_id):
    sql = '''
        insert into abc_cis_basic.v2_property_config_item (id, `key`, value, scope, scope_id, is_deleted, created_by, created,
                                                        last_modified_by, last_modified, key_first, key_second, key_third,
                                                        key_fourth,
                                                        key_fifth)
        select substr(uuid_short(), 4),
            'clinicNurseSettings.enterListSettings.itemType',
            value,
            scope,
            scope_id,
            is_deleted,
            created_by,
            v2pci.created,
            last_modified_by,
            v2pci.last_modified,
            'clinicNurseSettings',
            'enterListSettings',
            'itemType',
            key_fourth,
            key_fifth
        from abc_cis_basic.v2_property_config_item v2pci
                inner join abc_cis_basic.organ o on v2pci.scope_id = o.id
        where `key` = 'clinicNurseSettings.newListDisplaySetting' and o.parent_id = '{0}'
        and v2pci.scope_id not in (select scope_id
                                    from abc_cis_basic.v2_property_config_item
                                    where `key` = 'clinicNurseSettings.enterListSettings.itemType'); 
    '''.format(chain_id)
    print sql
    db_client = db.DBClient('abc_cis_mixed', 'abc_cis_basic')
    db_client.execute(sql)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)

if __name__ == '__main__':
    main()
