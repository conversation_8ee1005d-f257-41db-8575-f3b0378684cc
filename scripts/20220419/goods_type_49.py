#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys 
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db 
import argparse
import logging
logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)


class UpdateGoodsType(object):

    def __init__(self):
        self._db_client_cache = {}


    def _get_db_client(self, cluster, database):
        k = '{0}-{1}'.format(cluster, database)
        if k in self._db_client_cache.keys():
            return self._db_client_cache[k]
        
        db_client = db.DBClient(cluster, database)
        self._db_client_cache[k] = db_client
        return db_client


    def copy_goods_items(self, chain_id):
        logging.info('execute copy_goods_items chain_id: {0}'.format(chain_id))
        sql = '''
        select id, organ_id as chain_id from v2_goods where type = 4 and sub_type = 9 and organ_id = '{0}'
        '''.format(chain_id)

        db_client = self._get_db_client('adb', 'abc_cis_goods')
        goods = db_client.fetchall(sql)
        
        if goods is None or len(goods) == 0:
            return 

        values = ','.join([ ''' ('{0}', '{1}') '''.format(item['id'], item['chain_id']) for item in goods ])

        insert_sql = '''
            insert into tmp_49_goods (id, chain_id) values {0}
        '''.format(values)

        charge_db_client = self._get_db_client('abc_cis_charge', 'abc_cis_charge')
        charge_db_client.execute(insert_sql)


        outpatient_db_client = self._get_db_client('abc_cis_outpatient', 'abc_cis_outpatient')
        outpatient_db_client.execute(insert_sql)

    def update_outpatient(self, chain_id):
        basic_db_client = self._get_db_client('abc_cis_mixed', 'abc_cis_basic')
        organs = basic_db_client.fetchall(''' select id from organ where parent_id = '{0}' '''.format(chain_id))
        clinic_ids = ','.join([ ''' '{0}' '''.format(organ['id']) for organ in organs ])
        sql = '''
        update abc_cis_outpatient.v2_outpatient_product_form_item as a
            inner join tmp_49_goods as b on a.product_id = b.id
        set a.type     = 19,
            a.sub_type = 0
        where a.type = 4
        and a.sub_type = 9
        and a.clinic_id in ({0})
        '''.format(clinic_ids)
        print sql

        outpatient_db_client = self._get_db_client('abc_cis_outpatient', 'abc_cis_outpatient')
        outpatient_db_client.execute(sql)

    def update_charge(self, chain_id):
        item_sql = '''
        update v2_charge_form_item  as a 
        inner join tmp_49_goods as b on a.product_id = b.id 
        set a.product_type     = 19,
            a.product_sub_type = 0,
            a.product_snapshot = if(json_length(a.product_snapshot) > 0,
                                json_set(a.product_snapshot, '$.type', 21), a.product_snapshot),
            a.product_snapshot = if(json_length(a.product_snapshot) > 0,
                                json_set(a.product_snapshot, '$.subType', 0), a.product_snapshot)
        where a.product_type = 4
        and a.product_sub_type = 9
        and a.chain_id = '{0}'
        '''.format(chain_id)

        print item_sql

        execute_sql = '''
        update v2_charge_execute_item as a
            inner join v2_charge_form_item as b on a.charge_form_item_id = b.id and a.clinic_id = b.clinic_id
            inner join tmp_49_goods as c on b.product_id = c.id
        set a.product_type     = 19,
            a.product_sub_type = 0
        where a.product_type = 4
        and a.product_sub_type = 9
        and a.chain_id = '{0}'
        and b.chain_id = '{0}'
        '''.format(chain_id)

        print execute_sql

        record_sql = '''
        update v2_charge_transaction_record as a
            inner join v2_charge_form_item as b on a.charge_form_item_id = b.id and a.clinic_id = b.clinic_id
            inner join tmp_49_goods as c on b.product_id = c.id
        set a.product_type     = 19,
            a.product_sub_type = 0
        where a.product_type = 4
        and a.product_sub_type = 9
        and a.chain_id = '{0}'
        and b.chain_id = '{0}'
        '''.format(chain_id)

        print record_sql

        charge_db_client = self._get_db_client('abc_cis_charge', 'abc_cis_charge')
        charge_db_client.execute(item_sql)
        charge_db_client.execute(execute_sql)
        charge_db_client.execute(record_sql)


    def update_promotion(self, chain_id):
        sqls = [
            # v2_promotion_discount_goods
            '''
            insert into v2_promotion_discount_goods (id, promotion_id, type, goods_id, goods_type, goods_c_m_spec, goods_sub_type, is_air_pharmacy, discount, chain_id, discount_type, is_deleted, created, created_by, last_modified, last_modified_by)
            select uuid_short(), promotion_id, type, 33, 19, '', 0, is_air_pharmacy,discount, chain_id, discount_type, is_deleted, created, created_by, last_modified, last_modified_by
            from v2_promotion_discount_goods where type = 1 and goods_type=4 and goods_sub_type=0 and chain_id = '{0}';
            ''',
            '''
            update v2_promotion_discount_goods set goods_id = 33, goods_type=19, goods_sub_type=0 where type = 1 and goods_type=4 and goods_sub_type=9 and chain_id = '{0}';
            ''',
            '''
            update v2_promotion_discount_goods set goods_type = 19, goods_sub_type = 0 where type = 2 and goods_type=4 and goods_sub_type=9 and chain_id = '{0}';
            ''',
            # v2_promotion_card_goods
            '''
            insert into v2_promotion_card_goods (id, chain_id, card_id, type, goods_rights_type, goods_id, goods_type, goods_sub_type, goods_c_m_spec, is_air_pharmacy, discount_type, discount, is_giving_limit, giving_count, package_id, is_deleted, created, created_by, last_modified, last_modified_by)
            select uuid_short(), chain_id, card_id, type, goods_rights_type, 33, 19, 0, goods_c_m_spec, is_air_pharmacy, discount_type, discount, is_giving_limit, giving_count, package_id, is_deleted, created, created_by, last_modified, last_modified_by
            from v2_promotion_card_goods where type = 1 and goods_type=4 and goods_sub_type=0 and chain_id = '{0}';
            ''',
            '''
            update v2_promotion_card_goods set goods_id = 33, goods_type=19, goods_sub_type=0 where type = 1 and goods_type=4 and goods_sub_type=9 and chain_id = '{0}';
            ''',
            '''
            update v2_promotion_card_goods set goods_type = 19, goods_sub_type = 0 where type = 2 and goods_type=4 and goods_sub_type=9 and chain_id = '{0}';
            ''',
            #  v2_promotion_goods
            '''
            insert into v2_promotion_goods (id, promotion_id, type, goods_id, goods_type, goods_sub_type, goods_c_m_spec, is_air_pharmacy, is_reverse, discount, is_deleted, created, created_by, last_modified, last_modified_by)
            select uuid_short(), promotion_id, type, 33, 19, 0, goods_c_m_spec, is_air_pharmacy, is_reverse, discount, is_deleted, created, created_by, last_modified, last_modified_by
            from v2_promotion_goods where type = 1 and goods_type=4 and goods_sub_type=0 and is_reverse = 0 and promotion_id in (select id from v2_promotion where chain_id = '{0}');
            ''',
            '''
            update v2_promotion_goods set goods_id = 33, goods_type = 19, goods_sub_type = 0 where type = 1 and goods_type=4 and goods_sub_type=9 and is_reverse = 0 and promotion_id in (select id from v2_promotion where chain_id = '{0}');
            ''',
            '''
            update v2_promotion_goods set goods_type = 19, goods_sub_type = 0 where type = 2 and goods_type=4 and goods_sub_type=9 and promotion_id in (select id from v2_promotion where chain_id = '{0}');
            '''
        ]

        promotion_db_client = self._get_db_client('abc_cis_charge', 'abc_cis_promotion')

        for sql in sqls:
            sql_f = sql.format(chain_id)
            print sql_f
            promotion_db_client.execute(sql_f)

    def update_shebao(self, chain_id):
        sql = '''
            update shebao_chengdu_matched_code set type = 19 , goods_sub_type = 0 where type = 4 and goods_sub_type=9 and chain_id = '{0}'
        '''.format(chain_id)
        print sql

        shebao_db_client = self._get_db_client('abc_cis_bill', 'abc_cis_shebao')
        shebao_db_client.execute(sql)

    def update_goods(self, chain_id):
        sqls = [
            '''
            update v2_goods
            set type    = 19,
                sub_type= 0,
                type_id = 33
            where type = 4
            and sub_type = 9
            and organ_id = '{0}'
            ''',
            '''
            update v2_goods_custom_type set type_id = 33 where type_id = 24 and chain_id = '{0}'
            '''
        ]

        goods_db_client = self._get_db_client('abc_cis_stock', 'abc_cis_goods')
        for sql in sqls:
            sql_f = sql.format(chain_id)
            print sql_f
            goods_db_client.execute(sql_f)
    
    def clear_tmp_goods(self):
        charge_db_client = self._get_db_client('abc_cis_charge', 'abc_cis_charge')
        charge_db_client.execute('truncate tmp_49_goods')

        outpatient_db_client = self._get_db_client('abc_cis_outpatient', 'abc_cis_outpatient')
        outpatient_db_client.execute('truncate tmp_49_goods')

    def execute_env(self, env):
        sql = '''select chain_id from v2_clinic_gray_organ where env = '{0}' '''.format(env)
        basic_db_client = self._get_db_client('abc_cis_mixed', 'abc_cis_basic')
        organs = basic_db_client.fetchall(sql)
        for organ in organs:
            self.execute_chain(organ['chain_id'])

    def execute_chain(self, chain_id):
        self.clear_tmp_goods()
        self.copy_goods_items(chain_id)
        self.update_outpatient(chain_id)
        self.update_charge(chain_id)
        self.update_promotion(chain_id)
        self.update_shebao(chain_id)
        self.update_goods(chain_id)


def run(chain_id):
    rgt = UpdateGoodsType()
    rgt.execute_chain(chain_id)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)

if __name__ == '__main__':
    main()
