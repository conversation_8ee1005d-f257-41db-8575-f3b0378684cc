#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys 
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db 
import argparse
import requests

def run(chain_id):
    sql = '''
        insert ignore into 
        v2_clinic_data_permission_item (id, `key`, value, chain_id, clinic_id, key_first, key_second, key_third, key_fourth, key_fifth, created_by, last_modified_by)
        select v2pci.id,
        v2pci.`key`,
        v2pci.value,
        o.parent_id as chainId,
        v2pci.scope_id as clinicId,
        v2pci.key_first,
        v2pci.key_second,
        v2pci.key_third,
        v2pci.key_fourth,
        v2pci.key_fifth,
        v2pci.created_by,
        v2pci.last_modified_by
        from v2_property_config_item v2pci
        inner join v2_clinic_data_permission_config v2cdpc on v2pci.`key` = v2cdpc.`key` and v2cdpc.node_type = 1
        inner join organ o on v2pci.scope_id = o.id and o.parent_id = '{0}' and o.status < 90
        where v2pci.scope = 'clinic' and v2pci.is_deleted = 0;
    '''.format(chain_id)
    print sql
    db_client = db.DBClient('abc_cis_mixed', 'abc_cis_basic')
    db_client.execute(sql)

    # update es
    url = 'http://prod.rpc.abczs.cn/rpc/datasync/rebuild/abc-cdss-patient?chainId={0}&dataType=1&isCleanFirst=0'.format(chain_id)
    rsp = requests.put(url)
    print rsp.content

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)

if __name__ == '__main__':
    main()
