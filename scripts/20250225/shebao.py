#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.rpc import regionRpcHost
import json
import argparse
import requests

default_id = '00000000000000000000000000000000'


# 多库房把以前老的药房类型刷到新的标上
def flushRule(abcRegion, chain_id):
    goods_db_client = DBClient(abcRegion, 'abc_cis_bill', 'abc_cis_shebao', 'prod', True)
    goods_db_client.execute(
        """update shebao_national_reg_goods
                set is_bingtuan_data_changed        = is_data_changed,
    bingtuan_register_status        = register_status,
    bingtuan_register_failed_reason = register_failed_reason
            where region ='xinjiang_bingtuan' and chain_id ='{chainId}';"""
        .format(chainId=chain_id ));







def run(abcRegion, chain_id):
    flushRule(abcRegion, chain_id)

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.region_name, args.chain_id)


if __name__ == '__main__':
    main()
