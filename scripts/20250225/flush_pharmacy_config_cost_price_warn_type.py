# -*- coding: utf-8 -*-
"""
刷药店cost_price_warn_type
@name: flush_pharmacy_config_cost_price_warn_type.py
@author: <PERSON><PERSON><PERSON>
@email: <EMAIL>
@date: 2025-03-04 11:36:18
"""
import os
import sys
import argparse

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient

DEFAULT_ID = 'sys'

env = 'prod'


class UpdateData:
    chain_id = None
    basic_db_client = None
    goods_db_client = None

    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', env, True)
        self.goods_db_client = DBClient(self.region_name, 'abc_cis_stock', 'abc_cis_goods', env, True)

    def run(self):
        self.flush_pharmacy_config_cost_price_warn_type()

    def flush_pharmacy_config_cost_price_warn_type(self):
        chain_organ = self.basic_db_client.fetchone("""
        select * from organ where id = '{chainId}' and status = 1 and his_type = 10
        """.format(chainId=self.chain_id))
        if chain_organ is None:
            return
        chain_config = self.goods_db_client.fetchone("""
        select * from v2_goods_chain_config where chain_id = '{chainId}'
        """.format(chainId=self.chain_id))
        view_mode = chain_organ['view_mode']
        if chain_config:
            update_sql = """
            update v2_goods_chain_config set cost_price_warn_type = 3 where chain_id = '{chainId}' and cost_price_warn_type = 0;
            """.format(chainId=self.chain_id)
            # print(update_sql)
            self.goods_db_client.execute(update_sql)
        clinic_config_list = self.goods_db_client.fetchall("""
        select * from v2_goods_clinic_config where chain_id = '{chainId}'
        """.format(chainId=self.chain_id))
        if clinic_config_list is None or len(clinic_config_list) == 0:
            return
        for clinic_config in clinic_config_list:
            if view_mode == 1:
                if chain_config:
                    update_sql = """
                    update v2_goods_clinic_config set cost_price_warn_type = 3, clinic_external_flag = clinic_external_flag&~504,
                    stock_warn_goods_turnover_days = {turnoverDays}, stock_days_of_day_avg_sell = {avgSell}, stock_warn_goods_will_expired_month = {willExpiredMonth},
                    profit_warn_flag = {profitWarnFlag}, cost_price_warn_percent = {costPriceWarnPercent}, unsalable_warn_days = {unsalableWarnDays}
                    where clinic_id = '{clinicId}' and cost_price_warn_type = 0;
                    """.format(clinicId=clinic_config['clinic_id'], turnoverDays=chain_config['stock_warn_goods_turnover_days'],
                               avgSell=chain_config['stock_days_of_day_avg_sell'], willExpiredMonth=chain_config['stock_warn_goods_will_expired_month'],
                               profitWarnFlag=chain_config['profit_warn_flag'], costPriceWarnPercent=chain_config['cost_price_warn_percent'],
                               unsalableWarnDays=chain_config['unsalable_warn_days'])
                else:
                    update_sql = """
                    update v2_goods_clinic_config set cost_price_warn_type = 3, clinic_external_flag = clinic_external_flag&~504 where clinic_id = '{clinicId}' and cost_price_warn_type = 0;
                    """.format(clinicId=clinic_config['clinic_id'])
            else:
                update_sql = """
                update v2_goods_clinic_config set cost_price_warn_type = 3 where clinic_id = '{clinicId}' and cost_price_warn_type = 0;
                """.format(clinicId=clinic_config['clinic_id'])
            # print(update_sql)
            self.goods_db_client.execute(update_sql)
        pass


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    update_data = UpdateData(args.region_name, args.chain_id)
    update_data.run()


if __name__ == '__main__':
    main()
