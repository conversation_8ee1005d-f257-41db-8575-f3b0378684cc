# -*- coding: utf-8 -*-
"""
@name: flush_shebao_pay_limit_price.py
@author: <PERSON><PERSON><PERSON>
@email: <EMAIL>
@date: 2024-11-21 14:35:56
"""
import os
import sys
import argparse
import logging
import json
from datetime import date, datetime, time, timedelta

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import requests
from multizone.rpc import regionRpcHost
from multizone.db import DBClient

DEFAULT_ID = '00000000000000000000000000000000'

env = 'prod'


class UpdateData:
    chain_id = None
    basic_db_client = None
    goods_db_client = None
    charge_db_client = None

    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', env, True)
        self.goods_db_client = DBClient(self.region_name, 'abc_cis_stock', 'abc_cis_goods', env, True)
        self.charge_db_client = DBClient(self.region_name, 'abc_cis_charge', 'abc_cis_charge', env, True)

    def run(self):
        self.update_single_compose_opt_clinic_id_to_null()
        self.trans_type_price_limit_rule_to_goods()
        self.trans_product_price_limit_rule_to_goods()

    def update_single_compose_opt_clinic_id_to_null(self):
        """
        更新单店对码项compose_opt表clinicId为null
        """
        organ = self.basic_db_client.fetchone("""
        select id, view_mode, his_type from organ where parent_id = '{chainId}' and view_mode = 1 and status = 1 and node_type = 2;
        """.format(chainId=self.chain_id))
        # print(organ)
        if organ is None:
            return
        view_mode = organ['view_mode']
        his_type = organ['his_type']
        clinic_id = organ['id']
        # 连锁、医院、药店不刷
        if view_mode != 1:
            # print('exit view_mode')
            return
        if his_type == 100 or his_type == 10:
            # print('exit his_type')
            return
        # 降单店的数据，删除
        delete_sql = """
        update v2_goods_compose_opt set is_deleted = 1, last_modified_user_id = 'sys' where chain_id = '{chainId}' and clinic_id is null and compose_type = 10 and is_deleted = 0 and last_modified_user_id != 'sys'
        """.format(chainId=self.chain_id)
        self.goods_db_client.execute(delete_sql)
        # 修改单店 clinic_id 为null
        update_sql = """
        update v2_goods_compose_opt set clinic_id = null, last_modified_user_id = 'sys' where chain_id = '{chainId}' and clinic_id = '{clinicId}' and compose_type = 10 and is_deleted = 0
        """.format(chainId=self.chain_id, clinicId=clinic_id)
        # print(update_sql)
        # print(delete_sql)
        self.goods_db_client.execute(update_sql)
        requests.get("""http://{rpcHost}/rpc/v3/goods/jenkins/clean-chain-redis-cache?chainId={chainId}"""
        .format(chainId=self.chain_id, rpcHost=regionRpcHost(self.region_name, env=env)))

    def trans_type_price_limit_rule_to_goods(self):
        """
        迁移按类型限价规则到 goods
        """
        type_rule_list = self.charge_db_client.fetchall('''
        select * from v2_charge_medicare_limit_price_type where chain_id = '{chainId}' and is_deleted = 0;
        '''.format(chainId=self.chain_id))
        if type_rule_list is None:
            return
        to_goods_req = {'chainId': self.chain_id, 'list': []}
        for rule in type_rule_list:
            item = {
                'id': rule['id'],
                'clinicId': rule['clinic_id'],
                'openSwitch': rule['open_switch'],
                'priceType': rule['price_type'],
                'limitRate': rule['limit_rate'],
                'type': rule['type'],
                'limitDetail': json.loads(rule['limit_detail']) if rule['limit_detail'] is not None else None,
                'exceedLimitPriceRule': rule['exceed_limit_price_rule'],
                'created': f"{rule['gmt_create']}" if rule['gmt_create'] is not None else datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'lastModified': f"{rule['gmt_modified']}" if rule['gmt_modified'] is not None else datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'createdBy': rule['created_by'] if rule['created_by'] is not None else DEFAULT_ID,
                'lastModifiedBy': rule['last_modified_by'] if rule['last_modified_by'] is not None else DEFAULT_ID,
            }
            # print(item)
            to_goods_req['list'].append(item)
        if to_goods_req['list'] is None:
            return
        # print(json.dumps(to_goods_req))
        # print(to_goods_req)
        try:
            rep_body = requests.post(url=f"http://{regionRpcHost(self.region_name, env=env)}/rpc/v3/goods/jenkins/trans-shebao-pay-limit-price-type-rule",
                                     json=to_goods_req).content.decode('utf-8')
            print(rep_body)
        except Exception as e:
            logging.error(f"迁移类型医保限价规则 error:{self.chain_id},{e}")

    def trans_product_price_limit_rule_to_goods(self):
        """
        迁移单品限价到goods
        :return:
        """
        product_rule_list = self.charge_db_client.fetchall('''
        select * from v2_charge_medicare_limit_price_product where chain_id = '{chainId}' and is_deleted = 0;
        '''.format(chainId=self.chain_id))
        if product_rule_list is None:
            return
        to_goods_req = {'chainId': self.chain_id, 'list': []}
        for rule in product_rule_list:
            item = {
                'clinicId': rule['clinic_id'],
                'goodsId': rule['product_id'],
                'type': rule['type'],
                'limitPackagePrice': str(rule['limit_package_unit_price']) if rule['limit_package_unit_price'] is not None else None,
                'limitPiecePrice': str(rule['limit_piece_unit_price']) if rule['limit_piece_unit_price'] is not None else None,
                'exceedLimitPriceRule': rule['exceed_limit_price_rule'],
                'employeeId': rule['created_by'],
            }
            # print(rule)
            # print(item)
            to_goods_req['list'].append(item)
        if (to_goods_req is None) or (to_goods_req['list'] is None):
            return
        # print(json.dumps(to_goods_req))
        # print(to_goods_req)
        try:
            rep_body = requests.post(url=f"http://{regionRpcHost(self.region_name, env=env)}/rpc/v3/goods/jenkins/trans-shebao-pay-limit-price-product-rule",
                                     json=to_goods_req).content.decode('utf-8')
            print(rep_body)
        except Exception as e:
            logging.error(f"迁移单品医保限价规则 error:{self.chain_id},{e}")

    def flush_combine_fee_compose(self):
        try:
            rsp = requests.put('''http://{regionRpcHost}/rpc/v3/goods/jenkins/flush-goods-fee-compose/{chainId}'''.format(
                regionRpcHost=regionRpcHost(self.region_name, env=env), chainId=self.chain_id))
            logging.info(f'rsp: {rsp.content.decode("utf-8")}')
        except Exception as e:
            logging.error(f'flush_combine_fee_compose error:{self.chain_id},{e}')

    def flush_single_fee_compose(self):
        try:
            rsp = requests.put('''http://{regionRpcHost}/rpc/v3/goods/jenkins/flush-single-goods-fee-compose/{chainId}'''.format(
                regionRpcHost=regionRpcHost(self.region_name, env=env), chainId=self.chain_id))
            logging.info(f'rsp: {rsp.content.decode("utf-8")}')
        except Exception as e:
            logging.error(f'flush_single_fee_compose error:{self.chain_id},{e}')


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    update_data = UpdateData(args.region_name, args.chain_id)
    update_data.run()


if __name__ == '__main__':
    main()