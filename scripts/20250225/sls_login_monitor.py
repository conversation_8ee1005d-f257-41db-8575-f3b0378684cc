import os
import json
import requests
from datetime import datetime, timedelta
from aliyun.log import Log<PERSON>lient
from multizone.db import DBClient
from mail import send_alert
from ip2geotools.databases.noncommercial import Ip2Proxy

class LoginMonitor:
    def __init__(self, zone='ShangHai'):
        self.db_client = DBClient(zone, 'abc_cis_mixed')
        self.log_client = LogClient(
            os.getenv('SLS_ENDPOINT'),
            os.getenv('ALIYUN_ACCESS_KEY'),
            os.getenv('ALIYUN_SECRET_KEY')
        )
        self.test_log_client = LogClient(
            os.getenv('SLS_TEST_ENDPOINT'),
            os.getenv('ALIYUN_ACCESS_KEY'),
            os.getenv('ALIYUN_SECRET_KEY')
        )
        self.ip_proxy = Ip2Proxy()

    def fetch_login_logs(self):
        # 获取最近1小时登录日志
        query = """
        * | SELECT 
            parse_regexp(message_index, 'employeeId:(?P<employeeId>.+), token.*\\] (?P<Ip>.+) \\(Mo.*uuid:(?P<uid>.+) client-info') AS (employeeId, Ip, uid),
            user_agent,
            count(*) AS queryCount
        FROM log 
        GROUP BY employeeId, Ip, uid, user_agent
        ORDER BY queryCount DESC
        """
        resp = self.log_client.get_logs(
            project=os.getenv('SLS_PROJECT'),
            logstore=os.getenv('SLS_LOGSTORE'),
            from_time=int((datetime.now() - timedelta(hours=1)).timestamp()),
            to_time=int(datetime.now().timestamp()),
            query=query,
            offset=0,
            size=100
        )
        return resp.get_logs()

    def analyze_ips(self, logs):
        alerts = []
        for log in logs:
            ip = log['Ip']
            employee_id = log['employeeId']
            device = log['user_agent']
            count = log['queryCount']
            
            is_abnormal, reason = self.is_abnormal(ip, count, employee_id, device)
            if is_abnormal:
                alerts.append({
                    'employee_id': employee_id,
                    'ip': ip,
                    'device': device,
                    'count': count,
                    'reason': reason,
                    'timestamp': datetime.now().isoformat()
                })
        return alerts

    def check_ip_reputation(self, ip):
        """检查IP是否为高危IP"""
        url = f"https://api.threatbook.cn/v3/ip/query"
        params = {
            "apikey": os.getenv('THREATBOOK_APIKEY'),
            "resource": ip,
            "field": "severity"
        }
        try:
            resp = requests.get(url, params=params)
            return resp.json().get('data', {}).get(ip, {}).get('severity') == '高危'
        except:
            return False

    def is_proxy_ip(self, ip):
        """检查是否为代理IP"""
        try:
            result = self.ip_proxy.get(ip, 'proxy')
            return result.proxy_type != '-'
        except:
            return False

    def check_testenv_access(self, ip):
        """检查是否访问过测试环境"""
        test_query = f"* | select count(1) as cnt where remote_addr='{ip}' and url like '%/test-api/%'"
        try:
            test_resp = self.test_log_client.get_logs(
                project=os.getenv('SLS_TEST_PROJECT'),
                logstore=os.getenv('SLS_TEST_LOGSTORE'),
                query=test_query,
                from_time=int((datetime.now() - timedelta(days=7)).timestamp()),
                to_time=int(datetime.now().timestamp())
            )
            return test_resp.get_count() > 0
        except:
            return False

    def check_login_behavior(self, employee_id, current_ip, current_device):
        """检查用户登录行为是否异常"""
        profile = self.db_client.query(f"SELECT common_ips, common_devices FROM user_login_profile WHERE employee_id='{employee_id}'")
        
        # 首次登录初始化画像
        if not profile:
            self.init_user_profile(employee_id, current_ip, current_device)
            return False
        
        # 检查IP异常
        ip_risk = current_ip not in json.loads(profile['common_ips'])[:5]  # 前5个常用IP
        
        # 检查设备异常
        device_risk = current_device not in json.loads(profile['common_devices'])
        
        return ip_risk or device_risk

    def init_user_profile(self, employee_id, ip, device):
        """初始化用户画像"""
        self.db_client.execute(
            "INSERT INTO user_login_profile (employee_id, common_ips, common_devices, last_login) "
            "VALUES (%s, %s, %s, %s)",
            (employee_id, json.dumps([ip]), json.dumps([device]), datetime.now())
        )

    def is_abnormal(self, ip, count, employee_id=None, device=None):
        """综合异常检测逻辑"""
        # 频率检测
        if count > 10:  # 1小时内超过10次登录
            return True, "登录频率异常"

        # IP风险检测
        if self.check_ip_reputation(ip):
            return True, "高危IP"

        # 代理IP检测
        if self.is_proxy_ip(ip):
            return True, "代理IP"

        # 测试环境访问检测
        if self.check_testenv_access(ip):
            return True, "访问过测试环境"

        # 黑名单检测
        if self.db_client.query(f"SELECT 1 FROM blacklist_ips WHERE ip='{ip}'"):
            return True, "IP已被拉黑"

        # 多设备登录检测
        if employee_id and device and self.check_login_behavior(employee_id, ip, device):
            return True, "异地或多设备登录"

        return False, ""

    def run(self):
        logs = self.fetch_login_logs()
        alerts = self.analyze_ips(logs)
        if alerts:
            send_alert(
                recipients=['<EMAIL>'],
                subject='登录安全告警',
                content=self.generate_alert_content(alerts)
            )
            self.log_alerts(alerts)

    def generate_alert_content(self, alerts):
        content = "安全告警\n\n"
        for alert in alerts:
            content += f"""
员工ID: {alert['employee_id']}
IP地址: {alert['ip']}
设备信息: {alert['device']}
访问次数: {alert['count']}
告警原因: {alert['reason']}
时间: {alert['timestamp']}
-------------------"""
        return content

    def log_alerts(self, alerts):
        # 记录到数据库
        pass

if __name__ == '__main__':
    monitor = LoginMonitor()
    monitor.run()
