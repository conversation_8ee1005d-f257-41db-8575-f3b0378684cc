# -*- coding: utf-8 -*-
"""
刷 goods shebaoPayMode
@name: flush_goods_shebao_pay_mode.py
@author: <PERSON><PERSON><PERSON>
@email: <EMAIL>
@date: 2025-03-04 11:36:18
"""
import os
import sys
import argparse
from datetime import datetime

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient

DEFAULT_ID = 'sys'
env = 'prod'
BATCH_SIZE = 1000  # 批量更新的大小

class UpdateData:
    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', env, True)
        self.goods_db_client = DBClient(self.region_name, 'abc_cis_stock', 'abc_cis_goods', env, True)
        self.ob_goods_db_client = DBClient(self.region_name, 'ob', 'abc_cis_goods', env, True)

    def run(self):
        self.flush_goods_shebao_pay_mode()

    def flush_goods_shebao_pay_mode(self):
        # 总部不刷
        organ_list = self.basic_db_client.fetchall("""
        select * from organ where parent_id = '{chainId}' and node_type = 2 and status = 1
        """.format(chainId=self.chain_id))
        
        if not organ_list:
            print(f"连锁 {self.chain_id} 没有需要处理的门店")
            return
            
        print(f"开始处理连锁 {self.chain_id}, 共有 {len(organ_list)} 个门店需要处理")
        
        for organ in organ_list:
            clinic_id = organ['id']
            start_time = datetime.now()
            print(f"\n开始处理门店 {clinic_id}, 开始时间: {start_time}")
            
            offset = 0
            total_updated = 0
            
            while True:
                # 一次性获取需要更新的商品ID列表
                query = """
                select a.id, b.organ_id, b.shebao_code_national_matched_status matched_status 
                from v2_goods a
                join v2_goods_extend b on a.organ_id = b.chain_id and a.id = b.goods_id and b.organ_id = '{clinicId}'
                where a.organ_id = '{chainId}' and a.status = 1 and a.type in (1, 2)
                and ((b.shebao_code_national_matched_status = 1 and b.shebao_pay_mode is null) 
                     or (b.shebao_code_national_matched_status = -1 and b.shebao_pay_mode is not null))
                limit {limit} offset {offset}
                """.format(
                    chainId=self.chain_id,
                    clinicId=clinic_id,
                    limit=BATCH_SIZE,
                    offset=offset
                )
                goods_extend_list = self.ob_goods_db_client.fetchall(query)

                if not goods_extend_list:
                    break

                # 构建批量更新SQL
                status_1_ids = []
                status_neg_1_ids = []
                
                for goods_extend in goods_extend_list:
                    if goods_extend['matched_status'] == 1:
                        status_1_ids.append(f"'{goods_extend['id']}'")
                    elif goods_extend['matched_status'] == -1:
                        status_neg_1_ids.append(f"'{goods_extend['id']}'")

                try:
                    # 批量更新status=1的记录
                    if status_1_ids:
                        update_sql = """
                        update v2_goods_extend set shebao_pay_mode = 0 
                        where organ_id = '{clinicId}' and goods_id in ({goods_ids})
                        """.format(
                            clinicId=clinic_id,
                            goods_ids=','.join(status_1_ids)
                        )
                        # print(update_sql)
                        self.goods_db_client.execute(update_sql)

                    # 批量更新status=-1的记录
                    if status_neg_1_ids:
                        update_sql = """
                        update v2_goods_extend set shebao_pay_mode = null 
                        where organ_id = '{clinicId}' and goods_id in ({goods_ids})
                        """.format(
                            clinicId=clinic_id,
                            goods_ids=','.join(status_neg_1_ids)
                        )
                        # print(update_sql)
                        self.goods_db_client.execute(update_sql)

                    total_updated += len(goods_extend_list)
                    offset += BATCH_SIZE
                    
                    print(f"门店 {clinic_id} 已处理 {total_updated} 条记录")

                except Exception as e:
                    print(f"处理门店 {clinic_id} 时发生错误: {str(e)}")
                    continue

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            print(f"门店 {clinic_id} 处理完成, 共更新 {total_updated} 条记录")
            print(f"结束时间: {end_time}, 耗时: {duration:.2f} 秒")


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    start_time = datetime.now()
    print(f"开始执行时间: {start_time}")
    
    try:
        update_data = UpdateData(args.region_name, args.chain_id)
        update_data.run()
    except Exception as e:
        print(f"执行过程中发生错误: {str(e)}")
        sys.exit(1)
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    print(f"\n执行完成")
    print(f"开始时间: {start_time}")
    print(f"结束时间: {end_time}")
    print(f"总耗时: {duration:.2f} 秒")


if __name__ == '__main__':
    main()
