"""
    v2_message_switch_config新增一条发送发票短信的配置项
"""

import argparse
import os
import sys
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient

sms_template = """
    {"types": [1300001], "templates": [{"name": "ABC数电发票开票通知", "content": "【ABC数字医疗云】您的电子发票已开具成功，请点击链接查看"}]}
"""

def run(chain_id, region_name, env):
    message_db_client = DBClient(region_name, 'abc_cis_account_base', 'abc_cis_message', env, True)
    # 统计是否存在配置文件
    exist = message_db_client.fetchone("""
        select count(1) as num from v2_message_switch_config where chain_id = '{ChainId}' and is_deleted = 0
    """.format(ChainId=chain_id))
    if exist['num'] > 0:
        # 获取chain_id是有已经存在发票配置的数据信息
        id = message_db_client.fetchone("""
            select id from v2_message_switch_config where `key` = 'invoice.report' and chain_id = '{ChainId}' and is_deleted = 0
        """.format(ChainId=chain_id))
        # 已经存在配置文件且没有发票的配置文件,则新增一条数据
        if not id:
            message_db_client.execute("""
                INSERT INTO v2_message_switch_config (id, chain_id, `group`, `key`, name, notify_desc, notify_minutes_before, sms_template, wx_template, weapp_template, sms_switch, wx_switch, weapp_switch, is_deleted, created_by, created, last_modified_by, last_modified, notify_minutes_before_list) 
                VALUES (substr(uuid_short(), 5), '{ChainId}', '其他', 'invoice.report', 'ABC数电发票开票通知', '开票时填写发送到手机，数电发票开具成功后，将提醒患者查看', 0, '{SmsTemplate}', null, null, 0, 0, 0, 0, '00000000000000000000000000000000', CURRENT_TIMESTAMP, '00000000000000000000000000000000', CURRENT_TIMESTAMP, null)
            """.format(ChainId=chain_id, SmsTemplate=sms_template))


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.env:
        parser.print_help()
        sys.exit(-1)
    run(args.chain_id, args.region_name, args.env)

if __name__ == '__main__':
    main()