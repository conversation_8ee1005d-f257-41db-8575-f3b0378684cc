#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.rpc import regionRpcHost
import json
import argparse
import requests


# 多库房把以前老的药房类型刷到新的标上
def flushData(abcRegion, chain_id):
    charge_db_client = DBClient(abcRegion, 'abc_cis_charge', 'abc_cis_charge', 'prod', True)
    ob_client = DBClient(abcRegion, 'ob', 'abc_cis_charge', 'prod', True)

    updateSqls = ob_client.fetchall("""select
        concat('INSERT INTO v2_charge_pay_mode_relation (id, clinic_id, chain_id, pay_mode_config_id, sort, is_enable, is_deleted, created, created_by, last_modified_by, last_modified, config) VALUES (', substring(uuid_short(), 5), ', \\'', a.id, '\\', \\'', a.id, '\\', 25, 10, 1, 0, now(), "chenlei", "chenlei", now(), null)') as insertPayModeSql
        from abc_cis_basic.organ a left join abc_cis_charge.v2_charge_pay_mode_relation b on a.id = b.chain_id and b.pay_mode_config_id = 25 and a.address_city_id = '370200'
    where a.address_city_id = '370200' and b.id is null and a.id = '{chainId}'""".format(chainId=chain_id))
    # print(updateSqls)
    if updateSqls:
        print(updateSqls[0]['insertPayModeSql'])
        charge_db_client.execute(updateSqls[0]['insertPayModeSql'])

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    flushData(args.region_name, args.chain_id)


if __name__ == '__main__':
    main()
