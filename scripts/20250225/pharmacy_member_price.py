# -*- coding: utf-8 -*-
"""
药店会员价刷数据脚本
1. 需要将现在药店在营销服务配置的单品会员价同步到 goods 的库中，做数据的冗余，始终以 promotion 的数据为准
2. 将历史的药房定价数据的 targetType 改为 1

# promotion 有 goods 没有: 添加到 goods
select pd.associate_member_type_id as member_type_id, pdg.goods_id, pdg.clinic_id
from abc_cis_promotion_test.v2_promotion_discount pd
         inner join abc_cis_promotion_test.v2_promotion_discount_goods pdg on (pd.id = pdg.promotion_id)
         inner join abc_cis_basic_test.organ o on (pd.chain_id = o.id)
         left join (select * from abc_cis_goods_test.v2_goods_price_opt gpo where gpo.target_type = 10 and gpo.discount_type != 10 and gpo.is_deleted = 0) g
                   on (pd.associate_member_type_id = g.member_type_id and pdg.goods_id = g.goods_id and pdg.clinic_id = g.clinic_id)
where o.his_type = 10
  and pd.type = 0
  and pd.status = 1
  and pdg.type = 2
  and pdg.is_deleted = 0
  and g.id is null;

# goods 有 promotion 没有: 直接删除
select g.member_type_id, g.goods_id, g.clinic_id
from abc_cis_goods_test.v2_goods_price_opt g
         inner join abc_cis_basic_test.organ o on (g.chain_id = o.id)
         left join (select pdg.id, pd.associate_member_type_id as member_type_id, pdg.goods_id, pdg.clinic_id
                    from abc_cis_promotion_test.v2_promotion_discount pd
                             inner join abc_cis_promotion_test.v2_promotion_discount_goods pdg on (pd.id = pdg.promotion_id)
                    where pd.status = 1
                      and pd.type = 0
                      and pdg.is_deleted = 0
                      and pdg.type = 2) p
                   on (p.member_type_id = g.member_type_id and p.goods_id = g.goods_id and p.clinic_id = g.clinic_id)
where o.his_type = 10
  and g.is_deleted = 0
  and g.target_type = 10
  and g.discount_type != 10
  and p.id is null;

@name: pharmacy_member_price.py
@author: yinxiaoyang
@email: <EMAIL>
@date: 2025-03-06 11:28:44
"""
import argparse
import os
import sys

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from idwork import IdWork

from scripts.common.utils.lists import ListUtils
from scripts.common.utils.organ_util import OrganUtils
from scripts.common.utils.sqls import SqlUtils

DEFAULT_ID = '00000000000000000000000000000000'


class UpdateData:
    env = None
    chain_id = None
    basic_db_client = None
    goods_wdb_client = None
    goods_ob_client = None
    promotion_db_client = None
    db_suffix = None
    organ = None

    def __init__(self, region_name, chain_id, env):
        self.env = env
        self.chain_id = chain_id
        self.region_name = region_name
        self.basic_db_client = DBClient(self.region_name, 'ob', 'abc_cis_basic', env, True)
        self.goods_wdb_client = DBClient(self.region_name, 'abc_cis_stock', 'abc_cis_goods', self.env, True)
        if env == 'dev' or env == 'test':
            self.db_suffix = f'_{env}'
        else:
            self.db_suffix = ''

    def run(self):
        # 所有类型的门店都需要执行
        self.update_pharmacy_target_type()
        self.fill_price_order_item_id()

        # 判断当前门店是否是药店
        organ = self.basic_db_client.fetchone("""
            select * from organ where id = '{chainId}' and his_type = 10
        """.format(chainId=self.chain_id))
        self.organ = organ

        if organ is None:
            # 当前门店不是药店，直接忽略
            return

        self.goods_ob_client = DBClient(self.region_name, 'ob', 'abc_cis_goods', self.env, True)
        self.promotion_db_client = DBClient(self.region_name, 'ob', 'abc_cis_promotion', self.env, True)

        self.sync_promotion_member_price()

    def sync_promotion_member_price(self):
        # 处理 promotion 有但 goods 没有的情况
        self.sync_from_promotion_to_goods()
        # 处理 goods 有但 promotion 没有的情况
        self.sync_from_goods_to_promotion()

    def sync_from_promotion_to_goods(self):
        # 查询 promotion 中有但 goods 中没有的会员价记录
        promotion_records = self.promotion_db_client.fetchall("""
            select pdg.goods_id,
                   pd.chain_id,
                   pdg.clinic_id,
                   pdg.created,
                   pdg.created_by,
                   pdg.last_modified_by,
                   pdg.last_modified,
                   pd.associate_member_type_id as member_type_id,
                   pdg.discount_type,
                   pdg.discount as discount_value
            from abc_cis_promotion{db_suffix}.v2_promotion_discount pd
                     inner join abc_cis_promotion{db_suffix}.v2_promotion_discount_goods pdg on (pd.id = pdg.promotion_id)
                     left join (select * from abc_cis_goods{db_suffix}.v2_goods_price_opt gpo where gpo.target_type = 10 and gpo.is_deleted = 0) g
                               on (pd.associate_member_type_id = g.member_type_id and pdg.goods_id = g.goods_id and pdg.clinic_id = g.clinic_id)
            where pd.chain_id = '{chainId}'
              and pd.type = 0
              and pd.status = 1
              and pdg.goods_type in (1, 2, 7, 24)
              and pdg.type = 2
              and pdg.is_deleted = 0
              and g.id is null
            order by pdg.id;
        """.format(db_suffix=self.db_suffix, chainId=self.chain_id))

        if promotion_records is None or len(promotion_records) == 0:
            print(f"连锁 {self.chain_id} 没有需要从 promotion 同步到 goods 的会员价记录")
            return

        print(f"连锁 {self.chain_id} 需要从 promotion 同步到 goods 的会员价记录数: {len(promotion_records)}")

        # 将 promotion 中的会员价同步到 goods，批量插入，每批50条
        batch_size = 50
        total_records = len(promotion_records)
        self.id_work = IdWork(self.goods_wdb_client, False)
        self.id_work.config()

        # 总部定价
        goods_ids = ListUtils.dist_mapping(promotion_records, lambda e: e['goods_id'])
        goods_list = self.goods_ob_client.fetchall("""
            select *
            from v2_goods
            where id in ({goodsIds}) and status = 1;
        """.format(goodsIds=SqlUtils.to_in_value(goods_ids)))
        goods_id_to_goods = ListUtils.to_map(goods_list, lambda e: e['id'])

        # 门店定价
        normal_goods_price_list = []
        if not OrganUtils.is_single_clinic(self.organ):
            normal_goods_price_list.extend(self.goods_ob_client.fetchall("""
                select *
                from v2_goods_price_opt
                where chain_id = '{chainId}'
                  and goods_id in ({goodsIds})
                  and target_type = 0
                  and is_deleted = 0;
            """.format(chainId=self.chain_id, goodsIds=SqlUtils.to_in_value(goods_ids))))
        goods_price_key_to_goods_price = ListUtils.to_map(normal_goods_price_list, lambda e: e['clinic_id'] + e['goods_id'])

        for i in range(0, total_records, batch_size):
            batch_records = promotion_records[i:i + batch_size]
            values_list = []

            for record in batch_records:
                '''
                    -1:无（单店）
                    0 :子店无自主定价权, 子店无自主定价  (用总部价格)
                    1 :子店有自主定价权 ,子店没有自主定价（用总部价格）
                    2 :子店有自主定价权 ,子店有自主定价(子店设置的)（用子店价格）
                    21:子店有自主定价权 ,子店有自主定价(总部设置的)（用子店价格）
                    3 :子店无自主定价权, 子店有自主定价  (用子店价格)

                    单店和连锁总部:默认为 -1
                    连锁子店:默认为 2，同时查询 goods_price 表，看是否有单独配置，但是在第一次刷数据脚本时，不需要考虑连锁子店，如果要考虑的话，会存在没有门店零售定价但是有门店会员定价的情况（修改定价时 promotion 成功 goods 失败了），
                            这个时候是把门店零售定价也创建出来还是直接把营销那边的配置删除？
                '''
                sub_price_flag = -1
                '''
                    0 :跟随连锁配置
                    10:单独商品定价
                    
                    单店和连锁总部:默认为 0
                    连锁子店:还需要查询 goods_price 表，和 goods_price 配置中该商品的零售价则保持一致否则忽略，忽略的原因和 sub_price_flag 一样
                '''
                individual_pricing_type = 0
                own_type = 1 if record['chain_id'] == record['clinic_id'] else 0

                goods = goods_id_to_goods.get(record['goods_id'])
                if not goods:
                    print("商品 {goodsId} 不存在，但是有会员价配置".format(goodsId=record['goods_id']))
                    continue
                piece_price = goods['piece_price']
                package_price = goods['package_price']
                # TODO pacakge_cost_price 从哪里取？
                package_cost_price = goods['package_cost_price']

                if not OrganUtils.is_single_clinic(self.organ) and self.organ['id'] != self.organ['parent_id']:
                    goods_price_key = record['clinic_id'] + record['goods_id']
                    normal_goods_price = goods_price_key_to_goods_price.get(goods_price_key)
                    if not normal_goods_price:
                        print("门店 {clinicId} 商品 {goodsId} 零售价配置不存在，但是有会员价配置".format(clinicId=record['clinic_id'], goodsId=record['goods_id']))
                        continue
                    sub_price_flag = normal_goods_price['sub_price_flag']
                    individual_pricing_type = normal_goods_price['individual_pricing_type']
                    piece_price = normal_goods_price['piece_price']
                    package_price = normal_goods_price['package_price']
                    package_cost_price = normal_goods_price['package_cost_price']

                value_str = """('{id}', '{goodsId}', '{chainId}', '{clinicId}', '{piece_price}', '{package_price}', '{package_cost_price}', '{created_user_id}', 
                '{created_date}', '{last_modified_user_id}', '{last_modified_date}', '{sub_price_flag}', NULL, 1, NULL, 
                '{individual_pricing_type}', 0, 0, 0, 10, '{member_type_id}', '{discount_type}', '{discount_value}', '{own_type}', '{sort}')""".format(
                    id=self.id_work.getUIDLong(),
                    goodsId=record['goods_id'],
                    chainId=record['chain_id'],
                    clinicId=record['clinic_id'],
                    piece_price=piece_price,
                    package_price=package_price,
                    package_cost_price=package_cost_price,
                    created_user_id=record['created_by'],
                    created_date=record['created'],
                    last_modified_user_id=record['last_modified_by'],
                    last_modified_date=record['last_modified'],
                    sub_price_flag=sub_price_flag,
                    individual_pricing_type=individual_pricing_type,
                    member_type_id=record['member_type_id'],
                    discount_type=record['discount_type'],
                    discount_value=record['discount_value'],
                    own_type=own_type,
                    sort=0
                )
                # 将 value_str 中的'None' 转换为 NULL
                value_str = value_str.replace("'None'", "NULL")
                values_list.append(value_str)

            if values_list:
                batch_insert_sql = """
                insert into v2_goods_price_opt (id, goods_id, chain_id, clinic_id, piece_price, package_price, package_cost_price, created_user_id, 
                created_date, last_modified_user_id, last_modified_date, sub_price_flag, process_price, price_type, price_makeup_percent, 
                individual_pricing_type, pharmacy_type, pharmacy_no, is_deleted, target_type, member_type_id, discount_type, discount_value, own_type, sort)
                values {values};
                """.format(values=',\n'.join(values_list))
                print(batch_insert_sql)
                self.goods_wdb_client.execute(batch_insert_sql)

            print(f"已处理 {min(i + batch_size, total_records)}/{total_records} 条记录")

    def sync_from_goods_to_promotion(self):
        # 查询 goods 中有但 promotion 中没有的会员价记录，直接删除
        goods_records = self.basic_db_client.fetchall("""
            select g.id, g.member_type_id, g.goods_id, g.clinic_id
            from abc_cis_goods{db_suffix}.v2_goods_price_opt g
                     left join (select pdg.id, pd.associate_member_type_id as member_type_id, pdg.goods_id, pdg.clinic_id
                                from abc_cis_promotion{db_suffix}.v2_promotion_discount pd
                                         inner join abc_cis_promotion{db_suffix}.v2_promotion_discount_goods pdg on (pd.id = pdg.promotion_id)
                                where pd.status = 1
                                  and pd.type = 0
                                  and pdg.is_deleted = 0
                                  and pdg.type = 2) p
                               on (p.member_type_id = g.member_type_id and p.goods_id = g.goods_id and p.clinic_id = g.clinic_id)
            where g.chain_id = '{chainId}'
              and g.is_deleted = 0
              and g.target_type = 10
              and g.discount_type != 10
              and p.id is null
            order by g.id;
        """.format(
            db_suffix=self.db_suffix,
            basic=self.basic_db_client.database,
            goods=self.goods_wdb_client.database,
            promotion=self.promotion_db_client.database,
            chainId=self.chain_id,
        ))

        if goods_records is None or len(goods_records) == 0:
            print(f"连锁 {self.chain_id} 没有需要从 goods 删除的会员价记录")
            return

        print(f"门店 {self.chain_id} 需要从 goods 删除的 promotion 的会员价记录数: {len(goods_records)}")

        # 分批标记删除
        batch_size = 50
        total_records = len(goods_records)
        for i in range(0, total_records, batch_size):
            batch_records = goods_records[i:i + batch_size]
            update_sql = """
            update abc_cis_goods{db_suffix}.v2_goods_price_opt set is_deleted = 1 
            where id in ({ids})
            """.format(
                db_suffix=self.db_suffix,
                ids=','.join([str(record['id']) for record in batch_records])
            )
            print(update_sql)
            self.goods_wdb_client.execute(update_sql)

            print(f"已处理 {min(i + batch_size, total_records)}/{total_records} 条记录")

    def update_pharmacy_target_type(self):
        update_sql = """
            update abc_cis_goods{db_suffix}.v2_goods_price_opt set target_type = 1 
            where chain_id = '{chainId}' and target_type = 0 and pharmacy_type is not null and pharmacy_no is not null and is_deleted = 0;
        """.format(db_suffix=self.db_suffix, chainId=self.chain_id)

        print(update_sql)
        self.goods_wdb_client.execute(update_sql)

    def fill_price_order_item_id(self):
        update_sql = """
            update v2_goods_modify_price_order_item
            set item_id = id
            where item_id is null and chain_id = '{chainId}';
        """.format(chainId=self.chain_id)

        print(update_sql)
        self.goods_wdb_client.execute(update_sql)


def main():
    parser = argparse.ArgumentParser(description='药店会员价刷数据脚本')
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名称[ShangHai/HangZhou/...]')
    parser.add_argument('--env', help='环境[dev/test/prod]')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    update_data = UpdateData(args.region_name, args.chain_id, args.env)
    update_data.run()


if __name__ == '__main__':
    main()
