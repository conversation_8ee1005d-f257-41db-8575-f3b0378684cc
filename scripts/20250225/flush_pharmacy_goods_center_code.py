# -*- coding: utf-8 -*-
"""
刷河北药店档案中心编码
@name: flush_pharmacy_goods_center_code.py
@author: <PERSON><PERSON><PERSON>
@email: <EMAIL>
@date: 2024-12-25 20:02:40
"""
import os
import sys
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import argparse
import logging
import json
import re

from scripts.common.utils.lists import ListUtils
from scripts.common.utils.sqls import SqlUtils

from multizone.db import DBClient

DEFAULT_ID = '00000000000000000000000000000000'

# clinic_id = 'ffffffff0000000034aa1f92ad680002'

env='prod'


class UpdateData:
    chain_id = None
    basic_db_client = None
    goods_db_client = None
    domain_db_client = None

    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', env, True)
        self.domain_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_domain', env, True)
        self.goods_db_client = DBClient(self.region_name, 'abc_cis_stock', 'abc_cis_goods', env, True)

    def run(self):
        # print(re.sub(r"[\u4e00-\u9fa5]", "", '国药准字B20060001'))
        self.flush_hebei_pharmacy_goods_center_code()

    def flush_hebei_pharmacy_goods_center_code(self):
        organ_list = self.basic_db_client.fetchall("""
        select * from organ where parent_id = '{chainId}' and his_type = 10 and address_province_id = '130000' and status = 1
        """.format(chainId=self.chain_id))
        # and address_province_id = '130000';
        if organ_list is None or len(organ_list) == 0:
            return
        clinic_id_list = []
        for organ in organ_list:
            if organ['view_mode'] == 1:
                if organ['id'] != self.chain_id:
                    clinic_id_list.append(organ['id'])
            else:
                clinic_id_list.append(organ['id'])
        if len(clinic_id_list) == 0:
            return
        # -- ffffffff0000000034a8b1f426718000
        # 有没有国药准字的药品
        goods_list = self.goods_db_client.fetchall("""
        select * from v2_goods where organ_id = '{chainId}' and status = 1 and type = 1 and sub_type != 2 and medicine_nmpn is not null and medicine_nmpn != ''
        """.format(chainId=self.chain_id))
        if goods_list is None or len(goods_list) == 0:
            return
        goods_id_list = [x['id'] for x in goods_list]
        goods_id_sql_in = f"goods_id in ({SqlUtils.to_in_value(goods_id_list)}) and" if goods_id_list else ""
        clinic_id_sql_in = f"organ_id in ({SqlUtils.to_in_value(clinic_id_list)})" if clinic_id_list else ""
        # print(goods_id_sql_in)
        goods_extend_list = self.goods_db_client.fetchall("""
        select * from v2_goods_extend where {goods_id} chain_id = '{chainId}' and {clinicId}
        """.format(goods_id=goods_id_sql_in, chainId=self.chain_id, clinicId=clinic_id_sql_in))
        goods_id_to_extend = ListUtils.to_map(goods_extend_list, lambda x: x['goods_id'] + '-' + x['organ_id'])
        if goods_id_to_extend is None or len(goods_id_to_extend) == 0:
            return
        offset = 0
        limit = 10000
        domain_list = []
        while True:
            domain_page_list = self.domain_db_client.fetchall("""
            select * from domain_medicine_standard_match limit {offset}, {limit};
            """.format(offset=offset, limit=limit))
            if domain_page_list is None or len(domain_page_list) == 0:
                break
            domain_list.extend(domain_page_list)
            offset += limit
        if len(domain_list) == 0:
            return
        approved_code_to_list = ListUtils.group_by(domain_list, lambda x: re.sub(r"[\u4e00-\u9fa5]", "", x['approved_code'].lower()))
        sql_list = []
        for organ_id in clinic_id_list:
            for goods in goods_list:
                # print(goods)
                goods_id = goods['id']
                goods_extend = goods_id_to_extend.get(goods_id + '-' + organ_id)
                if goods_extend is None:
                    print(f'not found extend, {goods_id}, {organ_id}')
                    continue
                medicine_nmpn = goods['medicine_nmpn']
                if medicine_nmpn is None:
                    continue
                approved_code = re.sub(r"[\u4e00-\u9fa5]", "", f'{medicine_nmpn}')
                center_code_list = approved_code_to_list.get(approved_code.lower())
                if center_code_list is None or len(center_code_list) == 0:
                    print(f'not found {medicine_nmpn}')
                    continue
                # domain_info = domain_dict.get(approved_code)
                # if domain_info is None:
                #     print(f'not found {medicine_nmpn}')
                #     continue
                standardCode = None
                shebao_name = None
                manufacturer = goods['manufacturer_full']
                shebao_extend_info = goods_extend['shebao_extend_info']
                if shebao_extend_info is not None:
                    shebao_extend_info_json = json.loads(shebao_extend_info)
                    if shebao_extend_info_json is not None:
                        shebao_name = shebao_extend_info_json.get('shebaoName')
                        standardCode = shebao_extend_info_json.get('standardCode')

                exist_center_domain_info = None
                for center_code_info in center_code_list:
                    # 比较shebao_name、manufacturer、standardCode
                    std_cadn = center_code_info['cadn']
                    std_manuf = center_code_info['manufacture']
                    std_standard_code = center_code_info['admin_standard_code']
                    # 标准数据都没有
                    if std_cadn is None and std_manuf is None and std_standard_code is None:
                        continue
                    if shebao_name is None and manufacturer is None and standardCode is None:
                        continue
                    if is_equal(shebao_name, std_cadn):
                        if is_equal(manufacturer, std_manuf) or is_equal(standardCode, std_standard_code):
                            exist_center_domain_info = center_code_info
                            break
                    else:
                        if is_equal(manufacturer, std_manuf) or is_equal(standardCode, std_standard_code):
                            exist_center_domain_info = center_code_info
                            break

                if exist_center_domain_info is None:
                    exist_center_domain_info = center_code_list[0]

                if exist_center_domain_info is None:
                    continue

                center_code = exist_center_domain_info['center_code']
                if shebao_extend_info is None:
                    extend_data = {
                        'centerCode': center_code
                    }
                    sql = """
                    update v2_goods_extend set shebao_extend_info = '{extendData}' where goods_id = '{goodsId}' and organ_id = '{clinicId}';
                    """.format(extendData=json.dumps(extend_data), goodsId=goods['id'], clinicId=organ_id)
                    sql_list.append(sql)
                else:
                    # extend_data['centerCode'] = domain_info['center_code']
                    sql = """
                    update v2_goods_extend set shebao_extend_info = json_set(shebao_extend_info, '$.centerCode', '{centerCode}') where goods_id = '{goodsId}' and organ_id = '{clinicId}';
                    """.format(centerCode=center_code, goodsId=goods['id'], clinicId=organ_id)
                    sql_list.append(sql)
        if len(sql_list) == 0:
            return
        for sql in sql_list:
            # print(sql)
            if sql is None:
                continue
            self.goods_db_client.execute(sql)
        pass


def is_equal(a, b):
    if a is None or b is None:
        return False
    return a == b


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    update_data = UpdateData(args.region_name, args.chain_id)
    update_data.run()


if __name__ == '__main__':
    main()