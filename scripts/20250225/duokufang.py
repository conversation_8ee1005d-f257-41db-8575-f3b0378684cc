#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.rpc import regionRpcHost
import json
import argparse
import requests

default_id = '00000000000000000000000000000000'


# 多库房把以前老的药房类型刷到新的标上
def flushRule(abcRegion, chain_id):
    goods_db_client = DBClient(abcRegion, 'abc_cis_stock', 'abc_cis_goods', 'prod', True)
    basic_db_client = DBClient(abcRegion, 'abc_cis_mixed', 'abc_cis_property', 'prod', True)
    organs = basic_db_client.fetchall(
        ''' select id as clinicId,his_type as hisType from abc_cis_basic.organ where parent_id = '{chainId}' '''.format(chainId=chain_id))
    for organ in organs:
        hisType = organ['hisType']
        if hisType != 10:
           return ;
    goods_db_client.execute(
        """update v2_goods_pharmacy_rule as r inner join  v2_goods_clinic_config as c on r.clinic_id = c.clinic_id and c.his_type = 10 and  r.inner_flag = 1
set r.goods_type =JSON_ARRAY_APPEND(
r.goods_type,
'$.typeIdList',
JSON_OBJECT('typeId', 91)
)
where  JSON_CONTAINS(
    r.goods_type->'$.typeIdList',
    JSON_OBJECT('typeId', 17),
    '$'
) and not JSON_CONTAINS(
    r.goods_type->'$.typeIdList',
    JSON_OBJECT('typeId', 91),
    '$'
) and c.chain_id='{chainId}';"""
        .format(chainId=chain_id ));

    goods_db_client.execute(
        """
        update v2_goods_pharmacy_rule as r inner join  v2_goods_clinic_config as c on r.clinic_id = c.clinic_id and c.his_type = 10 and  r.inner_flag = 1
set r.goods_type =JSON_ARRAY_APPEND(
        r.goods_type,
        '$.typeIdList',
        JSON_OBJECT('typeId', 92)
    )
where  JSON_CONTAINS(
            r.goods_type->'$.typeIdList',
            JSON_OBJECT('typeId', 25),
            '$'
    ) and not JSON_CONTAINS(
            r.goods_type->'$.typeIdList',
            JSON_OBJECT('typeId', 92),
            '$'
    )
 and c.chain_id='{chainId}';"""
        .format(chainId=chain_id ));
    goods_db_client.execute(
        """
        update v2_goods_chain_config as c set goods_purchase_cycle_days = JSON_ARRAY_APPEND(JSON_ARRAY_APPEND(
                                                                                            goods_purchase_cycle_days,
                                                                                            '$',
                                                                                            JSON_OBJECT('typeId', 91, 'name', '消毒用品', 'days', 30)),'$', JSON_OBJECT('typeId', 92, 'name', '化妆品', 'days', 30))
where  c.his_type = 10 and not  JSON_CONTAINS(
        c.goods_purchase_cycle_days,
        JSON_OBJECT('typeId', 91),
        '$'
    )
 and c.chain_id='{chainId}';"""
        .format(chainId=chain_id ));






def run(abcRegion, chain_id):
    flushRule(abcRegion, chain_id)
    requests.put("""http://{rpcHost}/rpc/v3/goods/jenkins/stock-goods/out-order-to-in-order?chainId={chainId}""".format(
        chainId = chain_id,rpcHost=regionRpcHost(abcRegion)))


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.region_name, args.chain_id)


if __name__ == '__main__':
    main()
