"""

"""
import json
import logging
import os

import requests

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.config import region_id
import argparse

default_id = '00000000000000000000000000000000'

env_to_nuonuo_ope_mplatform_app_id = {
    'dev': 'SD56531018',
    'test': 'SD56531018',
    'pre': '67071828',
    'gray': '67071828',
    'prod': '67071828'
}


def update_nuonuo_fully_invoice_category(region_name, chain_id, env):
    db_cli = DBClient(region_name, 'abc_cis_bill', 'abc_cis_invoice', env, True)
    # 1、v2_invoice_config.type
    invoice_configs = db_cli.fetchall(
        f"""select * from v2_invoice_config where chain_id = '{chain_id}' and type = 1 and app_id = '{env_to_nuonuo_ope_mplatform_app_id[env]}' and is_deleted = 0;""")
    db_cli.execute(
        f"""update v2_invoice_config set type = 3 where chain_id = '{chain_id}' and type = 1 and app_id = '{env_to_nuonuo_ope_mplatform_app_id[env]}' and is_deleted = 0;""")
    # 2、v2_invoice_bill_config.invoice_category
    clinic_ids = [c['clinic_id'] for c in invoice_configs]
    if len(clinic_ids) > 0:
        db_cli.execute(
            f"""update v2_invoice_bill_config set invoice_category = 3 where clinic_id in ({', '.join([f"'{c}'" for c in clinic_ids])}) and invoice_category = 1 and is_deleted = 0;""")
    # 3、v2_invoice_record.invoice_category
    # 4、v2_invoice_sheet.invoice_category
    db_cli.execute(f"""
    update v2_invoice_sheet s inner join v2_invoice_record r on s.id = r.invoice_sheet_id
    set s.invoice_category = 3,
        r.invoice_category = 3
    where s.invoice_category = 1
      and s.invoice_line = 'pc'
      and s.chain_id = '{chain_id}'
    """)


def insert_or_update_invoice_fee_item(env, region_name):
    province_city_district_supplier_id_mappings = {}
    for r in ['ShangHai', 'HangZhou']:
        basic_db_client = DBClient(r, 'ob', 'abc_cis_basic', 'prod', False,
                                   jumper_user='jiangxf' if r == 'HangZhou' else 'jiangxiaofeng')
        db_client = DBClient(r, 'ob', 'abc_cis_invoice', 'prod', False,
                             jumper_user='jiangxf' if r == 'HangZhou' else 'jiangxiaofeng')
        rows = db_client.fetchall(
            f"""select * from v2_invoice_config where mapping_config is not null and is_deleted = 0;""")
        for row in rows:
            supplier_id = row['invoice_supplier_id']
            clinic_id = row['clinic_id']
            mapping_config = row['mapping_config']
            clinic = basic_db_client.fetchone(f"""select * from organ where id = '{clinic_id}';""")
            key = f"""{clinic['address_province_id']}_{clinic['address_city_id']}_{clinic['address_district_id']}_{supplier_id}"""
            if mapping_config not in province_city_district_supplier_id_mappings:
                province_city_district_supplier_id_mappings[mapping_config] = []
            if key not in province_city_district_supplier_id_mappings[mapping_config]:
                province_city_district_supplier_id_mappings[mapping_config].append(key)

    for mapping_config_str, keys in province_city_district_supplier_id_mappings.items():
        print(f"""mapping_config: {mapping_config_str}, keys: {keys}""")
        mapping_config = json.loads(mapping_config_str)
        region_ids = keys[0].split('_')
        province_id = region_ids[0]
        city_id = region_ids[1]
        district_id = region_ids[2]
        supplier_id = region_ids[3]
        scope_id = province_id
        scope_type = 10
        if city_id in ['110100', '120100', '310100', '500100']:
            # 直辖市
            scope_id = city_id
            scope_type = 20
        rsp = json.loads(
            requests.post(
                url=f'http://{env if env != "prod" else "pre"}.rpc.abczs.cn/rpc/invoice/feetype-mapping',
                headers={
                    '__region_id__': str(region_id(region_name))
                },
                json={
                    'scopeId': scope_id,
                    'operatorId': default_id,
                    'scopeType': scope_type,
                    'supplierId': supplier_id,
                    'items': [
                                 {
                                     'feeTypeId': 33,
                                     'itemCode': mapping_config['defaultOutpatientMapping']['code'],
                                     'itemName': mapping_config['defaultOutpatientMapping']['name'],
                                     'businessFlag': 0
                                 }
                             ] + [
                                 {
                                     'feeTypeId': i['feeTypeId'],
                                     'itemCode': i['code'],
                                     'itemName': i['name'],
                                     'businessFlag': 0
                                 } for i in mapping_config['outpatients']
                             ]
                }
            ).content.decode('utf-8')
        )
        logging.info(f"""rsp: {rsp}""")


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    update_nuonuo_fully_invoice_category(args.region_name, args.chain_id, args.env)
    # insert_or_update_invoice_fee_item(args.env)


if __name__ == '__main__':
    main()
