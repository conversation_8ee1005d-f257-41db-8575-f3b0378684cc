#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys 
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db 
import argparse


def query_chain_config(chain_id):
    sql = '''
        select o.id,o.parent_id, o.node_type, o.view_mode, o.his_type
        from abc_cis_basic.organ o inner join abc_cis_goods.v2_goods_chain_config cfg on cfg.chain_id = o.id and  o.id = '{0}'
        WHERE  cfg.node_type != o.node_type or cfg.view_mode != o.view_mode or cfg.his_type != o.his_type
    '''.format(chain_id)

    db_client = db.DBClient('ods', 'abc_cis_goods')
    return db_client.fetchall(sql)

def query_clinic_config(chain_id):
    sql = '''
        select o.id,o.parent_id, o.node_type, o.view_mode, o.his_type
        from abc_cis_basic.organ o inner join abc_cis_goods.v2_goods_clinic_config cfg on cfg.clinic_id = o.id and o.parent_id = '{0}'
        where  cfg.node_type != o.node_type or cfg.view_mode != o.view_mode or cfg.his_type != o.his_type
    '''.format(chain_id)

    db_client = db.DBClient('ods', 'abc_cis_goods')
    return db_client.fetchall(sql)

def update_goods_chain_config(goods_db_client, chain_config):
    update_sql = '''
        update abc_cis_goods.v2_goods_chain_config set node_type = '{0}',view_mode = '{1}' , his_type = '{2}' where  chain_id = '{3}'
    '''.format(chain_config['node_type'], chain_config['view_mode'], chain_config['his_type'],chain_config['id'])
    goods_db_client.execute(update_sql)

def update_goods_clinic_config(goods_db_client, clinic_config):
    update_sql = '''
        update abc_cis_goods.v2_goods_clinic_config set node_type = '{0}',view_mode = '{1}' , his_type = '{2}' where  clinic_id = '{3}'
    '''.format(clinic_config['node_type'], clinic_config['view_mode'], clinic_config['his_type'],clinic_config['id'])
    goods_db_client.execute(update_sql)


def run(chain_id):
    goods_db_client = db.DBClient('abc_cis_stock', 'abc_cis_goods')
    chain_config_list = query_chain_config(chain_id)
    if  chain_config_list and len(chain_config_list) > 0:
        for chain_config in chain_config_list:
            update_goods_chain_config(goods_db_client, chain_config)


    clinic_config_list = query_clinic_config(chain_id)
    if  clinic_config_list and len(clinic_config_list) > 0:
        for clinic_config in clinic_config_list:
            update_goods_clinic_config(goods_db_client, clinic_config)



def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)

if __name__ == '__main__':
    main()
