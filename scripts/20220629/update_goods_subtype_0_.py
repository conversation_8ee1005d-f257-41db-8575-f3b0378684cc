#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys 
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db 
import argparse
import logging
logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)
import datetime
from dateutil.relativedelta import relativedelta


class UpdateGoodsType(object):

    def __init__(self):
        self._db_client_cache = {}


    def _get_db_client(self, cluster, database):
        k = '{0}-{1}'.format(cluster, database)
        if k in self._db_client_cache.keys():
            return self._db_client_cache[k]
        
        db_client = db.DBClient(cluster, database)
        self._db_client_cache[k] = db_client
        return db_client
    def copy_items_v2(self, chain_id):
        charge_db_client = self._get_db_client('abc_cis_charge', 'abc_cis_charge')
        logging.info('execute copy charge-form-item id chain_id: {0}'.format(chain_id))
        beginDate='2021-01-01'
        #update v2_charge_form_item  先从abd里面查出来，其他库都查不出来
        insert_sql = '''
                 insert into tmp_subtype0_form_item (id, chain_id)
        select id,chain_id
        from v2_charge_form_item
        where chain_id = '{0}'
          and product_type in( 5,12,13)  and created > '{1}'
          and product_sub_type = 1 ;
        '''.format(chain_id,beginDate)
        charge_db_client.execute(insert_sql)
        insert_sql = '''
                 insert into tmp_subtype0_trans (id, chain_id)
        select id,chain_id
        from v2_charge_transaction_record
        where chain_id = '{0}'
          and product_type in( 5,12,13)  and created > '{1}'
          and product_sub_type = 1 ;
        '''.format(chain_id,beginDate)
        charge_db_client.execute(insert_sql)

    def copy_items(self, chain_id):
        db_client = self._get_db_client('adb', 'abc_cis_charge')
        charge_db_client = self._get_db_client('abc_cis_charge', 'abc_cis_charge')
        logging.info('execute copy charge-form-item id chain_id: {0}'.format(chain_id))
        beginDate='2022-03-01'
        #update v2_charge_form_item  先从abd里面查出来，其他库都查不出来
        sql = '''
        select id,chain_id
        from v2_charge_form_item
        where chain_id = '{0}'
          and product_type in( 5,12,13)  and created > '{1}'
          and product_sub_type = 1 ;
        '''.format(chain_id,beginDate)
        formIdList = db_client.fetchall(sql)
        if formIdList is not None and len(formIdList) > 0:
            values = ','.join([ ''' ('{0}', '{1}') '''.format(item['id'], item['chain_id']) for item in formIdList ])
            insert_sql = '''
                 insert into tmp_subtype0_form_item (id, chain_id) values {0}
            '''.format(values)
            charge_db_client.execute(insert_sql)
        sql = '''
        select id,chain_id
        from v2_charge_transaction_record
        where chain_id = '{0}'
          and product_type in( 5,12,13)  and created > '{1}'
          and product_sub_type = 1 ;
        '''.format(chain_id,beginDate)
        recordIdList = db_client.fetchall(sql)
        if recordIdList is not None and len(recordIdList) > 0:
            values = ','.join([ ''' ('{0}', '{1}') '''.format(item['id'], item['chain_id']) for item in recordIdList ])
            insert_sql = '''
                 insert into tmp_subtype0_trans (id, chain_id) values {0}
            '''.format(values)
            charge_db_client = self._get_db_client('abc_cis_charge', 'abc_cis_charge')
            charge_db_client.execute(insert_sql)


    def update_charge(self, chain_id):
        # 四个月四个月的来整
        now_date = datetime.datetime.now()
        PER_MONTH = 12
        index = 0
        #ABC诊所管家不到四年
        while index < 4 :
            endDate = (now_date - relativedelta(months= index*PER_MONTH) ).strftime('%Y-%m-%d')
            beginDate = (now_date -  relativedelta(months= (index+1) *PER_MONTH) ).strftime('%Y-%m-%d')
            self.update_charge_peroid(chain_id,beginDate,endDate)
            index += 1

    def update_charge_peroid(self, chain_id):
        charge_db_client = self._get_db_client('abc_cis_charge', 'abc_cis_charge')
        update_sql ='''
                    update v2_charge_form_item as a inner join tmp_subtype0_form_item as b on a.id = b.id and a.chain_id = b.chain_id
                        set a.product_sub_type = 0
                        where a.chain_id = '{0}';
                '''.format(chain_id)
        charge_db_client.execute(update_sql)
        update_sql ='''
                    update v2_charge_transaction_record as a inner join tmp_subtype0_trans as b on a.id = b.id and a.chain_id = b.chain_id
                        set a.product_sub_type = 0
                        where a.chain_id = '{0}';
                '''.format(chain_id)
        charge_db_client.execute(update_sql)



    def update_promotion(self, chain_id):
        sqls = [
            #  挂号费
            '''
            update v2_promotion_goods a inner join v2_promotion b on a.promotion_id = b.id
            set goods_sub_type = 0
            where b.chain_id = '{0}'
              and a.type = 1
              and a.goods_type  = 5
              and a.goods_sub_type = 1;
            ''',
            '''
           update v2_promotion_discount_goods
           set goods_sub_type = 0
           where chain_id = '{0}'
             and type = 1
             and goods_type = 5
             and goods_sub_type = 1;
            ''',

            '''
            update v2_promotion_card_goods
            set goods_sub_type = 0
            where chain_id = '{0}'
              and type = 1
              and goods_type = 5
              and goods_sub_type = 1;
            ''',
            # 在线问诊
            '''
            update v2_promotion_goods a inner join v2_promotion b on a.promotion_id = b.id
            set goods_sub_type = 0
            where b.chain_id = '{0}'
              and a.type = 1
              and a.goods_type = 12
              and a.goods_sub_type = 1;
            ''',
            '''
            update v2_promotion_discount_goods
            set goods_sub_type = 0
            where chain_id = '{0}'
              and type = 1
              and goods_type = 12
              and goods_sub_type = 1;
            ''',
            '''
            update v2_promotion_card_goods
            set goods_sub_type = 0
            where chain_id = '{0}'
              and type = 1
              and goods_type = 12
              and goods_sub_type = 1;
            ''',
            # 快递费
            '''
            update v2_promotion_goods a inner join v2_promotion b on a.promotion_id = b.id
            set goods_sub_type = 0
            where b.chain_id = '{0}'
              and a.type = 1
              and a.goods_type = 13
              and a.goods_sub_type = 1;
            ''',
            '''
            update v2_promotion_discount_goods
            set goods_sub_type = 0
            where chain_id = '{0}'
              and type = 1
              and goods_type = 13
              and goods_sub_type = 1;
            ''',
            '''
            update v2_promotion_card_goods
            set goods_sub_type = 0
            where chain_id = '{0}'
              and type = 1
              and goods_type = 13
              and goods_sub_type = 1;
            '''
        ]

        promotion_db_client = self._get_db_client('abc_cis_charge', 'abc_cis_promotion')

        for sql in sqls:
            sql_f = sql.format(chain_id)
            print sql_f
            promotion_db_client.execute(sql_f)


    def execute_chain(self, chain_id):
        self.update_promotion(chain_id)
#         self.copy_items(chain_id)
#         self.update_charge_peroid(chain_id)


def run(chain_id):
    rgt = UpdateGoodsType()
    rgt.execute_chain(chain_id)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)

if __name__ == '__main__':
    main()
