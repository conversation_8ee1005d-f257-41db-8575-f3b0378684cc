#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys 
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

import db 
import argparse


def update_reg_support_shebao_paymode_config(chain_id):
    sql = '''
    update shebao_registration_fees_matched_code
            set shebao_pay_mode = 2,
            shebao_code = null,
            last_modified = now()
        where chain_id = '{0}' and is_deleted = 0 and shebao_code = 'DISABLED';
    '''.format(chain_id)

    db_client = db.DBClient('abc_cis_bill', 'abc_cis_shebao')
    return db_client.execute(sql)
def update_jiagong_support_shebao_paymode_config(chain_id):
    sql = '''
    update shebao_process_fees_matched_code
    set shebao_pay_mode = 2,
        shebao_code = null,
        last_modified = now()
    where chain_id = '{0}'  and is_deleted = 0 and shebao_code = 'DISABLED';
    '''.format(chain_id)

    db_client = db.DBClient('abc_cis_bill', 'abc_cis_shebao')
    return db_client.execute(sql)


def run(chain_id):
    update_reg_support_shebao_paymode_config(chain_id)
    update_jiagong_support_shebao_paymode_config(chain_id)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id)

if __name__ == '__main__':
    main()
