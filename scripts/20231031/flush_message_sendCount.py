"""
刷新formIsDeleted 的数据
"""
import argparse
import os
import sys
from datetime import date, timedelta

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient


class UpdateData:
    chain_id = None
    short_url_db_client = None
    emr_db_client = None
    scform_db_client = None
    basic_db_client = None
    id_work = None
    patient_tag_template_chain_id = None
    type = None
    businessType = None

    def __init__(self, region_name, chain_id, env):
        self.chain_id = chain_id
        self.region_name = region_name
        self.adb_client = DBClient(self.region_name, 'abc_cis_account_base', 'abc_cis_message', env, True)
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_account_base', 'abc_cis_message', env, True)
        self.beginTime = date.today() - timedelta(days=365)
        # self.endTime = date.today() + timedelta(days=1)
        self.endTime = date.today() + timedelta(days=1)

    def run(self):
        self.updateMessageSendCount()

    def updateMessageSendCount(self):
        try:
            sql = '''
                select DATE_FORMAT(created, '%Y%m%d') as day, sum(message_count) as total
                from v2_message_log
                where chain_id = '{chainId}'
                  and created >= '{beginDate}'
                  and created <= '{endDate}'
                  and message_channel = '{messageChannel}'
                  and is_bill = 0
                group by day;
            '''

            insertSql = '''
            insert into v2_message_send_stat (chain_id, `date`, sms_count, wx_count) values
            '''
            valueSql = '''('{chainId}', '{day}', '{smsCount}', '{wxCount}')'''

            conflictSql = ''' on duplicate key update sms_count = values(sms_count), wx_count = values(wx_count), last_modified = now()'''

            # 一个月一个月的刷
            beginDate = self.beginTime
            while beginDate < self.endTime:
                endDate = beginDate + timedelta(days=15)
                if endDate > self.endTime:
                    endDate = self.endTime

                print('sms ' + sql.format(chainId=self.chain_id, beginDate=beginDate, endDate=endDate, messageChannel=0))
                smsMessageSendStatList = self.adb_client.fetchall(
                    sql.format(chainId=self.chain_id, beginDate=beginDate, endDate=endDate, messageChannel=0))

                print('wx '+ sql.format(chainId=self.chain_id, beginDate=beginDate, endDate=endDate, messageChannel=1))
                wxMessageSendStatList = self.adb_client.fetchall(
                    sql.format(chainId=self.chain_id, beginDate=beginDate, endDate=endDate, messageChannel=1))
                smsMsgStatMap = {smsMsgSendStat['day']: smsMsgSendStat['total'] for smsMsgSendStat in
                                 smsMessageSendStatList}
                wxMsgStatMap = {wxMsgSendStat['day']: wxMsgSendStat['total'] for wxMsgSendStat in wxMessageSendStatList}

                values = []
                currentDate = beginDate
                print(self.chain_id + ' ' + currentDate.strftime("%Y-%m-%d") + ' ' + endDate.strftime("%Y-%m-%d"))
                while currentDate <= endDate:
                    day = currentDate.strftime("%Y%m%d")
                    smsCount = smsMsgStatMap.get(day)
                    if smsCount is None:
                        smsCount = 0
                    wxCount = wxMsgStatMap.get(day)
                    if wxCount is None:
                        wxCount = 0

                    if wxCount != 0 or smsCount != 0:
                        values.append(
                            valueSql.format(chainId=self.chain_id, day=day, smsCount=smsCount, wxCount=wxCount))
                    currentDate += timedelta(days=1)

                try:
                    if len(values) != 0:
                        print(insertSql + ','.join(values) + conflictSql)
                        self.basic_db_client.execute(insertSql + ','.join(values) + conflictSql)
                except Exception as e:
                    print(self.chain_id, insertSql)
                    print(e)

                beginDate = endDate

        except Exception as e:
            print(self.chain_id)
            print(e)


def main():
    # basic_db_client = DBClient('ShangHai', 'abc_cis_basic', 'abc_cis_basic', 'test', True)
    # chainSql = '''
    #     select id
    #     from organ
    #     where node_type = 1;
    # '''
    # chains = basic_db_client.fetchall(chainSql)
    # for chain in chains:
    #     updateData = UpdateData('ShangHai', chain['id'], 'test')
    #     updateData.run()

    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    updateData = UpdateData(args.region_name, args.chain_id, 'prod')
    updateData.run()

    # region = 'ShangHai'
    # chain_id = '628f2c02d90c480fa26fbed3d579ebc2'
    # updateData = UpdateData(region, chain_id, 'test')
    # updateData.run()


if __name__ == '__main__':
    main()
