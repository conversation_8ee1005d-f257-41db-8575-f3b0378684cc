#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import logging
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from multizone.rediscli import RedisClient
import argparse
import requests

default_id = '00000000000000000000000000000000'

def run(region_name, chain_id, env):
    adb_client = DBClient(region_name, 'ods', 'abc_cis_examination', env, True)
    invoice_client = DBClient(region_name, 'abc_cis_bill', 'abc_cis_invoice', env, True)
    invoice_bill_configs = invoice_client.fetchall(f"""
        select * from v2_invoice_bill_config where chain_id = '{chain_id}'
    """)
    exist_clinic_ids = [invoice_bill_config['clinic_id'] for invoice_bill_config in invoice_bill_configs]
    stmts = adb_client.fetchall(f"""
    select v2_scope_id, CONCAT('INSERT INTO v2_invoice_bill_config (id, chain_id, clinic_id, enable_auto_bill, invoice_category, refund_auto_destroy, normal_invoice_bill_strategy, is_deleted, created_by, created, last_modified_by, last_modified) VALUES (substr(uuid_short(), 4), \\'',
               o.parent_id, '\\', \\'', v2_scope_id, '\\', ', 0, ', ', 0, ', ', if(find_in_set('1', vals) > 0, 1, 0), ', ',
               0, ', ', 0,
               ', ',
               '\\'00000000000000000000000000000000\\'', ', \\'', now(), '\\', ',
               '\\'00000000000000000000000000000000\\'', ', \\'', now(), '\\');') as stmt
from (
         select v2_scope_id, group_concat(value) as vals
         from abc_cis_property.v2_property_config_item v2pci
         where `key` in (
                         'invoiceManagement.registrationRefundAutoDestroy',
                         'invoiceManagement.outpatientRefundAutoDestroy',
                         'invoiceManagement.hospitalRefundAutoDestroy'
             )
           and scope = 'clinic'
         group by v2_scope_id
     ) as t
         inner join abc_cis_basic.organ o on t.v2_scope_id = o.id
         where o.parent_id = '{chain_id}';""")
    if len(stmts) == 0:
        logging.info('stmts is empty')
        return
    for stmt in stmts:
        if exist_clinic_ids.__contains__(stmt['v2_scope_id']):
            logging.info(f"clinic: {stmt['v2_scope_id']} already insert v2_invoice_bill_config")
            continue
        invoice_client.execute(stmt['stmt'])

    # 实收金额
    invoice_client.execute(f"""
    update v2_invoice_record
    set received_fee = invoice_fee
    where invoice_fee is not null
      and business_id is not null
      and goods_fee_type_id is null
      and received_fee is null
      and chain_id = '{chain_id}';
    """)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.region_name, args.chain_id, 'prod')
    # run('ShangHai', 'ffffffff00000000146808c695534000', 'dev')


if __name__ == '__main__':
    main()
