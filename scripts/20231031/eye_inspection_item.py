"""
刷新formIsDeleted 的数据
"""
import json
import os
import sys
import argparse
from datetime import date, datetime, time, timedelta

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient

default_id = '00000000000000000000000000000000'
standard_chain_id_map = {
    'dev': {
        'normal_template_chain_id': 'ffffffff000000000c5a1308069aa000',
        'eye_template_chain_id': 'ffffffff0000000025ba072801ec0000'
    },
    'test': {
        'normal_template_chain_id': 'ffffffff000000001cdb3b8007024000',
        'eye_template_chain_id': 'ffffffff00000000266aac780890e000'
    },
    'prod': {
        'normal_template_chain_id': 'ffffffff000000001f2d1f400a1e6000',
        'eye_template_chain_id': 'ffffffff0000000026f573b00acb4000'
    }
}

f = open('./eye_inspection_item.sql', "w+")


def pair_split_left_right(standard_chain_id, names, is_standard, is_template, env):
    adb = DBClient('ShangHai', 'adb', 'abc_cis_examination', env, True)
    if is_template == 1:
        rows = adb.fetchall(
            f"""select * from v2_examination_item where inspect_type is not null and name in ({','.join([f"'{n}'" for n in names])}) 
            and is_standard = {is_standard} and is_deleted = 0
            {f" and chain_id = '{standard_chain_id}'" if standard_chain_id is not None else ''}
            ;""")
    else:
        rows = adb.fetchall(
            f"""select * from v2_examination_item where inspect_type is not null and name in ({','.join([f"'{n}'" for n in names])}) 
                    and is_standard = {is_standard} and is_deleted = 0
                    and chain_id != '{standard_chain_id}'
                    ;""")
    if len(rows) <= 0:
        print(f'skip')
        return
    item_ids = [row['id'] for row in rows]
    goods_id_name_inspect_type_to_source_id = {}
    src_id_to_goods_id = {}
    if is_standard == 0:
        src_items = adb.fetchall(
            f"""select * from v2_examination_item where inspect_type is not null and name in ({','.join([f"'{n}'" for n in names])}) 
            and is_deleted = 0
            {f" and chain_id = '{standard_chain_id}'" if standard_chain_id is not None else ''}
            ;""")
        # 先补充prod
        if env == 'prod':
            src_items.extend([
                {'id': 'af57630b1dfb4356b7d365438eff1ec7', 'goods_id': '', 'name': '明视瞳孔直径', 'inspect_type': 1},
                {'id': 'ff237f9ef1ea467ba8f2b47e35d60f81', 'goods_id': '', 'name': '暗视瞳孔直径', 'inspect_type': 1},
                {'id': '35ae1346afc44038b464693880dbe79d', 'goods_id': 'ffffffff000000002661524811b8c000', 'name': '暗视瞳孔直径', 'inspect_type': 1},
                {'id': 'b421001cc675437e8719fb46f8d15c77', 'goods_id': 'ffffffff000000002661524811b8c000', 'name': '明视瞳孔直径', 'inspect_type': 1}
            ])
        goods_id_name_inspect_type_to_source_id = {f"{src_item['goods_id']}_{src_item['name']}_{src_item['inspect_type']}": src_item['id'] for src_item in src_items}
        src_id_to_goods_id = {src_item['id']: src_item['goods_id'] for src_item in src_items}
    ref_details = adb.fetchall(
        f"""select * from v2_examination_item_ref_detail where is_deleted = 0 and item_id in ({','.join([f"'{item_id}'" for item_id in item_ids])});""")
    item_id_to_ref_details = {}
    for ref_detail in ref_details:
        item_id_ref_details = item_id_to_ref_details.get(ref_detail['item_id'], [])
        item_id_ref_details.append(ref_detail)
        item_id_to_ref_details[ref_detail['item_id']] = item_id_ref_details
    for row in rows:
        uuid = adb.fetchone("select replace(uuid(), '-', '') as uuid")['uuid']
        sql = f"""
            INSERT INTO v2_examination_item (id, clinic_id, chain_id, type, name, en_name, search_text,
                                                             unit, ref, result_display_scale, is_deleted, created_user_id,
                                                             created_date, last_modified_user_id, last_modified_date,
                                                             item_code, goods_id, source_id, inspect_type, component_type,
                                                             options, constraints, group_id, value_quantity, display_name,
                                                             is_standard, tag, sort, additional, item_type) 
            VALUES ('{uuid}', '{row['clinic_id']}', '{row['chain_id']}', {row['type']}, '{row['name']}',
            '{row['en_name'].replace('B', 'R')}', '{row['search_text']}','{row['unit']}','{row['ref']}',{row['result_display_scale']},{row['is_deleted']},
            '{default_id}',now(),'{default_id}',now(),null,'{row['goods_id']}',
            '{None if is_standard == 1 else goods_id_name_inspect_type_to_source_id.get(f"{src_id_to_goods_id.get(row['source_id'], None)}_{row['name']}_{1}", None)}',1,{row['component_type']},'{row['options']}','{row['constraints']}',{row['group_id']}, {row['value_quantity']},'{row['display_name']}',
            {row['is_standard']}, '{row['tag']}',{row['sort']},'{{"column":2}}',{row['item_type']}
            );""".replace("'None'", 'null').replace("None", 'null')

        f.write(sql + os.linesep)
        item_id_ref_details = item_id_to_ref_details[row['id']]
        if len(item_id_to_ref_details) <= 0:
            continue
        for item_id_ref_detail in item_id_ref_details:
            ref_detail_sql = f"""
            INSERT INTO v2_examination_item_ref_detail (id, item_id, sex, start_age, end_age, age_unit, ref, is_deleted, created, created_by, last_modified, last_modified_by, sample_type) 
            VALUES (substr(uuid_short(), 4), '{uuid}', '{item_id_ref_detail['sex']}', {item_id_ref_detail['start_age']}, {item_id_ref_detail['end_age']}, '{item_id_ref_detail['age_unit']}', '{item_id_ref_detail['ref']}', {item_id_ref_detail['is_deleted']}, now(), '{default_id}', now(), '{default_id}', '{item_id_ref_detail['sample_type']}');
            """.replace("'None'", 'null').replace("None", 'null')
            f.write(ref_detail_sql + os.linesep)


if __name__ == '__main__':
    env = 'prod'
    # 先插入is_standard
    # pair_split_left_right(None, ['明视瞳孔直径', '暗视瞳孔直径'], 1, 1, env)

    # 再插入模版门店的
    # pair_split_left_right(standard_chain_id_map[env]['eye_template_chain_id'], ['明视瞳孔直径', '暗视瞳孔直径'], 0, 1, env)

    # 再插入其他门店的
    pair_split_left_right(standard_chain_id_map[env]['eye_template_chain_id'], ['明视瞳孔直径', '暗视瞳孔直径'], 0, 0, env)
