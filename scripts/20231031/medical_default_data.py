#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os
import sys
import argparse
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from itertools import groupby

from idwork import IdWork

from multizone.db import DBClient
from multizone.rpc import regionRpcHost
import requests


default_id = '00000000000000000000000000000000'




class UpdateData:
    chain_id = None
    basic_db_client = None
    id_work = None
    type = None
    businessType = None

    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_mixed', 'abc_cis_basic', 'prod', True)

    def run(self):
        self.flush_medicine_config()

    ###

    def flush_medicine_config(self):


        organHisType = self.basic_db_client.fetchone(
            ''' select his_type from organ where id = '{chainId}' '''.format(chainId=self.chain_id))
        if organHisType:
            if organHisType['his_type'] == 0:
                print("organHisType is 0")
                return
        self.id_work = IdWork(self.emr_db_client, False)
        self.id_work.config()


        try:
            requests.get(
               """http://{rpcHost}/rpc/emr/data/copy?targetChainId={chainId}""".format(
                chainId=self.chain_id, rpcHost=regionRpcHost(self.region_name)))
        except Exception as e:
            print(e)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)



    updateData = UpdateData(args.region_name, args.chain_id)
    updateData.run()

    # region = 'ShangHai'
    # chain_id = 'ffffffff00000000347857b55f734000'
    # updateData = UpdateData(region, chain_id)
    # updateData.run()



if __name__ == '__main__':
    main()
