#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
import argparse

default_id = '00000000000000000000000000000000'


def update_roles(chain_id, region_name):
    basic_db_client = DBClient(region_name, 'abc_cis_account_base', 'abc_cis_message', 'prod', True)

    # 奖励发放通知
    basic_db_client.execute("""
    insert into v2_message_switch_config(id, chain_id, `group`, `key`, name, notify_desc,
                                                             notify_minutes_before, sms_template, wx_template,
                                                             weapp_template, sms_switch, wx_switch, weapp_switch,
                                                             is_deleted, created_by, created, last_modified_by,
                                                             last_modified)
    select substr(uuid_short(), 4),
           chain_id,
           '营销',
           'marketing.referrer-reward',
           '奖励放发通知',
           '活动奖励发放时，立即通知被奖励人',
           0,
           '{{"types": [100014], "templates": [{{"name": "奖励放发通知", "content": "【ABC数字医疗云】恭喜你获得3积分奖励，请前往微诊所-个人中心查看详情。"}}]}}',
           null,
           null,
           0,
           0,
           0,
           0,
           '00000000000000000000000000000000',
           now(),
           '00000000000000000000000000000000',
           now()
    from v2_message_switch_config
    where is_deleted = 0
      and chain_id = '{chainId}'
    group by chain_id;
    """.format(chainId=chain_id))


def run(chain_id, region_name):
    update_roles(chain_id, region_name)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id, args.region_name)


if __name__ == '__main__':
    main()
