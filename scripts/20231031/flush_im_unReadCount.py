"""
刷新formIsDeleted 的数据
"""
import os
import sys
import argparse
from datetime import date, datetime, time, timedelta

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient


class UpdateData:
    chain_id = None
    short_url_db_client = None
    emr_db_client = None
    scform_db_client = None
    basic_db_client = None
    id_work = None
    patient_tag_template_chain_id = None
    type = None
    businessType = None

    def __init__(self, region_name, chain_id):
        self.chain_id = chain_id
        self.region_name = region_name
        self.basic_db_client = DBClient(self.region_name, 'abc_cis_account_base', 'abc_cis_im', 'prod', True)
        self.beginSyncTime = datetime.combine(date.today() - timedelta(days=30), time.min)

    def run(self):
        self.updateMessageSyncUnReadCount()

    def updateMessageSyncUnReadCount(self):
        try:
            sql = '''
            update v2_im_participant_message_sync a
            inner join v2_im_conversation b on a.conversation_id = b.id
            set a.clinic_id = b.clinic_id
                where a.chain_id = '{chainId}' and b.chain_id = '{chainId}'
            '''
            self.basic_db_client.execute(sql.format(chainId=self.chain_id))

            # 查询该连锁会话 v2_im_participant_message_sync
            # for 循环一个一个的更新会话的未读消息个数
            msgSyncListSql = '''
                    select a.id, a.participant_id as userId, a.participant_type as userType, a.sync_time as syncTime, a.conversation_id as conversationId
                    from v2_im_participant_message_sync a
                             inner join v2_im_conversation b on a.conversation_id = b.id
                    where a.chain_id = '{chainId}'
                      and b.chain_id = '{chainId}'
                      and b.scene_type = 1
                '''
            participantMessageSyncList = self.basic_db_client.fetchall(msgSyncListSql.format(chainId=self.chain_id))
            print("id:", self.chain_id, ' size: ', len(participantMessageSyncList))

            clinicCountSql = '''
                SELECT count(immessage0_.id) AS cnt
                FROM v2_im_message immessage0_
                WHERE immessage0_.created >= '{syncTime}'
                  and immessage0_.notify_flag = 0
                  and immessage0_.from_user_type = 1
                  AND immessage0_.is_deleted = 0
                  and immessage0_.status < 3
                  and immessage0_.conversation_id = '{conversationId}'
            '''

            patientCountSql = '''
               SELECT count(immessage0_.id) AS cnt
                FROM v2_im_message immessage0_
                WHERE immessage0_.created >= '{syncTime}'
                  AND immessage0_.is_deleted = 0
                  and immessage0_.status < 3
                  and immessage0_.is_deleted = 0
                  and immessage0_.conversation_id = '{conversationId}'
                  and immessage0_.from_user_id != '{userId}'
            '''

            updateSql = '''
            update v2_im_participant_message_sync set un_read_count = '{unReadCount}' where id = '{id}'
                        '''

            for participantMessageSync in participantMessageSyncList:
                id = participantMessageSync['id']
                conversationId = participantMessageSync['conversationId']
                userId = participantMessageSync['userId']
                userType = participantMessageSync['userType']
                syncTime = participantMessageSync['syncTime']

                if self.beginSyncTime > syncTime:
                    syncTime = self.beginSyncTime

                cnt = 0
                if userType == 1:
                    cntRst = self.basic_db_client.fetchone(
                        patientCountSql.format(syncTime=syncTime, conversationId=conversationId, userId=userId))
                    cnt = 0
                    if cntRst is not None:
                        cnt = cntRst['cnt']
                else:
                    cntRst = self.basic_db_client.fetchone(
                        clinicCountSql.format(syncTime=syncTime, conversationId=conversationId))
                    if cntRst is not None:
                        cnt = cntRst['cnt']

                if cnt != 0:
                    print(updateSql.format(unReadCount=cnt, id=id))
                    self.basic_db_client.execute(updateSql.format(unReadCount=cnt, id=id))
        except Exception as e:
            print(e)


def main():
    # basic_db_client = DBClient('ShangHai', 'abc_cis_basic', 'abc_cis_basic', 'test', True)
    # chainSql = '''
    #     select id
    #     from organ
    #     where node_type = 1;
    # '''
    # chains = basic_db_client.fetchall(chainSql)
    # for chain in chains:
    #     updateData = UpdateData('ShangHai', chain['id'])
    #     updateData.run()

    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)
    updateData = UpdateData(args.region_name, args.chain_id)
    updateData.run()

    # region = 'ShangHai'
    # chain_id = 'ffffffff0000000002b49290004d2000'
    # updateData = UpdateData(region, chain_id)
    # updateData.run()


if __name__ == '__main__':
    main()
