#! /usr/bin/env python3
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""
监管自费病人数据迁移脚本
将自费病人数据从旧表迁移到新表中
"""
import os
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import argparse
import json
import logging
import datetime

from multizone.db import DBClient

# 配置日志
logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)

def migrate_supervision_self_pay_data(db_client, chain_id):
    """
    迁移监管自费病人数据
    
    Args:
        db_client: 数据库客户端
        chain_id: 连锁ID
    """
    logging.info(f'开始迁移连锁 {chain_id} 的监管自费病人数据')
    
    # 迁移 supervision_self_pay_charge_sheet 到 v2_supervision_charge_sheet
    migrate_charge_sheet_sql = f'''
    INSERT INTO v2_supervision_charge_sheet (
        id, chain_id, clinic_id, charge_sheet_id, patient_order_id, 
        business_data, protocol_type, upload_data, upload_status, upload_time, 
        business_created, business_last_modified, charged_time, charge_type, 
        shebao_pay, id_card_no, patient_name, doctor_id, doctor_name, 
        created, last_modified, created_by, last_modified_by
    )
    SELECT 
        id, chain_id, clinic_id, charge_sheet_id, patient_order_id, 
        null, 1, null, upload_status, upload_time,
        charged_time, charged_time, charged_time, type,
        0, id_card_no, patient_name, doctor_id, null,
        created, created, created_by, created
    FROM supervision_self_pay_charge_sheet
    WHERE chain_id = '{chain_id}' AND created >= '2025-06-05 23:59:59'
    '''
    
    charge_sheet_count = db_client.execute(migrate_charge_sheet_sql)
    logging.info(f'成功迁移 {charge_sheet_count} 条 supervision_self_pay_charge_sheet 数据到 v2_supervision_charge_sheet')
    
    # 迁移 v2_supervision_selfpay_sync_day 到 v2_supervision_charge_sheet_sync_day
    migrate_sync_day_sql = f'''
    INSERT INTO v2_supervision_charge_sheet_sync_day (
        id, chain_id, clinic_id, protocol_type,
        sync_day, comment, created, created_by,
        last_modified_by, last_modified
    )
    SELECT 
        id, chain_id, clinic_id, 1,
        sync_day, comment, created, created_by,
        last_modified_by, last_modified
    FROM v2_supervision_selfpay_sync_day
    WHERE chain_id = '{chain_id}' AND created >= '2025-06-05 23:59:59'
    '''
    
    sync_day_count = db_client.execute(migrate_sync_day_sql)
    logging.info(f'成功迁移 {sync_day_count} 条 v2_supervision_selfpay_sync_day 数据到 v2_supervision_charge_sheet_sync_day')
    
    return charge_sheet_count, sync_day_count

def main():
    parser = argparse.ArgumentParser(description='监管自费病人数据迁移')
    parser.add_argument('--chain-id', required=True, help='连锁id')
    parser.add_argument('--region-name', required=True, help='分区名字可直接查配置(ShangHai/HangZhou)')
    parser.add_argument('--env', required=True, help='环境(dev/test/prod)')
    args = parser.parse_args()
    
    if not args.chain_id or not args.region_name or not args.env:
        parser.print_help()
        sys.exit(-1)
    
    logging.info(f'开始执行监管自费病人数据迁移, 连锁ID: {args.chain_id}, 区域: {args.region_name}, 环境: {args.env}')
    
    try:
        # 连接数据库
        db_client = DBClient(args.region_name, 'abc_cis_stat', 'abc_cis_supervision', args.env)
        
        # 执行数据迁移
        charge_sheet_count, sync_day_count = migrate_supervision_self_pay_data(db_client, args.chain_id)
        
        logging.info(f'数据迁移完成! 共迁移 {charge_sheet_count} 条收费单数据和 {sync_day_count} 条同步日期数据')
        
    except Exception as e:
        logging.error(f'数据迁移失败: {str(e)}')
        sys.exit(1)
    finally:
        # 关闭数据库连接
        if 'db_client' in locals():
            db_client.close()
            
    logging.info('监管自费病人数据迁移脚本执行完毕')

if __name__ == '__main__':
    main()
