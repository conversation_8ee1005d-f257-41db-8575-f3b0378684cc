# -*- coding: utf-8 -*-
"""
@name: syncShenqiGoodsMapping.py
@author: <PERSON><PERSON><PERSON>
@email: <EMAIL>
@date: 2024-04-15 14:14:44
"""
import os
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import argparse

from multizone.db import DBClient

DEFAULT_ID = '00000000000000000000000000000000'


def main():
    """
    神奇在他们系统建档后，会把信息同步到中间库
    定时将关系同步至bis库(神奇商品ID为goods的shortId)
    """
    parser = argparse.ArgumentParser()
    parser.add_argument('--env', help='环境 dev/test/prod')
    parser.add_argument('--chainId', type=str, help='连锁ID')
    parser.add_argument('--supplierId', type=str, help='供应商ID')

    args = parser.parse_args()
    if not args.env:
        parser.print_help()
        sys.exit(-1)
    env = args.env
    chain_id = args.chainId
    supplier_id = args.supplierId
    if supplier_id is None or supplier_id == '':
        supplier_id = '0'
    basic_db_client = DBClient('ShangHai', 'abc_cis_mixed', 'abc_cis_basic', env, True)
    oragn_his_type = basic_db_client.fetchone("""
        select his_type from organ where id = '{chainId}'
    """.format(chainId=chain_id))
    if not oragn_his_type:
        return
    if oragn_his_type['his_type'] != 10:
        return
    bis_db_client = DBClient('ShangHai', 'abc_cis_mixed', 'abc_bis', env, True)
    not_exist_list = bis_db_client.fetchall("""
        select * from v1_supplier_goods a
            left join v1_supplier_goods_relation b on a.goods_id = b.supplier_goods_id and a.supplier_id = b.supplier_id and b.chain_id = '{chainId}' and b.is_deleted = 0
        where a.supplier_id = '{supplierId}' and a.is_deleted = 0 and b.id is null;
    """.format(supplierId=supplier_id, chainId=chain_id))
    insert_list = []
    for goods in not_exist_list:
        supplier_goods_id = goods['goods_id']
        if supplier_goods_id is None:
            continue
        insert_sql = """
            insert into v1_supplier_goods_relation (id, chain_id, supplier_id, goods_short_id, supplier_goods_id, is_deleted, created, created_by, last_modified, last_modified_by)
            values (substr(uuid_short(), 5), '{chainId}', '{supplierId}', '{supplierGoodsId}', '{supplierGoodsId}', 0, now(), '{DEFAULT_ID}', now(), '{DEFAULT_ID}');
        """.format(chainId=chain_id, supplierId=supplier_id, DEFAULT_ID=DEFAULT_ID, supplierGoodsId=supplier_goods_id)
        insert_list.append(insert_sql)
    for exec_sql in insert_list:
        if exec_sql is None:
            continue
        # print(exec_sql)
        bis_db_client.execute(exec_sql)


if __name__ == '__main__':
    main()
