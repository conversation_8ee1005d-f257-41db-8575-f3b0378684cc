#! /usr/bin/env python3
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""


"""
import os
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import argparse
import datetime
from multizone.log import AliyunLogClient
logger = AliyunLogClient('Master', 'prod_longtime').logger


from multizone.db import DBClient
default_id = '00000000000000000000000000000000'
ANSI_RESET = "\033[0m"
ANSI_RED = "\033[31m"
ANSI_GREEN = "\033[32m"
ANSI_YELLOW = "\033[33m"
ANSI_BLUE = "\033[34m"


# 获取当前日期
current_date = datetime.date.today()


### 同步小满中医诊所价格到空中药房
### 频率
def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--env', help='运行环境')
    args = parser.parse_args()
    runEnv = ''
    if (args.env != 'prod'):
        runEnv = '_' + args.env
    bis_db_client = DBClient('ShangHai', 'internal_system', 'abc_bis', args.env, True)
    ods_client = DBClient('ShangHai', 'ods', 'abc_cis_goods', args.env, True)
    #p环境需要置为小满his的单店：
    #name: "预发布-月亮城店"
    #_clinic_id=77719d57861646a788726d1c5587f474;
    #_chain_id=77719d57861646a788726d1c5587f473;
    #线上需要置为小满的供应商：
    #"supplierId": "10001",
    #"supplierName": "ABC测试供应商",
    #"vendorId": "10002",
    #"vendorName": "店铺A",
    #"vendorType": 1,
    sqlSelect="""
        select CONCAT('update v1_goods_sku set unit_price =', g.piece_price * k.his_trans_ratio, ' where id =', k.id,
              ';') as exeSql
        from abc_cis_goods.v2_goods as g
        inner join abc_bis.v1_goods_sku as k
                    on g.organ_id = 'ffffffff0000000034841671e3ef4000' and
                       g.short_id = k.his_goods_code  and
                       k.vendor_id in (10053, 10055, 10054)
        where abs(k.unit_price - g.piece_price * k.his_trans_ratio) > 0
        order by abs(k.unit_price - g.piece_price * k.his_trans_ratio) desc
    """.format(runEnv=runEnv)
    goods_sql_res = ods_client.fetchall(sqlSelect)
    logger.info(f"成都小满同步 执行SQL = {sqlSelect}")

    for stmt in goods_sql_res:
        sql = stmt['exeSql']
        if sql is None:
            continue
        logger.info(f"成都小满同步 改价 = {sql}")
        bis_db_client.execute(sql)


    hz_ods_client = DBClient('HangZhou', 'ods', 'abc_cis_goods', args.env, False)
    #p环境需要置为小满his的单店：
    #name: "预发布-月亮城店"
    #_clinic_id=77719d57861646a788726d1c5587f474;
    #_chain_id=77719d57861646a788726d1c5587f473;
    #线上需要置为小满的供应商：
    #"supplierId": "10001",
    #"supplierName": "ABC测试供应商",
    #"vendorId": "10002",
    #"vendorName": "店铺A",
    #"vendorType": 1,
    sqlSelect="""
        select CONCAT('update v1_goods_sku set unit_price =', g.piece_price * k.his_trans_ratio, ' where id =', k.id,
              ';') as exeSql
        from abc_cis_goods.v2_goods as g
        inner join abc_bis.v1_goods_sku as k
                    on g.organ_id = 'ffffffff0000000034ed8b90eb310000' and
                       g.short_id = k.his_goods_code  and
                       k.vendor_id in (10090)
        where abs(k.unit_price - g.piece_price * k.his_trans_ratio) > 0
        order by abs(k.unit_price - g.piece_price * k.his_trans_ratio) desc
    """.format(runEnv=runEnv)
    goods_sql_res = hz_ods_client.fetchall(sqlSelect)
    logger.info(f"重庆小满同步 执行SQL = {sqlSelect}")

    for stmt in goods_sql_res:
        sql = stmt['exeSql']
        if sql is None:
            continue
        logger.info(f"重庆小满同步 改价 = {sql}")
        bis_db_client.execute(sql)
if __name__ == '__main__':
    main()
