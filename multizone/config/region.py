#! /usr/bin/env python3
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2023 robins
#
# Distributed under terms of the MIT license/private/var/folders/pz/kwzyd6zd0s98pg_w3d0bd7gm0000gn/T/pip-uninstall-Tr_3rX/easy_install.py.


import inspect
import os

# 获取当前脚本的文件路径
current_script_path = inspect.getframeinfo(inspect.currentframe()).filename
# 获取当前脚本所在的目录路径
current_script_directory = os.path.dirname(os.path.abspath(os.path.join(current_script_path, "..")))

region_id_name_map = {
    '1': 'ShangHai',
    '2': 'HangZhou'
}

region_name_id_map = {
    'ShangHai': '1',
    'HangZhou': '2'
}


def region_name(region_id):
    return region_id_name_map.get(region_id)


def region_id(region_name):
    return region_name_id_map.get(region_name)
