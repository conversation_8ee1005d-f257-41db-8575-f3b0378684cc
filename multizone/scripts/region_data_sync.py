#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""


"""
from datetime import date
from datetime import time
from datetime import datetime
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient, escape_string
from multizone.rpc import AbcCisMonitorClient as abcCisMonitorClient
from multizone.log import AliyunLogClient

default_id = '00000000000000000000000000000000'

logger = AliyunLogClient('Master', 'prod').logger

def execute_master_slave_data_sync(**kwargs):
    master_region = kwargs.get('master_region')
    master_cluster = kwargs.get('master_cluster')
    slave_cluster = kwargs.get('slave_cluster')
    if slave_cluster is None:
        slave_cluster = master_cluster
    jumper_user = kwargs.get('jumper_user', 'schedulerx')
    database = kwargs.get('database')
    table = kwargs.get('table')
    slave_region_list = kwargs.get('slave_region_list')
    env = kwargs.get('env')
    whereSql = kwargs.get('whereSql')
    custom_update_dict = kwargs.get('custom_update_dict')
    last_modified_column = kwargs.get('last_modified_column')
    do_execute_master_slave_data_sync(master_region, master_cluster, slave_cluster, jumper_user, database, table, slave_region_list, env, whereSql, custom_update_dict, last_modified_column)

def do_execute_master_slave_data_sync(master_region, master_cluster, slave_cluster, jumper_user, database, table, slave_region_list, env, whereSql, custom_update_dict, last_modified_column):
    """
    :param master_region:
    :param cluster:
    :param database:
    :param table:
    :param slave_region_list:
    :param env:
    :param whereSql:
    :param custom_update_dict:
    :param last_modified_column:
    :return:
    """
    execute_data_sync(master_region=master_region,
                      master_cluster=master_cluster, master_db=database, master_table=table,
                      slave_region_list=slave_region_list,
                      slave_cluster=slave_cluster, jumper_user=jumper_user, slave_db=database, slave_table=table,
                      env=env, whereSql=whereSql, custom_update_dict=custom_update_dict,
                      last_modified_column=last_modified_column)

def execute_data_sync(master_region, master_cluster, master_db, master_table, slave_region_list, slave_cluster, jumper_user, slave_db, slave_table, env, whereSql, custom_update_dict, last_modified_column):
    """
    :param master_region: 主分区 Master
    :param master_cluster: 主分区集群 abc_cis_mixed
    :param master_db: 主分区数据库 abc_oa
    :param master_table: 主分区表 examination_device_model
    :param slave_region_list: 从分区列表 ["ShangHai", "HangZhou"]
    :param slave_cluster: 从分区集群 abc_cis_stock
    :param slave_db: 从分区数据库 abc_cis_goods
    :param slave_table: 从分区表 v2_goods_examination_device_model
    :param env: 环境 dev/test/prod
    :param whereSql: where 条件 and chain_id = '001'
    :param custom_update_dict: 自定义更新字段 {'chain_id': '001'}
    :param last_modified_column: 最近修改时间字段 默认值last_modified
    :return:
    """

    sql = None
    try:
        if last_modified_column is None:
            if whereSql is not None and len(whereSql) > 0:
                sql = """select * from {0} where {1};""".format(master_table, whereSql)
            else:
                sql = """select * from {0};""".format(master_table)
        else:
            if whereSql is not None and len(whereSql) > 0:
                sql = """select * from {0} where {1} >= '{2} 00:00:00' and {1} <= '{2} 23:59:59' and {3};""".format(master_table, last_modified_column, date.today(), whereSql)
            else:
                sql = """select * from {0} where {1} >= '{2} 00:00:00' and {1} <= '{2} 23:59:59';""".format(master_table, last_modified_column, date.today())
        master_dbclient = DBClient(master_region, master_cluster, master_db, env)
        logger.info(f"""execute_sql:{sql}""")
        rows = master_dbclient.fetchall(sql)

        if len(rows) == 0:
            logger.info("没有需要同步的数据")
            return
        columns = master_dbclient.show_columns(master_table)
        column_names = [column['Field'] for column in columns]

        update_values = []
        for column in columns:
            column_name = column['Field']
            column_key = column['Key']
            if column_key != 'PRI':
                update_values.append(f"""`{column_name}` = VALUES(`{column_name}`)""")

        for region in slave_region_list:
            slave_dbclient = DBClient(zone=region, cluster=slave_cluster, database=slave_db, env=env, no_tunnel=False, jumper_user=jumper_user)
            # rows 拆分1组1000条
            rows_1000 = [rows[i:i + 1000] for i in range(0, len(rows), 1000)]
            for rows_list in rows_1000:
                insert_values = []
                for row in rows_list:
                    values = []
                    for column in columns:
                        column_name = column['Field']
                        meta_value = row[column_name]
                        value = row[column_name] if row[column_name] is not None else 'null'
                        if isinstance(meta_value, str):
                            value = escape_string(value)
                        if custom_update_dict is not None and custom_update_dict.__contains__(column_name):
                            value = custom_update_dict[column_name]
                        if isinstance(meta_value, str) or isinstance(meta_value, date) or isinstance(meta_value, time) or isinstance(meta_value, datetime):
                            values.append("""'{}'""".format(value))
                        else:
                            values.append("""{}""".format(value))
                    insert_values.append('({0})'.format(', '.join(values)))
                insert_sql = """
    INSERT INTO {0}
    VALUES {1}
    ON DUPLICATE KEY UPDATE {2};
                """.format('{0} ({1})'.format(slave_table, ', '.join([f"`{column_name}`" for column_name in column_names])),
                           '{}'.format(',\n '.join(insert_values)),
                           '{}'.format(', '.join(update_values)))
                slave_dbclient.execute(insert_sql)

        #     slave_dbclient.close()
        # master_dbclient.close()
    except Exception as e:
        logger.error(f"""error:{e}""")
        abcCisMonitorClient.sendServiceAlertMessage("多分区数据同步失败", f"sql = {sql}, error = {str(e)}")

