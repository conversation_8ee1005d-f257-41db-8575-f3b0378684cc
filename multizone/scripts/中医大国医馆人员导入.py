#! /usr/bin/env python3
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
#
# Distributed under terms of the MIT license.

"""


"""
import os
import time

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import json
import logging
import re
import yagmail
import pandas as pd
import zipfile
import datetime
import requests
import xmltodict
import zipfile
import io
import math
import subprocess
import xml.etree.ElementTree as ET
from multizone.db import DBClient
# db_client = DBClient('ShangHai','abc_cis_mixed','abc_cis_domain','prod',True)
# 医生导入路径 /api/v3/clinics/employees/invite
# 医生导入，协议
# {
#     "name": "医生姓名",
#     "mobile": "联系电话",
#     "countryCode": "86",
#     "extendInfo": {
#         "handSignPicUrl": "",
#         "role": "",
#         "roles": [
#             1
#         ],
#         "groupIds": [],
#         "moduleIds": "2,301,302",
#         "showInWeClinic": 1,
#         "practiceImgUrl": "",
#         "headImgUrl": "",
#         "introduction": "简介",
#         "assistFor": {
#             "isAll": false,
#             "scope": []
#         },
#         "practiceInfo": [
#             {
#                 "type": "01|医生",
#                 "title": "01|主任医师"
#             }
#         ],
#         "code": "",
#         "sex": "性别：男",
#         "nation": "民族：06|藏族",
#         "birthday": "出生年月:2024-09-11",
#         "certType": "身份证",
#         "certNo": "身份证号码：130823199008280050",
#         "practiceCertCode": "职业证书编号",
#         "credentials": "资格证书编号",
#         "practiceBeginDate": "执业起始时间：2024-09-01",
#         "isInMultipleClinic": "是否多点执业：0",
#         "goodAt": "",
#         "tags": [
#             {
#                 "id": "00000000000000000000000000000001",
#                 "name": "教授"
#             },
#             {
#                 "id": "00000000000000000000000000000002",
#                 "name": "副教授"
#             }
#         ],
#         "chongqingExtend": {
#             "isExperts": "",
#             "email": "电子邮件:<EMAIL>",
#             "isChinesePrescriptionPrivilege": "",
#             "chinesePrescriptionRelatedDoctorCategory": []
#         },
#         "nationalDoctorCode": "国家编码：D510104047170",
#         "isAdmin": false,
#         "pass": 1
#     }
# }




def initData(excleFile):
    #tag 名字和id的字典 https://region2.abcyun.cn/api/v3/clinics/tags/list-chain-tags?1726452375067
    dictListCopyFromApi = [
            {
                "id": "00000000000000000000000000000001",
                "name": "教授",
                "type": 0
            },
            {
                "id": "00000000000000000000000000000002",
                "name": "副教授",
                "type": 0
            },
            {
                "id": "00000000000000000000000000000003",
                "name": "省级名中医",
                "type": 0
            },
            {
                "id": "00000000000000000000000000000004",
                "name": "市级名中医",
                "type": 0
            },
            {
                "id": "00000000000000000000000000000005",
                "name": "省级名医",
                "type": 0
            },
            {
                "id": "00000000000000000000000000000006",
                "name": "市级名医",
                "type": 0
            },
            {
                "id": "00000000000000000000000000000007",
                "name": "名老中医",
                "type": 0
            },
            {
                "id": "00000000000000000000000000000008",
                "name": "工作室专家",
                "type": 0
            },
            {
                "id": "00000000000000000000000000000009",
                "name": "名医之后",
                "type": 0
            },
            {
                "id": "00000000000000000000000000000010",
                "name": "医学博士",
                "type": 0
            },
            {
                "id": "00000000000000000000000000000110",
                "name": "医学硕士",
                "type": 0
            },
            {
                "id": "00000000000000000000000000000011",
                "name": "研究生导师",
                "type": 0
            },
            {
                "id": "00000000000000000000000000000012",
                "name": "博士生导师",
                "type": 0
            },
            {
                "id": "ffffffff0000000034d5482c2708c000",
                "name": "主任医师",
                "type": 1
            },
            {
                "id": "ffffffff0000000034d5482d47088000",
                "name": "副主任医师",
                "type": 1
            },
            {
                "id": "ffffffff0000000034d5482ec7054000",
                "name": "执业医师",
                "type": 1
            },
            {
                "id": "ffffffff0000000034d5483067090000",
                "name": "主治医师",
                "type": 1
            },
            {
                "id": "ffffffff0000000034d54831e7090000",
                "name": "主任中医师",
                "type": 1
            },
            {
                "id": "ffffffff0000000034d54832a7050000",
                "name": "副主任中医师",
                "type": 1
            },
            {
                "id": "ffffffff0000000034d548332708c000",
                "name": "中医师",
                "type": 1
            },
            {
                "id": "ffffffff0000000034d54833c704c000",
                "name": "医师",
                "type": 1
            },
            {
                "id": "ffffffff0000000034d54834e7084000",
                "name": "研究员",
                "type": 1
            },
            {
                "id": "ffffffff0000000034d548370704c000",
                "name": "全国名中医",
                "type": 1
            },
            {
                "id": "ffffffff0000000034d5483a47050000",
                "name": "国家973首席科学家",
                "type": 1
            },
            {
                "id": "ffffffff0000000034d5483ee7084000",
                "name": "国医大师",
                "type": 1
            },
            {
                "id": "ffffffff0000000034d5500ee7088000",
                "name": "成都市名中医",
                "type": 1
            },
            {
                "id": "ffffffff0000000034d55015a7088000",
                "name": "国务院特殊津贴专家",
                "type": 1
            },
            {
                "id": "ffffffff0000000034d55019e7084000",
                "name": "国医馆名老中医",
                "type": 1
            },
            {
                "id": "ffffffff0000000034d5501c47050000",
                "name": "国医馆优秀学传承导师",
                "type": 1
            },
            {
                "id": "ffffffff0000000034d5501da7088000",
                "name": "四川省名中医",
                "type": 1
            },
            {
                "id": "ffffffff0000000034d5501fe704c000",
                "name": "首届四川省名中医",
                "type": 1
            },
            {
                "id": "ffffffff0000000034d5502127084000",
                "name": "全国五一劳动奖",
                "type": 1
            },
            {
                "id": "ffffffff0000000034d55022e7050000",
                "name": "全国首届名中医",
                "type": 1
            },
            {
                "id": "ffffffff0000000034d5502547054000",
                "name": "四川省名医",
                "type": 1
            },
            {
                "id": "ffffffff0000000034d5502767088000",
                "name": "陕西省名中医",
                "type": 1
            },
            {
                "id": "ffffffff0000000034d55031c7088000",
                "name": "重庆市名中医",
                "type": 1
            },
            {
                "id": "ffffffff0000000034d5503327090000",
                "name": "卫生事业功勋人物",
                "type": 1
            },
            {
                "id": "ffffffff0000000034d55034e7084000",
                "name": "四川省突出贡献专家",
                "type": 1
            },
            {
                "id": "ffffffff0000000034d5503807084000",
                "name": "省事首批名中医",
                "type": 1
            }
        ]
    tagNameToTagId ={}
    for e in dictListCopyFromApi:
        tagNameToTagId[e['name']] = e['id']
    df = pd.read_excel(excleFile)

    # 将每一行转换为一个字典
    json_list = df.to_dict(orient='records')

    # 将字典转换为JSON字符串
    for excelRow in json_list:
        #转成 医生导入，协议
        post_body={}
        post_body['name'] = excelRow['医生姓名']
        post_body['mobile'] = re.sub(r'[\u202d\u202c]', '', str(excelRow['联系电话']))
        post_body['countryCode'] = '86'
        post_body['extendInfo'] = {}
        post_body['extendInfo']['role'] = ''
        post_body['extendInfo']['roles'] = [1]
        post_body['extendInfo']['groupIds'] = []
        post_body['extendInfo']['moduleIds'] = '2,301,302'
        post_body['extendInfo']['showInWeClinic'] = 1
        post_body['extendInfo']['practiceImgUrl'] = ''
        post_body['extendInfo']['headImgUrl'] = ''
        post_body['extendInfo']['assistFor'] = {}
        post_body['extendInfo']['assistFor']['isAll'] = False
        post_body['extendInfo']['assistFor']['scope'] = []
        post_body['extendInfo']['practiceInfo'] = []
        post_body['extendInfo']['code'] = ''
        post_body['extendInfo']['sex'] = excelRow['性别']
        #民族的处理
        nation= excelRow['民族']
        if nation is None :
            post_body['extendInfo']['nation'] = None
        #包含汉
        elif isinstance(nation, str) and nation.find('汉') != -1:
            post_body['extendInfo']['nation'] = '01|汉族'
        post_body['extendInfo']['birthday'] = excelRow['出生年月日'] if excelRow['出生年月日'] is not None and isinstance(excelRow['出生年月日'],str) else None
        post_body['extendInfo']['certType'] = '身份证'
        post_body['extendInfo']['certNo'] = excelRow['身份证号码'] if excelRow['身份证号码'] is not None and isinstance(excelRow['身份证号码'],str) else None
        post_body['extendInfo']['practiceCertCode'] = excelRow['职业证书编号'] if excelRow['职业证书编号'] is not None and isinstance(excelRow['职业证书编号'],str) else None
        post_body['extendInfo']['credentials'] = str(excelRow['资格证书编号']) if excelRow['资格证书编号'] is not None and isinstance(excelRow['资格证书编号'] ,str) else None
        # post_body['extendInfo']['practiceBeginDate'] = excelRow['执业起始时间']
        # post_body['extendInfo']['practiceBeginDate'] = excelRow['执业起始时间']
        if excelRow['是否多点执业'] == '是':
            post_body['extendInfo']['isInMultipleClinic'] = '1'
        else:
            post_body['extendInfo']['isInMultipleClinic'] = '0'
        if excelRow['fstrCategory'] == '中医':
            post_body['extendInfo']['practiceScope'] = '19|中医专业'
        if excelRow['执业起始时间'] and isinstance(excelRow['执业起始时间'], str):
            if '1900-01-01' != excelRow['执业起始时间'][:10]:
                #excelRow['执业起始时间'] 前10位
                post_body['extendInfo']['credentialsRegisteredDate'] = excelRow['执业起始时间'][:10]
                post_body['extendInfo']['practiceBeginDate'] = excelRow['执业起始时间'][:10]
                post_body['extendInfo']['practiceRegisteredDate'] = excelRow['执业起始时间'][:10]
        if excelRow['开始时间'] and isinstance(excelRow['开始时间'], str):
            if '1900-01-01' != excelRow['开始时间'][:10]:
                post_body['extendInfo']['clinicPracticeBeginDate'] = excelRow['开始时间'][:10]


        if excelRow['发证机关']  and isinstance(excelRow['发证机关'], str) and excelRow['发证机关'].find('四川省') != -1:
            post_body['extendInfo']['practicePlace'] = {}
            post_body['extendInfo']['practicePlace']['addressCityId'] ='510100'
            post_body['extendInfo']['practicePlace']['addressCityName'] ='成都市'
            post_body['extendInfo']['practicePlace']['addressProvinceId'] ='510000'
            post_body['extendInfo']['practicePlace']['addressProvinceName'] ='四川'



        # post_body['extendInfo']['introduction'] = excelRow['简介']
        post_body['extendInfo']['goodAt'] = excelRow['简介'] if excelRow['简介'] is not None and isinstance(excelRow['简介'],str) else None
        post_body['extendInfo']['introduction'] = excelRow['简介'] if excelRow['简介'] is not None and isinstance(excelRow['简介'],str) else None
        post_body['extendInfo']['tags'] = []
        if excelRow["职称"] and tagNameToTagId.get(excelRow["职称"]) != -1:
            post_body['extendInfo']['tags'].append({"id": tagNameToTagId.get(excelRow["职称"]), "name": excelRow["职称"]})
        if excelRow["职称"] :
            if tagNameToTagId.get(excelRow["职称"]) == '主任医师':
                post_body['extendInfo']['practiceInfo'].append({"type": "01|医生","title": "01|主任医师"})
            if tagNameToTagId.get(excelRow["职称"]) == '副主任医师':
                post_body['extendInfo']['practiceInfo'].append({"type": "01|医生","title": "02|副主任医师"})
            if tagNameToTagId.get(excelRow["职称"]) == '主治医师':
                post_body['extendInfo']['practiceInfo'].append({"type": "01|医生","title": "03|主治医师"})
            if tagNameToTagId.get(excelRow["职称"]) == '医师':
                post_body['extendInfo']['practiceInfo'].append({"type": "01|医生","title": "04|医师"})
        if excelRow["标识"] and tagNameToTagId.get(excelRow["标识"]):
            post_body['extendInfo']['tags'].append({"id": tagNameToTagId.get(excelRow["标识"]), "name": excelRow["标识"]})
        post_body['extendInfo']['chongqingExtend'] = {}
        post_body['extendInfo']['chongqingExtend']['isExperts'] = ''
        post_body['extendInfo']['chongqingExtend']['email'] = excelRow['电子邮箱'] if excelRow['电子邮箱'] is not None and isinstance(excelRow['电子邮箱'],str) else None
        post_body['extendInfo']['chongqingExtend']['isChinesePrescriptionPrivilege'] = ''
        post_body['extendInfo']['chongqingExtend']['chinesePrescriptionRelatedDoctorCategory'] = []
        post_body['extendInfo']['nationalDoctorCode'] = excelRow['国家编码'] if excelRow['国家编码'] is not None and isinstance(excelRow['国家编码'],str) else None
        post_body['extendInfo']['isAdmin'] = False
        post_body['extendInfo']['pass'] = 1
        #打印结果
        print(post_body)
        #copy
        headers={
            'abc-client-info': 'pc-v2024.37.25;',
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'content-type': 'application/json;charset=UTF-8',
            'cookie':'uuid=aee78e27599a45aa88774ddeb3c777da; Hm_lvt_0e76138257ab1ae8bc1a9375b4180e64=1723467734; grayflag=; _is_refresh_token_=TRUE; Qs_lvt_359035=1726275745%2C1726317118%2C1726364691%2C1726394951%2C1726450800; grayflag=; Qs_pv_359035=231336980088373380%2C2353074353302693400%2C2346800331651611600%2C1552966268345484000%2C2270812937062508300; _global_token_=eyJhbGciOiJIUzI1NiJ9.eyJlbXBsb3llZUlkIjoiZmZmZmZmZmYwMDAwMDAwMDM0ZDJhNDQ1ODM0NDQwMDAiLCJlbXBsb3llZVNob3J0SWQiOiIzODA2Mjg1MjUzNTQzNDc3MjQ4IiwiZW1wbG95ZWVOYW1lIjoi5L-e57-U55GeIiwibG9naW5XYXkiOiJwYXNzd29yZCIsImNzcmZUb2tlbiI6IjBmOTNjYTBiLTYzMzQtNDdhOC1hMDI0LTQzYjc4YWE1NTQ4NSIsImxvZ2luU2lkZSI6InBjIiwiYWxsb3dNdWx0aSI6MCwidmVyc2lvbiI6MiwidXVpZCI6ImE5OWM3ZTQ4LTAzZTEtNDY0OS1iYjQzLWU5ZmQ3ZTg4NmNhOCIsInJlZ2lvblRva2VuIjpmYWxzZSwiaWF0IjoxNzI2NDY1NjEwfQ.UaSjahY4UWVOhccdvSj5p8MOyv6qUzuHEKM_7w80O4Q; _login_way=password; tfstk=frFogpXv5mt6_Fvmotl5pUXCAkWxPUGIHkdKvXnF3mob2XhRdXbnmkbQwL38K-qUD0lKw7C3KbNYeBnRVX4UXXjOX1CTPzGSTGItD_fodfukYB-KLnl2m7Ikv1CTPz8PxOrl6wTkCSitYXkrLjzqc2dET0uPomun5p-zYkzVo2uM4HuEzsuqy4cEYklFor8RCEo8YuNV4Hz-UXqILS0o3tUq0SPPJqkobzoc61FDTYmazmAFj1DJWmuggMt-AS44a2ZVMQc3u2qia-j2Zk4zCkga4sxrr8zgWxVCbB0b3uMxr-be30zZ4bo0OwOI48UU9vVPbenzNuFmCWtlDDeTk7k0bid0Av2zQbPhjsSz6KJNSRdIuwF2dpMrlqmtzc_umHDGIY_codysUqgP6ZbDd-DrlqmOoZvwmYujz1C..; _abcyun_token_=eyJhbGciOiJIUzI1NiJ9.eyJlbXBsb3llZUlkIjoiZmZmZmZmZmYwMDAwMDAwMDM0ZDJhNDQ1ODM0NDQwMDAiLCJlbXBsb3llZVNob3J0SWQiOiIzODA2Mjg1MjUzNTQzNDc3MjQ4IiwiZW1wbG95ZWVOYW1lIjoi5L-e57-U55GeIiwibG9naW5XYXkiOiJwYXNzd29yZCIsImNsaW5pY0lkIjoiZmZmZmZmZmYwMDAwMDAwMDM0ZDRhMjUxNjM0OTgwMDEiLCJjbGluaWNTaG9ydElkIjoiMzgwNjg0NjA1NTQ3NjcyMzcxMyIsImNsaW5pY1R5cGUiOjIsImNoYWluSWQiOiJmZmZmZmZmZjAwMDAwMDAwMzRkNGEyNTE0MzQ5ODAwMCIsImNoYWluU2hvcnRJZCI6IjM4MDY4NDYwNTQ5Mzk4NTI4MDAiLCJ2aWV3TW9kZSI6MSwiaGlzVHlwZSI6MCwiY3NyZlRva2VuIjoiYjE4MmFiMDAtYzI0Zi00OWVjLWFmMmMtYTczODIyNTY2MmM3IiwibG9naW5FZGl0aW9uIjoiNDAiLCJsb2dpblNpZGUiOiJwYyIsImFsbG93TXVsdGkiOjAsInZlcnNpb24iOjIsInN1cHBvcnRlZEJ1c2luZXNzIjoxLCJ1dWlkIjoiYTk5YzdlNDgtMDNlMS00NjQ5LWJiNDMtZTlmZDdlODg2Y2E4IiwicmVnaW9uSWQiOiIyIiwicmVnaW9uVG9rZW4iOnRydWUsImlhdCI6MTcyNjQ2NTYxM30.wcRxD_b0Q6nmb4qxj9bykmXfOMEVch0JBccFyjPuf1Q; x-csrf-token=b182ab00-c24f-49ec-af2c-a738225662c7; _clinic_id=ffffffff0000000034d4a25163498001; _chain_id=ffffffff0000000034d4a25143498000; _user_info_={%22id%22:%22ffffffff0000000034d2a44583444000%22}',
            'origin': 'https://region2.abcyun.cn',
            'priority': 'u=1, i',
            'referer': 'https://region2.abcyun.cn/settings/clinic/employees/ffffffff0000000034d549ed22c38000',
            'sec-ch-ua': '"Chromium";v="128", "Not;A=Brand";v="24", "Google Chrome";v="128"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'x-aua': 'ABCMallPC/pc-v2024.37.25',
            'x-csrf-token': '2e343aa9-c9aa-4af5-a2c7-a576a3d33a25',
            '__region_id__': '2'
        }
        rsp =requests.post('https://region2.abcyun.cn/api/v3/clinics/employees/invite?223456789',json=post_body,headers=headers)
        str_data = rsp.content.decode('utf-8')


        print(str_data)
        # break





    return json_list

# 获取当前年份和月份
current_date = datetime.date.today()
year_month = (current_date - datetime.timedelta(days=1)).strftime("%Y-%m-%d")
# 获取文件名
# baseDir = "/Users/<USER>/Downloads"
excelFile = "/Users/<USER>/Downloads/中医大-医生账号A.xlsx"
# initData(excelFile)
headers={
    'abc-client-info': 'pc-v2024.37.25;',
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'zh-CN,zh;q=0.9',
    'content-type': 'application/json;charset=UTF-8',
    'cookie':'uuid=aee78e27599a45aa88774ddeb3c777da; Hm_lvt_0e76138257ab1ae8bc1a9375b4180e64=1723467734; grayflag=; _is_refresh_token_=TRUE; Qs_lvt_359035=1726275745%2C1726317118%2C1726364691%2C1726394951%2C1726450800; grayflag=; Qs_pv_359035=231336980088373380%2C2353074353302693400%2C2346800331651611600%2C1552966268345484000%2C2270812937062508300; _global_token_=eyJhbGciOiJIUzI1NiJ9.eyJlbXBsb3llZUlkIjoiZmZmZmZmZmYwMDAwMDAwMDM0ZDJhNDQ1ODM0NDQwMDAiLCJlbXBsb3llZVNob3J0SWQiOiIzODA2Mjg1MjUzNTQzNDc3MjQ4IiwiZW1wbG95ZWVOYW1lIjoi5L-e57-U55GeIiwibG9naW5XYXkiOiJwYXNzd29yZCIsImNzcmZUb2tlbiI6IjBmOTNjYTBiLTYzMzQtNDdhOC1hMDI0LTQzYjc4YWE1NTQ4NSIsImxvZ2luU2lkZSI6InBjIiwiYWxsb3dNdWx0aSI6MCwidmVyc2lvbiI6MiwidXVpZCI6ImE5OWM3ZTQ4LTAzZTEtNDY0OS1iYjQzLWU5ZmQ3ZTg4NmNhOCIsInJlZ2lvblRva2VuIjpmYWxzZSwiaWF0IjoxNzI2NDY1NjEwfQ.UaSjahY4UWVOhccdvSj5p8MOyv6qUzuHEKM_7w80O4Q; _login_way=password; tfstk=frFogpXv5mt6_Fvmotl5pUXCAkWxPUGIHkdKvXnF3mob2XhRdXbnmkbQwL38K-qUD0lKw7C3KbNYeBnRVX4UXXjOX1CTPzGSTGItD_fodfukYB-KLnl2m7Ikv1CTPz8PxOrl6wTkCSitYXkrLjzqc2dET0uPomun5p-zYkzVo2uM4HuEzsuqy4cEYklFor8RCEo8YuNV4Hz-UXqILS0o3tUq0SPPJqkobzoc61FDTYmazmAFj1DJWmuggMt-AS44a2ZVMQc3u2qia-j2Zk4zCkga4sxrr8zgWxVCbB0b3uMxr-be30zZ4bo0OwOI48UU9vVPbenzNuFmCWtlDDeTk7k0bid0Av2zQbPhjsSz6KJNSRdIuwF2dpMrlqmtzc_umHDGIY_codysUqgP6ZbDd-DrlqmOoZvwmYujz1C..; _abcyun_token_=eyJhbGciOiJIUzI1NiJ9.eyJlbXBsb3llZUlkIjoiZmZmZmZmZmYwMDAwMDAwMDM0ZDJhNDQ1ODM0NDQwMDAiLCJlbXBsb3llZVNob3J0SWQiOiIzODA2Mjg1MjUzNTQzNDc3MjQ4IiwiZW1wbG95ZWVOYW1lIjoi5L-e57-U55GeIiwibG9naW5XYXkiOiJwYXNzd29yZCIsImNsaW5pY0lkIjoiZmZmZmZmZmYwMDAwMDAwMDM0ZDRhMjUxNjM0OTgwMDEiLCJjbGluaWNTaG9ydElkIjoiMzgwNjg0NjA1NTQ3NjcyMzcxMyIsImNsaW5pY1R5cGUiOjIsImNoYWluSWQiOiJmZmZmZmZmZjAwMDAwMDAwMzRkNGEyNTE0MzQ5ODAwMCIsImNoYWluU2hvcnRJZCI6IjM4MDY4NDYwNTQ5Mzk4NTI4MDAiLCJ2aWV3TW9kZSI6MSwiaGlzVHlwZSI6MCwiY3NyZlRva2VuIjoiYjE4MmFiMDAtYzI0Zi00OWVjLWFmMmMtYTczODIyNTY2MmM3IiwibG9naW5FZGl0aW9uIjoiNDAiLCJsb2dpblNpZGUiOiJwYyIsImFsbG93TXVsdGkiOjAsInZlcnNpb24iOjIsInN1cHBvcnRlZEJ1c2luZXNzIjoxLCJ1dWlkIjoiYTk5YzdlNDgtMDNlMS00NjQ5LWJiNDMtZTlmZDdlODg2Y2E4IiwicmVnaW9uSWQiOiIyIiwicmVnaW9uVG9rZW4iOnRydWUsImlhdCI6MTcyNjQ2NTYxM30.wcRxD_b0Q6nmb4qxj9bykmXfOMEVch0JBccFyjPuf1Q; x-csrf-token=b182ab00-c24f-49ec-af2c-a738225662c7; _clinic_id=ffffffff0000000034d4a25163498001; _chain_id=ffffffff0000000034d4a25143498000; _user_info_={%22id%22:%22ffffffff0000000034d2a44583444000%22}',
    'origin': 'https://region2.abcyun.cn',
    'priority': 'u=1, i',
    'referer': 'https://region2.abcyun.cn/settings/clinic/employees/ffffffff0000000034d549ed22c38000',
    'sec-ch-ua': '"Chromium";v="128", "Not;A=Brand";v="24", "Google Chrome";v="128"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"macOS"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'x-aua': 'ABCMallPC/pc-v2024.37.25',
    'x-csrf-token': '2e343aa9-c9aa-4af5-a2c7-a576a3d33a25',
    '__region_id__': '2'
}
post_body={
    "name": "中医妇产科",
    "type": 1,
    "principal": "",
    "isClinical": 1,
    "mainMedical": "fa6a6b78-a44a-11e9-99a1-acde48001122",
    "secondMedical": "fa7bcf6c-a44a-11e9-99a1-acde48001122",
    "departmentAddress": "",
    "mobile": "",

    "customId": "ZYK0003",
    "id": "ffffffff0000000034d54d44c7050000",
    "tagId": "ffffffff0000000034d54d44c7050001",
    "isDefault": 0,
    "mainMedicalName": "中医科",
    "mainMedicalCode": "50",
    "chainId": "ffffffff0000000034d4a25143498000",
    "clinicId": "ffffffff0000000034d4a25163498001",
    "secondMedicalName": "妇产科",
    "secondMedicalCode": "5003",
    "created": "2024-09-16T04:50:46Z",
    "status": 1,
    "lastModified": "2024-09-16T04:50:46Z",
    "employeeList": [
    {
        "employeeId": "495b3ac900f4eb79d0d529e038413fdd",
        "businessScope": 1
    },
    {
        "employeeId": "5955d7549d4d47cfbdc4960aa466495e",
        "businessScope": 1
    },
    {
        "employeeId": "ed0d7b043d8f3c7a6e65be5d8965020a",
        "businessScope": 1
    },
    {
        "employeeId": "ffffffff00000000130e961806daa000",
        "businessScope": 1
    },
    {
        "employeeId": "ffffffff0000000017b5c5f8083b0000",
        "businessScope": 1
    },
    {
        "employeeId": "ffffffff0000000029e31c880cdcc000",
        "businessScope": 1
    },
    {
        "employeeId": "ffffffff000000002b1368a00da64000",
        "businessScope": 1
    },
    {
        "employeeId": "ffffffff00000000348040b2239f0000",
        "businessScope": 1
    },
    {
        "employeeId": "ffffffff0000000034d5514f87084001",
        "businessScope": 1
    },
    {
        "employeeId": "ffffffff0000000034d551948704c001",
        "businessScope": 1
    },
    {
        "employeeId": "ffffffff0000000034d5519927084000",
        "businessScope": 1
    },
    {
        "employeeId": "ffffffff0000000034d5519a27090000",
        "businessScope": 1
    },
    {
        "employeeId": "ffffffff0000000034d5519b67090000",
        "businessScope": 1
    }
    ]
}
rsp =requests.put('https://region2.abcyun.cn/api/v3/clinics/departments/ffffffff0000000034d54d44c7050000?1726477253828',json=post_body,headers=headers)
