#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""


"""
import logging
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys
import argparse
import logging

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient
from region_data_sync import execute_master_slave_data_sync, execute_data_sync
from datetime import date

default_id = '00000000000000000000000000000000'


def migMobanGoods(chain_id, env, server_instance, full_sync):
    # cis-goods
    execute_master_slave_data_sync(master_region='Master', master_cluster='abc_cis_stock', database='abc_cis_goods', table='v2_goods',
                      slave_region_list = ["HangZhou"],
                      jumper_user=server_instance,
                      env=env,
                      whereSql="""organ_id = '{chainId}'""".format(chainId=chain_id),
                      custom_update_dict= None,
                      last_modified_column= None if full_sync else 'last_modified_date')
    execute_master_slave_data_sync(master_region='Master', master_cluster='ob', slave_cluster='abc_cis_stock', database='abc_cis_goods',
                                   table='v2_goods_medical_stat',
                                   slave_region_list=["HangZhou"],
                                   jumper_user=server_instance,
                                   env=env,
                                   whereSql="""chain_id = '{chainId}'""".format(chainId=chain_id),
                                   custom_update_dict=None,
                                   last_modified_column=None)
    execute_master_slave_data_sync(master_region='Master', master_cluster='ob', slave_cluster='abc_cis_stock', database='abc_cis_goods',
                                   table='v2_goods_extend',
                                   slave_region_list=["HangZhou"],
                                   jumper_user=server_instance,
                                   env=env,
                                   whereSql=""" chain_id = '{chainId}'""".format(chainId=chain_id),
                                   custom_update_dict=None,
                                   last_modified_column=None if full_sync else 'last_modified')

    execute_master_slave_data_sync(master_region='Master', master_cluster='abc_cis_stock', database='abc_cis_goods', table='v2_goods_compose_opt',
                      slave_region_list = ["HangZhou"],
                      jumper_user=server_instance,
                      env=env,
                      whereSql=""" chain_id = '{chainId}'""".format(chainId=chain_id),
                      custom_update_dict= None,
                      last_modified_column= None if full_sync else 'last_modified_date')

    execute_master_slave_data_sync(master_region='Master', master_cluster='abc_cis_stock', database='abc_cis_goods', table='v2_goods_inner_system_goods',
                      slave_region_list = ["HangZhou"],
                      jumper_user=server_instance,
                      env=env,
                      whereSql=""" chain_id = '{chainId}'""".format(chainId=chain_id),
                      custom_update_dict= None,
                      last_modified_column= None if full_sync else 'last_modified')

    # cis-examination
    execute_master_slave_data_sync(master_region='Master', master_cluster='abc_cis_outpatient', database='abc_cis_examination',table='v2_examination_item',
                      slave_region_list=["HangZhou"],
                      jumper_user=server_instance,
                      env=env,
                      whereSql=""" chain_id = '{chainId}'""".format(chainId=chain_id),
                      custom_update_dict=None,
                      last_modified_column=None if full_sync else 'last_modified_date')

    exam_databaseclient = DBClient('Master', 'abc_cis_outpatient', 'abc_cis_examination', env)
    sql = f"""select id from v2_examination_item where chain_id = '{chain_id}' {f" and date(`last_modified_date`) = '{date.today()}'" if not full_sync else ''};"""
    logging.info(f"query exam_item_ids sql: {sql}")
    exam_item_ids = exam_databaseclient.fetchall(sql)
    if len(exam_item_ids) > 0:
        exam_item_ids_1000 = [exam_item_ids[i:i + 1000] for i in range(0, len(exam_item_ids), 1000)]
        for exam_item_ids_list in exam_item_ids_1000:
            execute_master_slave_data_sync(master_region='Master', master_cluster='abc_cis_outpatient', database='abc_cis_examination', table='v2_examination_item_ref_detail',
                              slave_region_list=["HangZhou"],
                              jumper_user=server_instance,
                              env=env,
                              whereSql=f""" item_id in ({','.join([f"'{exam_item_id['id']}'" for exam_item_id in exam_item_ids_list])})""",
                              custom_update_dict=None,
                              last_modified_column=None if full_sync else 'last_modified')

    # cis-outpatient
    execute_master_slave_data_sync(master_region='Master', master_cluster='abc_cis_outpatient', database='abc_cis_outpatient', table='v2_outpatient_template_catalogue',
                      slave_region_list=["HangZhou"],
                      jumper_user=server_instance,
                      env=env,
                      whereSql=""" chain_id = '{chainId}'""".format(chainId=chain_id),
                      custom_update_dict=None,
                      last_modified_column=None if full_sync else 'last_modified')

    execute_master_slave_data_sync(master_region='Master', master_cluster='abc_cis_outpatient', database='abc_cis_outpatient', table='v2_outpatient_template_diagnosis_treatment',
                      slave_region_list=["HangZhou"],
                      jumper_user=server_instance,
                      env=env,
                      whereSql=""" chain_id = '{chainId}'""".format(chainId=chain_id),
                      custom_update_dict=None,
                      last_modified_column=None if full_sync else 'last_modified')

    execute_master_slave_data_sync(master_region='Master', master_cluster='abc_cis_outpatient', database='abc_cis_outpatient', table='v2_outpatient_template_medical_record',
                      slave_region_list=["HangZhou"],
                      jumper_user=server_instance,
                      env=env,
                      whereSql=""" chain_id = '{chainId}'""".format(chainId=chain_id),
                      custom_update_dict=None,
                      last_modified_column=None if full_sync else 'last_modified')

    execute_master_slave_data_sync(master_region='Master', master_cluster='abc_cis_outpatient', database='abc_cis_outpatient', table='v2_outpatient_template_prescription',
                      slave_region_list=["HangZhou"],
                      jumper_user=server_instance,
                      env=env,
                      whereSql=""" chain_id = '{chainId}'""".format(chainId=chain_id),
                      custom_update_dict=None,
                      last_modified_column=None if full_sync else 'last_modified')

    # cis-short-url
    execute_master_slave_data_sync(master_region='Master', master_cluster='abc_cis_mixed', database='abc_cis_shorturl', table='v2_short_url_catalogue',
                      slave_region_list=["HangZhou"],
                      jumper_user=server_instance,
                      env=env,
                      whereSql=""" chain_id = '{chainId}'""".format(chainId=chain_id),
                      custom_update_dict=None,
                      last_modified_column=None if full_sync else 'last_modified')

    # his-emr
    execute_master_slave_data_sync(master_region='Master', master_cluster='scrm_hospital', database='abc_his_emr',
                                   table='v1_emr_medical',
                                   slave_region_list=["HangZhou"],
                                   jumper_user=server_instance,
                                   env=env,
                                   whereSql=""" chain_id = '{chainId}'""".format(chainId=chain_id),
                                   custom_update_dict=None,
                                   last_modified_column=None if full_sync else 'last_modified')

    execute_master_slave_data_sync(master_region='Master', master_cluster='scrm_hospital', database='abc_his_emr',
                                   table='v1_emr_medical_type',
                                   slave_region_list=["HangZhou"],
                                   jumper_user=server_instance,
                                   env=env,
                                   whereSql=""" chain_id = '{chainId}'""".format(chainId=chain_id),
                                   custom_update_dict=None,
                                   last_modified_column=None if full_sync else 'last_modified')

    execute_master_slave_data_sync(master_region='Master', master_cluster='scrm_hospital', database='abc_his_emr', table='v1_emr_medical_doc',
                      slave_region_list=["HangZhou"],
                      jumper_user=server_instance,
                      env=env,
                      whereSql=""" chain_id = '{chainId}'""".format(chainId=chain_id),
                      custom_update_dict=None,
                      last_modified_column=None if full_sync else 'last_modified')

    execute_master_slave_data_sync(master_region='Master', master_cluster='scrm_hospital', database='abc_his_emr', table='v1_emr_medical_doc_template',
                      slave_region_list=["HangZhou"],
                      jumper_user=server_instance,
                      env=env,
                      whereSql=""" chain_id = '{chainId}'""".format(chainId=chain_id),
                      custom_update_dict=None,
                      last_modified_column=None if full_sync else 'last_modified')

    execute_master_slave_data_sync(master_region='Master', master_cluster='scrm_hospital', database='abc_his_emr', table='v1_emr_medical_doc_template_control_combine',
                      slave_region_list=["HangZhou"],
                      jumper_user=server_instance,
                      env=env,
                      whereSql=""" chain_id = '{chainId}'""".format(chainId=chain_id),
                      custom_update_dict=None,
                      last_modified_column=None if full_sync else 'last_modified')

    execute_master_slave_data_sync(master_region='Master', master_cluster='scrm_hospital', database='abc_his_emr', table='v1_emr_medical_doc_template_control_combine_relation',
                      slave_region_list=["HangZhou"],
                      jumper_user=server_instance,
                      env=env,
                      whereSql=""" chain_id = '{chainId}'""".format(chainId=chain_id),
                      custom_update_dict=None,
                      last_modified_column=None if full_sync else 'last_modified')

    execute_master_slave_data_sync(master_region='Master', master_cluster='scrm_hospital', database='abc_his_emr', table='v1_emr_medical_doc_template_control',
                      slave_region_list=["HangZhou"],
                      jumper_user=server_instance,
                      env=env,
                      whereSql=""" (chain_id = '{chainId}'  or id < 1000)""".format(chainId=chain_id),
                      custom_update_dict=None,
                      last_modified_column=None if full_sync else 'last_modified')

    execute_master_slave_data_sync(master_region='Master', master_cluster='scrm_hospital', database='abc_his_emr', table='v1_emr_doc_entry',
                      slave_region_list=["HangZhou"],
                      jumper_user=server_instance,
                      env=env,
                      whereSql=""" chain_id = '{chainId}'""".format(chainId=chain_id),
                      custom_update_dict=None,
                      last_modified_column=None if full_sync else 'last_modified')

    execute_master_slave_data_sync(master_region='Master', master_cluster='scrm_hospital', database='abc_his_emr', table='v1_emr_medical_control_detail',
                      slave_region_list=["HangZhou"],
                      jumper_user=server_instance,
                      env=env,
                      whereSql=""" chain_id = '{chainId}'""".format(chainId=chain_id),
                      custom_update_dict=None,
                      last_modified_column=None if full_sync else 'last_modified')

    execute_master_slave_data_sync(master_region='Master', master_cluster='scrm_hospital', database='abc_his_emr', table='v1_emr_medical_control_info',
                      slave_region_list=["HangZhou"],
                      jumper_user=server_instance,
                      env=env,
                      whereSql=""" chain_id = '{chainId}'""".format(chainId=chain_id),
                      custom_update_dict=None,
                      last_modified_column=None if full_sync else 'last_modified')

    # cis-form
    execute_master_slave_data_sync(master_region='Master', master_cluster='abc_cis_mixed', database='abc_cis_form', table='v2_form_template_component',
                      slave_region_list=["HangZhou"],
                      jumper_user=server_instance,
                      env=env,
                      whereSql=""" chain_id = '{chainId}'""".format(chainId=chain_id),
                      custom_update_dict=None,
                      last_modified_column=None if full_sync else 'created')

    execute_master_slave_data_sync(master_region='Master', master_cluster='abc_cis_mixed', database='abc_cis_form', table='v2_form_template',
                      slave_region_list=["HangZhou"],
                      jumper_user=server_instance,
                      env=env,
                      whereSql=""" chain_id = '{chainId}'""".format(chainId=chain_id),
                      custom_update_dict=None,
                      last_modified_column=None if full_sync else 'last_modified')

    execute_master_slave_data_sync(master_region='Master', master_cluster='abc_cis_mixed', database='abc_cis_form', table='v2_form_data',
                      slave_region_list=["HangZhou"],
                      jumper_user=server_instance,
                      env=env,
                      whereSql=""" chain_id = '{chainId}'""".format(chainId=chain_id),
                      custom_update_dict=None,
                      last_modified_column=None if full_sync else 'last_modified')

    # cis-patient
    execute_master_slave_data_sync(master_region='Master', master_cluster='abc_cis_account_base', database='abc_cis_patient',
                                   table='v2_patient_revisit_template',
                                   slave_region_list=["HangZhou"],
                                   jumper_user=server_instance,
                                   env=env,
                                   whereSql=""" chain_id = '{chainId}'""".format(chainId=chain_id),
                                   custom_update_dict=None,
                                   last_modified_column=None if full_sync else 'last_modified')

    execute_master_slave_data_sync(master_region='Master', master_cluster='abc_cis_account_base', database='abc_cis_patient',
                                   table='v2_patient_source_type',
                                   slave_region_list=["HangZhou"],
                                   jumper_user=server_instance,
                                   env=env,
                                   whereSql=""" chain_id = '{chainId}'""".format(chainId=chain_id),
                                   custom_update_dict=None,
                                   last_modified_column=None if full_sync else 'last_modified')

    execute_master_slave_data_sync(master_region='Master', master_cluster='abc_cis_bill',
                                   database='abc_cis_shebao',
                                   table='shebao_clinic_config',
                                   slave_region_list=["HangZhou"],
                                   jumper_user=server_instance,
                                   env=env,
                                   whereSql=""" chain_id = '{chainId}'""".format(chainId=chain_id),
                                   custom_update_dict=None,
                                   last_modified_column=None if full_sync else 'last_modified')

    execute_master_slave_data_sync(master_region='Master', master_cluster='abc_cis_stock',
                                   database='abc_cis_goods',
                                   table='v2_goods_chain_config',
                                   slave_region_list=["HangZhou"],
                                   jumper_user=server_instance,
                                   env=env,
                                   whereSql=""" chain_id = '{chainId}'""".format(chainId=chain_id),
                                   custom_update_dict=None,
                                   last_modified_column=None if full_sync else 'last_modified')

    execute_master_slave_data_sync(master_region='Master', master_cluster='abc_cis_stock',
                                   database='abc_cis_goods',
                                   table='v2_goods_clinic_config',
                                   slave_region_list=["HangZhou"],
                                   jumper_user=server_instance,
                                   env=env,
                                   whereSql=""" chain_id = '{chainId}'""".format(chainId=chain_id),
                                   custom_update_dict=None,
                                   last_modified_column=None if full_sync else 'last_modified')


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--env', help='环境 dev/test/prod')
    parser.add_argument('--server-instance', help='服务实例 schedulerx/jenkins')
    parser.add_argument('--full-sync', type=int, help='是否全量同步')
    args = parser.parse_args()
    if not args.env:
        parser.print_help()
        sys.exit(-1)

    server_instance = args.server_instance
    logging.info(f'full-sync: {args.full_sync}, server-instance: {server_instance}')
    #模版门店ID可以查看 scGoods的配置文件
    if args.env == 'prod':
        migMobanGoods('00000000000000000000000000000000', args.env, server_instance, args.full_sync == 1) #prod system
        migMobanGoods('ffffffff000000001f2d1f400a1e6000', args.env, server_instance, args.full_sync == 1) #prod 普通诊所模版门店
        migMobanGoods('ffffffff000000000c5a1308069aa000', args.env, server_instance, args.full_sync == 1) #prod 口腔模版门店
        migMobanGoods('ffffffff0000000026f573b00acb4000', args.env, server_instance, args.full_sync == 1) #prod 眼科模版门店
        migMobanGoods('ffffffff00000000347a3895e21e0000', args.env, server_instance, args.full_sync == 1) #prod 医院模版门店
        migMobanGoods('ffffffff0000000034909b8ced558000', args.env, server_instance, args.full_sync == 1) #prod 新口腔模版门店
        migMobanGoods('ffffffff000000001b7f3bf009bb4000', args.env, server_instance, args.full_sync == 1)  # prod 体检系统模版
        migMobanGoods('ffffffff0000000034909ba6cd558000', args.env, server_instance, args.full_sync == 1)  # prod 诊所管家文书模板店
    elif args.env == 'test':
        migMobanGoods('ffffffff000000001cdb3b8007024000', args.env, server_instance, args.full_sync == 1) #prod 普通诊所模版门店
        # migMobanGoods('ffffffff000000001cdb3db807024000', args.env, args.full_sync == 1) #prod 口腔模版门店
        migMobanGoods('ffffffff00000000266aac780890e000', args.env, server_instance, args.full_sync == 1) #prod 眼科模版门店
    elif args.env == 'dev':
        migMobanGoods('ffffffff000000000c5a1308069aa000', args.env, server_instance, args.full_sync == 1)  # prod 普通诊所模版门店
        # migMobanGoods('ffffffff000000000c5a1308069aa000', args.env, args.full_sync == 1)  # prod 口腔模版门店
        migMobanGoods('ffffffff0000000025ba072801ec0000', args.env, server_instance, args.full_sync == 1)  # prod 眼科模版门店



if __name__ == '__main__':
    main()
