#! /usr/bin/env python3
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
#
# Distributed under terms of the MIT license.

"""


"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import argparse
import logging
import oss2
import yagmail
import datetime
from kubernetes import client, config
from kubernetes.client import V1Scale, V1ScaleSpec
import yaml
from sshtunnel import SSHTunnelForwarder

from multizone.db import DBClient

default_id = '00000000000000000000000000000000'
ANSI_RESET = "\033[0m"
ANSI_RED = "\033[31m"
ANSI_GREEN = "\033[32m"
ANSI_YELLOW = "\033[33m"
ANSI_BLUE = "\033[34m"
from jinja2 import Template

# 分区配置字典表
ZONE_CONFIG_MAP = {
    # 上海分区
    'ShangHai': '/zone_shanghai.yaml',
    # 杭州分区
    'HangZhou': '/zone_hangzhou.yaml',
    # 主分区
    'Master': '/zone_shanghai.yaml',
    # 从分区
    'Slave': ['/zone_hangzhou.yaml']
}

# 获取当前日期
current_date = datetime.date.today()
import inspect

# 获取当前脚本的文件路径
current_script_path = inspect.getframeinfo(inspect.currentframe()).filename
# 获取当前脚本所在的目录路径
current_script_directory = os.path.dirname(os.path.abspath(os.path.join(current_script_path, "..")))


# 伸缩k8s节点
class K8SScaleUtils(object):
    def __init__(self, region):
        self.region = region

        if region == 'ShangHai':
            # 加载上海k8s配置
            self.config = config.load_kube_config(current_script_directory + '/abc-shanghai-prod-kubeconfig.yml')
        elif region == 'HangZhou':
            self.config = config.load_kube_config(current_script_directory + '/abc-hangzhou-prod-kubeconfig.yml')
        self.v1 = client.CoreV1Api()
        self.v1beta1 = client.AppsV1Api()


class OrganUtils(object):
    def __init__(self, abcRegion, today):

        self.regionId = abcRegion == "ShangHai" and 1 or 2
        self.db_client = DBClient(abcRegion, 'abc_cis_mixed', 'abc_cis_basic', 'prod',
                                  abcRegion == "ShangHai" and True or False)
        self.beginDate = today  # 计算开始日期 yyyy-mm-dd
        self.endDate = (datetime.datetime.strptime(today, "%Y-%m-%d") + datetime.timedelta(days=1)).strftime("%Y-%m-%d")
        # 计算beginDate前面一个月的日期 yyyy-mm-dd
        self.monthStr = (datetime.datetime.strptime(today, "%Y-%m-%d") - datetime.timedelta(days=30)).strftime(
            "%Y-%m-%d")
        self.db_client = DBClient(abcRegion, 'abc_cis_mixed', 'abc_cis_basic', 'prod',
                                  abcRegion == "ShangHai" and True or False)
        self.ods_client = DBClient(abcRegion, 'ods', 'abc_cis_charge', 'prod',
                                   abcRegion == "ShangHai" and True or False)

    def getRegion(self):
        # 获取灰度诊所的数量
        sql = '''SELECT 
                        count(1) as grayChainCount ,
                        sum(IF(o.his_type=0,1,0)) as grayClinicChainCount ,
                        sum(IF(o.his_type=1,1,0)) as grayOralChainCount ,
                        sum(IF(o.his_type=2,1,0)) as grayEyeChainCount ,
                        sum(IF(o.his_type=10,1,0)) as grayPharmacyChainCount ,
                        sum(IF(o.his_type=100,1,0)) as grayHospitalChainCount 
                         
                FROM abc_cis_basic.v2_clinic_gray_organ as g inner join abc_cis_basic.v2_clinic_region_organ as r
                    on g.chain_id = r.chain_id  
                    inner join organ as o on g.chain_id = o.id
                        WHERE r.region_id = {regionId}
                       
                        
                        ;'''.format(regionId=self.regionId)
        grayCount = self.db_client.fetchone(sql)
        sql = '''SELECT 
                    count(1) as totalChainCount , 
                                            sum(IF(o.his_type=0,1,0)) as totalClinicChainCount ,
                        sum(IF(o.his_type=1,1,0)) as totalOralChainCount ,
                        sum(IF(o.his_type=2,1,0)) as totalEyeChainCount ,
                        sum(IF(o.his_type=10,1,0)) as totalPharmacyChainCount ,
                        sum(IF(o.his_type=100,1,0)) as totalHospitalChainCount 

                FROM v2_clinic_region_organ  as r 
                inner join organ as o on r.chain_id = o.id
                WHERE r.region_id ={regionId}
                    '''.format(regionId=self.regionId)
        totalCount = self.db_client.fetchone(sql);
        return {
            'grayChainCount': grayCount['grayChainCount'],
            'grayClinicChainCount': grayCount['grayClinicChainCount'],
            'grayOralChainCount': grayCount['grayOralChainCount'],
            'grayEyeChainCount': grayCount['grayEyeChainCount'],
            'grayPharmacyChainCount': grayCount['grayPharmacyChainCount'],
            'grayHospitalChainCount': grayCount['grayHospitalChainCount'],
            'prodChainCount': totalCount['totalChainCount'] - grayCount['grayChainCount'],
            'prodClinicChainCount': totalCount['totalClinicChainCount'] - grayCount['grayClinicChainCount'],
            'prodOralChainCount': totalCount['totalOralChainCount'] - grayCount['grayOralChainCount'],
            'prodEyeChainCount': totalCount['totalEyeChainCount'] - grayCount['grayEyeChainCount'],
            'prodPharmacyChainCount': totalCount['totalPharmacyChainCount'] - grayCount['grayPharmacyChainCount'],
            'prodHospitalChainCount': totalCount['totalHospitalChainCount'] - grayCount['grayHospitalChainCount']
        }

    # 获取分区门店的活跃数量 直接从db里面获取
    def getRegionFromDbDirectly(self):
        sql = '''select date as date, region_id as regionId, gray_chain_count as grayChainCount, prod_chain_count as prodChainCount,
            gray_active_chain_count as grayActiveChainWeightCount,
            prod_active_chain_count as prodActiveChainWeightCount, 
            gray_active_real_chain_count as grayActiveRealChainCount,
            prod_active_real_chain_count as prodActiveRealChainCount, 
            gray_clinic_chain_count as grayClinicChainCount, prod_clinic_chain_count as prodClinicChainCount,
            gray_active_clinic_chain_count as grayActiveClinicChainCount, prod_active_clinic_chain_count as prodActiveClinicChainCount, gray_oral_chain_count as grayOralChainCount,
            prod_oral_chain_count as prodOralChainCount, gray_active_oral_chain_count as grayActiveOralChainCount, prod_active_oral_chain_count as prodActiveOralChainCount,
            gray_eye_chain_count as grayEyeChainCount, prod_eye_chain_count as prodEyeChainCount, gray_active_eye_chain_count as grayActiveEyeChainCount,
            prod_active_eye_chain_count as prodActiveEyeChainCount, gray_pharmacy_chain_count as grayPharmacyChainCount, prod_pharmacy_chain_count as prodPharmacyChainCount,
            gray_active_pharmacy_chain_count as grayActivePharmacyChainCount, prod_active_pharmacy_chain_count as prodActivePharmacyChainCount, gray_hospital_chain_count as grayHospitalChainCount,
            prod_hospital_chain_count as prodHospitalChainCount, gray_active_hospital_chain_count as grayActiveHospitalChainCount, prod_active_hospital_chain_count as prodActiveHospitalChainCount
            from abc_cis_basic.v2_clinic_gray_chain_count where  region_id = {regionId} order by id desc limit 1'''.format(
            regionId=self.regionId)
        count = self.db_client.fetchone(sql)
        print("直接从db查询分区门店数量:", count, ",sql:", sql)
        return count


class K8sUtils(object):
    def __init__(self, region, k8sClusterName):
        self.region = region
        self.regionId = region == "ShangHai" and 1 or 2
        self.db_client = DBClient(region, 'abc_cis_mixed', 'abc_cis_basic', 'prod',
                                  region == "ShangHai" and True or False)

        if k8sClusterName == 'abc-prod':
            # 加载上海k8s配置
            self.config = config.load_kube_config(current_script_directory + '/abc-shanghai-prod-kubeconfig.yml')
        elif k8sClusterName == 'abc-prod-hangzhou':
            self.config = config.load_kube_config(current_script_directory + '/abc-hangzhou-prod-kubeconfig.yml')
        elif k8sClusterName == 'abc-his-region1-test':
            self.config = config.load_kube_config(current_script_directory + '/abc-shanghai-dev-kubeconfig.yml')
        elif k8sClusterName == 'abc-his-region1-dev':
            self.config = config.load_kube_config(current_script_directory + '/abc-shanghai-dev-kubeconfig.yml')
        self.v1 = client.CoreV1Api()
        self.v1beta1 = client.AppsV1Api()

    def getDynamicConfig(self):
        # 获取分区的动态配置
        sql = '''select service_name   as serviceName,
                        total_replicas as totalReplicas,
                        max_replicas   as maxReplicas,
                        min_replicas   as minReplicas,
                        prod_weight    as prodWeight,
                        gray_weight    as grayWeight
                from v2_clinic_gray_service_pods_scale
                where region_id = {regionId}
                  and status = 1'''.format(regionId=self.regionId)
        configList = self.db_client.fetchall(sql)
        # 按 serviceName 名字组织成字典，方便查找
        serviceNameToConfig = {}
        self.service_names = set()
        for config in configList:
            serviceNameToConfig[config['serviceName']] = config
            self.service_names.add(config['serviceName'])
        return serviceNameToConfig

    # 获取服务的Tag
    def getServerTag(self, pod):
        # 参数检查
        if not pod:
            return None
        images = [container.image for container in pod.spec.containers]
        if not images:
            return None
        # 提取最后一个:后面的部分 就是服务的tag
        return images[0].split(':')[-1]

class MailUtils(object):
    def __init__(self,title):
        self.sender = ' '
        # 加载模板文件
        # 然后可以使用相对路径来引用其他文件
        file_path = os.path.join(CURRENT_DIR, "../../mail/k8s/k8s_gray_jinja.html")
        with open(file_path) as file:
            self.template = Template(file.read())
        # 渲染HTML文件
        # 格式化日期为字符串
        self.currentDate = current_date.strftime("%Y-%m-%d")
        # 配置邮箱信息
        self.email = '<EMAIL>'
        self.password = 'Abc@20250523'
        self.smtp_server = 'smtp.exmail.qq.com'
        self.smtp_port = 465  # 使用SSL，端口号465

        # 初始化 yagmail 客户端
        self.yag = yagmail.SMTP(self.email, self.password, smtp_ssl=True, host='smtp.exmail.qq.com', port=465)
        # 配置 SMTP 服务器信息

        # 接收邮件设置
        # 邮件内容
        # to_email = '<EMAIL>'
        # to_email = '<EMAIL>'
        self.subject = f'{title}-{self.currentDate}'
        self.to_receivers = ['<EMAIL>','<EMAIL>']
        self.cc_receivers = []

    def uploadFileToOss(self, subDir, ossFileName, htmlContent):
        # 填写从STS服务获取的临时访问密钥（AccessKey ID和AccessKey Secret）。
        sts_access_key_id = 'LTAI4G6HRsiB2yKkuA2vpS5F'
        sts_access_key_secret = '******************************'
        # 使用代码嵌入的RAM用户的访问密钥配置访问凭证。
        auth = oss2.Auth(sts_access_key_id, sts_access_key_secret)
        # yourEndpoint填写Bucket所在地域对应的Endpoint。以华东1（杭州）为例，Endpoint填写为https://oss-cn-hangzhou.aliyuncs.com。
        # 填写Bucket名称。
        bucket = oss2.Bucket(auth, 'oss-cn-chengdu.aliyuncs.com', 'cd-cis-static-assets-dev')

        # 上传文件。
        # 如果需要在上传文件时设置文件存储类型（x-oss-storage-class）和访问权限（x-oss-object-acl），请在put_object中设置相关Header。
        # headers = dict()
        # headers["x-oss-storage-class"] = "Standard"
        # headers["x-oss-object-acl"] = oss2.OBJECT_ACL_PRIVATE
        # 填写Object完整路径和字符串。Object完整路径中不能包含Bucket名称。
        result = bucket.put_object('back-end-monitor/' + subDir + '/' + ossFileName, htmlContent)
        # result = bucket.put_object_from_file('.txt', 'Hello OSS')

    def sendMail(self, msg):
        html = self.template.render(compareDate=self.currentDate, regionList=msg)

        self.uploadFileToOss('multi-zone', f"k8s-{self.currentDate}.html", html)
        print(f"[{html}]")
        try:
            # 发送邮件
            sendResult = self.yag.send(to=self.to_receivers, cc=self.cc_receivers, subject=self.subject, contents=html)
            print(sendResult)
        except Exception as e:
            print(e)

        # 关闭 yagmail 客户端
        self.yag.close()

def regionReport(today,regionName,k8sClusterName,title):
    root = {}
    organUtils = OrganUtils(regionName, today)
    orR = organUtils.getRegionFromDbDirectly()
    root['regionName'] = (regionName == 'ShangHai' and '上海' or '杭州') + title
    # 把查询到的数据放到root中
    root['date'] = orR['date']
    root['grayChainCount'] = orR['grayChainCount']
    root['prodChainCount'] = orR['prodChainCount']
    root['grayClinicChainCount'] = orR['grayClinicChainCount']
    root['prodClinicChainCount'] = orR['prodClinicChainCount']
    root['grayOralChainCount'] = orR['grayOralChainCount']
    root['prodOralChainCount'] = orR['prodOralChainCount']
    root['grayEyeChainCount'] = orR['grayEyeChainCount']
    root['prodEyeChainCount'] = orR['prodEyeChainCount']
    root['grayPharmacyChainCount'] = orR['grayPharmacyChainCount']
    root['prodPharmacyChainCount'] = orR['prodPharmacyChainCount']
    root['grayHospitalChainCount'] = orR['grayHospitalChainCount']
    root['prodHospitalChainCount'] = orR['prodHospitalChainCount']

    root['grayActiveRealChainCount'] = orR['grayActiveRealChainCount']
    root['prodActiveRealChainCount'] = orR['prodActiveRealChainCount']
    root['grayActiveChainWeightCount'] = orR['grayActiveChainWeightCount']
    root['prodActiveChainWeightCount'] = orR['prodActiveChainWeightCount']

    root['grayActiveClinicChainCount'] = orR['grayActiveClinicChainCount']
    root['prodActiveClinicChainCount'] = orR['prodActiveClinicChainCount']
    root['grayActiveOralChainCount'] = orR['grayActiveOralChainCount']
    root['prodActiveOralChainCount'] = orR['prodActiveOralChainCount']
    root['grayActiveEyeChainCount'] = orR['grayActiveEyeChainCount']
    root['prodActiveEyeChainCount'] = orR['prodActiveEyeChainCount']
    root['grayActivePharmacyChainCount'] = orR['grayActivePharmacyChainCount']
    root['prodActivePharmacyChainCount'] = orR['prodActivePharmacyChainCount']
    root['grayActiveHospitalChainCount'] = orR['grayActiveHospitalChainCount']
    root['prodActiveHospitalChainCount'] = orR['prodActiveHospitalChainCount']
    return root


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    # 参数date 可选 如果没指定位今天
    parser.add_argument('--date', type=str, default=datetime.date.today().strftime("%Y-%m-%d"))
    # 参数opType 可选 如果没有指定默认为0
    # 0 计算活跃诊所 ，更新导数据库
    # 1 根据活跃度伸缩节点  手动执行
    parser.add_argument('--opType', type=int, default=0)
    args = parser.parse_args()
    today = args.date
    if args.opType == 1:
        #这个不能过凌晨，算的是当天的
        regionList = [regionReport(today, 'ShangHai', 'abc-prod','分区灰度/正式用户数量'), regionReport(today, 'HangZhou', 'abc-prod-hangzhou','分区灰度/正式用户数量')]
        print(regionList)
        print("发送邮件----")
        MailUtils('灰度/正式用户数量').sendMail(regionList)
    elif args.opType == 2:
        namespace = 'abc-his-region1-dev'
        k8s = K8sUtils('ShangHai',namespace)
        #获取所有的service列表
        services = k8s.v1.list_namespaced_service(namespace)
        # 获取现有的 ConfigMap 或创建一个新的 ConfigMap
        try:
            config_map = k8s.v1.read_namespaced_config_map(name="probe-config", namespace=namespace)
        except client.exceptions.ApiException as e:
            if e.status == 404:  # ConfigMap not found, create a new one
                config_map = client.V1ConfigMap(
                    api_version="v1",
                    kind="ConfigMap",
                    metadata=client.V1ObjectMeta(name="probe-config", namespace=namespace)
                )
            else:
                raise
        # 获取指定命名空间中的所有 Pods
        pods = k8s.v1.list_namespaced_pod(namespace=namespace, watch=False)
        deployments = k8s.v1beta1.list_namespaced_deployment(namespace=namespace)
        probes_data = {}
        #遍历所有的service
        for service in services.items:
            #获取service的名称
            service_name = service.metadata.name
            if service_name !="abc-cis-sc-goods-export-service":
                continue

            #获取service的namespace
            namespace = service.metadata.namespace

            # 获取 Service 的 selector 字段
            selector = service.spec.selector

            # 匹配 Pods
            matched_pods = [pod for pod in pods.items if
                            all(label in pod.metadata.labels.items() for label in selector.items())]

            # for pod in matched_pods:
                # #关闭所有的service的心跳检测
                # # 移除 Liveness Probes 和 Readiness Probes
                # for container in pod.spec.containers:
                #     if container.liveness_probe is not None:
                #         probes_data[f"{service_name}-livenessProbe"] = str(container.liveness_probe)
                #     if container.readiness_probe is not None:
                #         probes_data[f"{service_name}-readinessProbe"] = str(container.readiness_probe)
                #     if container.startup_probe is not None:
                #         probes_data[f"{service_name}-startUpProbe"] = str(container.startup_probe)
                #     container.liveness_probe = None
                #     container.readiness_probe = None
                #     container.startup_probe = None
                #
                # # 更新 Pod
                # k8s.v1.patch_namespaced_pod(name=pod.metadata.name,namespace=namespace, body=pod)
            #匹配deploymentsdeployments = {V1DeploymentList} {'api_version': 'apps/v1',\n 'items': [{'api_version': None,\n            'kind': None,\n            'metadata': {'annotations': {'deployment.kubernetes.io/revision': '1'},\n                         'creation_timestamp': datetime.datetime(2023, 7, 27, 5, 46, 3... View
            # 匹配特定选择器的 Deployments
            # 匹配特定选择器的 Deployments
            matched_deployments = [deployment for deployment in deployments.items if
                                   all(label in deployment.spec.template.metadata.labels.items() for label in selector.items())]


            #遍历 matched_deployments
            for deployment in matched_deployments:
                # 获取 Deployment 的名称
                deployment_name = deployment.metadata.name
                # 获取 Deployment 的 namespace
                namespace = deployment.metadata.namespace
                # 获取 Deployment 的 spec
                deployment_spec = deployment.spec
                # 获取 Deployment 的 spec 的 template
                template = deployment_spec.template
                # 获取 Deployment 的 spec 的 template 的 spec
                pod_spec = template.spec
                # 获取 Deployment 的 spec 的 template 的 spec 的 containers
                containers = pod_spec.containers
                # 遍历 containers
                for container in containers:
                    # 关闭所有的service的心跳检测
                    # 移除 Liveness Probes 和 Readiness Probes
                    if container.liveness_probe is not None:
                        probes_data[f"{service_name}-livenessProbe"] = str(container.liveness_probe)
                    if container.readiness_probe is not None:
                        probes_data[f"{service_name}-readinessProbe"] = str(container.readiness_probe)
                    if container.startup_probe is not None:
                        probes_data[f"{service_name}-startUpProbe"] = str(container.startup_probe)
                    container.liveness_probe = None
                    container.readiness_probe = None
                    container.startup_probe = None
                # 更新 Deployment
                k8s.v1beta1.patch_namespaced_deployment(name=deployment_name, namespace=namespace, body=deployment)

            #将 probes_data更新到config_map.data 中
            config_map.data = probes_data
            # 确保不包含 resourceVersion 字段
            config_map.metadata.resource_version = None
            try:
                # 尝试创建 ConfigMap
                k8s.v1.create_namespaced_config_map(namespace=namespace, body=config_map)
                print("ConfigMap created successfully.")
            except client.exceptions.ApiException as e:
                if e.status == 409:  # 资源已存在
                    print("ConfigMap already exists. Updating instead.")
                    try:
                        # 更新 ConfigMap
                        k8s.v1.replace_namespaced_config_map(namespace=namespace, name="probe-config", body=config_map)
                        print("ConfigMap updated successfully.")
                    except client.exceptions.ApiException as e:
                        print("Exception when calling CoreV1Api->replace_namespaced_config_map: %s\n" % e)
                else:
                    print("Exception when calling CoreV1Api->create_namespaced_config_map: %s\n" % e)

