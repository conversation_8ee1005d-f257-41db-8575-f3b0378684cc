import argparse
import logging
import os
import sys
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import yagmail
import pandas as pd
from datetime import date, time, datetime, timedelta
from prettytable import PrettyTable
from io import BytesIO
from tempfile import NamedTemporaryFile
from base64 import b64encode

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.db import DBClient
from multizone.rpc import AbcCisMonitorClient as monitor


def ai_usage_stat():
    today = date.today()
    combined_data = {}
    global_db_cli = DBClient('ShangHai', 'ob', 'abc_cis_basic', 'prod', False)
    # Query for 功能开放诊所数 grouped by created date
    open_clinics_query = f'''
            SELECT DATE(created) AS date, COUNT(DISTINCT o.id) AS open_clinic_count
            FROM abc_cis_property.v2_property_config_item AS p
            JOIN abc_cis_basic.organ AS o ON o.parent_id = p.v2_scope_id
            WHERE p.key = 'chainBasic.deepseek.enable' AND p.value = 1
              AND o.node_type = 2 and o.status < 99
            GROUP BY DATE(created)
            '''
    open_clinics_result = global_db_cli.fetchall(open_clinics_query)

    # Initialize open clinics data
    open_clinics_data = {row['date']: row['open_clinic_count'] for row in open_clinics_result}

    for region in ['ShangHai', 'HangZhou']:
        ai_db_cli = DBClient(region, 'ob', 'abc_cis_ai', 'prod', False)

        # Query for 使用诊所数, 使用人员数, 使用次数
        usage_query = f'''
        SELECT * FROM (
        SELECT 
            DATE(created) AS date,
            COUNT(DISTINCT clinic_id) AS used_clinic_count,
            COUNT(DISTINCT created_by) AS user_count,
            COUNT(*) AS usage_count
        FROM v2_ai_analysis_result
        WHERE type in (0, 1) and created >= '{today - timedelta(days=7)} 00:00:00' and created <= '{today - timedelta(days=1)} 23:59:59'
        GROUP BY DATE(created)
        ) t ORDER BY t.date DESC 
        '''

        # Execute queries and fetch results
        usage_result = ai_db_cli.fetchall(usage_query)

        # Combine results by date
        for row in usage_result:
            d = row['date']
            if d not in combined_data:
                combined_data[d] = {
                    'used_clinic_count': 0,
                    'user_count': 0,
                    'usage_count': 0,
                    'open_clinic_count': open_clinics_data.get(d, 0)
                }
            combined_data[d]['used_clinic_count'] += row['used_clinic_count']
            combined_data[d]['user_count'] += row['user_count']
            combined_data[d]['usage_count'] += row['usage_count']

    # Prepare data for graph and Excel
    dates = sorted(combined_data.keys())
    used_clinic_counts = [combined_data[d]['used_clinic_count'] for d in dates]
    user_counts = [combined_data[d]['user_count'] for d in dates]
    usage_counts = [combined_data[d]['usage_count'] for d in dates]
    open_clinic_counts = [combined_data[d]['open_clinic_count'] for d in dates]

    # Generate bar chart
    plt.figure(figsize=(12, 6))
    x = range(len(dates))
    bar1 = plt.bar(x, used_clinic_counts, width=0.2, label='use_clinic', align='center')
    bar2 = plt.bar([i + 0.2 for i in x], user_counts, width=0.2, label='use_employee', align='center')
    bar3 = plt.bar([i + 0.4 for i in x], usage_counts, width=0.2, label='use_count', align='center')
    bar4 = plt.bar([i + 0.6 for i in x], open_clinic_counts, width=0.2, label='open_clinic', align='center')
    plt.xticks([i + 0.3 for i in x], dates, rotation=45)
    plt.title('Last 7 Days AI Usage Statistics')
    plt.xlabel('date')
    plt.ylabel('number')
    plt.legend()

    # Annotate values
    for bar in bar1 + bar2 + bar3 + bar4:
        plt.text(bar.get_x() + bar.get_width() / 2, bar.get_height(), f'{bar.get_height()}', ha='center', va='bottom')

    plt.tight_layout()

    # Save plot to a temporary file
    img_data = BytesIO()
    plt.savefig(img_data, format='png')
    plt.close()
    img_data.seek(0)
    img_base64 = b64encode(img_data.read()).decode('utf-8')

    # Generate Excel file
    df = pd.DataFrame([{'日期': d, '使用诊所数': combined_data[d]['used_clinic_count'],
                        '使用人员数': combined_data[d]['user_count'], '使用次数': combined_data[d]['usage_count'],
                        '开通诊所数': combined_data[d]['open_clinic_count']} for d in dates])
    excel_file = NamedTemporaryFile(delete=False, suffix='.xlsx')
    df.to_excel(excel_file.name, index=False)
    excel_file.close()

    # Create HTML content
    html_content = f"""
    <html>
    <body>
        <h1>最近7天AI使用统计</h1>
        <h3>功能开放诊所数「{sum(open_clinic_counts)}」</h3>
        <img src='data:image/png;base64,{img_base64}' />
    </body>
    </html>
    """

    # Send email
    email = '<EMAIL>'
    password = '3j2x5tSGBwiEKBft'
    yag = yagmail.SMTP(user=email, password=password, smtp_ssl=True, host='smtp.exmail.qq.com', port=465)
    yag.send(
        to=['<EMAIL>', '<EMAIL>', '<EMAIL>'],
        cc=['<EMAIL>', '<EMAIL>'],
        subject='AI 使用统计',
        contents=html_content,
        attachments=[excel_file.name]
    )

    print('Email sent successfully.')


if __name__ == '__main__':
    ai_usage_stat()
