import argparse
import logging
import os
import sys
from datetime import date, time, datetime, timedelta
import requests
import pandas as pd
import json
from tempfile import NamedTemporaryFile
from aliyun.log import GetLogsRequest

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.db import DBClient
from multizone.rpc import AbcCisMonitorClient as monitor
from multizone.log.logclient import AliyunLogClient
from scripts.common.utils.lists import ListUtils as list_utils


def export_ai_usage_detail(date):
    ai_usage_chain_ids_query = f"""
    select distinct chain_id
       from abc_cis_ai.v2_ai_analysis_result
       where type in (0,1,90,100,110,120,130,140,150,160,190,200)
         and created >= '{date} 00:00:00' and created <= '{date} 23:59:59'
    """
    export_data = []
    last_7_day_unused_doctor_data = []
    ai_diagnosis_modify_config_items = []
    employee_map = {}
    clinic_map = {}
    employee_id_ai_use_count_map = {}
    for region in ['ShangHai', 'HangZhou']:
        ai_db_cli = DBClient(region, 'ob', 'abc_cis_ai', 'prod', False)
        _chain_ids = ai_db_cli.fetchall(ai_usage_chain_ids_query)
        # chain_ids = [_chain_id['chain_id'] for _chain_id in _chain_ids]
        # export_data.extend(ai_db_cli.fetchall(export_ai_usage_detail_sql(date, chain_ids)))
        #
        # ai_db_cli_test = DBClient('ShangHai', 'test', 'abc_cis_ai', 'test', True)
        # _doctor_ids = ai_db_cli_test.fetchall("""
        # select doctor_id
        # from (select doctor_id, max(last_modified) last_modified
        #       from v2_ai_analysis_stat
        #       where use_ai_day_count > 1
        #       group by doctor_id) t
        # where last_modified <= date_sub(now(), interval 7 day)
        # """)
        # last_7_day_unused_doctor_data.extend(ai_db_cli.fetchall(f"""
        # select e.name       as '医生',
        #        o.name       as '门店',
        #        count(*) / 7 as '最近7天日均门诊量'
        # from abc_cis_outpatient.v2_outpatient_sheet s
        #          left join abc_cis_basic.employee e on e.id = s.created_by
        #          left join abc_cis_basic.organ o on o.id = s.clinic_id
        # where s.created_by in ({', '.join(f"'{doctor_id['doctor_id']}'" for doctor_id in _doctor_ids)})
        # and is_deleted = 0
        # and is_draft = 0
        # and import_flag = 0
        # and s.created between date_sub(now(), interval 7 day) and now()
        # group by s.created_by;
        # """))

        ai_diagnosis_modify_config_items.extend(sls_data_stat('''* and (message :ai_diagnosis_modify_config)|select regexp_extract("message", '(?<=employeeId = ).*(?=, chainId)') as employeeId, regexp_extract("message", '(?<=clinicId = ).*(?=, time)') as clinicId, count(*) as c group by employeeId, clinicId order by c desc''', date, region, 'prod_longtime', lambda x: f'{x["employeeId"]}_{x["clinicId"]}').values())
        employee_ids = list(set([item['employeeId'] for item in ai_diagnosis_modify_config_items]))
        clinic_ids = list(set([item['clinicId'] for item in ai_diagnosis_modify_config_items]))
        employee_map.update(list_utils.to_map(ai_db_cli.fetchall(f'''select * from abc_cis_basic.employee where id in ({','.join([f"'{employee_id}'" for employee_id in employee_ids])})'''), lambda x: x['id']))
        clinic_map.update(list_utils.to_map(ai_db_cli.fetchall(f'''select * from abc_cis_basic.organ where id in ({','.join([f"'{clinic_id}'" for clinic_id in clinic_ids])})'''), lambda x: x['id']))
        employee_id_ai_use_count_map.update(list_utils.to_map(ai_db_cli.fetchall(f'''select created_by, count(*) as c from abc_cis_ai.v2_ai_analysis_result where type = 0 AND created >= '{date} 00:00:00' and created <= '{date} 23:59:59' group by created_by'''), lambda x: x['created_by']))
    # df = pd.DataFrame([{'医生': d['医生'],
    #                     '门店': d['门店'],
    #                     '门店地址': d['门店地址'],
    #                     '当日完诊量': d['当日完诊量'],
    #                     '总使用次数': d['总使用次数'],
    #                     '总使用次数「按门诊单去重」': d['总使用次数「按门诊单去重」'],
    #                     '诊断/辩证采纳次数「按门诊单去重」': d['诊断/辩证采纳次数「按门诊单去重」'],
    #                     '处方采纳次数「按门诊单去重」': d['处方采纳次数「按门诊单去重」'],
    #                     '使用率': d['使用率'],
    #                     } for d in
    #                    sorted(export_data, key=lambda x: x['使用率'] if x['使用率'] is not None else 0, reverse=True)])
    # excel_file = NamedTemporaryFile(delete=False, suffix='.xlsx')
    # df.to_excel(excel_file.name, index=False)
    # excel_file.close()
    #
    # last_7_day_df = pd.DataFrame([{'医生': d['医生'],
    #                                '门店': d['门店'],
    #                                '最近7天日均门诊量': d['最近7天日均门诊量']
    #                                } for d in
    #                               sorted(last_7_day_unused_doctor_data, key=lambda x: x['最近7天日均门诊量'] if x['最近7天日均门诊量'] is not None else 0,
    #                                      reverse=True)])
    # last_7_day_excel_file = NamedTemporaryFile(delete=False, suffix='.xlsx')
    # last_7_day_df.to_excel(last_7_day_excel_file.name, index=False)
    # last_7_day_excel_file.close()

    employee_ai_diagnosis_modify_config_df = pd.DataFrame([{'医生': employee_map[item['employeeId']]['name'],
                                                            '门店': clinic_map[item['clinicId']]['name'],
                                                            '修改配置次数': item['c'],
                                                            'AI诊疗次数': employee_id_ai_use_count_map.get(item['employeeId'], {}).get('c', 0),
                                                            '修改配置占比': f'{int(item["c"]) / employee_id_ai_use_count_map.get(item["employeeId"], {}).get("c", 1) * 100:.2f}%'
                                                            } for item in
                                                           sorted(ai_diagnosis_modify_config_items,
                                                                  key=lambda item: item['c'],
                                                                  reverse=True)])
    employee_ai_diagnosis_modify_config_excel_file = NamedTemporaryFile(delete=False, suffix='.xlsx')
    employee_ai_diagnosis_modify_config_df.to_excel(employee_ai_diagnosis_modify_config_excel_file.name, index=False)
    employee_ai_diagnosis_modify_config_excel_file.close()

    corp_id = 'ww1c19d79cfd0283ac'
    corp_secret = 'QXgSKbS4Zf4qB-Fkq-JwA60aozLqq9ZDjVK-RFPkoIs'
    agent_id = '1000047'
    agent_secret = '-xgUr3Ut_Lt0fISEfG7kIoAVtZdPiXq-sCXFsshQTbI'

    qyapi = 'https://qyapi.weixin.qq.com'
    access_token = json.loads(
        requests.get(
            url=f'{qyapi}/cgi-bin/gettoken',
            params={
                'corpid': corp_id,
                'corpsecret': agent_secret
            }
        ).content.decode('utf-8')
    )['access_token']
    logging.info(f'access_token: {access_token}')

    # sent_excel_file(access_token, agent_id, excel_file, qyapi)
    # sent_excel_file(access_token, agent_id, last_7_day_excel_file, qyapi)
    sent_excel_file(access_token, agent_id, employee_ai_diagnosis_modify_config_excel_file, qyapi)


def sent_excel_file(access_token, agent_id, excel_file, qyapi):
    # 上传文件
    media_id = json.loads(
        requests.post(
            url=f'{qyapi}/cgi-bin/media/upload',
            params={
                'access_token': access_token,
                'type': 'file'
            },
            files={
                'media': open(excel_file.name, 'rb')
            }
        ).content.decode('utf-8')
    )['media_id']
    # 推文件
    sent_file_rsp = json.loads(
        requests.post(
            url=f'{qyapi}/cgi-bin/message/send',
            params={
                'access_token': access_token
            },
            json={
                "touser": "JiangXiaoFeng|DiPao",
                # "toparty": "PartyID1|PartyID2",
                # "totag": "TagID1 | TagID2",
                "msgtype": "file",
                "agentid": agent_id,
                "file": {
                    "media_id": media_id
                },
                "safe": 0,
                "enable_duplicate_check": 0,
                "duplicate_check_interval": 1800
            }
        ).content.decode('utf-8')
    )
    logging.info(f'sent_file: {sent_file_rsp}')


def export_ai_usage_detail_sql(date, chain_ids):
    export_sql = f"""
    SELECT *, 总使用次数「按门诊单去重」 * 100 / 当日完诊量 as '使用率'
    from (SELECT /*+ query_timeout(180000000)*/
              e.id                                                                                       as doctorId,
              o.id                                                                                       as clinicId,
              e.name                                                                                     as '医生',
              o.name                                                                                     as '门店',
              concat(o.address_province_name, o.address_city_name)                                       as '门店地址',
              count(distinct if(s.status = 1, s.id, null))                                               as '当日完诊量',
              count(distinct r.id)                                                                       as '总使用次数',
              count(distinct JSON_UNQUOTE(JSON_EXTRACT(r.request, '$.data.medicalRecord.value.outpatientSheetId'))) as '总使用次数「按门诊单去重」',
              count(distinct IF(r.extend_flag & 1 > 0, JSON_UNQUOTE(JSON_EXTRACT(r.request, '$.data.medicalRecord.value.outpatientSheetId')), null)) as '诊断/辩证采纳次数「按门诊单去重」',
              count(distinct IF(r.extend_flag & 2 > 0, JSON_UNQUOTE(JSON_EXTRACT(r.request, '$.data.medicalRecord.value.outpatientSheetId')), null)) as '处方采纳次数「按门诊单去重」'
          FROM abc_cis_outpatient.v2_outpatient_sheet s
                   left join abc_cis_ai.v2_ai_analysis_result r
                             on JSON_UNQUOTE(JSON_EXTRACT(r.request, '$.data.medicalRecord.value.outpatientSheetId')) = s.id
                   left join abc_cis_basic.employee e on e.id = s.created_by
                   left join abc_cis_basic.organ o on o.id = s.clinic_id
          WHERE s.chain_id in ({', '.join(f"'{chain_id}'" for chain_id in chain_ids)})
            and (s.chain_id not in (select distinct organ_id from abc_ops_stat.ignore_organ) or o.inner_flag = 0)
            and s.created >= '{date} 00:00:00' and s.created <= '{date} 23:59:59'
            and s.is_deleted = 0
            and s.import_flag = 0
          group by s.created_by, s.clinic_id) as t
    order by t.当日完诊量 desc;
    """
    return export_sql


def ai_usage_continue_stat(date):
    dev_db_cli = DBClient('ShangHai', 'test', 'abc_cis_ai', 'test', False)
    maxUseageCount = 0
    for region in ['ShangHai', 'HangZhou']:
        ai_db_cli = DBClient(region, 'ob', 'abc_cis_ai', 'prod', False)
        # Query for 使用诊所数, 使用人员数, 使用次数
        usage_person_query = f'''
        SELECT
            created_by,
            COUNT(if(type in (0, 1), 1, null)) AS 每人使用次数
        FROM v2_ai_analysis_result
        WHERE created >= '{date} 00:00:00' and created <= '{date} 23:59:59'
        and (chain_id not in (select organ_id from abc_ops_stat.ignore_organ) or chain_id not in (select id from abc_cis_basic.organ where inner_flag = 1 and node_type = 1))
        group by created_by
        order by 每人使用次数 desc
        '''
        # Execute queries and fetch results
        usage_person_result = ai_db_cli.fetchall(usage_person_query)

        # Process the result and update the v2_ai_analysis_stat table
        for row in usage_person_result:
            doctor_id = row['created_by']
            count = row['每人使用次数']

            if count > maxUseageCount:
                maxUseageCount = count

            # Check if the doctor exists in the v2_ai_analysis_stat table
            check_doctor_query = f'''
            SELECT id, use_ai_day_count FROM v2_ai_analysis_stat 
            WHERE doctor_id = '{doctor_id}' and status = 0;
            '''
            doctor_stat = dev_db_cli.fetchone(check_doctor_query)

            if doctor_stat:
                # Doctor exists, update the use_ai_day_count
                update_query = f'''
                UPDATE v2_ai_analysis_stat 
                SET use_ai_day_count = use_ai_day_count + 1, 
                    last_modified = '{date}' 
                WHERE doctor_id = '{doctor_id}' and status = 0 and last_modified < '{date}' ;
                '''
                dev_db_cli.execute(update_query)
            else:
                # Doctor doesn't exist, insert a new record
                insert_query = f'''
                INSERT INTO v2_ai_analysis_stat 
                (doctor_id, use_ai_day_count, status, created, last_modified) 
                VALUES ('{doctor_id}', 1, 0, '{date}', '{date}');
                '''
                dev_db_cli.execute(insert_query)
        # 把last_modified小于date的，status=0的，把status更新成1
        update_query = f'''
        UPDATE v2_ai_analysis_stat 
        SET status = 1 
        WHERE last_modified < '{date}' and status = 0;
        '''
        dev_db_cli.execute(update_query)
        return maxUseageCount


def ai_usage_stat(flushDate):
    combined_data = {
        # AI诊疗
        'AI诊疗_使用诊所数量': 0,
        'AI诊疗_用户数量': 0,
        'AI诊疗_使用次数': 0,
        'AI诊疗_使用次数「按门诊单去重」': 0,
        'AI诊疗_使用诊断/辩证次数「按门诊单去重」': 0,
        'AI诊疗_使用中西成药次数「按门诊单去重」': 0,
        'AI诊疗_使用中药次数「按门诊单去重」': 0,
        'AI诊疗_使用外治次数「按门诊单去重」': 0,
        'AI诊疗_诊断/辩证采纳次数「按门诊单去重」': 0,
        'AI诊疗_中西成药采纳次数「按门诊单去重」': 0,
        'AI诊疗_中药采纳次数「按门诊单去重」': 0,
        'AI诊疗_外治采纳次数「按门诊单去重」': 0,
        'AI诊疗_诊断/辩证采纳率「按门诊单去重」': 0,
        'AI诊疗_中西成药采纳率「按门诊单去重」': 0,
        'AI诊疗_中药采纳率「按门诊单去重」': 0,
        'AI诊疗_外治采纳率「按门诊单去重」': 0,
        'AI诊疗_总使用token': 0,
        'AI诊疗_药品匹配率': 0,
        # AI舌象
        'AI舌象_使用诊所数量': 0,
        'AI舌象_用户数量': 0,
        'AI舌象_分析次数': 0,
        'AI舌象_采纳次数': 0,
        'AI舌象_采纳率': 0,
        # AI语音
        'AI语音_使用诊所数量': 0,
        'AI语音_用户数量': 0,
        'AI语音_分析次数': 0,
        'AI语音_使用次数': 0,
        'AI语音_病历分析次数': 0,
        'AI语音_使用次数「按门诊单去重」': 0,
        'AI语音_ASR时长': 0,
        'AI语音_病历采纳次数': 0,
        'AI语音_病历采纳率': 0,
        'AI语音_总使用token': 0,
        # AI检验项目推荐
        'AI检验项目推荐_使用次数': 0,
        'AI检验项目推荐_用户数量': 0,
        'AI检验项目推荐_采纳次数': 0,
        'AI检验项目推荐_采纳率': 0,
        'AI检验项目推荐_总使用token': 0,
        # AI检验报告解读
        'AI检验报告解读_使用次数': 0,
        'AI检验报告解读_查看次数': 0,
        'AI检验报告解读_查看率': 0,
        'AI检验报告解读_总使用token': 0
    }

    dev_db_cli = DBClient('ShangHai', 'test', 'abc_cis_ai', 'test', False)
    region_list = ['ShangHai', 'HangZhou']
    for region in region_list:
        ai_db_cli = DBClient(region, 'ob', 'abc_cis_ai', 'prod', False)
        # AI舌象
        ai_tongue = sls_data_stat('''* and (message :tongue_do_analyze or message :tongue_accept_suggestion)|select regexp_extract("message", '(?<=buttonName = ).*(?=, fromWay)') as buttonName, count(distinct regexp_extract("message", '(?<=clinicId = ).*(?=, time)')) as cc, count(distinct regexp_extract("message", '(?<=employeeId = ).*(?=, chainId)')) as ec, count(*) as c group by buttonName''', flushDate, region)
        combined_data['AI舌象_使用诊所数量'] += int(ai_tongue['执行舌象分析']['cc'])
        combined_data['AI舌象_用户数量'] += int(ai_tongue['执行舌象分析']['ec'])
        combined_data['AI舌象_分析次数'] += int(ai_tongue['执行舌象分析']['c'])
        combined_data['AI舌象_采纳次数'] += int(ai_tongue['采纳舌象分析建议']['c'])

        types = '0,1,60,70,90,100,110,120,130,140,150,160,190,200,210'

        # Query for 使用诊所数, 使用人员数, 使用次数
        usage_query = f'''
            SELECT
            COUNT(DISTINCT if(type = 0, clinic_id, null)) AS AI诊疗_使用诊所数量,
            COUNT(DISTINCT if(type = 0, created_by, null)) AS AI诊疗_用户数量,
            COUNT(DISTINCT if(type = 210, created_by, null)) AS AI检验项目推荐_用户数量,
            COUNT(if(type = 0, 1, null)) AS AI诊疗_使用次数,
            COUNT(if(type = 210, 1, null)) AS AI检验项目推荐_使用次数,
            COUNT(if(type = 60, 1, null)) AS AI语音_病历分析次数,
            COUNT(if(type = 70, 1, null)) AS AI检验报告解读_使用次数,
            SUM(if(type in (0,1,90,100,110,120,130,140,150,160,190,200), ifnull(JSON_EXTRACT(`usage`, '$.totalTokens'), 0), 0)) AS AI诊疗_总使用token,
            SUM(if(type in (60), ifnull(JSON_EXTRACT(`usage`, '$.totalTokens'), 0), 0)) AS AI语音_总使用token,
            SUM(if(type in (210), ifnull(JSON_EXTRACT(`usage`, '$.totalTokens'), 0), 0)) AS AI检验项目推荐_总使用token,
            SUM(if(type in (70), ifnull(JSON_EXTRACT(`usage`, '$.totalTokens'), 0), 0)) AS AI检验报告解读_总使用token,
            100 * SUM(if(extend_info is not null and extend_info ->> '$.adoptionMatchRate' is not null, extend_info ->> '$.adoptionMatchRate', 0)) / COUNT(if(extend_info is not null and extend_info ->> '$.adoptionMatchRate' is not null, 1, null)) 'AI诊疗_药品匹配率',
            COUNT(distinct if(type = 0, JSON_EXTRACT(request, '$.data.medicalRecord.value.outpatientSheetId'), null)) AS 'AI诊疗_使用次数「按门诊单去重」',
            COUNT(distinct if(type = 200, JSON_EXTRACT(request, '$.data.medicalRecord.value.outpatientSheetId'), null)) AS 'AI诊疗_使用诊断/辩证次数「按门诊单去重」',
            COUNT(distinct if(type = 100 and JSON_EXTRACT(request, '$.promptName') = 'suggestion-chinese-prescription', JSON_EXTRACT(request, '$.data.medicalRecord.value.outpatientSheetId'), null)) AS 'AI诊疗_使用中药次数「按门诊单去重」',
            COUNT(distinct if(type = 100 and JSON_EXTRACT(request, '$.promptName') = 'suggestion-western-prescription', JSON_EXTRACT(request, '$.data.medicalRecord.value.outpatientSheetId'), null)) AS 'AI诊疗_使用中西成药次数「按门诊单去重」',
            COUNT(distinct if(type in (110, 120, 130, 140, 150, 160), JSON_EXTRACT(request, '$.data.medicalRecord.value.outpatientSheetId'), null)) AS 'AI诊疗_使用外治次数「按门诊单去重」',
            COUNT(distinct IF(extend_flag & 1 > 0, ifnull(JSON_EXTRACT(request, '$.data.medicalRecord.value.outpatientSheetId'), JSON_EXTRACT(request, '$.medicalRecord.outpatientSheetId')), null)) AS 'AI诊疗_诊断/辩证采纳次数「按门诊单去重」',
            COUNT(distinct IF(extend_flag & 8 > 0, ifnull(JSON_EXTRACT(request, '$.data.medicalRecord.value.outpatientSheetId'), JSON_EXTRACT(request, '$.medicalRecord.outpatientSheetId')), null)) AS 'AI诊疗_中药采纳次数「按门诊单去重」',
            COUNT(distinct IF(extend_flag & 16 > 0, ifnull(JSON_EXTRACT(request, '$.data.medicalRecord.value.outpatientSheetId'), JSON_EXTRACT(request, '$.medicalRecord.outpatientSheetId')), null)) AS 'AI诊疗_中西成药采纳次数「按门诊单去重」',
            COUNT(distinct IF(extend_flag & 32 > 0, ifnull(JSON_EXTRACT(request, '$.data.medicalRecord.value.outpatientSheetId'), JSON_EXTRACT(request, '$.medicalRecord.outpatientSheetId')), null)) AS 'AI诊疗_外治采纳次数「按门诊单去重」'
        FROM v2_ai_analysis_result
        WHERE type in ({types}) AND created >= '{flushDate} 00:00:00' and created <= '{flushDate} 23:59:59'
        '''

        asr_usage_query = f'''
        select COUNT(DISTINCT clinic_id) AS AI语音_使用诊所数量,
               COUNT(DISTINCT created_by) AS AI语音_用户数量,
               COUNT(*) AS AI语音_使用次数,
               COUNT(DISTINCT outpatient_sheet_id) AS AI语音_使用次数「按门诊单去重」,
               SUM(ifnull(business_info ->> '$.duration', 0) / 1000) AS AI语音_ASR时长
        from abc_cis_outpatient.v2_outpatient_medical_record_attachment 
        where business_type = 40 and business_info is not null and created >= '{flushDate} 00:00:00' and created <= '{flushDate} 23:59:59'
        '''

        ai_asr_log = sls_data_stat('''* and (message :voice_record_accept)|select regexp_extract("message", '(?<=buttonName = ).*(?=, fromWay)') as buttonName, count(distinct regexp_extract("message", '(?<=clinicId = ).*(?=, time)')) as cc, count(distinct regexp_extract("message", '(?<=employeeId = ).*(?=, chainId)')) as ec, count(*) as c group by buttonName''', flushDate, region)
        combined_data['AI语音_病历采纳次数'] += int(ai_asr_log['采纳语音病历']['c'])

        # AI检验项目推荐采纳次数
        ai_examination_accept = sls_data_stat('''* and (message :ai_examination_accept)|select regexp_extract("message", '(?<=buttonName = ).*(?=, fromWay)') as buttonName, count(distinct regexp_extract("message", '(?<=clinicId = ).*(?=, time)')) as cc, count(distinct regexp_extract("message", '(?<=employeeId = ).*(?=, chainId)')) as ec, count(*) as c group by buttonName''',flushDate, region)
        combined_data['AI检验项目推荐_采纳次数'] += int(ai_examination_accept.get('采纳AI推荐检验项目', {}).get('c', 0))

        # AI检验报告解读查看次数
        ai_exam_report_view = sls_data_stat('''* and message-index: ai and message-index: knowledge-retrieve | select count(*) as c''', flushDate, region, 'prod', lambda x: x['c'])
        combined_data['AI检验报告解读_查看次数'] += int(list(ai_exam_report_view.values())[0]['c'])

        # Execute queries and fetch results
        usage_result = ai_db_cli.fetchall(usage_query)
        asr_usage_result = ai_db_cli.fetchall(asr_usage_query)

        # Query for 使用诊所数, 使用人员数, 使用次数
        usage_person_query = f'''
        select max(pCount) as 每人使用次数,
        max(pMaxTokenCount) as 人日最多使用token 
        from (
        SELECT
            created_by  ,
            COUNT(if(type = 0, 1, null)) AS pCount,
            SUM(ifnull(JSON_EXTRACT(`usage`, '$.totalTokens'),0)) AS pMaxTokenCount
        FROM v2_ai_analysis_result
        WHERE type in ({types}) AND created >= '{flushDate} 00:00:00' and created <= '{flushDate} 23:59:59'
        and (chain_id not in (select organ_id from abc_ops_stat.ignore_organ) or chain_id not in (select id from abc_cis_basic.organ where inner_flag = 1 and node_type = 1))
        group by  created_by
        ) as a 
        '''
        # Execute queries and fetch results
        usage_person_result = ai_db_cli.fetchall(usage_person_query)

        for row in asr_usage_result:
            combined_data['AI语音_使用诊所数量'] += row['AI语音_使用诊所数量']
            combined_data['AI语音_用户数量'] += row['AI语音_用户数量']
            combined_data['AI语音_使用次数'] += row['AI语音_使用次数']
            combined_data['AI语音_使用次数「按门诊单去重」'] += row['AI语音_使用次数「按门诊单去重」']
            combined_data['AI语音_ASR时长'] += row['AI语音_ASR时长']

        # Combine results by date
        for row in usage_result:
            combined_data['AI诊疗_使用诊所数量'] += row['AI诊疗_使用诊所数量']
            combined_data['AI诊疗_用户数量'] += row['AI诊疗_用户数量']
            combined_data['AI诊疗_使用次数'] += row['AI诊疗_使用次数']
            combined_data['AI诊疗_使用次数「按门诊单去重」'] += row['AI诊疗_使用次数「按门诊单去重」']
            combined_data['AI诊疗_使用诊断/辩证次数「按门诊单去重」'] += row['AI诊疗_使用诊断/辩证次数「按门诊单去重」']
            combined_data['AI诊疗_使用中药次数「按门诊单去重」'] += row['AI诊疗_使用中药次数「按门诊单去重」']
            combined_data['AI诊疗_使用中西成药次数「按门诊单去重」'] += row['AI诊疗_使用中西成药次数「按门诊单去重」']
            combined_data['AI诊疗_使用外治次数「按门诊单去重」'] += row['AI诊疗_使用外治次数「按门诊单去重」']
            combined_data['AI诊疗_总使用token'] += row['AI诊疗_总使用token']

            combined_data['AI诊疗_药品匹配率'] += row['AI诊疗_药品匹配率']
            combined_data['AI诊疗_诊断/辩证采纳次数「按门诊单去重」'] += row['AI诊疗_诊断/辩证采纳次数「按门诊单去重」']
            combined_data['AI诊疗_中药采纳次数「按门诊单去重」'] += row['AI诊疗_中药采纳次数「按门诊单去重」']
            combined_data['AI诊疗_中西成药采纳次数「按门诊单去重」'] += row['AI诊疗_中西成药采纳次数「按门诊单去重」']
            combined_data['AI诊疗_外治采纳次数「按门诊单去重」'] += row['AI诊疗_外治采纳次数「按门诊单去重」']

            combined_data['AI语音_病历分析次数'] += row['AI语音_病历分析次数']
            combined_data['AI语音_总使用token'] += row['AI语音_总使用token']

            combined_data['AI检验项目推荐_使用次数'] += row['AI检验项目推荐_使用次数']
            combined_data['AI检验项目推荐_用户数量'] += row['AI检验项目推荐_用户数量']
            combined_data['AI检验项目推荐_总使用token'] += row['AI检验项目推荐_总使用token']

            combined_data['AI检验报告解读_使用次数'] += row['AI检验报告解读_使用次数']
            combined_data['AI检验报告解读_总使用token'] += row['AI检验报告解读_总使用token']
        # for row in usage_person_result:
        #     if row['每人使用次数'] > combined_data['每人使用最大次数']:
        #         combined_data['每人使用最大次数'] = row['每人使用次数']
        #     # 转成int
        #     if int(row['人日最多使用token']) > combined_data['人日最多使用token']:
        #         combined_data['人日最多使用token'] = int(row['人日最多使用token'])

    combined_data['AI诊疗_药品匹配率'] = round(combined_data['AI诊疗_药品匹配率'] / len(region_list), 2)
    if combined_data['AI诊疗_使用诊断/辩证次数「按门诊单去重」'] > 0:
        logging.info(f"AI诊疗_使用诊断/辩证次数「按门诊单去重」{combined_data['AI诊疗_使用诊断/辩证次数「按门诊单去重」']}" )
        combined_data['AI诊疗_诊断/辩证采纳率「按门诊单去重」'] = round(combined_data['AI诊疗_诊断/辩证采纳次数「按门诊单去重」'] / combined_data['AI诊疗_使用诊断/辩证次数「按门诊单去重」'], 2) * 100
    if combined_data['AI诊疗_使用中药次数「按门诊单去重」'] > 0:
        logging.info(f"AI诊疗_使用中药次数「按门诊单去重」{combined_data['AI诊疗_使用中药次数「按门诊单去重」']}")
        combined_data['AI诊疗_中药采纳率「按门诊单去重」'] = round(combined_data['AI诊疗_中药采纳次数「按门诊单去重」'] / combined_data['AI诊疗_使用中药次数「按门诊单去重」'], 2) * 100
    if combined_data['AI诊疗_使用中西成药次数「按门诊单去重」'] > 0:
        logging.info(f"AI诊疗_使用中西成药次数「按门诊单去重」{combined_data['AI诊疗_使用中西成药次数「按门诊单去重」']}")
        combined_data['AI诊疗_中西成药采纳率「按门诊单去重」'] = round(combined_data['AI诊疗_中西成药采纳次数「按门诊单去重」'] / combined_data['AI诊疗_使用中西成药次数「按门诊单去重」'], 2) * 100
    if combined_data['AI诊疗_使用外治次数「按门诊单去重」'] > 0:
        logging.info(f"AI诊疗_使用外治次数「按门诊单去重」{combined_data['AI诊疗_使用外治次数「按门诊单去重」']}")
        combined_data['AI诊疗_外治采纳率「按门诊单去重」'] = round(combined_data['AI诊疗_外治采纳次数「按门诊单去重」'] / combined_data['AI诊疗_使用外治次数「按门诊单去重」'], 2) * 100
    if combined_data['AI舌象_分析次数'] > 0:
        logging.info(f"AI舌象_分析次数 {combined_data['AI舌象_分析次数']}")
        combined_data['AI舌象_采纳率'] = round(combined_data['AI舌象_采纳次数'] / combined_data['AI舌象_分析次数'], 2) * 100
    if combined_data['AI语音_病历分析次数'] > 0:
        logging.info(f"AI语音_病历分析次数 {combined_data['AI语音_病历分析次数']}")
        combined_data['AI语音_病历采纳率'] = round(combined_data['AI语音_病历采纳次数'] / combined_data['AI语音_病历分析次数'], 2) * 100
    if combined_data['AI检验项目推荐_使用次数'] > 0:
        logging.info(f"AI检验项目推荐_使用次数 {combined_data['AI检验项目推荐_使用次数']}")
        combined_data['AI检验项目推荐_采纳率'] = round(combined_data['AI检验项目推荐_采纳次数'] / combined_data['AI检验项目推荐_使用次数'], 2) * 100
    if combined_data['AI检验报告解读_使用次数'] > 0:
        logging.info(f"AI检验报告解读_使用次数 {combined_data['AI检验报告解读_使用次数']}")
        combined_data['AI检验报告解读_查看率'] = round(combined_data['AI检验报告解读_查看次数'] / combined_data['AI检验报告解读_使用次数'], 2) * 100

    # Query for 使用诊所数, 使用人员数, 使用次数
    usage_person_query = f'''
        select 
            max(use_ai_day_count)            as 最大连续使用天数,
            sum(use_ai_day_count) / count(1) as 平均连续使用天数,
            SUM(IF(use_ai_day_count > 1, 1, 0)) as 连续使用两天人次数,
            SUM(IF(status = 0 and use_ai_day_count > 2 ,1, 0)) as 当前使用超过2天的医生数,
            SUM(IF(use_ai_day_count = 1, 1, 0)) as 使用一天人次数
        from v2_ai_analysis_stat
        '''
    dev_db_cli.execute(usage_person_query)
    usage_person_result = dev_db_cli.fetchall(usage_person_query)

    # combined_data['最大连续使用天数'] = usage_person_result[0]['最大连续使用天数']
    # combined_data['平均连续使用天数'] = usage_person_result[0]['平均连续使用天数']
    # combined_data['连续使用两天人次数'] = usage_person_result[0]['连续使用两天人次数']
    # combined_data['使用一天人次数'] = usage_person_result[0]['使用一天人次数']
    # combined_data['当前使用超过2天的医生数'] = usage_person_result[0]['当前使用超过2天的医生数']

    # Send message to WeChat
    # 格式化人均使用次数，保留两位小数
    ai_diagnosis_avg_usage_count = 0
    ai_diagnosis_avg_usage_token = 0
    ai_asr_medical_avg_usage_token = 0
    ai_exam_goods_avg_usage_token = 0
    ai_asr_avg_duration = 0
    if combined_data['AI诊疗_用户数量'] > 0:
        ai_diagnosis_avg_usage_count = combined_data['AI诊疗_使用次数'] / combined_data['AI诊疗_用户数量']
        ai_diagnosis_avg_usage_token = combined_data['AI诊疗_总使用token'] / combined_data['AI诊疗_用户数量']
    if combined_data['AI语音_用户数量'] > 0:
        ai_asr_medical_avg_usage_token = combined_data['AI语音_总使用token'] / combined_data['AI语音_用户数量']
    if combined_data['AI语音_使用次数'] > 0:
        ai_asr_avg_duration = round(combined_data['AI语音_ASR时长'] / 60 / combined_data['AI语音_使用次数'], 2)
    if combined_data['AI检验项目推荐_用户数量'] > 0:
        ai_exam_goods_avg_usage_token = combined_data['AI检验项目推荐_总使用token'] / combined_data['AI检验项目推荐_用户数量']

    content = f"""## {flushDate} AI使用统计

### AI诊疗
- **日使用**: <font color=\"warning\">诊所{combined_data['AI诊疗_使用诊所数量']}, 医生{combined_data['AI诊疗_用户数量']}, 次数{combined_data['AI诊疗_使用次数']}, 人均{ai_diagnosis_avg_usage_count:.2f}</font>
- **采纳情况**: <font color=\"warning\">AI诊疗次数「按门诊单去重」{combined_data['AI诊疗_使用次数「按门诊单去重」']}</font>
 - **诊断/辩证采纳**: <font color=\"warning\">{combined_data['AI诊疗_诊断/辩证采纳次数「按门诊单去重」']}(采纳率{combined_data['AI诊疗_诊断/辩证采纳率「按门诊单去重」']:.2f}%)</font>
 - **中西成药采纳**: <font color=\"warning\">{combined_data['AI诊疗_中西成药采纳次数「按门诊单去重」']}(采纳率{combined_data['AI诊疗_中西成药采纳率「按门诊单去重」']:.2f}%)</font>
 - **中药采纳**: <font color=\"warning\">{combined_data['AI诊疗_中药采纳次数「按门诊单去重」']}(采纳率{combined_data['AI诊疗_中药采纳率「按门诊单去重」']:.2f}%)</font>
 - **外治采纳**: <font color=\"warning\">{combined_data['AI诊疗_外治采纳次数「按门诊单去重」']}(采纳率{combined_data['AI诊疗_外治采纳率「按门诊单去重」']:.2f}%)</font>
- **Token使用**: <font color=\"warning\">总消耗{combined_data['AI诊疗_总使用token'] / 10000:.2f}万, 人日均消耗{ai_diagnosis_avg_usage_token:.2f}</font>
- **药品匹配率**: <font color=\"warning\">{combined_data['AI诊疗_药品匹配率']:.2f}%</font>
-----------------------------
### AI舌象
- **日使用**: <font color=\"warning\">诊所{combined_data['AI舌象_使用诊所数量']}, 医生{combined_data['AI舌象_用户数量']}, AI分析次数{combined_data['AI舌象_分析次数']}</font>
- **采纳情况**: <font color=\"warning\">采纳次数{combined_data['AI舌象_采纳次数']}, 采纳率{combined_data['AI舌象_采纳率']:.2f}%</font>
-----------------------------
### AI语音
- **日使用**: <font color=\"warning\">诊所{combined_data['AI语音_使用诊所数量']}, 医生{combined_data['AI语音_用户数量']}, 次数{combined_data['AI语音_使用次数']}, 语音平均时长{ai_asr_avg_duration:.2f}min</font>
- **采纳情况**: <font color=\"warning\">AI语音次数「按门诊单去重」{combined_data['AI语音_使用次数「按门诊单去重」']}, 采纳次数{combined_data['AI语音_病历采纳次数']}, 采纳率{combined_data['AI语音_病历采纳率']:.2f}%</font>
- **Token使用**: <font color=\"warning\">总消耗{combined_data['AI语音_总使用token'] / 10000:.2f}万, 人日均消耗{ai_asr_medical_avg_usage_token:.2f}</font>
- **ASR使用**: <font color=\"warning\">{combined_data['AI语音_ASR时长'] / 3600:.2f}h</font>
-----------------------------
### AI检验项目推荐
- **日使用**: <font color=\"warning\">推荐次数{combined_data['AI检验项目推荐_使用次数']}, 采纳开出次数{combined_data['AI检验项目推荐_采纳次数']}(采纳率{combined_data['AI检验项目推荐_采纳率']:.2f}%)</font>
- **Token使用**: <font color=\"warning\">总消耗{combined_data['AI检验项目推荐_总使用token'] / 10000:.2f}万, 人日均消耗{ai_exam_goods_avg_usage_token:.2f}</font>
-----------------------------
### AI检验报告解读
- **日使用**: <font color=\"warning\">AI生成次数{combined_data['AI检验报告解读_使用次数']}, 报告查看次数{combined_data['AI检验报告解读_查看次数']}(查看率{combined_data['AI检验报告解读_查看率']:.2f}%)</font>
- **Token使用**: <font color=\"warning\">总消耗{combined_data['AI检验报告解读_总使用token'] / 10000:.2f}万</font>
"""
    logging.info(content)
    # 包括全平台日总消耗token、人日均消耗token、人日最高消耗token、人日最低消耗token
    requests.post(
        url='https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=623c33f6-c566-448f-b541-79717683a4a0',
        # url='https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=42dacafd-d6c5-46b4-bf31-762ebf53a104',
        json={
            "msgtype": "markdown",
            "markdown": {
                "content": content
            }
        })


def sls_data_stat(query, flushDate, region, logstore='prod_longtime', key_func=lambda x: x['buttonName']):
    log_client_factory = AliyunLogClient(region, 'prod')
    from_time = int(datetime.strptime(f'{flushDate} 00:00:00', '%Y-%m-%d %H:%M:%S').timestamp())
    to_time = int(datetime.strptime(f'{flushDate} 23:59:59', '%Y-%m-%d %H:%M:%S').timestamp())
    log_client = log_client_factory.client
    query_req = GetLogsRequest(project=log_client_factory.log_config['project'], logstore=logstore,
                               fromTime=from_time, toTime=to_time, query=query, line=1000, offset=0)
    sls_log_rsp = log_client.get_logs(query_req)
    sls_data_stat_rsp = list_utils.to_map(sls_log_rsp.body, key_func)
    logging.info(f'sls_data_stat_rsp: {sls_data_stat_rsp}')
    return sls_data_stat_rsp


if __name__ == '__main__':
    today = datetime.now().date()
    process_date = today - timedelta(days=1)
    print(f"Processing date: {process_date}")
    ai_usage_continue_stat(process_date)
    #  #跑前面30天数据，从最远的日期开始跑
    # for i in range(30, 1, -1):
    #     process_date = today - timedelta(days=i)
    #     print(f"Processing date: {process_date}")
    #     ai_usage_continue_stat(process_date)
    ai_usage_stat(process_date)
    export_ai_usage_detail(process_date)
    print(f"Processing date success: {process_date}")
