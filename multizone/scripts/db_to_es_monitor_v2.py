#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""


"""
import os
import sys
from datetime import datetime, timedelta

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from scripts.common.utils.lists import ListUtils
from scripts.common.utils.sqls import SqlUtils
from multizone.es import ESClient

try:
    from multizone.log import AliyunLogClient
except Exception as e:
    pass
from multizone.db import DBClient

import argparse


class ChargeSheetChecker:
    es_index_name = "v3-cis-cdss-charge-sheet"

    def __init__(self, client_holder, chain_ids, check_miss_begin_time, check_miss_end_time, es_index_name):
        self.client_holder = client_holder
        self.chain_ids = chain_ids
        self.check_miss_begin_time = check_miss_begin_time
        self.check_miss_end_time = check_miss_end_time
        if es_index_name:
            self.es_index_name = es_index_name

    def check(self):
        self.charge_sheet_check(self.client_holder, self.chain_ids, self.check_miss_begin_time, self.check_miss_end_time)

    def charge_sheet_check(self, client_holder, chain_ids, check_miss_begin_time, check_miss_end_time):

        need_sync_sheet_ids = set()

        # 校验前 5 分钟的状态同步情况
        check_sync_lost_begin_time = check_miss_begin_time
        check_sync_lost_end_time = check_miss_end_time
        today_last5_total = self.check_sync_lost_sheet(client_holder, chain_ids, check_sync_lost_begin_time, check_sync_lost_end_time, need_sync_sheet_ids)

        # 校验昨天的 5 分钟的状态同步情况
        yesterday_last5_total = None
        if client_holder.check_yesterday:
            check_sync_lost_begin_time = check_miss_begin_time - timedelta(days=1)
            check_sync_lost_end_time = check_miss_end_time - timedelta(days=1)
            yesterday_last5_total = self.check_sync_lost_sheet(client_holder, chain_ids, check_sync_lost_begin_time, check_sync_lost_end_time, need_sync_sheet_ids)

        # 为空则忽略
        if not need_sync_sheet_ids:
            client_holder.info(f'charge 索引同步检查完成，today_last5_total:{today_last5_total}，yesterday_last5_total:{yesterday_last5_total}，没有需要同步的数据')
            return

        client_holder.warn(f'charge 索引同步检查完成，today_last5_total:{today_last5_total}，yesterday_last5_total:{yesterday_last5_total}，需要同步的数据: {need_sync_sheet_ids}')
        # 将 need_sync_sheet_ids 拆分成 500 一组
        need_sync_sheet_id_list = list(need_sync_sheet_ids)
        for i in range(0, len(need_sync_sheet_id_list), 500):
            client_holder.charge_write_client.execute("""
                update v2_charge_sheet
                set created = DATE_ADD(created, interval 1 second)
                where id in ({sheet_ids});
            """.format(sheet_ids=SqlUtils.to_in_value(need_sync_sheet_id_list[i:i + 500])))

    def check_sync_lost_sheet(self, client_holder, chain_ids, begin_time, end_time, need_sync_sheet_ids):
        client_holder.info(f'开始执行 charge_sheet {begin_time}-{end_time} 索引 {self.es_index_name} 同步检查')
        # 分页处理，每页 100 条
        page_size = 100
        page_num = 0
        total = 0
        while True:
            offset = page_num * page_size
            # 如果 chain_ids 不为空，则拼接到查询语句中
            chain_id_sql_in = f"chain_id in ({SqlUtils.to_in_value(chain_ids)}) and" if chain_ids else ""
            charge_sheets = client_holder.charge_read_client.fetchall("""
                select id, status, chain_id, patient_id, include_item_type
                from abc_cis_charge.v2_charge_sheet
                where {chain_id}
                  last_modified between '{begin_time}' and '{end_time}'
                limit {offset}, {limit};
            """.format(chain_id=chain_id_sql_in, begin_time=begin_time, end_time=end_time, limit=page_size, offset=offset))
            total += len(charge_sheets)

            if not charge_sheets:
                break

            # 查询患者信息
            patient_id_in_str = SqlUtils.to_in_value(ListUtils.dist_mapping(charge_sheets, lambda e: e['patient_id']))
            patients = client_holder.patient_read_client.fetchall("""
                select id, name, is_member, sex, sn, id_card_cipher, mobile_cipher, id_card_last6, mobile_last4
                from abc_cis_patient.v2_patient
                where id in ({ids});
            """.format(ids=patient_id_in_str))
            # client_holder.info(f'patients {json.dumps(patients)}')

            self.check_sync_charge_sheet(client_holder, charge_sheets, patients, need_sync_sheet_ids)

            page_num += 1

        return total

    def check_sync_charge_sheet(self, client_holder, charge_sheets, patients, need_sync_sheet_ids):
        if not charge_sheets:
            return

        charge_sheet_ids = ListUtils.dist_mapping(charge_sheets, lambda e: e['id'])
        dsl = {
            "_source": {"includes": ["id", "status", "patientId", "patientName", "patientMobileCipher", "patientMobileLast4",
                                     "patientIdCardCipher", "patientIdCardLast6", "patientIsMember", "patientSex", "patientSn",
                                     "includeItemType",
                                     ]},
            "query": {
                "bool": {
                    "filter": [
                        {
                            "terms": {
                                "id": charge_sheet_ids
                            }
                        }
                    ]
                }
            },
            "size": len(charge_sheets)
        }
        search_result = client_holder.es_normal_client.search(self.es_index_name, dsl)
        # search_result = client_holder.es_normal_client.search("v3-cis-cdss-charge-sheet_2024-10-23", dsl)
        search_result_items = search_result['hits']['hits']
        id_to_search_result_item = ListUtils.to_map(search_result_items, lambda e: e['_source']['id'])
        patient_id_to_patient = ListUtils.to_map(patients, lambda e: e['id'])

        # 判断状态是否一致，判断是否有患者姓名，没有则添加到需要同步的列表中
        for sheet in charge_sheets:
            sheet_id = sheet['id']
            sheet_status = sheet['status']
            chain_id = sheet['chain_id']
            db_patient_id = sheet['patient_id']
            db_patient = patient_id_to_patient.get(db_patient_id)
            es_sheet = id_to_search_result_item.get(sheet_id)
            if not es_sheet:
                need_sync_sheet_ids.add(sheet_id)
                client_holder.warn(f'sheet_id: {sheet_id} chain_id: {chain_id} 不存在于 ES 中')
                continue

            es_sheet_status = es_sheet['_source']['status']
            if sheet_status != es_sheet_status:
                need_sync_sheet_ids.add(sheet_id)
                client_holder.warn(f'sheet_id: {sheet_id} chain_id: {chain_id} 状态不一致，DB: {sheet_status}，ES: {es_sheet_status}')
                continue

            patient_id = es_sheet['_source'].get('patientId')
            patient_name = es_sheet['_source'].get('patientName')
            # 如果患者ID不为空，并且不等于 00000000000000000000000000000000，则判断患者姓名是否为空
            if patient_id and patient_id != '00000000000000000000000000000000':
                if not patient_name:
                    need_sync_sheet_ids.add(sheet_id)
                    client_holder.warn(f'sheet_id: {sheet_id} chain_id: {chain_id} 患者姓名为空，患者ID: {patient_id}')
                    continue
                elif db_patient:
                    # 字段校验是否一致
                    if db_patient['name'] != patient_name:
                        need_sync_sheet_ids.add(sheet_id)
                        client_holder.warn(f'sheet_id: {sheet_id} chain_id: {chain_id} 患者姓名不一致，DB: {db_patient["name"]}，ES: {patient_name}')
                        continue
                    # elif db_patient['is_member'] != es_sheet['_source'].get('patientIsMember'):
                    #     need_sync_sheet_ids.add(sheet_id)
                    #     client_holder.warn(f'sheet_id: {sheet_id} chain_id: {chain_id} 患者是否会员不一致，DB: {db_patient["is_member"]}，ES: {es_sheet["_source"].get("patientIsMember")}')
                    #     continue
                    elif db_patient['mobile_cipher'] != es_sheet['_source'].get('patientMobileCipher'):
                        need_sync_sheet_ids.add(sheet_id)
                        client_holder.warn(f'sheet_id: {sheet_id} chain_id: {chain_id} 患者手机号不一致，DB: {db_patient["mobile_cipher"]}，ES: {es_sheet["_source"].get("patientMobileCipher")}')
                        continue
                    elif db_patient['mobile_last4'] != es_sheet['_source'].get('patientMobileLast4'):
                        need_sync_sheet_ids.add(sheet_id)
                        client_holder.warn(f'sheet_id: {sheet_id} chain_id: {chain_id} 患者手机号后4位不一致，DB: {db_patient["mobile_last4"]}，ES: {es_sheet["_source"].get("patientMobileLast4")}')
                        continue
                    elif db_patient['id_card_cipher'] != es_sheet['_source'].get('patientIdCardCipher'):
                        need_sync_sheet_ids.add(sheet_id)
                        client_holder.warn(f'sheet_id: {sheet_id} chain_id: {chain_id} 患者身份证号不一致，DB: {db_patient["id_card_cipher"]}，ES: {es_sheet["_source"].get("patientIdCardCipher")}')
                        continue
                    elif db_patient['id_card_last6'] != es_sheet['_source'].get('patientIdCardLast6'):
                        need_sync_sheet_ids.add(sheet_id)
                        client_holder.warn(f'sheet_id: {sheet_id} chain_id: {chain_id} 患者身份证号后6位不一致，DB: {db_patient["id_card_last6"]}，ES: {es_sheet["_source"].get("patientIdCardLast6")}')
                        continue
                    # elif db_patient['sex'] != es_sheet['_source'].get('patientSex'):
                    #     need_sync_sheet_ids.add(sheet_id)
                    #     client_holder.warn(f'sheet_id: {sheet_id} chain_id: {chain_id} 患者性别不一致，DB: {db_patient["sex"]}，ES: {es_sheet["_source"].get("patientSex")}')
                    #     continue
                    elif db_patient['sn'] != es_sheet['_source'].get('patientSn'):
                        need_sync_sheet_ids.add(sheet_id)
                        client_holder.warn(f'sheet_id: {sheet_id} chain_id: {chain_id} 患者编号不一致，DB: {db_patient["sn"]}，ES: {es_sheet["_source"].get("patientSn")}')
                        continue

            # 包含的 itemType 检查
            db_include_item_type = sheet['include_item_type'] if sheet['include_item_type'] else 0
            es_include_item_type = es_sheet['_source'].get('includeItemType', 0) if es_sheet['_source'].get('includeItemType') else 0
            if db_include_item_type != es_include_item_type:
                need_sync_sheet_ids.add(sheet_id)
                client_holder.warn(f'sheet_id: {sheet_id} chain_id: {chain_id} 包含的 itemType 不一致，DB: {db_include_item_type}，ES: {es_include_item_type}')
                continue

            # 如果ES的患者ID不等于DB的患者ID
            if patient_id and patient_id != sheet['patient_id']:
                need_sync_sheet_ids.add(sheet_id)
                client_holder.warn(f'sheet_id: {sheet_id} chain_id: {chain_id} 患者ID不一致，DB: {sheet["patient_id"]}，ES: {patient_id}')
                continue


class ExaminationSheetChecker:

    def __init__(self, client_holder, chain_ids, check_miss_begin_time, check_miss_end_time):
        self.client_holder = client_holder
        self.chain_ids = chain_ids
        self.check_miss_begin_time = check_miss_begin_time
        self.check_miss_end_time = check_miss_end_time

    def check(self):
        self.examination_sheet_check(self.client_holder, self.chain_ids, self.check_miss_begin_time, self.check_miss_end_time)
        pass

    def examination_sheet_check(self, client_holder, chain_ids, check_miss_begin_time, check_miss_end_time):
        need_sync_sheet_ids = set()

        # 校验前 5 分钟的状态同步情况
        check_sync_lost_begin_time = check_miss_begin_time
        check_sync_lost_end_time = check_miss_end_time
        today_last5_total = self.check_sync_lost_sheet(client_holder, chain_ids, check_sync_lost_begin_time, check_sync_lost_end_time, need_sync_sheet_ids)

        # 校验昨天的 5 分钟的状态同步情况
        yesterday_last5_total = None
        if client_holder.check_yesterday:
            check_sync_lost_begin_time = check_miss_begin_time - timedelta(days=1)
            check_sync_lost_end_time = check_miss_end_time - timedelta(days=1)
            yesterday_last5_total = self.check_sync_lost_sheet(client_holder, chain_ids, check_sync_lost_begin_time, check_sync_lost_end_time, need_sync_sheet_ids)

        # 为空则忽略
        if not need_sync_sheet_ids:
            client_holder.info(f'examination_sheet 索引同步检查完成，today_last5_total:{today_last5_total}，yesterday_last5_total:{yesterday_last5_total}，没有需要同步的数据')
            return

        client_holder.warn(f'examination_sheet 索引同步检查完成，today_last5_total:{today_last5_total}，yesterday_last5_total:{yesterday_last5_total}，需要同步的数据: {need_sync_sheet_ids}')
        # 将 need_sync_sheet_ids 拆分成 500 一组
        need_sync_sheet_id_list = list(need_sync_sheet_ids)
        for i in range(0, len(need_sync_sheet_id_list), 500):
            client_holder.examination_write_client.execute("""
                update v2_examination_sheet
                set created = DATE_ADD(created, interval 1 second)
                where id in ({sheet_ids});
            """.format(sheet_ids=SqlUtils.to_in_value(need_sync_sheet_id_list[i:i + 500])))

    def check_sync_lost_sheet(self, client_holder, chain_ids, begin_time, end_time, need_sync_ids):
        client_holder.info(f'开始执行 examination_sheet [{begin_time}]-[{end_time}] 索引同步检查')

        # 分页处理，每页 100 条
        page_size = 100
        page_num = 0
        total = 0
        while True:
            offset = page_num * page_size
            # 如果 chain_ids 不为空，则拼接到查询语句中
            chain_id_sql_in = f"chain_id in ({SqlUtils.to_in_value(chain_ids)}) and" if chain_ids else ""
            datas = client_holder.examination_read_client.fetchall("""
                select id, chain_id
                from abc_cis_examination.v2_examination_sheet
                where {chain_id}
                  created between '{begin_time}' and '{end_time}'
                limit {limit} offset {offset};
            """.format(chain_id=chain_id_sql_in, begin_time=begin_time, end_time=end_time, limit=page_size, offset=offset))
            total += len(datas)

            if not datas:
                break

            self.check_sync_examination_sheet(client_holder, datas, need_sync_ids)

            page_num += 1

        return total

    def check_sync_examination_sheet(self, client_holder, datas, need_sync_ids):
        if not datas:
            return

        ids = ListUtils.dist_mapping(datas, lambda e: e['id'])
        dsl = {
            "_source": {"includes": ["id", "status"]},
            "query": {
                "bool": {
                    "filter": [
                        {
                            "terms": {
                                "id": ids
                            }
                        },
                        {
                            "exists": {
                                "field": "status"
                            }
                        }
                    ]
                }
            },
            "size": len(datas)
        }
        index_name = "v3-examination-sheet-prod"
        search_result = client_holder.es_normal_client.search(index_name, dsl)
        search_result_items = search_result['hits']['hits']
        id_to_search_result_item = ListUtils.to_map(search_result_items, lambda e: e['_source']['id'])

        # 判断状态是否一致，判断是否有患者姓名，没有则添加到需要同步的列表中
        for data in datas:
            id = data['id']
            chain_id = data['chain_id']
            es_sheet = id_to_search_result_item.get(id)
            if not es_sheet:
                need_sync_ids.add(id)
                client_holder.warn(f'id: {id} chain_id: {chain_id} 不存在或不完整于 {index_name} 中')
                continue


class ExaminationMergeSheetChecker:

    def __init__(self, client_holder, chain_ids, check_miss_begin_time, check_miss_end_time):
        self.client_holder = client_holder
        self.chain_ids = chain_ids
        self.check_miss_begin_time = check_miss_begin_time
        self.check_miss_end_time = check_miss_end_time

    def check(self):
        self.examination_merge_sheet_check(self.client_holder, self.chain_ids, self.check_miss_begin_time, self.check_miss_end_time)
        pass

    def examination_merge_sheet_check(self, client_holder, chain_ids, check_miss_begin_time, check_miss_end_time):
        need_sync_sheet_ids = set()

        # 校验前 5 分钟的状态同步情况
        check_sync_lost_begin_time = check_miss_begin_time
        check_sync_lost_end_time = check_miss_end_time
        today_last5_total = self.check_sync_lost_sheet(client_holder, chain_ids, check_sync_lost_begin_time, check_sync_lost_end_time, need_sync_sheet_ids)

        # 校验昨天的 5 分钟的状态同步情况
        yesterday_last5_total = None
        if client_holder.check_yesterday:
            check_sync_lost_begin_time = check_miss_begin_time - timedelta(days=1)
            check_sync_lost_end_time = check_miss_end_time - timedelta(days=1)
            yesterday_last5_total = self.check_sync_lost_sheet(client_holder, chain_ids, check_sync_lost_begin_time, check_sync_lost_end_time, need_sync_sheet_ids)

        # 为空则忽略
        if not need_sync_sheet_ids:
            client_holder.info(f'examination_merge_sheet 索引同步检查完成，today_last5_total:{today_last5_total}，yesterday_last5_total:{yesterday_last5_total}，没有需要同步的数据')
            return

        client_holder.warn(f'examination_merge_sheet 索引同步检查完成，today_last5_total:{today_last5_total}，yesterday_last5_total:{yesterday_last5_total}，需要同步的数据: {need_sync_sheet_ids}')
        # 将 need_sync_sheet_ids 拆分成 500 一组
        need_sync_sheet_id_list = list(need_sync_sheet_ids)
        for i in range(0, len(need_sync_sheet_id_list), 500):
            client_holder.examination_write_client.execute("""
                update v2_examination_merge_sheet
                set created = DATE_ADD(created, interval 1 second)
                where id in ({sheet_ids});
            """.format(sheet_ids=SqlUtils.to_in_value(need_sync_sheet_id_list[i:i + 500])))

    def check_sync_lost_sheet(self, client_holder, chain_ids, begin_time, end_time, need_sync_ids):
        client_holder.info(f'开始执行 examination_merge_sheet [{begin_time}]-[{end_time}] 索引同步检查')

        # 分页处理，每页 100 条
        page_size = 100
        page_num = 0
        total = 0
        while True:
            offset = page_num * page_size
            # 如果 chain_ids 不为空，则拼接到查询语句中
            chain_id_sql_in = f"chain_id in ({SqlUtils.to_in_value(chain_ids)}) and" if chain_ids else ""
            datas = client_holder.examination_read_client.fetchall("""
                select id, chain_id
                from abc_cis_examination.v2_examination_merge_sheet
                where {chain_id}
                  created between '{begin_time}' and '{end_time}'
                limit {limit} offset {offset};
            """.format(chain_id=chain_id_sql_in, begin_time=begin_time, end_time=end_time, limit=page_size, offset=offset))
            total += len(datas)

            if not datas:
                break

            self.check_sync_examination_merge_sheet(client_holder, datas, need_sync_ids)

            page_num += 1

        return total

    def check_sync_examination_merge_sheet(self, client_holder, datas, need_sync_ids):
        if not datas:
            return

        ids = ListUtils.dist_mapping(datas, lambda e: e['id'])
        dsl = {
            "_source": {"includes": ["id"]},
            "query": {
                "bool": {
                    "filter": [
                        {
                            "terms": {
                                "id": ids
                            }
                        }
                    ]
                }
            },
            "size": len(datas)
        }
        index_name = "v3-examination-merge-sheet-prod"
        search_result = client_holder.es_normal_client.search(index_name, dsl)
        search_result_items = search_result['hits']['hits']
        id_to_search_result_item = ListUtils.to_map(search_result_items, lambda e: e['_source']['id'])

        for data in datas:
            id = data['id']
            es_sheet = id_to_search_result_item.get(id)
            if not es_sheet:
                need_sync_ids.add(id)
                client_holder.warn(f'id: {id} 不存在或不完整于 {index_name} 中')
                continue


class OutpatientSheetChecker:
    def __init__(self, client_holder, chain_ids, check_miss_begin_time, check_miss_end_time):
        self.client_holder = client_holder
        self.chain_ids = chain_ids
        self.check_miss_begin_time = check_miss_begin_time
        self.check_miss_end_time = check_miss_end_time

    def check(self):
        self.outpatient_sheet_check(self.client_holder, self.chain_ids, self.check_miss_begin_time, self.check_miss_end_time)
        pass

    def outpatient_sheet_check(self, client_holder, chain_ids, check_miss_begin_time, check_miss_end_time):
        need_sync_sheet_ids = set()

        # 校验前 5 分钟的状态同步情况
        check_sync_lost_begin_time = check_miss_begin_time
        check_sync_lost_end_time = check_miss_end_time
        today_last5_total = self.check_sync_lost_sheet(client_holder, chain_ids, check_sync_lost_begin_time, check_sync_lost_end_time, need_sync_sheet_ids)

        # 校验昨天的 5 分钟的状态同步情况
        yesterday_last5_total = None
        if client_holder.check_yesterday:
            check_sync_lost_begin_time = check_miss_begin_time - timedelta(days=1)
            check_sync_lost_end_time = check_miss_end_time - timedelta(days=1)
            yesterday_last5_total = self.check_sync_lost_sheet(client_holder, chain_ids, check_sync_lost_begin_time, check_sync_lost_end_time, need_sync_sheet_ids)

        # 为空则忽略
        if not need_sync_sheet_ids:
            client_holder.info(f'outpatient_sheet 索引同步检查完成，today_last5_total:{today_last5_total}，yesterday_last5_total:{yesterday_last5_total}，没有需要同步的数据')
            return

        client_holder.warn(f'outpatient_sheet 索引同步检查完成，today_last5_total:{today_last5_total}，yesterday_last5_total:{yesterday_last5_total}，需要同步的数据: {need_sync_sheet_ids}')
        # 将 need_sync_sheet_ids 拆分成 500 一组
        need_sync_sheet_id_list = list(need_sync_sheet_ids)
        for i in range(0, len(need_sync_sheet_id_list), 500):
            client_holder.outpatient_write_client.execute("""
                update v2_outpatient_sheet
                set created = DATE_ADD(created, interval 1 second)
                where id in ({sheet_ids});
            """.format(sheet_ids=SqlUtils.to_in_value(need_sync_sheet_id_list[i:i + 500])))

    def check_sync_lost_sheet(self, client_holder, chain_ids, begin_time, end_time, need_sync_ids):
        client_holder.info(f'开始执行 outpatient_sheet [{begin_time}]-[{end_time}] 索引同步检查')

        # 分页处理，每页 100 条
        page_size = 100
        page_num = 0
        total = 0
        while True:
            offset = page_num * page_size
            # 如果 chain_ids 不为空，则拼接到查询语句中
            chain_id_sql_in = f"chain_id in ({SqlUtils.to_in_value(chain_ids)}) and" if chain_ids else ""
            datas = client_holder.outpatient_read_client.fetchall("""
                select id
                from abc_cis_outpatient.v2_outpatient_sheet
                where {chain_id}
                  created between '{begin_time}' and '{end_time}'
                limit {limit} offset {offset};
            """.format(chain_id=chain_id_sql_in, begin_time=begin_time, end_time=end_time, limit=page_size, offset=offset))
            total += len(datas)

            if not datas:
                break

            self.check_sync_outpatient_sheet(client_holder, datas, need_sync_ids)

            page_num += 1

        return total

    def check_sync_outpatient_sheet(self, client_holder, datas, need_sync_ids):
        if not datas:
            return

        ids = ListUtils.dist_mapping(datas, lambda e: e['id'])
        dsl = {
            "_source": {"includes": ["id"]},
            "query": {
                "bool": {
                    "filter": [
                        {
                            "terms": {
                                "id": ids
                            }
                        }
                    ]
                }
            },
            "size": len(datas)
        }
        index_name = "v3-cis-cdss-outpatient-sheet"
        search_result = client_holder.es_normal_client.search(index_name, dsl)
        search_result_items = search_result['hits']['hits']
        id_to_search_result_item = ListUtils.to_map(search_result_items, lambda e: e['_source']['id'])

        for data in datas:
            id = data['id']
            es_sheet = id_to_search_result_item.get(id)
            if not es_sheet:
                need_sync_ids.add(id)
                client_holder.warn(f'id: {id} 不存在或不完整于 {index_name} 中')
                continue


class DispensingSheetChecker:
    def __init__(self, client_holder, chain_ids, check_miss_begin_time, check_miss_end_time):
        self.client_holder = client_holder
        self.chain_ids = chain_ids
        self.check_miss_begin_time = check_miss_begin_time
        self.check_miss_end_time = check_miss_end_time

    def check(self):
        self.dispensing_sheet_check(self.client_holder, self.chain_ids, self.check_miss_begin_time, self.check_miss_end_time)
        pass

    def dispensing_sheet_check(self, client_holder, chain_ids, check_miss_begin_time, check_miss_end_time):
        need_sync_sheet_ids = set()

        # 校验前 5 分钟的状态同步情况
        check_sync_lost_begin_time = check_miss_begin_time
        check_sync_lost_end_time = check_miss_end_time
        today_last5_total = self.check_sync_lost_sheet(client_holder, chain_ids, check_sync_lost_begin_time, check_sync_lost_end_time, need_sync_sheet_ids)

        # 校验昨天的 5 分钟的状态同步情况
        yesterday_last5_total = None
        if client_holder.check_yesterday:
            check_sync_lost_begin_time = check_miss_begin_time - timedelta(days=1)
            check_sync_lost_end_time = check_miss_end_time - timedelta(days=1)
            yesterday_last5_total = self.check_sync_lost_sheet(client_holder, chain_ids, check_sync_lost_begin_time, check_sync_lost_end_time, need_sync_sheet_ids)

        # 为空则忽略
        if not need_sync_sheet_ids:
            client_holder.info(f'dispensing_sheet 索引同步检查完成，today_last5_total:{today_last5_total}，yesterday_last5_total:{yesterday_last5_total}，没有需要同步的数据')
            return

        client_holder.warn(f'dispensing_sheet 索引同步检查完成，today_last5_total:{today_last5_total}，yesterday_last5_total:{yesterday_last5_total}，需要同步的数据: {need_sync_sheet_ids}')
        # 将 need_sync_sheet_ids 拆分成 500 一组
        need_sync_sheet_id_list = list(need_sync_sheet_ids)
        for i in range(0, len(need_sync_sheet_id_list), 500):
            client_holder.dispensing_write_client.execute("""
                update v2_dispensing_sheet
                set created = DATE_ADD(created, interval 1 second)
                where id in ({sheet_ids});
            """.format(sheet_ids=SqlUtils.to_in_value(need_sync_sheet_id_list[i:i + 500])))

    def check_sync_lost_sheet(self, client_holder, chain_ids, begin_time, end_time, need_sync_ids):
        client_holder.info(f'开始执行 dispensing_sheet [{begin_time}]-[{end_time}] 索引同步检查')

        # 分页处理，每页 100 条
        page_size = 100
        page_num = 0
        total = 0
        while True:
            offset = page_num * page_size
            # 如果 chain_ids 不为空，则拼接到查询语句中
            chain_id_sql_in = f"chain_id in ({SqlUtils.to_in_value(chain_ids)}) and" if chain_ids else ""
            datas = client_holder.dispensing_read_client.fetchall("""
                select id
                from abc_cis_dispensing.v2_dispensing_sheet
                where {chain_id}
                  created between '{begin_time}' and '{end_time}'
                limit {limit} offset {offset};
            """.format(chain_id=chain_id_sql_in, begin_time=begin_time, end_time=end_time, limit=page_size, offset=offset))
            total += len(datas)

            if not datas:
                break

            self.check_sync_dispensing_sheet(client_holder, datas, need_sync_ids)

            page_num += 1

        return total

    def check_sync_dispensing_sheet(self, client_holder, datas, need_sync_ids):
        if not datas:
            return

        ids = ListUtils.dist_mapping(datas, lambda e: e['id'])
        dsl = {
            "_source": {"includes": ["id", "patientId", "patientName"]},
            "query": {
                "bool": {
                    "filter": [
                        {
                            "terms": {
                                "id": ids
                            }
                        }
                    ]
                }
            },
            "size": len(datas)
        }
        index_name = "v3-abc-cdss-dispense-sheet-prod"
        search_result = client_holder.es_normal_client.search(index_name, dsl)
        search_result_items = search_result['hits']['hits']
        id_to_search_result_item = ListUtils.to_map(search_result_items, lambda e: e['_source']['id'])

        for data in datas:
            id = data['id']
            es_sheet = id_to_search_result_item.get(id)

            if not es_sheet:
                need_sync_ids.add(id)
                continue

            patient_id = es_sheet['_source'].get('patientId')
            patient_name = es_sheet['_source'].get('patientName')
            # 如果患者ID不为空，并且不等于 00000000000000000000000000000000，则判断患者姓名是否为空
            if patient_id and patient_id != '00000000000000000000000000000000' and not patient_name:
                need_sync_ids.add(id)
                continue


class RegistrationFormItemChecker:
    def __init__(self, client_holder, chain_ids, check_miss_begin_time, check_miss_end_time):
        self.client_holder = client_holder
        self.chain_ids = chain_ids
        self.check_miss_begin_time = check_miss_begin_time
        self.check_miss_end_time = check_miss_end_time

    def check(self):
        self.registration_form_item_check(self.client_holder, self.chain_ids, self.check_miss_begin_time, self.check_miss_end_time)
        pass

    def registration_form_item_check(self, client_holder, chain_ids, check_miss_begin_time, check_miss_end_time):
        need_sync_sheet_ids = set()

        # 校验前 5 分钟的状态同步情况
        check_sync_lost_begin_time = check_miss_begin_time
        check_sync_lost_end_time = check_miss_end_time
        today_last5_total = self.check_sync_lost_sheet(client_holder, chain_ids, check_sync_lost_begin_time, check_sync_lost_end_time, need_sync_sheet_ids)

        # 校验昨天的 5 分钟的状态同步情况
        yesterday_last5_total = None
        if client_holder.check_yesterday:
            check_sync_lost_begin_time = check_miss_begin_time - timedelta(days=1)
            check_sync_lost_end_time = check_miss_end_time - timedelta(days=1)
            yesterday_last5_total = self.check_sync_lost_sheet(client_holder, chain_ids, check_sync_lost_begin_time, check_sync_lost_end_time, need_sync_sheet_ids)

        # 为空则忽略
        if not need_sync_sheet_ids:
            client_holder.info(f'registration_form_item 索引同步检查完成，today_last5_total:{today_last5_total}，yesterday_last5_total:{yesterday_last5_total}，没有需要同步的数据')
            return

        client_holder.warn(f'registration_form_item 索引同步检查完成，today_last5_total:{today_last5_total}，yesterday_last5_total:{yesterday_last5_total}，需要同步的数据: {need_sync_sheet_ids}')
        # 将 need_sync_sheet_ids 拆分成 500 一组
        need_sync_sheet_id_list = list(need_sync_sheet_ids)
        for i in range(0, len(need_sync_sheet_id_list), 500):
            client_holder.registration_write_client.execute("""
                update v2_registration_form_item
                set created = DATE_ADD(created, interval 1 second)
                where id in ({sheet_ids});
            """.format(sheet_ids=SqlUtils.to_in_value(need_sync_sheet_id_list[i:i + 500])))

    def check_sync_lost_sheet(self, client_holder, chain_ids, begin_time, end_time, need_sync_ids):
        client_holder.info(f'开始执行 registration_form_item [{begin_time}]-[{end_time}] 索引同步检查')

        # 分页处理，每页 100 条
        page_size = 100
        page_num = 0
        total = 0
        while True:
            offset = page_num * page_size
            # 如果 chain_ids 不为空，则拼接到查询语句中
            chain_id_sql_in = f"chain_id in ({SqlUtils.to_in_value(chain_ids)}) and" if chain_ids else ""
            datas = client_holder.registration_read_client.fetchall("""
                select id
                from abc_cis_registration.v2_registration_form_item
                where {chain_id}
                  created between '{begin_time}' and '{end_time}'
                limit {limit} offset {offset};
            """.format(chain_id=chain_id_sql_in, begin_time=begin_time, end_time=end_time, limit=page_size, offset=offset))
            total += len(datas)

            if not datas:
                break

            self.check_sync_registration_form_item(client_holder, datas, need_sync_ids)

            page_num += 1

        return total

    def check_sync_registration_form_item(self, client_holder, datas, need_sync_ids):
        if not datas:
            return

        ids = ListUtils.dist_mapping(datas, lambda e: e['id'])
        dsl = {
            "_source": {"includes": ["registrationFormItemId", "patientId", "patientName"]},
            "query": {
                "bool": {
                    "filter": [
                        {
                            "terms": {
                                "registrationFormItemId": ids
                            }
                        }
                    ]
                }
            },
            "size": len(datas)
        }
        index_name = "v3-cis-cdss-registration-form"
        search_result = client_holder.es_normal_client.search(index_name, dsl)
        search_result_items = search_result['hits']['hits']
        id_to_search_result_item = ListUtils.to_map(search_result_items, lambda e: e['_source']['registrationFormItemId'])

        for data in datas:
            id = data['id']
            es_sheet = id_to_search_result_item.get(id)

            if not es_sheet:
                need_sync_ids.add(id)
                continue

            patient_id = es_sheet['_source'].get('patientId')
            patient_name = es_sheet['_source'].get('patientName')
            # 如果患者ID不为空，并且不等于 00000000000000000000000000000000，则判断患者姓名是否为空
            if patient_id and patient_id != '00000000000000000000000000000000' and not patient_name:
                need_sync_ids.add(id)
                continue


class PeOrderSheetChecker:
    def __init__(self, client_holder, chain_ids, check_miss_begin_time, check_miss_end_time):
        self.client_holder = client_holder
        self.chain_ids = chain_ids
        self.check_miss_begin_time = check_miss_begin_time
        self.check_miss_end_time = check_miss_end_time

    def check(self):
        self.pe_order_sheet_check(self.client_holder, self.chain_ids, self.check_miss_begin_time, self.check_miss_end_time)
        pass

    def pe_order_sheet_check(self, client_holder, chain_ids, check_miss_begin_time, check_miss_end_time):
        need_sync_sheet_ids = set()

        # 校验前 5 分钟的状态同步情况
        check_sync_lost_begin_time = check_miss_begin_time
        check_sync_lost_end_time = check_miss_end_time
        today_last5_total = self.check_sync_lost_sheet(client_holder, chain_ids, check_sync_lost_begin_time, check_sync_lost_end_time, need_sync_sheet_ids)

        # 校验昨天的 5 分钟的状态同步情况
        yesterday_last5_total = None
        if client_holder.check_yesterday:
            check_sync_lost_begin_time = check_miss_begin_time - timedelta(days=1)
            check_sync_lost_end_time = check_miss_end_time - timedelta(days=1)
            yesterday_last5_total = self.check_sync_lost_sheet(client_holder, chain_ids, check_sync_lost_begin_time, check_sync_lost_end_time, need_sync_sheet_ids)

        # 为空则忽略
        if not need_sync_sheet_ids:
            client_holder.info(f'pe_order_sheet 索引同步检查完成，today_last5_total:{today_last5_total}，yesterday_last5_total:{yesterday_last5_total}，没有需要同步的数据')
            return

        client_holder.warn(f'pe_order_sheet 索引同步检查完成，today_last5_total:{today_last5_total}，yesterday_last5_total:{yesterday_last5_total}，需要同步的数据: {need_sync_sheet_ids}')
        # 将 need_sync_sheet_ids 拆分成 500 一组
        need_sync_sheet_id_list = list(need_sync_sheet_ids)
        for i in range(0, len(need_sync_sheet_id_list), 500):
            client_holder.pe_order_write_client.execute("""
                update v1_pe_sheet
                set created = DATE_ADD(created, interval 1 second)
                where id in ({sheet_ids});
            """.format(sheet_ids=SqlUtils.to_in_value(need_sync_sheet_id_list[i:i + 500])))

    def check_sync_lost_sheet(self, client_holder, chain_ids, begin_time, end_time, need_sync_ids):
        client_holder.info(f'开始执行 pe_order_sheet [{begin_time}]-[{end_time}] 索引同步检查')

        # 分页处理，每页 100 条
        page_size = 100
        page_num = 0
        total = 0
        while True:
            offset = page_num * page_size
            # 如果 chain_ids 不为空，则拼接到查询语句中
            chain_id_sql_in = f"chain_id in ({SqlUtils.to_in_value(chain_ids)}) and" if chain_ids else ""
            datas = client_holder.pe_order_read_client.fetchall("""
                select id
                from abc_pe_order.v1_pe_sheet
                where {chain_id}
                  created between '{begin_time}' and '{end_time}'
                limit {limit} offset {offset};
            """.format(chain_id=chain_id_sql_in, begin_time=begin_time, end_time=end_time, limit=page_size, offset=offset))
            total += len(datas)

            if not datas:
                break

            self.check_sync_pe_order_sheet(client_holder, datas, need_sync_ids)

            page_num += 1

        return total

    def check_sync_pe_order_sheet(self, client_holder, datas, need_sync_ids):
        if not datas:
            return

        ids = ListUtils.dist_mapping(datas, lambda e: e['id'])
        dsl = {
            "_source": {"includes": ["id"]},
            "query": {
                "bool": {
                    "filter": [
                        {
                            "terms": {
                                "id": ids
                            }
                        }
                    ]
                }
            },
            "size": len(datas)
        }
        index_name = "v3-pe-order-sheet-prod"
        search_result = client_holder.es_normal_client.search(index_name, dsl)
        search_result_items = search_result['hits']['hits']
        id_to_search_result_item = ListUtils.to_map(search_result_items, lambda e: e['_source']['id'])

        for data in datas:
            id = data['id']
            es_sheet = id_to_search_result_item.get(id)

            if not es_sheet:
                need_sync_ids.add(id)
                continue

class GoodsChecker:
    def __init__(self, client_holder, chain_ids, check_miss_begin_time, check_miss_end_time):
        self.client_holder = client_holder
        self.chain_ids = chain_ids
        self.check_miss_begin_time = check_miss_begin_time
        self.check_miss_end_time = check_miss_end_time

    def check(self):
        self.goods_info_check(self.client_holder, self.chain_ids, self.check_miss_begin_time, self.check_miss_end_time)
        pass

    def goods_info_check(self, client_holder, chain_ids, check_miss_begin_time, check_miss_end_time):
        need_sync_goods_ids = set()

        # 校验前 5 分钟的状态同步情况
        check_sync_lost_begin_time = check_miss_begin_time
        check_sync_lost_end_time = check_miss_end_time
        today_last5_total = self.check_sync_lost_goods(client_holder, chain_ids, check_sync_lost_begin_time, check_sync_lost_end_time, need_sync_goods_ids)

        # 校验昨天的 5 分钟的状态同步情况
        yesterday_last5_total = None
        if client_holder.check_yesterday:
            check_sync_lost_begin_time = check_miss_begin_time - timedelta(days=1)
            check_sync_lost_end_time = check_miss_end_time - timedelta(days=1)
            yesterday_last5_total = self.check_sync_lost_goods(client_holder, chain_ids, check_sync_lost_begin_time, check_sync_lost_end_time, need_sync_goods_ids)

        # 为空则忽略
        if not need_sync_goods_ids:
            client_holder.info(f'goods 索引同步检查完成，today_last5_total:{today_last5_total}，yesterday_last5_total:{yesterday_last5_total}，没有需要同步的数据')
            return

        client_holder.warn(f'goods 索引同步检查完成，today_last5_total:{today_last5_total}，yesterday_last5_total:{yesterday_last5_total}，需要同步的数据: {need_sync_goods_ids}')
        # 将 need_sync_sheet_ids 拆分成 500 一组
        need_sync_sheet_id_list = list(need_sync_goods_ids)
        for i in range(0, len(need_sync_sheet_id_list), 500):
            client_holder.goods_write_client.execute("""
                update abc_cis_goods.v2_goods
                set created_date = DATE_ADD(created_date, interval 1 second )
                where id in ({sheet_ids});
            """.format(sheet_ids=SqlUtils.to_in_value(need_sync_sheet_id_list[i:i + 500])))

    def check_sync_lost_goods(self, client_holder, chain_ids, begin_time, end_time, need_sync_ids):
        client_holder.info(f'开始执行 goods [{begin_time}]-[{end_time}] 索引同步检查')

        # 分页处理，每页 100 条
        page_size = 100
        page_num = 0
        total = 0
        while True:
            offset = page_num * page_size
            # 如果 chain_ids 不为空，则拼接到查询语句中
            chain_id_sql_in = f"organ_id in ({SqlUtils.to_in_value(chain_ids)}) and" if chain_ids else ""
            datas = client_holder.goods_read_client.fetchall("""
                select id, name, medicine_nmpn
                from abc_cis_goods.v2_goods
                where {chain_id}
                  last_modified_date between '{begin_time}' and '{end_time}'
                limit {limit} offset {offset};
            """.format(chain_id=chain_id_sql_in, begin_time=begin_time, end_time=end_time, limit=page_size, offset=offset))
            total += len(datas)

            if not datas:
                break

            self.check_sync_goods(client_holder, datas, need_sync_ids)

            page_num += 1

        return total

    def check_sync_goods(self, client_holder, datas, need_sync_ids):
        if not datas:
            return

        ids = ListUtils.dist_mapping(datas, lambda e: e['id'])
        dsl = {
            "_source": {"includes": ["id", "name", "medicineNmpn"]},
            "query": {
                "bool": {
                    "filter": [
                        {
                            "terms": {
                                "id": ids
                            }
                        }
                    ]
                }
            },
            "size": len(datas)
        }
        index_name = "prod-cis-search-goods-nested"
        search_result = client_holder.ha_es_client.search(index_name, dsl)
        search_result_items = search_result['hits']['hits']
        id_to_search_result_item = ListUtils.to_map(search_result_items, lambda e: e['_source']['id'])

        for data in datas:
            id = data['id']
            es_sheet = id_to_search_result_item.get(id)

            if not es_sheet:
                need_sync_ids.add(id)
                continue

            # 校验 name 和 medicineNmpn 是否相同
            if data['name'] != es_sheet['_source']['name'] or data['medicine_nmpn'] != es_sheet['_source']['medicineNmpn']:
                need_sync_ids.add(id)
                continue


def run(client_holder, chain_id_str, time, interval, args, time_before_offset):
    # 开始时间为昨天的 5 分钟前，结束时间为昨天的当前时间，日期为昨天
    if time_before_offset:
        check_miss_begin_time = time - timedelta(minutes=interval + 7)
        check_miss_end_time = time - timedelta(minutes=5)
    else:
        check_miss_begin_time = time
        check_miss_end_time = time + timedelta(minutes=interval)

    client_holder.info(f'开始执行 {check_miss_begin_time}-{check_miss_end_time} 索引同步检查')

    # chain_id 按照逗号分割
    chain_ids = chain_id_str.split(',') if chain_id_str else []

    # 收费单检查
    if client_holder.check_charge_sheet:
        charge_sheet_checker = ChargeSheetChecker(client_holder, chain_ids, check_miss_begin_time, check_miss_end_time, args.check_charge_es_index_name)
        charge_sheet_checker.check()

    # 检查检验单检查
    if client_holder.check_examination_sheet:
        examination_sheet_checker = ExaminationSheetChecker(client_holder, chain_ids, check_miss_begin_time, check_miss_end_time)
        examination_sheet_checker.check()

    if client_holder.check_examination_merge_sheet:
        examination_sheet_merge_checker = ExaminationMergeSheetChecker(client_holder, chain_ids, check_miss_begin_time, check_miss_end_time)
        examination_sheet_merge_checker.check()

    # 门诊单校验
    if client_holder.check_outpatient_sheet:
        outpatient_sheet_checker = OutpatientSheetChecker(client_holder, chain_ids, check_miss_begin_time, check_miss_end_time)
        outpatient_sheet_checker.check()

    # 发药单校验
    if client_holder.check_dispensing_sheet:
        dispensing_sheet_checker = DispensingSheetChecker(client_holder, chain_ids, check_miss_begin_time, check_miss_end_time)
        dispensing_sheet_checker.check()

    # 挂号单校验
    if client_holder.check_registration_form_item:
        registration_form_item_checker = RegistrationFormItemChecker(client_holder, chain_ids, check_miss_begin_time, check_miss_end_time)
        registration_form_item_checker.check()

    # 住院单校验
    if client_holder.check_pe_order_sheet:
        pe_order_sheet_checker = PeOrderSheetChecker(client_holder, chain_ids, check_miss_begin_time, check_miss_end_time)
        pe_order_sheet_checker.check()

    # 商品校验
    client_holder.logger.info(f"check_goods: {client_holder.check_goods}")
    if client_holder.check_goods:
        goods_checker = GoodsChecker(client_holder, chain_ids, check_miss_begin_time, check_miss_end_time)
        goods_checker.check()


class ClientHolder:
    check_yesterday = True

    check_charge_sheet = False
    charge_read_client = None
    charge_write_client = None

    check_examination_sheet = False
    check_examination_merge_sheet = False
    examination_read_client = None
    examination_write_client = None

    check_outpatient_sheet = False
    outpatient_read_client = None
    outpatient_write_client = None

    check_dispensing_sheet = False
    dispensing_read_client = None
    dispensing_write_client = None

    check_registration_form_item = False
    registration_read_client = None
    registration_write_client = None

    check_pe_order_sheet = False
    pe_order_read_client = None
    pe_order_write_client = None

    patient_read_client = None

    check_goods = False
    goods_read_client = None
    goods_write_client = None

    ha_es_client = None
    es_client = None
    es_normal_client = None
    logger = None

    def __init__(self, region, env, args):
        no_tunnel = args.local != 'true'
        self.check_yesterday = args.check_yesterday != 'false'
        read_client = DBClient(region, 'ob', 'abc_cis_charge', env, no_tunnel)
        need_ha_es = False
        if args.check_charge_sheet:
            # self.charge_read_client = DBClient(region, 'ob', 'abc_cis_charge', env, no_tunnel)
            self.charge_read_client = read_client
            self.charge_write_client = DBClient(region, 'abc_cis_charge', 'abc_cis_charge', env, no_tunnel)
            self.patient_read_client = read_client
            self.check_charge_sheet = True

        if args.check_examination_sheet or args.check_examination_merge_sheet:
            # self.examination_read_client = DBClient(region, 'ob', 'abc_cis_examination', env, no_tunnel)
            self.examination_read_client = read_client
            self.examination_write_client = DBClient(region, 'abc_cis_outpatient', 'abc_cis_examination', env, no_tunnel)
            if args.check_examination_sheet:
                self.check_examination_sheet = True
            if args.check_examination_merge_sheet:
                self.check_examination_merge_sheet = True

        if args.check_outpatient_sheet:
            # self.outpatient_read_client = DBClient(region, 'ob', 'abc_cis_outpatient', env, no_tunnel)
            self.outpatient_read_client = read_client
            self.outpatient_write_client = DBClient(region, 'abc_cis_outpatient', 'abc_cis_outpatient', env, no_tunnel)
            self.check_outpatient_sheet = True

        if args.check_dispensing_sheet:
            # self.dispensing_read_client = DBClient(region, 'ob', 'abc_cis_dispensing', env, no_tunnel)
            self.dispensing_read_client = read_client
            self.dispensing_write_client = DBClient(region, 'abc_cis_stock_zip', 'abc_cis_dispensing', env, no_tunnel)
            self.check_dispensing_sheet = True

        if args.check_registration_form_item:
            # self.registration_read_client = DBClient(region, 'ob', 'abc_cis_registration', env, no_tunnel)
            self.registration_read_client = read_client
            self.registration_write_client = DBClient(region, 'abc_cis_outpatient', 'abc_cis_registration', env, no_tunnel)
            self.check_registration_form_item = True

        if args.check_pe_order_sheet:
            # self.pe_order_read_client = DBClient(region, 'ob', 'abc_pe_order', env, no_tunnel)
            self.pe_order_read_client = read_client
            self.pe_order_write_client = DBClient(region, 'scrm_hospital', 'abc_pe_order', env, no_tunnel)
            self.check_pe_order_sheet = True
        
        if args.check_goods:
            self.goods_read_client = read_client
            self.goods_write_client = DBClient(region, 'abc_cis_stock', 'abc_cis_goods', env, no_tunnel)
            self.check_goods = True
            need_ha_es = True

        self.es_normal_client = ESClient(region, env, True, True)
        if need_ha_es:
            self.ha_es_client = ESClient(region, env, False, True)

        try:
            self.logger = AliyunLogClient(region, 'prod').logger
        except Exception as e:
            print(f'初始化日志客户端失败')

    def info(self, msg, *args, **kwargs):
        if self.logger:
            self.logger.info(msg, *args, **kwargs)
        else:
            print(msg)

    def error(self, msg, *args, **kwargs):
        if self.logger:
            self.logger.error(msg, *args, **kwargs)
        else:
            print(msg)

    def warn(self, msg, *args, **kwargs):
        if self.logger:
            self.logger.warn(msg, *args, **kwargs)
        else:
            print(msg)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--region', help='分区id', required=False)
    parser.add_argument('--chain-id', help='连锁id', required=False)
    parser.add_argument('--env', help='环境 dev/test/prod')
    parser.add_argument('--begin-date', help='开始日期 yyyy-MM-dd', required=False)
    parser.add_argument('--end-date', help='结束日期 yyyy-MM-dd', required=False)
    parser.add_argument('--interval', help='时间间隔，单位分钟', required=False)
    parser.add_argument('--check-charge-sheet', help='检查收费单', required=False)
    parser.add_argument('--check-charge-es-index-name', help='是否本地执行', required=False)
    parser.add_argument('--check-examination-sheet', help='检查检查检验单', required=False)
    parser.add_argument('--check-examination-merge-sheet', help='检查检查检验Merge单', required=False)
    parser.add_argument('--check-outpatient-sheet', help='检查门诊单', required=False)
    parser.add_argument('--check-dispensing-sheet', help='检查发药单', required=False)
    parser.add_argument('--check-registration-form-item', help='检查挂号单', required=False)
    parser.add_argument('--check-pe-order-sheet', help='检查住院单', required=False)
    parser.add_argument('--check-goods', help='检查商品', required=False)
    parser.add_argument('--check-yesterday', help='是否检查昨天', required=False)
    parser.add_argument('--local', help='是否本地执行', required=False)
    args, argv = parser.parse_known_args()
    if not args.env:
        parser.print_help()
        sys.exit(-1)

    # 从 args 中获取日期，如果有开始日期和结束日期，则从开始日期的 00:00:00 到结束日期的 23:59:59，按照 5 分钟的时间间隔进行检查
    # 如果没有开始日期和结束日期，则只检查当前时间的 5 分钟
    env = args.env
    interval = int(args.interval if args.interval else 5)
    region = args.region if args.region else 'ShangHai'
    client_holder = ClientHolder(region, env, args)
    if args.begin_date and args.end_date:
        begin_date = datetime.strptime(args.begin_date, '%Y-%m-%d %H:%M:%S')
        end_date = datetime.strptime(args.end_date, '%Y-%m-%d %H:%M:%S')
        current_time = begin_date
        end_time = end_date
        while current_time <= end_time:
            try:
                run(client_holder, args.chain_id, current_time, interval, args, False)
            except Exception as e:
                client_holder.error("执行失败", e)
                # 再把异常抛出去
                raise e
            current_time += timedelta(minutes=interval)
    else:
        run(client_holder, args.chain_id, datetime.now(), interval, args, True)


if __name__ == '__main__':
    main()
