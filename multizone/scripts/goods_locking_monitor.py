#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""


"""
import json
import os
import logging

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys
from datetime import date, datetime, timedelta

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.rediscli import RedisClient,redisHost
from multizone.log import AliyunLogClient
from multizone.db import DBClient
from multizone.config import region_name

import argparse

default_id = '00000000000000000000000000000000'

env_name_to_value = {
    'pre': 2,
    'gray': 1,
    'prod': 0
}

env_value_to_name = {
    2: 'pre',
    1: 'gray',
    0: 'prod'
}

def run(chain_id, clinic_id, goods_id, created, op_type, force_delete, env, online_env, region_name):
    region_list = [region_name] if region_name is not None else ["ShangHai", "HangZhou"] if env == 'prod' else ["ShangHai"]
    logger = AliyunLogClient(region_list[0], 'prod').logger
    for region in region_list:
        logger.info(f'--------------------锁库异常监控开始 {region}--------------------')
        goods_redis_cli = RedisClient(region, 'abc-redis', 78 if env == 'prod' else 0, env, region_name is not None)
        goods_db_wr_cli = DBClient(region, 'abc_cis_stock', 'abc_cis_goods', env, region_name is not None)
        goods_db_cli = DBClient(region, 'ods', 'abc_cis_goods', env, region_name is not None)
        charge_db_cli = DBClient(region, 'ods', 'abc_cis_charge', env, region_name is not None)
        dispensing_db_cli = DBClient(region, 'ods', 'abc_cis_dispensing', env, region_name is not None)
        basic_db_cli = DBClient(region, 'ods', 'abc_cis_basic', env, region_name is not None)

        query_locking_sql = f"""
            select /*+ max_execution_time(600000)*/ * from v2_goods_stock_locking where type = 30 and status in (0, 10) 
            {f"and chain_id = '{chain_id}'" if chain_id is not None else ''} 
            {f"and clinic_id = '{clinic_id}'" if clinic_id is not None else ''} 
            {f"and goods_id = '{goods_id}'" if goods_id is not None else ''} 
            {f"and created >= '{created} 00:00:00' and created <= '{created} 23:59:59'" if created is not None else ''}
        """
        logger.info(f"query_locking_sql = {query_locking_sql}")
        goods_lockings = goods_db_cli.fetchall(query_locking_sql)
        if len(goods_lockings) == 0:
            logger.info(f"没有异常解锁的数据")
            continue
        # 开始分析原因
        chain_id_to_locking_list = {}
        for goods_locking in goods_lockings:
            locking_chain_id = goods_locking['chain_id']
            if locking_chain_id in chain_id_to_locking_list:
                chain_id_to_locking_list[locking_chain_id].append(goods_locking)
            else:
                chain_id_to_locking_list[locking_chain_id] = [goods_locking]

        chain_id_to_chain = {}
        if online_env:
            chains = basic_db_cli.fetchall(f"""
                select o.id as chain_id, o.name as name, o.short_name as short_name, ifnull(go.env, 0) as env
                from organ o
                         left join v2_clinic_gray_organ go on o.id = go.chain_id
                where o.node_type = 1
            """)
            chain_id_to_chain.update({chain['chain_id']: chain for chain in chains})
        for locking_chain_id in chain_id_to_locking_list:
            locking_chain = chain_id_to_chain.get(locking_chain_id)
            if locking_chain is None or (online_env and env_name_to_value[online_env] != locking_chain['env']):
                logger.info(f'--------------------skip chain_id = {locking_chain_id}, env = {env_value_to_name[locking_chain["env"]] if locking_chain is not None else "none"}--------------------')
                continue
            # sql_file = open(f'./update_goods_stock_locking_{region}_{locking_chain_id}.sql', "w+")
            backup_sql_file = open(f'./backup_goods_stock_locking_{region}_{locking_chain_id}.sql', "w+")
            # redis_cmd_file = open(f'./clear_goods_locking_redis_{region}_{locking_chain_id}.sh', "w+")
            locking_chain_update_sql = ""
            logger.info(f"--------------------start analysis chain_id = {locking_chain_id}, env = {env_value_to_name[locking_chain['env']] if locking_chain is not None else 'none'}--------------------")
            backup_sql_file.write(f"# --------------------start analysis chain_id = {locking_chain_id}--------------------" + os.linesep)
            # redis_cmd_file.write(f"--------------------start analysis chain_id = {locking_chain_id}--------------------" + os.linesep)
            chain_id_unlocking_list = []
            query_chain_locking_sql = f"""
                select /*+ max_execution_time(600000)*/ * from v2_goods_stock_locking where chain_id = '{locking_chain_id}' and type = 30 and status in (0, 10)
            """
            logger.info(f"query_chain_locking_sql = {query_chain_locking_sql}")
            chain_id_locking_list = goods_db_cli.fetchall(query_chain_locking_sql)
            if chain_id_locking_list is None or len(chain_id_locking_list) == 0:
                logger.info(f"没有异常解锁的数据 chain_id = {locking_chain_id}")
                continue
            locking_order_id_to_locking_list = {}
            for chain_id_locking in chain_id_locking_list:
                locking_order_id = chain_id_locking['locking_order_id']
                if locking_order_id in locking_order_id_to_locking_list:
                    locking_order_id_to_locking_list[locking_order_id].append(chain_id_locking)
                else:
                    locking_order_id_to_locking_list[locking_order_id] = [chain_id_locking]
            locking_order_ids = list(set([locking['locking_order_id'] for locking in chain_id_locking_list]))
            # 查charge
            locking_order_ids2000 = [locking_order_ids[i:i + 2000] for i in range(0, len(locking_order_ids), 2000)]
            charge_sheets = []
            dispensing_sheets = []
            charge_forms = []
            charge_form_items = []
            for locking_order_ids1 in locking_order_ids2000:
                charge_sheets.extend(
                    charge_db_cli.fetchall(f"""
                            select /*+ max_execution_time(600000)*/ * from v2_charge_sheet where chain_id = '{locking_chain_id}' and id in ({','.join([f"'{locking_order_id}'" for locking_order_id in locking_order_ids1])});
                        """)
                )
                charge_forms.extend(
                    charge_db_cli.fetchall(f"""
                            select /*+ max_execution_time(600000)*/ * from v2_charge_form where chain_id = '{locking_chain_id}' and charge_sheet_id in ({','.join([f"'{locking_order_id}'" for locking_order_id in locking_order_ids1])});
                        """)
                )
                charge_form_items.extend(
                    charge_db_cli.fetchall(f"""
                            select /*+ max_execution_time(600000)*/ * from v2_charge_form_item where chain_id = '{locking_chain_id}' and charge_sheet_id in ({','.join([f"'{locking_order_id}'" for locking_order_id in locking_order_ids1])});
                        """)
                )
                # 查dispensing
                dispensing_sheets.extend(
                    dispensing_db_cli.fetchall(f"""
                            select /*+ max_execution_time(600000)*/ * from v2_dispensing_sheet where chain_id = '{locking_chain_id}' and source_sheet_id in ({','.join([f"'{locking_order_id}'" for locking_order_id in locking_order_ids1])});
                        """)
                )
            charge_sheet_id_to_charge_sheet = {charge_sheet['id']: charge_sheet for charge_sheet in charge_sheets}
            charge_form_id_to_charge_form = {charge_form['id']: charge_form for charge_form in charge_forms}
            charge_form_item_id_to_charge_form_item = {charge_form_item['id']: charge_form_item for charge_form_item in charge_form_items}
            charge_sheet_id_to_charge_form_items = {}
            for charge_form_item in charge_form_items:
                charge_sheet_id = charge_form_item['charge_sheet_id']
                if charge_sheet_id in charge_sheet_id_to_charge_form_items:
                    charge_sheet_id_to_charge_form_items[charge_sheet_id].append(charge_form_item)
                else:
                    charge_sheet_id_to_charge_form_items[charge_sheet_id] = [charge_form_item]

            dispensing_sheet_ids = [dispensing_sheet['id'] for dispensing_sheet in dispensing_sheets]
            dispensing_form_items = []
            if len(dispensing_sheet_ids) > 0:
                dispensing_sheet_ids2000 = [dispensing_sheet_ids[i:i + 2000] for i in range(0, len(dispensing_sheet_ids), 2000)]
                for dispensing_sheet_ids1 in dispensing_sheet_ids2000:
                    dispensing_form_items.extend(
                        dispensing_db_cli.fetchall(f"""
                                        select /*+ max_execution_time(600000)*/ * from v2_dispensing_form_item where chain_id = '{locking_chain_id}' and dispensing_sheet_id in ({','.join([f"'{dispensing_sheet_id}'" for dispensing_sheet_id in dispensing_sheet_ids1])});
                                    """)
                    )
            charge_form_item_id_to_dispensing_form_items = {}
            for dispensing_form_item in dispensing_form_items:
                charge_form_item_id = dispensing_form_item['source_form_item_id']
                if charge_form_item_id in charge_form_item_id_to_dispensing_form_items:
                    charge_form_item_id_to_dispensing_form_items[charge_form_item_id].append(dispensing_form_item)
                else:
                    charge_form_item_id_to_dispensing_form_items[charge_form_item_id] = [dispensing_form_item]

            # 一个sheet分析
            for locking_order_id in locking_order_id_to_locking_list:
                locking_order_id_locking_list = locking_order_id_to_locking_list[locking_order_id]
                redis_key = f"""_scgoods_locking:0:{locking_chain_id}:{locking_order_id}"""
                goods_locking_redis_res = goods_redis_cli.client.hgetall(redis_key)
                if goods_locking_redis_res is None or len(goods_locking_redis_res) == 0:
                    # logger.warning(f"goods_locking_redis_res is None or empty {goods_locking_redis_res}")
                    # 查不到redis只能查charge_form_item用goodsId关联了
                    charge_sheet_id_charge_form_items = charge_sheet_id_to_charge_form_items.get(locking_order_id, None)
                    if charge_sheet_id_charge_form_items is None or len(charge_sheet_id_charge_form_items) == 0:
                        logger.error(f"严重错误!!!charge_form_items is None or empty, charge_sheet_id = {locking_order_id}")
                        backup_sql_file.write(f"# 严重错误!!!charge_form_items is None or empty, charge_sheet_id = {locking_order_id}" + os.linesep)
                        backup_sql_file.write(f"""update v2_goods_stock_locking set status = 0 where chain_id = '{locking_chain_id}' and locking_order_id = '{locking_order_id}';""" + os.linesep)
                        if op_type == 1:
                            goods_db_wr_cli.execute(f"""update v2_goods_stock_locking set status = 30 where chain_id = '{locking_chain_id}' and locking_order_id = '{locking_order_id}';\n""")
                            logger.warning(f"goods_locking_redis_res is None or empty, so not need execute redis_cli.delete where chain_id = '{locking_chain_id}' and locking_order_id = '{locking_order_id}'")
                        # redis_cmd_file.write(f"# 严重错误!!!charge_form_items is None or empty, charge_sheet_id = {locking_order_id}" + os.linesep)
                        # redis_cmd_file.write(f"""del {redis_key};""" + os.linesep)
                    else:
                        goods_id_to_charge_sheet_id_charge_form_items = {}
                        for charge_form_item in charge_sheet_id_charge_form_items:
                            product_id = charge_form_item['product_id']
                            if product_id in goods_id_to_charge_sheet_id_charge_form_items:
                                goods_id_to_charge_sheet_id_charge_form_items[product_id].append(charge_form_item)
                            else:
                                goods_id_to_charge_sheet_id_charge_form_items[product_id] = [charge_form_item]

                        for locking_order_id_locking in locking_order_id_locking_list:
                            if force_delete == 1:
                                goods_db_wr_cli.execute(f"""update v2_goods_stock_locking set status = 30, last_modified_by = 'force_delete' where chain_id = '{locking_chain_id}' and locking_order_id = '{locking_order_id}' and goods_id = '{locking_order_id_locking['goods_id']}';\n""")
                                logger.warning(f"""force_delete = 1, goods_locking_redis_res is None or empty, so not need execute redis_cli.delete where chain_id = '{locking_chain_id}' and locking_order_id = '{locking_order_id}' and goods_id = '{locking_order_id_locking['goods_id']}'""")
                                continue
                            if datetime.date(locking_order_id_locking['created']) < date.today() - timedelta(days=90):
                                logger.error(f"严重错误!!!locking exceed 90 days unlock, charge_sheet_id = {locking_order_id}, goods_id = {locking_order_id_locking['goods_id']}")
                                if op_type == 1:
                                    goods_db_wr_cli.execute(f"""update v2_goods_stock_locking set status = 30 where chain_id = '{locking_chain_id}' and locking_order_id = '{locking_order_id}' and goods_id = '{locking_order_id_locking['goods_id']}';\n""")
                                    logger.warning(f"""goods_locking_redis_res is None or empty, so not need execute redis_cli.delete where chain_id = '{locking_chain_id}' and locking_order_id = '{locking_order_id}' and goods_id = '{locking_order_id_locking['goods_id']}'""")
                                continue
                            goods_id_charge_sheet_id_charge_form_items = goods_id_to_charge_sheet_id_charge_form_items.get(locking_order_id_locking['goods_id'])
                            if goods_id_charge_sheet_id_charge_form_items is None or len(goods_id_charge_sheet_id_charge_form_items) == 0:
                                logger.error(f"严重错误!!!charge_form_items is None or empty, charge_sheet_id = {locking_order_id}, goods_id = {locking_order_id_locking['goods_id']}")
                                backup_sql_file.write(f"# 严重错误!!!charge_form_items is None or empty, charge_sheet_id = {locking_order_id}, goods_id = {locking_order_id_locking['goods_id']}" + os.linesep)
                                backup_sql_file.write(f"""update v2_goods_stock_locking set status = 0 where chain_id = '{locking_chain_id}' and locking_order_id = '{locking_order_id}' and goods_id = '{locking_order_id_locking['goods_id']}';""" + os.linesep)
                                if op_type == 1:
                                    goods_db_wr_cli.execute(f"""update v2_goods_stock_locking set status = 30 where chain_id = '{locking_chain_id}' and locking_order_id = '{locking_order_id}' and goods_id = '{locking_order_id_locking['goods_id']}';\n""")
                                    logger.warning(f"""goods_locking_redis_res is None or empty, so not need execute redis_cli.delete where chain_id = '{locking_chain_id}' and locking_order_id = '{locking_order_id}' and goods_id = '{locking_order_id_locking['goods_id']}'""")
                                # redis_cmd_file.write(f"# 严重错误!!!charge_form_items is None or empty, charge_sheet_id = {locking_order_id}, goods_id = {locking_order_id_locking['goods_id']}" + os.linesep)
                                # redis_cmd_file.write(f"""del {redis_key};""" + os.linesep)
                                continue
                            for goods_id_charge_sheet_id_charge_form_item in goods_id_charge_sheet_id_charge_form_items:
                                # 一个goods开在两个form_item
                                analysisLockingReason(locking_order_id_locking,
                                                      goods_id_charge_sheet_id_charge_form_item['charge_form_id'],
                                                      goods_locking_redis_res,
                                                      goods_id_charge_sheet_id_charge_form_item,
                                                      charge_sheet_id_to_charge_sheet,
                                                      charge_form_id_to_charge_form,
                                                      charge_form_item_id_to_dispensing_form_items,
                                                      logger,
                                                      chain_id_unlocking_list,
                                                      backup_sql_file,
                                                      locking_chain_update_sql,
                                                      goods_db_wr_cli,
                                                      goods_redis_cli,
                                                      op_type)
                    continue
                else:
                    lock_id_to_lock_form_id = {}
                    lock_id_to_lock_form_items = {}
                    locking_order_id_locking_form_items = []
                    for hash_key in goods_locking_redis_res:
                        if hash_key == 'baseInfo'.encode():
                            continue
                        hash_value = json.loads(goods_locking_redis_res[hash_key])
                        lock_form_id = hash_value['lockFormId']
                        locking_form_items = hash_value['lockingFormItems'][1]
                        if len(locking_form_items) == 0:
                            # 当前的form被删除
                            continue
                        locking_order_id_locking_form_items.extend(locking_form_items)
                        for locking_form_item in locking_form_items:
                            lock_id = locking_form_item['lockId']
                            lock_id_to_lock_form_id[lock_id] = lock_form_id
                            if lock_id in lock_id_to_lock_form_items:
                                lock_id_to_lock_form_items[lock_id].append(locking_form_item)
                            else:
                                lock_id_to_lock_form_items[lock_id] = [locking_form_item]

                    if len(locking_order_id_locking_form_items) == 0:
                        logger.error(f"严重错误!!!locking_form_items is None or empty, locking_order_id = {locking_order_id}, cmd = hgetall {redis_key}")
                        backup_sql_file.write(f"# 严重错误!!!locking_form_items is None or empty, locking_order_id = {locking_order_id}" + os.linesep)
                        backup_sql_file.write(f"""update v2_goods_stock_locking set status = 0 where chain_id = '{locking_chain_id}' and locking_order_id = '{locking_order_id}';""" + os.linesep)
                        if op_type == 1:
                            goods_db_wr_cli.execute(f"""update v2_goods_stock_locking set status = 30 where chain_id = '{locking_chain_id}' and locking_order_id = '{locking_order_id}';\n""")
                            logger.info(f"execute cmd: del {redis_key}")
                            goods_redis_cli.client.delete(redis_key)
                        # redis_cmd_file.write(f"# 严重错误!!!locking_form_items is None or empty, locking_order_id = {locking_order_id}" + os.linesep)
                        # redis_cmd_file.write(f"""del {redis_key};""" + os.linesep)
                        continue

                    for locking_order_id_locking in locking_order_id_locking_list:
                        if force_delete == 1:
                            goods_db_wr_cli.execute(f"""update v2_goods_stock_locking set status = 30, last_modified_by = 'force_delete' where chain_id = '{locking_chain_id}' and locking_order_id = '{locking_order_id}' and lock_id = {locking_order_id_locking['lock_id']};\n""")
                            lock_form_id = lock_id_to_lock_form_id.get(locking_order_id_locking['lock_id'])
                            if lock_form_id is not None:
                                hash_key = f'F_{lock_form_id}'.encode('utf-8')
                                logger.info(f"execute cmd: hdel {redis_key} {hash_key}")
                                goods_redis_cli.client.hdel(redis_key, hash_key)
                            continue
                        if datetime.date(locking_order_id_locking['created']) < date.today() - timedelta(days=90):
                            logger.error(f"严重错误!!!locking exceed 90 days unlock, locking_order_id = {locking_order_id}, lock_id = {locking_order_id_locking['lock_id']}")
                            if op_type == 1:
                                goods_db_wr_cli.execute(f"""update v2_goods_stock_locking set status = 30 where chain_id = '{locking_chain_id}' and locking_order_id = '{locking_order_id}' and lock_id = {locking_order_id_locking['lock_id']};\n""")
                                lock_form_id = lock_id_to_lock_form_id.get(locking_order_id_locking['lock_id'])
                                if lock_form_id is not None:
                                    hash_key = f'F_{lock_form_id}'.encode('utf-8')
                                    logger.info(f"execute cmd: hdel {redis_key} {hash_key}")
                                    goods_redis_cli.client.hdel(redis_key, hash_key)
                            continue
                        lock_id_lock_form_items = lock_id_to_lock_form_items.get(locking_order_id_locking['lock_id'])
                        if lock_id_lock_form_items is None or len(lock_id_lock_form_items) == 0:
                            logger.error(f"严重错误!!!lock_form_items is None or empty, locking_order_id = {locking_order_id}, lock_id = {locking_order_id_locking['lock_id']}")
                            backup_sql_file.write(f"# 严重错误!!!lock_form_items is None or empty, locking_order_id = {locking_order_id}, lock_id = {locking_order_id_locking['lock_id']}" + os.linesep)
                            backup_sql_file.write(f"""update v2_goods_stock_locking set status = 0 where chain_id = '{locking_chain_id}' and locking_order_id = '{locking_order_id}' and lock_id = {locking_order_id_locking['lock_id']};""" + os.linesep)
                            if op_type == 1:
                                goods_db_wr_cli.execute(f"""update v2_goods_stock_locking set status = 30 where chain_id = '{locking_chain_id}' and locking_order_id = '{locking_order_id}' and lock_id = {locking_order_id_locking['lock_id']};\n""")
                                lock_form_id = lock_id_to_lock_form_id.get(locking_order_id_locking['lock_id'])
                                if lock_form_id is not None:
                                    hash_key = f'F_{lock_form_id}'.encode('utf-8')
                                    logger.info(f"execute cmd: hdel {redis_key} {hash_key}")
                                    goods_redis_cli.client.hdel(redis_key, hash_key)
                            # redis_cmd_file.write(f"# 严重错误!!!lock_form_items is None or empty, locking_order_id = {locking_order_id}, lock_id = {locking_order_id_locking['lock_id']}" + os.linesep)
                            # redis_cmd_file.write(f"""del {redis_key};""" + os.linesep)
                            continue
                        for lock_id_lock_form_item in lock_id_lock_form_items:
                            analysisLockingReason(locking_order_id_locking,
                                                  lock_id_to_lock_form_id[locking_order_id_locking['lock_id']],
                                                  goods_locking_redis_res,
                                                  charge_form_item_id_to_charge_form_item.get(lock_id_lock_form_item['lockFormItemId']),
                                                  charge_sheet_id_to_charge_sheet,
                                                  charge_form_id_to_charge_form,
                                                  charge_form_item_id_to_dispensing_form_items,
                                                  logger,
                                                  chain_id_unlocking_list,
                                                  backup_sql_file,
                                                  locking_chain_update_sql,
                                                  goods_db_wr_cli,
                                                  goods_redis_cli,
                                                  op_type)


            if len(chain_id_unlocking_list) > 0:
                logger.error(f"严重错误，操作过发药单仍然未解锁!!!chain_id = '{locking_chain_id}'\n{json.dumps(chain_id_unlocking_list, ensure_ascii=False)}")
            backup_sql_file.write(f"# --------------------end analysis chain_id = {locking_chain_id}--------------------" + os.linesep)
            # redis_cmd_file.write(f"# --------------------end analysis chain_id = {locking_chain_id}--------------------" + os.linesep)
            # redis_cmd_file.close()
            backup_sql_file.close()

            # if op_type == 1:
            #     try:
            #         redisCmd = '''cat {fileName} | redis-cli -h {redisHostUrl} -n 78 -p 6379'''.format(fileName=redis_cmd_file.name, redisHostUrl=redisHost(region, 'abcRedis', env))
            #         logger.info(f"execute del goods_locking redis_cmd: {redisCmd}")
            #         os.system(redisCmd)
            #     except Exception as e:
            #         logger.error(f"execute redis cmd error: {e}")

            logger.info(f"--------------------end analysis chain_id = {locking_chain_id}, env = {env_value_to_name[locking_chain['env']] if locking_chain is not None else 'none'}--------------------")
        logger.info(f'--------------------锁库异常监控结束 {region}--------------------')

def analysisLockingReason(locking,
                          lock_form_id,
                          goods_locking_redis_res,
                          charge_form_item,
                          charge_sheet_id_to_charge,
                          charge_form_id_to_charge_form,
                          charge_form_item_id_to_dispensing_form_items,
                          logger,
                          unlocking_list,
                          backup_sql_file,
                          locking_chain_update_sql,
                          goods_db_wr_cli,
                          goods_redis_cli,
                          op_type):
    # 分析原因
    locking_order_id = locking['locking_order_id']
    redis_key = f"""_scgoods_locking:0:{locking['chain_id']}:{locking_order_id}"""
    if charge_form_item is None or charge_form_item['is_deleted'] == 1:
        if charge_form_item is None:
            logger.error(f"严重错误!!!charge_form_item is None, locking_order_id = {locking_order_id}")
        if charge_form_item is not None and charge_form_item['is_deleted'] == 1:
            logger.error(f"严重错误!!!charge_form_item is deleted, locking_order_id = {locking_order_id}")
        # backup_sql_file.write("# 严重错误!!!charge_form_item is None or charge_form_item['is_deleted'] == 1" + os.linesep)
        # backup_sql_file.write(f"""update v2_goods_stock_locking set status = 0 where lock_id = '{locking['lock_id']}';""" + os.linesep)
        if op_type == 1:
            goods_db_wr_cli.execute(f"""update v2_goods_stock_locking set status = 30 where lock_id = '{locking['lock_id']}';\n""")
            update_or_delete_goods_locking_redis(locking['lock_id'], lock_form_id, redis_key, goods_locking_redis_res, goods_redis_cli, logger)
        # redis_cmd_file.write("# 严重错误!!!charge_form_item is None or charge_form_item['is_deleted'] == 1" + os.linesep)
        # redis_cmd_file.write(f"""del {redis_key};""" + os.linesep)
        return
    charge_form = charge_form_id_to_charge_form[charge_form_item['charge_form_id']]
    charge_sheet = charge_sheet_id_to_charge[charge_form_item['charge_sheet_id']]
    if charge_form is None or charge_form['is_deleted'] == 1:
        if charge_form is None:
            logger.error(f"严重错误!!!charge_form is None, locking_order_id = {locking_order_id}")
        if charge_form is not None and charge_form['is_deleted'] == 1:
            logger.error(f"严重错误!!!charge_form is deleted, locking_order_id = {locking_order_id}")
        # backup_sql_file.write("# 严重错误!!!charge_form is None or charge_form['is_deleted'] == 1" + os.linesep)
        # backup_sql_file.write(f"""update v2_goods_stock_locking set status = 0 where lock_id = '{locking['lock_id']}';""" + os.linesep)
        if op_type == 1:
            goods_db_wr_cli.execute(f"""update v2_goods_stock_locking set status = 30 where lock_id = '{locking['lock_id']}';\n""")
            update_or_delete_goods_locking_redis(locking['lock_id'], lock_form_id, redis_key, goods_locking_redis_res, goods_redis_cli, logger)
        # redis_cmd_file.write("# 严重错误!!!charge_form_item is None or charge_form_item['is_deleted'] == 1" + os.linesep)
        # redis_cmd_file.write(f"""del {redis_key};""" + os.linesep)
        return
    if charge_sheet is None or charge_sheet['is_deleted'] == 1 or charge_sheet['is_closed'] == 1:
        if charge_sheet is None:
            logger.error(f"严重错误!!!charge_sheet is None, locking_order_id = {locking_order_id}")
        if charge_sheet is not None and charge_sheet['is_deleted'] == 1:
            logger.error(f"严重错误!!!charge_sheet is deleted, locking_order_id = {locking_order_id}")
        if charge_sheet is not None and charge_sheet['is_closed'] == 1:
            logger.error(f"严重错误!!!charge_sheet is closed, locking_order_id = {locking_order_id}")
        # backup_sql_file.write("# 严重错误!!!charge_sheet is None or charge_sheet['is_deleted'] == 1 or charge_sheet['is_closed'] == 1" + os.linesep)
        # backup_sql_file.write(f"""update v2_goods_stock_locking set status = 0 where lock_id = '{locking['lock_id']}';""" + os.linesep)
        if op_type == 1:
            goods_db_wr_cli.execute(f"""update v2_goods_stock_locking set status = 30 where lock_id = '{locking['lock_id']}';\n""")
            update_or_delete_goods_locking_redis(locking['lock_id'], lock_form_id, redis_key, goods_locking_redis_res, goods_redis_cli, logger)
        # redis_cmd_file.write("# 严重错误!!!charge_form_item is None or charge_form_item['is_deleted'] == 1" + os.linesep)
        # redis_cmd_file.write(f"""del {redis_key};""" + os.linesep)
        return
    if charge_sheet['status'] == 4:
        logger.error(f"严重错误!!!charge_sheet is refund, locking_order_id = {locking_order_id}")
        if op_type == 1:
            goods_db_wr_cli.execute(f"""update v2_goods_stock_locking set status = 30 where lock_id = '{locking['lock_id']}';\n""")
            update_or_delete_goods_locking_redis(locking['lock_id'], lock_form_id, redis_key, goods_locking_redis_res, goods_redis_cli, logger)
        return
    if charge_sheet['status'] != 2 and charge_sheet['status'] != 3:
        # logger.warning("只有已收费和部分退费才有发药单")
        return

    charge_form_item_id_dispensing_form_items = charge_form_item_id_to_dispensing_form_items.get(charge_form_item['id'],None)
    if charge_form_item_id_dispensing_form_items is None or len(charge_form_item_id_dispensing_form_items) == 0:
        if charge_form_item['status'] == 0:
            # logger.warning(f"dispensing_form_items is None or empty, source_form_item_id = {charge_form_item['id']}, charge_form_item['status'] = {charge_form_item['status']}")
            return
        elif charge_form_item['status'] == 1:
            logger.error(f"严重错误!!!dispensing_form_items is None or empty, source_form_item_id = {charge_form_item['id']}, charge_form_item['status'] = {charge_form_item['status']}, locking_order_id = {locking_order_id}")
            # backup_sql_file.write(f"# 严重错误!!!dispensing_form_items is None or empty, source_form_item_id = {charge_form_item['id']}, charge_form_item['status'] = {charge_form_item['status']}" + os.linesep)
            # backup_sql_file.write(f"""update v2_goods_stock_locking set status = 0 where lock_id = '{locking['lock_id']}';""" + os.linesep)
            if op_type == 1:
                goods_db_wr_cli.execute(f"""update v2_goods_stock_locking set status = 30 where lock_id = '{locking['lock_id']}'\n;""")
                update_or_delete_goods_locking_redis(locking['lock_id'], lock_form_id, redis_key, goods_locking_redis_res, goods_redis_cli, logger)
            # redis_cmd_file.write(f"# 严重错误!!!dispensing_form_items is None or empty, source_form_item_id = {charge_form_item['id']}" + os.linesep)
            # redis_cmd_file.write(f"""del {redis_key};""" + os.linesep)
            return
        elif charge_form_item['status'] == 2 or charge_form_item['status'] == 3:
            # 退单
            logger.error(f"严重错误!!!charge_form_item_id = {charge_form_item['id']}, charge_form_item['status'] = {charge_form_item['status']}, locking_order_id = {locking_order_id}")
            # backup_sql_file.write(f"# 严重错误!!!charge_form_item_id = {charge_form_item['id']}, charge_form_item['status'] = {charge_form_item['status']}" + os.linesep)
            # backup_sql_file.write(f"""update v2_goods_stock_locking set status = 0 where lock_id = '{locking['lock_id']}';""" + os.linesep)
            if op_type == 1:
                goods_db_wr_cli.execute(f"""update v2_goods_stock_locking set status = 30 where lock_id = '{locking['lock_id']}'\n;""")
                update_or_delete_goods_locking_redis(locking['lock_id'], lock_form_id, redis_key, goods_locking_redis_res, goods_redis_cli, logger)
            # redis_cmd_file.write(f"""del {redis_key};""" + os.linesep)
            return
    else:
        for charge_form_item_id_dispensing_form_item in charge_form_item_id_dispensing_form_items:
            if charge_form_item_id_dispensing_form_item['is_deleted'] == 1:
                logger.error(f"严重错误!!!dispensing_form_item['is_deleted'] == 1, locking_order_id = {locking_order_id}")
                # backup_sql_file.write("# 严重错误!!!dispensing_form_item['is_deleted'] == 1" + os.linesep)
                # backup_sql_file.write(f"""update v2_goods_stock_locking set status = 0 where lock_id = '{locking['lock_id']}';""" + os.linesep)
                if op_type == 1:
                    goods_db_wr_cli.execute(f"""update v2_goods_stock_locking set status = 30 where lock_id = '{locking['lock_id']}';\n""")
                    update_or_delete_goods_locking_redis(locking['lock_id'], lock_form_id, redis_key, goods_locking_redis_res, goods_redis_cli, logger)
                # redis_cmd_file.write("# 严重错误!!!dispensing_form_item['is_deleted'] == 1" + os.linesep)
                # redis_cmd_file.write(f"""del {redis_key};""" + os.linesep)
                continue
            if charge_form_item_id_dispensing_form_item['status'] == 0:
                continue
            elif charge_form_item_id_dispensing_form_item['status'] == 3:
                logger.warning(f"发药单关闭不解锁 dispensing_form_item_id = {charge_form_item_id_dispensing_form_item['id']}")
                continue
            elif charge_form_item_id_dispensing_form_item['status'] == 4:
                # 部分发药
                remaining_unit_count = charge_form_item_id_dispensing_form_item['remaining_unit_count']
                remaining_dose_count = charge_form_item_id_dispensing_form_item['remaining_dose_count']
                lock_left_total_piece_count = locking['lock_left_total_piece_count']
                locking_piece_num = locking['locking_piece_num']
                is_dismounting = charge_form_item_id_dispensing_form_item['is_dismounting']
                if lock_left_total_piece_count == remaining_dose_count * remaining_unit_count * (locking_piece_num if is_dismounting == 0 else 1):
                    continue
                logger.error(f"严重错误!!!部分发药异常 待解锁数量!=待发药数量, locking_order_id = {locking_order_id}")
                # backup_sql_file.write("# 严重错误!!!部分发药异常 待解锁数量!=待发药数量" + os.linesep)
                # redis_cmd_file.write("# 严重错误!!!部分发药异常 待解锁数量!=待发药数量" + os.linesep)
            else:
                logger.error(f"严重错误!!!操作过发药单仍然未解锁, locking_order_id = {locking_order_id}")
                # backup_sql_file.write("# 严重错误!!!操作过发药单仍然未解锁" + os.linesep)
                # redis_cmd_file.write("# 严重错误!!!操作过发药单仍然未解锁" + os.linesep)

            # backup_sql_file.write(f"""update v2_goods_stock_locking set status = 0 where id = {str(locking['id'])};""" + os.linesep)
            if op_type == 1:
                goods_db_wr_cli.execute(f"""update v2_goods_stock_locking set status = 30 where id = {str(locking['id'])};\n""")
                update_or_delete_goods_locking_redis(locking['id'], lock_form_id, redis_key, goods_locking_redis_res, goods_redis_cli, logger)
            # redis_cmd_file.write(f"""del {redis_key};""" + os.linesep)
            unlocking_list.append({
                "id": str(locking['id']),
                "batch_id": str(locking['stock_id']),
                "clinic_id": str(locking['clinic_id']),
                "locking_piece_num": str(locking['locking_piece_num']),
                "locking_piece_count": str(locking['locking_piece_count']),
                "locking_package_count": str(locking['locking_package_count']),
                "lock_left_total_piece_count": str(locking['lock_left_total_piece_count']),
                "locking_total_piece_count": str(locking['locking_total_piece_count']),
                "lock_id": str(locking['lock_id']),
                "lock_status": str(locking['status']),
                "lock_goods_id": str(locking['goods_id']),
                "lock_pharmacy_type": str(locking['pharmacy_type']),
                "lock_pharmacy_no": str(locking['pharmacy_type']),
                "lock_created": str(locking['created']),
                "lock_last_modified": str(locking['last_modified']),
                "charge_form_item_id": str(charge_form_item['id']),
                "charge_form_item_status": str(charge_form_item['status']),
                "charge_form_item_goods_id": str(charge_form_item['product_id']),
                "charge_form_item_pharmacy_type": str(charge_form_item['pharmacy_type']),
                "charge_form_item_pharmacy_no": str(charge_form_item['pharmacy_no']),
                "charge_form_item_unit_count": str(charge_form_item['unit_count']),
                "charge_form_item_dose_count": str(charge_form_item['dose_count']),
                "charge_form_item_is_dismounting": str(charge_form_item['is_dismounting']),
                "charge_form_item_last_modified": str(charge_form_item['last_modified']),
                "dispensing_form_item_id": str(charge_form_item_id_dispensing_form_item['id']),
                "dispensing_form_item_status": str(charge_form_item_id_dispensing_form_item['status']),
                "dispensing_form_item_goods_id": str(charge_form_item_id_dispensing_form_item['product_id']),
                "dispensing_form_item_pharmacy_type": str(charge_form_item_id_dispensing_form_item['pharmacy_type']),
                "dispensing_form_item_pharmacy_no": str(charge_form_item_id_dispensing_form_item['pharmacy_no']),
                "dispensing_form_item_unit_count": str(charge_form_item_id_dispensing_form_item['unit_count']),
                "dispensing_form_item_dose_count": str(charge_form_item_id_dispensing_form_item['dose_count']),
                "dispensing_form_item_is_dismounting": str(charge_form_item_id_dispensing_form_item['is_dismounting']),
                "dispensing_form_item_remaining_unit_count": str(charge_form_item_id_dispensing_form_item['remaining_unit_count']),
                "dispensing_form_item_remaining_dose_count": str(charge_form_item_id_dispensing_form_item['remaining_dose_count']),
                "dispensing_form_item_last_modified": str(charge_form_item_id_dispensing_form_item['last_modified']),
                "select_sql": f"select * from v2_goods_stock_locking where id = {str(locking['id'])};",
                "update_sql": f"update v2_goods_stock_locking set status = 30 where id = {str(locking['id'])};",
                "del_redis_command": f"del _scgoods_locking:0:{locking['chain_id']}:{locking['locking_order_id']}"
            })

def update_or_delete_goods_locking_redis(lock_id, lock_form_id, redis_key, goods_locking_redis_res, goods_redis_cli, logger):
    if len(goods_locking_redis_res) == 0:
        logger.warning(f"""goods_locking_redis_res is None or empty, so not need execute redis_cli.delete where lock_id = '{lock_id}'""")
        return
    hash_key = f'F_{lock_form_id}'.encode('utf-8')
    hash_value = goods_locking_redis_res.get(hash_key)
    if hash_value is None:
        logger.warning(f"""hash_value is None or empty, so not need execute redis_cli.delete where lock_id = '{lock_id}'""")
        return
    lock_form_value = json.loads(hash_value.decode('utf-8'))
    locking_form_items = lock_form_value.get('lockingFormItems')
    if locking_form_items is None:
        logger.info(f"execute cmd: hdel {redis_key} {hash_key}")
        goods_redis_cli.client.hdel(redis_key, hash_key)
        return
    locking_form_items[1] = list(filter(lambda e: e['lockId'] != lock_id, locking_form_items[1]))
    if len(locking_form_items[1]) == 0:
        logger.info(f"execute cmd: hdel {redis_key} {hash_key}")
        goods_redis_cli.client.hdel(redis_key, hash_key)
        return
    lock_form_value['lockingFormItems'] = locking_form_items
    hash_value_for_update = json.dumps(lock_form_value, ensure_ascii=False).encode('utf-8')
    logger.info(f"execute cmd: hset {redis_key} {hash_key} {hash_value_for_update}")
    goods_redis_cli.client.hset(redis_key, hash_key, hash_value_for_update)


def main():
    raise Exception('请使用新的脚本')
    parser = argparse.ArgumentParser()
    parser.add_argument('--region-id', help='分区id', required=False)
    parser.add_argument('--chain-id', help='连锁id', required=False)
    parser.add_argument('--clinic-id', help='门店id', required=False)
    parser.add_argument('--goods-id', help='goodsId', required=False)
    parser.add_argument('--date', help='日期', required=False)
    parser.add_argument('--op-type', type=int, help='操作类型 0-查询 1-清理')
    parser.add_argument('--force-delete', type=int, help='强制删除')
    parser.add_argument('--env', help='环境 dev/test/prod')
    parser.add_argument('--online-env', help='线上环境 dev/test/pre/gray/prod')
    args = parser.parse_args()
    if (not args.op_type and args.op_type != 0) or not args.env:
        parser.print_help()
        sys.exit(-1)

    run(args.chain_id, args.clinic_id, args.goods_id, args.date, args.op_type, args.force_delete, args.env, args.online_env, region_name(args.region_id))
    # run('ffffffff000000003494b27a31400000', None, 'prod')
    # run(None, None, None, None, 0, 'dev', 'ShangHai')


if __name__ == '__main__':
    main()
