#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""
监控 dz-data-sync 的同步进度
"""
import argparse
import os
import sys
import time
from datetime import datetime

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.config.region import region_id_name_map
from multizone.db import DBClient
from multizone.rpc import AbcCisMonitorClient as monitor
from scripts.common.utils.lists import ListUtils

try:
    from multizone.log import AliyunLogClient
except Exception as e:
    pass


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--env', help='环境 dev/test/prod')
    parser.add_argument('--region-id', help='分区ID')
    args, argv = parser.parse_known_args()
    if not args.env:
        parser.print_help()
        sys.exit(-1)

    region_id = args.region_id
    region_name = region_id_name_map.get(region_id)
    if not region_name:
        return

    if args.env == 'prod':
        database = 'abc_cis_dz_data_sync'
    else:
        database = 'abc_cis_basic'
    data_sync_client = DBClient(region_name, 'ob', database, args.env, True)
    all_offset_storages = data_sync_client.fetchall("""
        select env, JSON_EXTRACT(offset_val, '$.ts_sec') as ts
        from (select *, ROW_NUMBER() OVER (PARTITION BY env ORDER BY record_insert_ts desc) AS n
              from data_sync_dz_offset_storage
              where region = {region} and env != 'local' AND record_insert_ts >= DATE_ADD(now(), interval -3 minute)) t
        where t.n <= 2;
    """.format(region=region_id))

    if not all_offset_storages:
        return

    # 按照 env 进行分组，分别找到每个 env 的最新的两条记录
    # 如果最新的一条记录的时间戳大于 10 分钟，则发送告警
    # 如果最新的一条记录的时间戳小于 10 分钟，但是第二新的记录的时间戳大于 10 分钟，则发送恢复通知
    # 否则不发送任何通知
    env_to_offset_storages = ListUtils.group_by(all_offset_storages, lambda e: e['env'])
    for env, offset_storages in env_to_offset_storages.items():
        if len(offset_storages) == 1:
            offset_storage = offset_storages[0]
            ts = offset_storage['ts']
            current = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            last_delay = int(time.time()) - int(ts)
            if last_delay - 600 > 0:
                monitor.sendServiceAlertMessage(f'{region_name} 数据同步延迟告警', f'\n环境：{env}\n时间：{current} \n延迟：{last_delay} 秒\n', region_name, args.env)
        elif len(offset_storages) == 2:
            # 按照时间戳排序，降序
            offset_storages = sorted(offset_storages, key=lambda e: e['ts'], reverse=True)
            offset_storage = offset_storages[0]
            ts = offset_storage['ts']
            current = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            last_delay = int(time.time()) - int(ts)
            # 如果最新的一条记录的时间戳大于 10 分钟，则发送告警
            if last_delay - 600 > 0:
                monitor.sendServiceAlertMessage(f'{region_name} 数据同步延迟告警', f'\n环境：{env}\n时间：{current} \n延迟：{last_delay} 秒\n', region_name, args.env)
            else:
                # 如果最新的一条记录的时间戳小于 10 分钟，但是第二新的记录的时间戳大于 10 分钟，则发送恢复通知
                offset_storage = offset_storages[1]
                ts = offset_storage['ts']
                current = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                delay = int(time.time()) - int(ts)
                if delay - 600 > 0:
                    monitor.sendServiceAlertMessage(f'{region_name} 数据同步延迟恢复', f'\n环境：{env}\n时间：{current} \n延迟：{last_delay} 秒\n', region_name, args.env)

    # for offset_storage in offset_storages:
    #     env = offset_storage['env']
    #     ts = offset_storage['ts']  # 单位（秒）
    #
    #     # 判断是否超过 10 分钟
    #     # 获取当前的时间 yyyy-MM-dd HH:mm:ss 格式
    #     current = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    #     delay = int(time.time()) - int(ts)
    #     if delay - 600 > 0:
    #         monitor.sendServiceAlertMessage(f'{region_name} 数据同步延迟告警', f'\n环境：{env}\n时间：{current} \n延迟：{delay} 秒\n', region_name, args.env)


if __name__ == '__main__':
    main()
