# -*- coding: utf-8 -*-
"""
@name: flush_supplier_name_py.py
@author: <PERSON><PERSON><PERSON>
@email: <EMAIL>
@date: 2024-06-21 12:00:25
"""
from multizone.db import DBClient
from pypinyin import pinyin, lazy_pinyin, Style


def main():
    ods_client = DBClient('ShangHai', 'ods', 'abc_cis_goods', 'prod', True)
    offset = 0
    limit = 1000
    while True:
        suppliers = get_supplier(ods_client, offset, limit)
        if not suppliers:
            break
        for supplier in suppliers:
            supplier_id = supplier['id']
            name = supplier['name']
            if supplier_id is None or name is None or len(name.strip()) == 0:
                continue
            first_py = ''.join([word[0].upper() for word in lazy_pinyin(name)])
            full_py = ''.join(lazy_pinyin(name)).upper()
            name_py = f'{first_py}|{full_py}'
            if len(name_py) > 256:
                name_py = name_py[:256]
            name_py = name_py.replace("'", "''")
            # print(supplier['id'], supplier['name'], supplier['name_py_first'])
            update_sql = """
                update v2_goods_supplier set name_py_first = '{name_py}' where id = '{id}'
            """.format(name_py=name_py, id=supplier_id)
            # print(update_sql)
            ods_client.execute(update_sql)

        offset += limit
    pass


def get_supplier(client, offset, limit):
    return client.fetchall("""
          select *
          from v2_goods_supplier
          where is_deleted = 0
          limit {offset}, {limit}
    """.format(offset=offset, limit=limit))


if __name__ == '__main__':
    main()