#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""
短信签名审核通知
"""
import os

import requests

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient

default_id = '00000000000000000000000000000000'


def main():
    sh_patient_db_client = DBClient('ShangHai', 'ods', 'abc_cis_message', 'prod', True)
    hz_patient_db_client = DBClient('HangZhou', 'ods', 'abc_cis_message', 'prod', False)
    # 查询上海和杭州待处理的短信签名审核通知
    wait_approval_notifies = []
    sh_wait_approval_notifies = sh_patient_db_client.fetchall('''
        select chain_id,
               name,
               created
        from v2_message_sms_sign_record
        where approve_status = 2
          and status = 1
    ''')
    wait_approval_notifies.extend(sh_wait_approval_notifies)

    hz_wait_approval_notifies = hz_patient_db_client.fetchall('''
        select chain_id,
               name,
               created
        from v2_message_sms_sign_record
        where approve_status = 2
          and status = 1
        order by created
    ''')
    wait_approval_notifies.extend(hz_wait_approval_notifies)

    if not wait_approval_notifies:
        return

    # 发送企业微信通知
    wait_approval_notifies_message = ""
    for i, wait_approval_notify in enumerate(wait_approval_notifies):
        # 如果是第一条，不加换行
        if i != 0:
            wait_approval_notifies_message += "\n"
        # 格式化时间类型为 yyyy-MM-dd HH:mm:ss
        created = wait_approval_notify['created'].strftime('%Y-%m-%d %H:%M:%S')
        wait_approval_notifies_message += f"> 连锁ID: <font color=\"comment\">{wait_approval_notify['chain_id']}</font>\n"
        wait_approval_notifies_message += f"> 申请签名: <font color=\"warning\">{wait_approval_notify['name']}</font>\n"
        wait_approval_notifies_message += f"> 申请时间: <font color=\"warning\">{created}</font>\n"

    requests.post(
        url='https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7efedba6-72e7-41b6-be6a-e88482654a6e',
        json={
            "msgtype": "markdown",
            "markdown": {
                "content": f"""[待处理短信签名审核]<font color=\"warning\"> {len(wait_approval_notifies)}条</font>\n{wait_approval_notifies_message}"""
            }
        })


if __name__ == '__main__':
    main()
