#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""


"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys
import argparse

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from region_data_sync import execute_data_sync

default_id = '00000000000000000000000000000000'


#库存数据初始化
def initGoodsData( env):
    execute_data_sync(master_region='Master', master_cluster='abc_cis_stock', master_db='abc_cis_goods', master_table='v2_goods_alias',
                      slave_region_list = ["HangZhou"],
                      slave_cluster='abc_cis_stock', slave_db='abc_cis_goods',
                      slave_table='v2_goods_alias',
                      env=env, whereSql= None, custom_update_dict= None, last_modified_column= None)
    execute_data_sync(master_region='Master', master_cluster='abc_cis_stock', master_db='abc_cis_goods', master_table='v2_goods_sys_type',
                      slave_region_list = ["HangZhou"],
                      slave_cluster='abc_cis_stock', slave_db='abc_cis_goods',
                      slave_table='v2_goods_sys_type',
                      env=env, whereSql= None, custom_update_dict= None, last_modified_column= None)
    execute_data_sync(master_region='Master', master_cluster='abc_cis_stock', master_db='abc_cis_goods', master_table='domain_medicine',
                      slave_region_list = ["HangZhou"],
                      slave_cluster='abc_cis_stock', slave_db='abc_cis_goods',
                      slave_table='domain_medicine',
                      env=env, whereSql= None, custom_update_dict= None, last_modified_column= None)
def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--env', help='环境 dev/test/prod')
    args = parser.parse_args()
    if not args.env:
        parser.print_help()
        sys.exit(-1)

    #模版门店ID可以查看 scGoods的配置文件
    if args.env == 'prod':
        initGoodsData( args.env)
    elif args.env == 'test':
        initGoodsData( args.env)



if __name__ == '__main__':
    main()
