"""
ES 索引对比
"""
import argparse
import concurrent.futures
import os
import sys
import threading
from datetime import datetime, timedelta
from queue import Queue

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.es import ESClient
from scripts.common.utils.lists import ListUtils
from multizone.db import DBClient

logger = None
try:
    from multizone.log import AliyunLogClient
except Exception as e:
    pass

# 创建一个任务队列
task_queue = Queue()

# 创建一个线程池
worker_count = 10
executor = concurrent.futures.ThreadPoolExecutor(max_workers=worker_count)

# 创建一个线程局部数据对象
thread_local_data = threading.local()


class ClientHolder:
    ob_client = None
    env = None
    region_name = None
    es_client = None
    local = None

    def __init__(self, env, region_name, local, idx):
        self.env = env
        self.region_name = region_name
        self.local = local
        no_tunnel = local != 'true'
        self.ob_client = DBClient(self.region_name, 'ob', 'abc_cis_charge', self.env, no_tunnel)
        self.es_client = ESClient(self.region_name, self.env, True, True)


def get_client_holder(env, region_name, local, idx):
    # 检查线程局部数据中是否已经有一个数据库连接
    client_holder = getattr(thread_local_data, 'db_client', None)
    # 如果没有，创建一个新的连接并存储在线程局部数据中
    if client_holder is None:
        client_holder = ClientHolder(env, region_name, local, idx)
        setattr(thread_local_data, 'db_client', client_holder)
    # 返回数据库连接
    return client_holder


class Scheduler:
    ob_client = None
    env = None
    region_name = None
    local = None
    target_index_name = None

    def __init__(self, env, region_name, local, target_index_name):
        self.env = env
        self.region_name = region_name
        self.local = local
        self.target_index_name = target_index_name if target_index_name else 'v3-cis-cdss-charge-sheet_2024-10-23'
        no_tunnel = local != 'true'
        self.ob_client = DBClient(self.region_name, 'ob', 'abc_cis_charge', self.env, no_tunnel)

    def run(self, begin_date, end_date):
        for i in range(worker_count):
            executor.submit(worker, self.env, self.region_name, self.local, self.target_index_name, i)

        begin_date = datetime.strptime(begin_date, '%Y-%m-%d %H:%M:%S')
        end_date = datetime.strptime(end_date, '%Y-%m-%d %H:%M:%S')

        while begin_date < end_date:
            offset = 0
            size = 200
            current_begin_date = begin_date
            current_end_date = current_begin_date + timedelta(minutes=30)
            while True:
                # 先分页遍历患者
                registration_form_items = self.ob_client.fetchall("""
                    select id
                    from v2_charge_sheet
                    where created between '{begin_date}' and '{end_date}'
                    LIMIT {offset}, {size};
                """.format(offset=offset, size=size, begin_date=current_begin_date, end_date=current_end_date))
                if not registration_form_items:
                    break
                offset += size

                task_queue.put(([e['id'] for e in registration_form_items]))
            begin_date = current_end_date

        task_queue.join()

        # 停止工作线程
        for i in range(worker_count):
            task_queue.put(None)


def compare_between_es_registration_index(index1_name, index2_name, ids, es_client):
    index1_search_result = es_client.search(index1_name, {
        "query": {
            "bool": {
                "filter": [
                    {
                        "terms": {
                            "id": ids
                        }
                    }
                ]
            }
        },
        "size": len(ids)
    })

    index2_search_result = es_client.search(index2_name, {
        "query": {
            "bool": {
                "filter": [
                    {
                        "terms": {
                            "id": ids
                        }
                    }
                ]
            }
        },
        "size": len(ids)
    })

    index1_hit_datas = index1_search_result['hits']['hits']
    index2_hit_datas = index2_search_result['hits']['hits']

    index1_hit_datas = ListUtils.to_map(index1_hit_datas, lambda e: e['_source']['id'])
    index2_hit_datas = ListUtils.to_map(index2_hit_datas, lambda e: e['_source']['id'])

    success_count = 0
    for id in ids:
        index1_data = index1_hit_datas.get(id)
        index2_data = index2_hit_datas.get(id)
        hasError = False
        if index1_data is None:
            error(f"{index1_name} 索引中没有 id: {id}")
            hasError = True
        if index2_data is None:
            error(f"{index2_name} 索引中没有 id: {id}")
            hasError = True
        index1_data = index1_data['_source']
        index2_data = index2_data['_source']

        # 字段一一对比
        for key in index1_data.keys():
            if index1_data[key] != index2_data[key]:
                error(f"id: {id}, {key} 字段不一致, {index1_name}: {index1_data[key]}, {index2_name}: {index2_data[key]}")
                hasError = True

        if not hasError:
            success_count += 1

    info(f"对比完成, 成功数: {success_count}, 总数: {len(ids)}")


def worker(env, region_name, local, target_index_name, idx):
    info(f'worker start {env} {region_name} {local}')
    client_holder = get_client_holder(env, region_name, local, idx)
    while True:
        task = task_queue.get()
        if task is None:
            break
        ids = task
        if not ids:
            task_queue.task_done()
            break

        # 对比索引
        try:
            compare_between_es_registration_index("v3-cis-cdss-charge-sheet", target_index_name, ids, client_holder.es_client)
        except Exception as e:
            print(e)

        task_queue.task_done()

    client_holder.f.flush()
    client_holder.f.close()


def info(msg, *args, **kwargs):
    if logger:
        logger.info(msg, *args, **kwargs)
    else:
        print(msg)


def error(msg, *args, **kwargs):
    if logger:
        logger.error(msg, *args, **kwargs)
    else:
        print(msg)


def warn(msg, *args, **kwargs):
    if logger:
        logger.warn(msg, *args, **kwargs)
    else:
        print(msg)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境 dev/test/prod')
    parser.add_argument('--begin-date', help='开始时间')
    parser.add_argument('--end-date', help='结束时间')
    parser.add_argument('--target-index-name', required=False)
    parser.add_argument('--local', help='是否本地', required=False)
    args = parser.parse_args()
    if not args.env:
        parser.print_help()
        sys.exit(-1)

    global logger
    logger = AliyunLogClient(args.region_name, 'prod').logger

    scheduler = Scheduler(args.env, args.region_name, args.local, args.target_index_name)
    scheduler.run(args.begin_date, args.end_date)


if __name__ == '__main__':
    main()
