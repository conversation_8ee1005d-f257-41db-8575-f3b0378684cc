#! /usr/bin/env python3
import argparse
import hashlib
import json
import logging
import os
from datetime import date, datetime, timedelta

from aliyun.log import GetLogsRequest

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.log import AliyunLogClient
from multizone.amqp import RabbitMqClient, RocketMqClient
from multizone.db import DBClient
from multizone.rpc import AbcCisMonitorClient as monitor_client
from scripts.common.utils.lists import ListUtils as list_utils


# f = open('message-amqp.txt', "w+")


def recover_sls_message_amqp(region_name, date, env):
    aliyun_log_client = AliyunLogClient(region_name, env)
    aliyun_log = aliyun_log_client.logger
    aliyun_log_client.bindHandler()
    ob_cli = DBClient(region_name, 'ob', 'abc_cis_data_analysis' if env != 'prod' else 'abc_cis_monitor', env, True)
    db_cli = DBClient(region_name, 'abc_cis_stock_zip', 'abc_cis_data_analysis' if env != 'prod' else 'abc_cis_monitor', env, True)
    rows = ob_cli.fetchall(
        f"""select * from sls_message_amqp where `status` = 0 and created >= '{date - timedelta(days=3)} 00:00:00' and created <= '{date} 23:59:59';""")
    if len(rows) == 0:
        aliyun_log.info('rows is empty')
        return
    for _env in list_utils.group_by(rows, lambda x: x['env']):
        rocketmq_cli = RocketMqClient(region_name, _env, False)
        try:
            rocketmq_cli.producer_start()
        except Exception as e:
            aliyun_log.error(f'rocketmq start error, {e}')
        rabbitmq_cli = RabbitMqClient(region_name, _env, False)

        success = 0
        failed = 0
        consumer_succ = {}
        consumer_fail = {}
        for row in rows:
            if row['provider'] == 'rocket':
                try:
                    ret = rocketmq_cli.send_message(topic=row['topic'], tag=row['tag'],
                                                                    message=row['message'])
                    aliyun_log.info(f'rocketmq sendResult = {ret}')
                    db_cli.execute(f"""update sls_message_amqp set status = 10, last_modified = now() where id = '{row['id']}';""")
                    success += 1
                    if row.get("group", "") not in consumer_succ:
                        consumer_succ[row.get("group", "")] = 0
                    consumer_succ[row.get("group", "")] += 1
                except Exception as e:
                    aliyun_log(f'rocketmq_cli.send_message error: {e}')
                    failed += 1
                    if row.get("group", "") not in consumer_fail:
                        consumer_fail[row.get("group", "")] = 0
                    consumer_fail[row.get("group", "")] += 1
            elif row['provider'] == 'rabbit':
                try:
                    ret = rabbitmq_cli.send_message(exchange=row['exchange'],
                                                    routing_key=row.get('routing_key', ''),
                                                    queue=row.get('queue', ''),
                                                    message=row['message'])
                    aliyun_log.info(f'rabbitmq sendResult = {ret}')
                    db_cli.execute(f"""update sls_message_amqp set status = 10, last_modified = now() where id = '{row['id']}';""")
                    success += 1
                    if row.get("queue", "") not in consumer_succ:
                        consumer_succ[row.get("queue", "")] = 0
                    consumer_succ[row.get("queue", "")] += 1
                except Exception as e:
                    aliyun_log(f'rabbitmq_cli.send_message error: {e}')
                    failed += 1
                    if row.get("queue", "") not in consumer_fail:
                        consumer_fail[row.get("queue", "")] = 0
                    consumer_fail[row.get("queue", "")] += 1
        rocketmq_cli.producer_shutdown()
        rabbitmq_cli.close()

        monitor_client.sendServiceAlertMessage(title=f'RabbitMQ/RocketMQ消息恢复',
                                               content=f'''total: {len(rows)}, success: {success}{f'({os.linesep.join([f"{cc}: {consumer_succ[cc]}" for cc in consumer_succ])})' if len(consumer_succ) > 0 else ''}, failed: {failed}{f'({os.linesep.join([f"{cc}: {consumer_fail[cc]}" for cc in consumer_fail])})' if len(consumer_fail) > 0 else ''}''',
                                               region=region_name, env=_env)


def pull_sls_message_amqp(region_name, date, env):
    ob_cli = DBClient(region_name, 'ob', 'abc_cis_data_analysis' if env != 'prod' else 'abc_cis_monitor', env, True)
    db_cli = DBClient(region_name, 'abc_cis_stock_zip', 'abc_cis_data_analysis' if env != 'prod' else 'abc_cis_monitor', env, True)
    rows = ob_cli.fetchall(
        f"""select * from sls_message_amqp where created >= '{date - timedelta(days=3)} 00:00:00' and created <= '{date} 23:59:59';""")
    do_pull_sls_message_amqp(db_cli, date, region_name, rows)


def do_pull_sls_message_amqp(db_cli, date, region_name, rows):
    rocketmq_unique_key_to_row = list_utils.to_map(rows, lambda
        x: f"{x['data_key']}_{hashlib.md5(x['message'].encode('utf-8')).hexdigest()}_{x['provider']}_{x['topic']}_{x['tag']}_{x['group']}_{x['env']}")
    rabbitmq_unique_key_to_row = list_utils.to_map(rows, lambda
        x: f"{x['data_key']}_{hashlib.md5(x['message'].encode('utf-8')).hexdigest()}_{x['provider']}_{x['exchange']}_{x['routing_key']}_{x['queue']}_{x['env']}")
    insert_sql = "insert into sls_message_amqp (data_key, provider, topic, tag, `group`, `exchange`, routing_key, queue, message, env, is_deleted, created, last_modified)"
    env_values = {}
    env_consumer_c = {}
    limit = 100
    for q in ['* and location: "cn.abcyun.cis.core.amqp.AmqpRetryAspect.logRocketProducerRetryMessage"',
              '* and location: cn.abcyun.cis.core.amqp.AmqpRetryLogUtils.logRabbit',
              '* and location: cn.abcyun.cis.core.amqp.AmqpRetryLogUtils.logRocket',
              '* and location: "cn.abcyun.cis.core.amqp.AmqpRetryAspect.logRabbitProducerRetryMessage"']:
        offset = 0
        print(f'from {logstore} query: {q}, offset: {offset}, limit: {limit}')
        while True:
            from_time = int(datetime.strptime(f'{date - timedelta(days=3)} 00:00:00', '%Y-%m-%d %H:%M:%S').timestamp())
            to_time = int(datetime.strptime(f'{date} 23:59:59', '%Y-%m-%d %H:%M:%S').timestamp())
            query_req = GetLogsRequest(project=log_client_factory.log_config['project'], logstore=logstore,
                                       fromTime=from_time, toTime=to_time, query=q, line=limit, offset=offset)
            # print(f'query_req: {query_req}')
            log_rsp = log_client.get_logs(query_req)
            if log_rsp is None or len(log_rsp.get_logs()) == 0 or log_rsp.get_count() == 0:
                break
            for log in log_rsp.body:
                # print(f'log: {log}')
                if log['message-amqp'] is None:
                    print(f'log["message-amqp"] is None')
                    continue
                __time__ = datetime.fromtimestamp(int(log['__time__']))
                message_amqp = json.loads(log['message-amqp'])
                message = json.dumps(message_amqp['message'], ensure_ascii=False)
                data_key = f"{message_amqp['uuid']}"
                _env = message_amqp['env']
                if _env not in env_values:
                    env_values[_env] = []
                if _env not in env_consumer_c:
                    env_consumer_c[_env] = {}
                if message_amqp['provider'] == 'rocket':
                    unique_key = f'''{data_key}_{hashlib.md5(message.encode('utf-8')).hexdigest()}_{message_amqp["provider"]}_{message_amqp.get("topic", "")}_{message_amqp.get("tag", "")}_{message_amqp.get("group", "")}_{message_amqp.get("env", "")}'''
                    if unique_key in rocketmq_unique_key_to_row:
                        print(f'{message_amqp["provider"]} unique_key: {unique_key} already exists')
                        continue
                    env_values[_env].append(
                        f"""('{data_key}', '{message_amqp['provider']}', '{message_amqp.get('topic', "")}', '{message_amqp.get('tag', "")}', '{message_amqp.get('group', "")}', '', '', '', '{message}', '{message_amqp.get('env', "")}', 0, '{__time__}', now())""")
                    rocketmq_unique_key_to_row[unique_key] = {}
                    if message_amqp.get("group", "") not in env_consumer_c[_env]:
                        env_consumer_c[_env][message_amqp.get("group", "")] = 0
                    env_consumer_c[_env][message_amqp.get("group", "")] += 1
                elif message_amqp['provider'] == 'rabbit':
                    unique_key = f'''{data_key}_{hashlib.md5(message.encode('utf-8')).hexdigest()}_{message_amqp["provider"]}_{message_amqp.get("exchange", "")}_{message_amqp.get("routingKey", "")}_{message_amqp.get("queue", "")}_{message_amqp.get("env", "")}'''
                    if unique_key in rabbitmq_unique_key_to_row:
                        print(f'{message_amqp["provider"]} unique_key: {unique_key} already exists')
                        continue
                    env_values[_env].append(
                        f"""('{data_key}', '{message_amqp['provider']}', '', '', '', '{message_amqp.get('exchange', "")}', '{message_amqp.get('routingKey', "")}', '{message_amqp.get('queue', "")}', '{message}', '{message_amqp.get('env', "")}', 0, '{__time__}', now())""")
                    rabbitmq_unique_key_to_row[unique_key] = {}
                    if message_amqp.get("queue", "") not in env_consumer_c[_env]:
                        env_consumer_c[_env][message_amqp.get("queue", "")] = 0
                    env_consumer_c[_env][message_amqp.get("queue", "")] += 1
            offset += limit
    if len(env_values) > 0:
        for env in env_values:
            values = env_values[env]
            if len(values) == 0:
                logging.warning(f'「{env}」len(values) == 0')
                continue
            values_str = ', '.join(values)
            insert_sql_values = f'{insert_sql} values {values_str};'
            print(f'「{env}」insert_sql_values: {insert_sql_values}')
            db_cli.execute(insert_sql_values)
            print(f'「{env}」pull_and_extract_amqp_logs done')
            monitor_client.sendServiceAlertMessage(title=f'同步RabbitMQ/RocketMQ失败日志',
                                                   content=f'本次共同步「{len(values)}({os.linesep.join([f"{cc}: {env_consumer_c[env][cc]}" for cc in env_consumer_c[env]])})」条', region=region_name, env=env)


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--env', help='环境 dev/test/prod')
    parser.add_argument('--region-name', help='分区')
    parser.add_argument('--date', help='日期')
    parser.add_argument('--op-type', help='操作类型 pull/recover')
    args = parser.parse_args()

    logstore = f'{args.env}_longtime' if args.env != 'prod' else 'abc-cis-retry-message'
    log_client_factory = AliyunLogClient(args.region_name, logstore)
    log_client = log_client_factory.client
    date1 = datetime.strptime(args.date, '%Y-%m-%d').date() if args.date is None or args.date == '' else date.today()
    if args.op_type == 'pull':
        pull_sls_message_amqp(args.region_name, date1, args.env)
    elif args.op_type == 'recover':
        recover_sls_message_amqp(args.region_name, date1, args.env)
