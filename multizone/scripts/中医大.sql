create table abc_cis_outpatient_external_backup.v2_outpatient_medical_record
(
    id                      varchar(32)                           not null comment '病历id'
        primary key,
    outpatient_sheet_id     varchar(32)                           not null comment '门诊单id',
    patient_id              varchar(32) default ''                null comment '患者id',
    patient_name            varchar(256)                          null comment '患者姓名',
    patient_mobile          varchar(32)                           null comment '患者手机号码',
    patient_sex             varchar(3)                            null comment '患者性别',
    patient_birthday        varchar(32)                           null comment '患者生日',
    patient_id_card         varchar(20)                           null comment '患者身份证号',
    department_name         varchar(32) default ''                null comment '科室',
    doctor_name             varchar(32) default ''                null comment '医生',
    chief_complaint         text                                  null comment '主诉',
    past_history            text                                  null comment '既往史',
    allergic_history        text                                  null comment '过敏史',
    family_history          text                                  null comment '家族史',
    personal_history        text                                  null comment '个人史',
    physical_examination    text                                  null comment '体格检查',
    diagnosis               text                                  null comment '诊断',
    doctor_advice           text                                  null comment '医嘱',
    present_history         text                                  null comment '现病史',
    syndrome                text                                  null comment '辨证',
    therapy                 text                                  null comment '治法',
    chinese_examination     text                                  null comment '望闻切诊',
    birth_history           text                                  null comment '出生史',
    epidemiological_history text                                  null comment '流行病学史',
    obstetrical_history     text                                  null comment '妇科月经婚育史',
    auxiliary_examinations  text                                  null comment '辅助检查',
    target                  text                                  null comment '目标',
    prognosis               text                                  null comment '预后',
    symptom_time            timestamp                             null comment '发病时间',
    syndrome_treatment      text                                  null comment '辩证治法',
    chinese_prescription    text                                  null comment '方药',
    disposals               text                                  null comment '处置',
    created                 timestamp   default CURRENT_TIMESTAMP not null comment '创建时间'
)
    comment '门诊病历';

create table abc_cis_outpatient_external_backup.v2_outpatient_prescription_form
(
    id                  varchar(32)                                not null comment '处方单id'
        primary key,
    outpatient_sheet_id varchar(32)                                not null comment '门诊单id',
    type                tinyint                                    null comment '处方类型',
    specification       varchar(64)                                null comment '类型（饮片、颗粒）',
    dose_count          int(11) unsigned default 0                 not null comment '剂数',
    daily_dosage        varchar(256)                               null comment '每日剂量',
    `usage`             varchar(64)                                null comment '用法',
    freq                varchar(64)                                null comment '用药频次',
    requirement         varchar(400)                               null comment '服用要求',
    usage_level         varchar(512)                               null comment '单次服用用量',
    usage_days          varchar(64)                                null comment '服用天数描述',
    sort                int              default 0                 not null comment '排序字段',
    pharmacy_name       varchar(256)                               null comment '药房名称',
    created             timestamp        default CURRENT_TIMESTAMP not null comment '创建时间'
)
    comment '处方单';

create table abc_cis_outpatient_external_backup.v2_outpatient_prescription_form_item
(
    id                   varchar(32)                                       not null comment '处方单项id'
        primary key,
    outpatient_sheet_id  varchar(32)                                       not null comment '门诊单id',
    prescription_form_id varchar(32)                                       not null comment '处方单id',
    type_id              int unsigned                                      null comment '药品类型id',
    type_name            varchar(50)                                       null comment '药品类型',
    medicine_cadn        varchar(256)                                      not null comment '药品通用品',
    name                 varchar(256)            default ''                null comment '药品商用名',
    specification        varchar(200)            default ''                null comment '药品规格',
    manufacturer         varchar(200)            default ''                not null comment '生产厂家',
    group_id             int                                               null comment '分组编号',
    `usage`              varchar(40)             default ''                not null comment '用法',
    ivgtt                decimal(9, 4)                                     null comment '输液滴速',
    ivgtt_unit           varchar(50)                                       null comment '输液滴塑单位',
    freq                 varchar(40)             default ''                not null comment '用药频次',
    dosage               varchar(256)            default ''                not null comment '单次剂量',
    dosage_unit          varchar(40)                                       not null comment '剂量单位',
    days                 int                                               null comment '用药天数',
    special_requirement  varchar(50)                                       not null comment '特殊要求',
    unit_count           decimal(15, 3) unsigned default 0.000             not null comment '数量',
    unit                 varchar(64)                                       null comment '单位',
    unit_price           decimal(15, 4) unsigned                           null comment '单位价格',
    sort                 smallint                default 0                 not null comment '排序字段',
    pharmacy_name        varchar(256)                                      null comment '药房名称',
    created              timestamp               default CURRENT_TIMESTAMP not null comment '创建时间'
)
    comment '处方单项目表';

create table abc_cis_outpatient_external_backup.v2_outpatient_product_form_item
(
    id                  varchar(32)                            not null
        primary key,
    outpatient_sheet_id varchar(64)                            not null comment '门诊单id',
    name                varchar(256) default ''                not null comment '治疗项名称',
    unit_count          decimal(15, 3)                         not null comment 'unit次数',
    unit                varchar(64)  default ''                not null comment '单位',
    unit_price          decimal(15, 4)                         null comment '单位价格',
    type_id             int unsigned                           null comment '药品类型id',
    type_name           varchar(50)                            null comment '药品类型',
    days                int                                    null comment '诊疗项目天数',
    daily_dosage        int                                    null comment '每天次数/每次数量',
    doctor_name         varchar(64)                            null comment '医生姓名',
    department_name     varchar(64)                            null comment '科室名称',
    freq                varchar(64)                            null comment '使用频率',
    created             timestamp    default CURRENT_TIMESTAMP not null comment '创建时间'
)
    comment '诊疗项目';

create table abc_cis_outpatient_external_backup.v2_registration
(
    id              varchar(32)                           not null comment '挂号id'
        primary key,
    patient_id      varchar(32) default ''                null comment '患者id',
    patient_name    varchar(256)                          null comment '患者姓名',
    patient_mobile  varchar(32)                           null comment '患者手机号码',
    patient_sex     varchar(3)                            null comment '患者性别',
    reserve_date    varchar(64)                           null comment '预约日期',
    doctor_id       varchar(32)                           null comment '医生',
    doctor_name     varchar(256)                          null comment '医生姓名',
    department_id   varchar(32)                           null comment '科室id',
    department_name varchar(256)                          null comment '科室名称',
    fee             decimal(15, 4)                        null comment '挂号金额',
    status          int                                   null comment '挂号状态',
    created         timestamp   default CURRENT_TIMESTAMP not null comment '创建时间'
)
    comment '挂号表';



create table abc_cis_outpatient_external_backup.v2_registration_schedule
(
    id              int                           not null comment '排班id'
        primary key,
    name            varchar(256)                    null comment '排班名称',
    start           varchar(64)                     null comment '开始时间',
    end             varchar(64)                     null comment '结束时间',
    service_num     int                             null comment '排班号数',
    employee_id     varchar(32)                     null comment '医生id',
    employee_name   varchar(256)                    null comment '医生姓名',
    working_date    varchar(64)                     null comment '预约日期',
    department_id   varchar(32)                     null comment '科室id',
    department_name varchar(256)                    null comment '科室名称',
    status          varchar(10)                     null comment '排班状态',
    consulting_room_name varchar(256)                null comment '诊室名称',
    created         timestamp   default CURRENT_TIMESTAMP not null comment '创建时间'
)
    comment '挂号排班表';
