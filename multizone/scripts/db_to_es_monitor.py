#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""


"""
import os
import sys
from datetime import datetime, timedelta

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from scripts.common.utils.lists import ListUtils
from scripts.common.utils.sqls import SqlUtils
from multizone.es import ESClient
from multizone.log import AliyunLogClient
from multizone.db import DBClient

import argparse


def check_miss_sheet(logger, chain_ids, begin_time, end_time, charge_adb_client, es_client, need_sync_sheet_ids):
    # 先统计数量，如果数量不一致，再去查询具体的 sheet_id
    chain_id_sql_in = SqlUtils.to_in_value(chain_ids)
    db_total_count = charge_adb_client.fetchone("""
        select count(1) as count
        from v2_charge_sheet
        where chain_id in ({chain_id})
          and created between '{begin_time}' and '{end_time}';
    """.format(chain_id=chain_id_sql_in, begin_time=begin_time, end_time=end_time))['count']

    # 格式化 time 为 yyyy-MM-dd HH:mm:ss
    begin_time = begin_time.strftime('%Y-%m-%d %H:%M:%S')
    end_time = end_time.strftime('%Y-%m-%d %H:%M:%S')
    dsl = {
        "query": {
            "bool": {
                "filter": [
                    {
                        "terms": {
                            "chainId": chain_ids
                        }
                    },
                    {
                        "range": {
                            "created": {
                                "gte": begin_time,
                                "lte": end_time
                            }
                        }
                    }
                ]
            }
        },
        "size": 0
    }
    # 将 JSON 数据转换为压缩后的一行格式
    search_result = es_client.search("v3-cis-cdss-charge-sheet", dsl)
    es_total_count = search_result['hits']['total']['value']

    if db_total_count == es_total_count:
        return
    logger.warn(f'DB {db_total_count} 和ES {es_total_count} 总数量不一致')

    # 查询具体的 sheet_id
    sheets = charge_adb_client.fetchall("""
        select id
        from v2_charge_sheet
        where chain_id in ({chain_id})
            and created between '{begin_time}' and '{end_time}';
    """.format(chain_id=chain_id_sql_in, begin_time=begin_time, end_time=end_time))
    for sheet in sheets:
        need_sync_sheet_ids.add(sheet['id'])
    pass


def check_sync_lost_sheet(logger, chain_ids, begin_time, end_time, charge_adb_client, es_client, need_sync_sheet_ids):
    # 分页处理，每页 100 条
    page_size = 100
    page_num = 0
    total = 0
    while True:
        offset = page_num * page_size
        # 如果 chain_ids 不为空，则拼接到查询语句中
        chain_id_sql_in = f"chain_id in ({SqlUtils.to_in_value(chain_ids)}) and" if chain_ids else ""
        charge_sheets = charge_adb_client.fetchall("""
            select id, status, chain_id, patient_id
            from v2_charge_sheet
            where {chain_id}
              last_modified between '{begin_time}' and '{end_time}'
            limit {limit} offset {offset};
        """.format(chain_id=chain_id_sql_in, begin_time=begin_time, end_time=end_time, limit=page_size, offset=offset))
        total += len(charge_sheets)

        if not charge_sheets:
            break

        check_sync_charge_sheet(logger, charge_sheets, es_client, need_sync_sheet_ids)

        page_num += 1

    return total


def check_sync_charge_sheet(logger, charge_sheets, es_client, need_sync_sheet_ids):
    if not charge_sheets:
        return

    # # 分组统计各个状态的数量
    # status_count = {}
    # for sheet in charge_sheets:
    #     status = sheet['status']
    #     if status not in status_count:
    #         status_count[status] = 0
    #     status_count[status] += 1

    charge_sheet_ids = ListUtils.dist_mapping(charge_sheets, lambda e: e['id'])
    dsl = {
        "_source": {"includes": ["id", "status", "patientId", "patientName"]},
        "query": {
            "bool": {
                "filter": [
                    {
                        "terms": {
                            "id": charge_sheet_ids
                        }
                    }
                ]
            }
        },
        "size": len(charge_sheets)
    }
    search_result = es_client.search("v3-cis-cdss-charge-sheet", dsl)
    # es_status_count = {}
    # for bucket in search_result['aggregations']['statusCount']['buckets']:
    #     es_status_count[int(bucket['key'])] = bucket['doc_count']
    search_result_items = search_result['hits']['hits']
    id_to_search_result_item = ListUtils.to_map(search_result_items, lambda e: e['_source']['id'])

    # 判断状态是否一致，判断是否有患者姓名，没有则添加到需要同步的列表中
    for sheet in charge_sheets:
        sheet_id = sheet['id']
        sheet_status = sheet['status']
        chain_id = sheet['chain_id']
        es_sheet = id_to_search_result_item.get(sheet_id)
        if not es_sheet:
            need_sync_sheet_ids.add(sheet_id)
            logger.warn(f'sheet_id: {sheet_id} chain_id: {chain_id} 不存在于 ES 中')
            continue

        es_sheet_status = es_sheet['_source']['status']
        if sheet_status != es_sheet_status:
            need_sync_sheet_ids.add(sheet_id)
            logger.warn(f'sheet_id: {sheet_id} chain_id: {chain_id} 状态不一致，DB: {sheet_status}，ES: {es_sheet_status}')
            continue

        patient_id = es_sheet['_source'].get('patientId')
        patient_name = es_sheet['_source'].get('patientName')
        # 如果患者ID不为空，并且不等于 00000000000000000000000000000000，则判断患者姓名是否为空
        if patient_id and patient_id != '00000000000000000000000000000000' and not patient_name:
            need_sync_sheet_ids.add(sheet_id)
            logger.warn(f'sheet_id: {sheet_id} chain_id: {chain_id} 患者姓名为空，患者ID: {patient_id}')
            continue

        # 如果ES的患者ID不等于DB的患者ID
        if patient_id and patient_id != sheet['patient_id']:
            need_sync_sheet_ids.add(sheet_id)
            logger.warn(f'sheet_id: {sheet_id} chain_id: {chain_id} 患者ID不一致，DB: {sheet["patient_id"]}，ES: {patient_id}')
            continue


def run(logger, charge_adb_client, es_client, charge_write_client, chain_id_str, time, interval):
    logger.info(f'开始执行 {time} charge 索引同步检查')

    # 开始时间为昨天的 10 分钟前，结束时间为昨天的当前时间，日期为昨天
    check_miss_begin_time = time - timedelta(minutes=interval + 5)
    check_miss_end_time = time - timedelta(minutes=5)

    # chain_id 按照逗号分割
    chain_ids = chain_id_str.split(',') if chain_id_str else []
    need_sync_sheet_ids = set()
    # 校验近 5 分钟的丢单情况
    # 由于 adb 同步延迟，所以不检查丢单情况，只并且检查 status 是否同步已经包含了丢单
    # check_miss_sheet(logger, chain_ids, check_miss_begin_time, check_miss_end_time, charge_adb_client, es_client, need_sync_sheet_ids)

    # 校验前 5 分钟的状态同步情况
    check_sync_lost_begin_time = check_miss_begin_time - timedelta(minutes=5)
    check_sync_lost_end_time = check_miss_end_time - timedelta(minutes=5)
    today_last5_total = check_sync_lost_sheet(logger, chain_ids, check_sync_lost_begin_time, check_sync_lost_end_time, charge_adb_client, es_client, need_sync_sheet_ids)

    # 校验昨天的 5 分钟的状态同步情况
    check_sync_lost_begin_time = check_miss_begin_time - timedelta(days=1)
    check_sync_lost_end_time = check_miss_end_time - timedelta(days=1)
    yesterday_last5_total = check_sync_lost_sheet(logger, chain_ids, check_sync_lost_begin_time, check_sync_lost_end_time, charge_adb_client, es_client, need_sync_sheet_ids)

    # 为空则忽略
    if not need_sync_sheet_ids:
        logger.info(f'charge 索引同步检查完成，today_last5_total:{today_last5_total}，yesterday_last5_total:{yesterday_last5_total}，没有需要同步的数据')
        return

    logger.warn(f'charge 索引同步检查完成，today_last5_total:{today_last5_total}，yesterday_last5_total:{yesterday_last5_total}，需要同步的数据: {need_sync_sheet_ids}')
    charge_write_client.execute("""
        update v2_charge_sheet
        set created = DATE_ADD(created, interval 1 second)
        where id in ({sheet_ids});
    """.format(sheet_ids=SqlUtils.to_in_value(need_sync_sheet_ids)))


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--region', help='分区id', required=False)
    parser.add_argument('--chain-id', help='连锁id', required=False)
    parser.add_argument('--env', help='环境 dev/test/prod')
    parser.add_argument('--begin-date', help='开始日期 yyyy-MM-dd', required=False)
    parser.add_argument('--end-date', help='结束日期 yyyy-MM-dd', required=False)
    parser.add_argument('--interval', help='时间间隔，单位分钟', required=False)
    args = parser.parse_args()
    if not args.env:
        parser.print_help()
        sys.exit(-1)

    # 从 args 中获取日期，如果有开始日期和结束日期，则从开始日期的 00:00:00 到结束日期的 23:59:59，按照 5 分钟的时间间隔进行检查
    # 如果没有开始日期和结束日期，则只检查当前时间的 5 分钟
    env = args.env
    interval = int(args.interval if args.interval else 5)
    region = args.region if args.region else 'ShangHai'
    charge_adb_client = DBClient(region, 'ods' if region == 'ShangHai' else 'ob', 'abc_cis_charge', env, True)
    charge_write_client = DBClient(region, 'abc_cis_charge', 'abc_cis_charge', env, True)
    es_client = ESClient(region, env, True, True)
    logger = AliyunLogClient(region, 'prod').logger
    if args.begin_date and args.end_date:
        begin_date = datetime.strptime(args.begin_date, '%Y-%m-%d')
        end_date = datetime.strptime(args.end_date, '%Y-%m-%d')
        current_time = begin_date
        end_time = end_date + timedelta(days=1)
        while current_time <= end_time:
            try:
                run(logger, charge_adb_client, es_client, charge_write_client, args.chain_id, current_time, interval)
            except Exception as e:
                logger.error("执行失败", exc_info=e)
                # 再把异常抛出去
                raise e
            current_time += timedelta(minutes=interval)
    else:
        run(logger, charge_adb_client, es_client, charge_write_client, args.chain_id, datetime.now(), interval)


if __name__ == '__main__':
    main()
