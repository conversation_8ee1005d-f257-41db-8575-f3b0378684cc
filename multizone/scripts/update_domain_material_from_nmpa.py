#! /usr/bin/env python3
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""


"""
import os
import time

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import json
import logging
import re
import yagmail
import zipfile
import datetime
import requests
import xmltodict
import zipfile
import io
import subprocess
import xml.etree.ElementTree as ET
from multizone.db import DBClient
db_client = DBClient('ShangHai','abc_cis_mixed','abc_cis_domain','prod',True)

# resume_byte_pos = 0
# headers = {}
#
# if os.path.exists(file_path):
#     resume_byte_pos = os.path.getsize(file_path)
#     headers["Range"] = f"bytes={resume_byte_pos}-"
#
# response = requests.get(url, headers=headers, stream=True)
#
# with open(file_path, "ab") as f:
#     for chunk in response.iter_content(chunk_size=8192):
#         if chunk:
#             f.write(chunk)
#sed -I '' 's/\x02//g' *.xml  #^B
#sed -I '' 's/\x03]//g' *.xml  #^B
#sed -I '' 's/&amp;//g' *.xml
#sed -I '' 's/&#1;//g' *.xml
#sed -I '' 's/灭.*埄༠//g' *.xml



# CREATE TABLE v2_domain_medical_material_traceable_code
# (
#     device_record_key                     VARCHAR(64) PRIMARY KEY COMMENT '数据库主键-国家药品监督局数据的主key',
#     name                                  VARCHAR(256) COMMENT 'cpmctymc-产品名称/通用名称--耗材的名字',
#     traceable_identification_code         VARCHAR(32) COMMENT 'zxxsdycpbs-最小销售单元产品标识-耗材追溯码设备标识ID',
#     related_traceable_code_list           json COMMENT '如果有大小包装多个追溯码',
#     shebao_national_code                  VARCHAR(64) COMMENT 'ybbm-医保耗材分类编码--国家医保码',
#     piece_num                             INT        NOT NULL DEFAULT 0 COMMENT 'zxxsdyzsydydsl-最小销售单元中使用单元的数量-最小包转单位',
# piece_unit VARCHAR(32) NULL COMMENT '耗材规格 小单位 从material_spece里面解析',
# package_unit VARCHAR(32) NULL COMMENT '耗材规格 小单位 从material_spece里面解析',
#     material_spec                         VARCHAR(64) COMMENT 'ggxh-型号规格/包装规格--国家药品监督局数据和goods上材料规格对不上',
#     material_type                         VARCHAR(10) COMMENT 'cplb-产品类别--映射到goods类型 耗材',
#     material_sub_type                     VARCHAR(10) COMMENT 'qxlb-医疗器械类别--映射到goods类型-subtype-耗材',
#     is_traceable_code_has_batch_no        VARCHAR(4) NULL COMMENT 'scbssfbhph-生产标识是否包含批号-耗材追溯码里面是否有批号',
#     is_traceable_code_has_serial_no       VARCHAR(4) NULL COMMENT 'scbssfbhph-生产标识是否包含序号-耗材追溯码里面是否有序号',
#     is_traceable_code_has_production_date VARCHAR(4) NULL COMMENT 'scbssfbhscrq-生产标识是否包含生产日期-耗材追溯码里面是否有生产日期',
#     is_traceable_code_has_expiry_date     VARCHAR(4) NULL COMMENT 'scbssfbhscrq-生产标识是否包含失效日期-耗材追溯码里面是否有失效日期',
#     udi_code_type                         VARCHAR(16) COMMENT 'cpbsbmtxmc-产品标识编码体系名称-耗材追溯码的编码方式',
#     udi_code_publish_date                 VARCHAR(16) COMMENT 'cpbsfbrq-产品标识发布日期- YYYY-MM-DD',
#     record_publish_date                   VARCHAR(16) COMMENT 'versionTime-版本的发布日期--国家药品监督局这条数据的发布日期YYYY-MM-DD',
#     record_delisting_date                 VARCHAR(16) COMMENT 'tsrq-退市日期 YYYY-MM-DD',
#     manufacturer_full                     VARCHAR(256) COMMENT 'ylqxzcrbarmc医疗器械注册人/备案人名称--厂家中文名',
#     english_manufacturer_full             VARCHAR(256) COMMENT 'ylqxzcrbarywmc-医疗器械注册人/备案人英文名称--厂家英文名',
#     unified_social_information_code       VARCHAR(256) COMMENT 'tyshxydm-统一社会信息代码',
#     certificate_name                      VARCHAR(256) COMMENT 'zczbhhzbapzbh-注册证编号或者备案凭证编号',
#     traceable_identification_code_first         VARCHAR(32) COMMENT '上一层包装的追溯码',
#     package_count_first                   INT        NULL COMMENT 'packingInfoList.bznhxyjcpbssl-包转数量',
#     package_unit_first                    VARCHAR(8) COMMENT 'packingInfoList.cpbzjb-包转单位',
#     traceable_identification_code_second         VARCHAR(32) COMMENT '上一层包装的追溯码',
#     package_count_second                  INT        NULL COMMENT 'packingInfoList.bznhxyjcpbssl-包转数量',
#     package_unit_second                   VARCHAR(8) COMMENT 'packingInfoList.cpbzjb-包转单位',
#     traceable_identification_code_third         VARCHAR(32) COMMENT '上一层包装的追溯码',
#     package_count_third                   INT        NULL COMMENT 'packingInfoList.bznhxyjcpbssl-包转数量',
#     package_unit_third                    VARCHAR(9) COMMENT 'packingInfoList.cpbzjb-包转单位',
#     remark                                VARCHAR(50) COMMENT 'cpms-产品描述',
#     version_number                        INT COMMENT 'versionNumber-版本号',
#     version_time                          DATE COMMENT '版本时间',
#     version_status                        VARCHAR(50) COMMENT '版本状态',
#     correction_number                     INT COMMENT 'correctionNumber-纠错次数',
#     correction_remark                     VARCHAR(50) COMMENT 'correctionRemark-纠错说明',
#     correction_time                       VARCHAR(50) COMMENT 'correctionTime-纠错日期',
#     package_info_list                     json COMMENT 'packingInfoList-包装信息原始数据--国家药品监督局数据	"packingInfoList":[ { "cpbzjb":"产品包装级别/盒", "bzcpbs":"包装产品标识(追溯码)/16973175660497", "bznhxyjcpbssl":"本级包装内包含小一级相同产品标识的包装数量/10", "bznhxyjbzcpbs":"包装内含小一级包装产品标识(追溯码)/06973175660499" } ]',
#     company_contact_list                  json COMMENT 'companyContactList-企业联系信息--国家药品监督局数据',
#     clinical_info_list                    json COMMENT 'clinicalInfoList-企业联系信息--国家药品监督局数据',
#     storage_info_list                     json COMMENT 'storageInfoList-储存或操作信息--国家药品监督局数据"storageInfoList":[ { "cchcztj":"储存或操作条件/温度", "zdz":"值范围/小", "zgz":"值范围/大", "jldw":"计量单位" } ]',
#     use_unit_product_identification       VARCHAR(32) COMMENT 'sydycpbs--使用单元产品标识--国家药品监督局数据-不知道含义',
#     has_ontology_directly_identified      INT        NOT NULL DEFAULT 0 COMMENT 'sfybtzjbs-是否有本体直接标识--国家药品监督局数据不知道含义',
#     ontology_directly_identified          VARCHAR(64) COMMENT 'btcpbs-本体产品标识--国家药品监督局数据-不知道含义',
#     ontology_directly_identified_xxx      VARCHAR(64) COMMENT 'btcpbsyzxxsdycpbssfyz-本体产品标识***--国家药品监督局数据-不知道含义',
#     product_serial_number                 VARCHAR(64) COMMENT 'cphhhbh-产品货号或编号***--国家药品监督局数据-不知道含义',
#     material_category_type_code           VARCHAR(32) COMMENT 'flbm-分类编码--国家药品监督局数据的类别码 08-06-05',
#     material_category_type_code_old       VARCHAR(32) COMMENT 'yflbm-原器械目录代码--国家药品监督局数据的类别码',
#     is_one_time_use                       VARCHAR(4) COMMENT 'sfbjwycxsy-是否一次性使用',
#     max_time_use                          VARCHAR(4) COMMENT 'zdcfsycs-最大重复使用次数',
#     is_composite_product                  VARCHAR(4) NULL COMMENT 'sfwblztlcp-是否为包类/组套类产品-国家药品监督局数据-不知道含义',
#     is_aseptic_packaging                  VARCHAR(4) NULL COMMENT 'sfwwjbz-是否为无菌包装-国家药品监督局数据-不知道含义',
#     is_sterilize_before_use               VARCHAR(4) NULL COMMENT 'syqsfxyjxmj-使用前是否需要进行灭菌-国家药品监督局数据-不知道含义',
#     sterilize_way                         VARCHAR(64) COMMENT 'mjfs-灭菌方式--国家药品监督局数据-不知道含义',
#     mr_safety_info                        VARCHAR(64) COMMENT 'cgzmraqxgxx-磁共振（MR）安全相关信息--国家药品监督局数据-不知道含义',
#     storage         VARCHAR(256) NULL COMMENT '存储条件'
#     created                               datetime   not null comment '创建时间',
#     last_modified                         datetime   not null comment '最近修改时间'
# ) DEFAULT CHARSET = utf8mb4
#   COLLATE = utf8mb4_0900_ai_ci comment '从国家药品监督局数据拉取的耗材追溯码数据';

def camel_to_snake(camel_str):
    # 将驼峰命名法的字符串转换为下划线命名法
    snake_str = ''.join(['_' + char.lower() if char.isupper() else char for char in camel_str]).lstrip('_')
    return snake_str
def insertOrUpdateData(xmlTree, batch_size=500):
    # 初始化一个空列表来存储数据
    data_list = []
    count = 0
    #遍历 devices 下面的device
    for device in xmlTree.findall('devices/device'):
        count +=1
        packageUnit = None
        pieceUnit = None
        materialSpec = device.find('ggxh').text if device.find('ggxh') is not None and device.find('ggxh').text is not None else ''
        # 假设所有的值都存储在一个字典中
        try:
            pattern = r"^(\d+)(\D+)/(\D+)$"
            match = re.search(pattern, materialSpec)
            if match:
                pieceNum = match.group(1)  # 提取的数字
                pieceUnit = match.group(2)  # 提取的“/”前的文字
                packageUnit = match.group(3)  # 提取的“/”后的文字
                materialSpec =None
                # newpieceNum = int( device.find('zxxsdyzsydydsl').text if device.find('zxxsdyzsydydsl') is not None else None)
                # print(f"数字: {pieceNum}-{newpieceNum}, 前面的文字: {pieceUnit}, 后面的文字: {packageUnit}")
            else:
                pieceNum = int(device.find('zxxsdyzsydydsl').text if device.find('zxxsdyzsydydsl') is not None else None)
                # pattern = r"^(\d+)(\D+)$"
                # match = re.search(pattern, materialSpec)
                # if match:
                #     pieceNum = match.group(1)  # 提取的数字
                #     packageUnit = match.group(2)  # 提取的“/”后的文字
                #     materialSpec =''
                #     print(f"数字: {pieceNum}, 前面的文字: {pieceUnit}, 后面的文字: {packageUnit}")
                # else:
                #     pieceNum = int(device.find('zxxsdyzsydydsl').text if device.find('zxxsdyzsydydsl') is not None else None)

        except (ValueError, TypeError):
            pieceNum = 0
        try:
            versionNumber = int(device.find('versionNumber').text if device.find('versionNumber') is not None else None)
        except (ValueError, TypeError):
            versionNumber = None
        try:
            correctionNumber = int(device.find('correctionNumber').text if device.find('correctionNumber') is not None else None)
        except (ValueError, TypeError):
            correctionNumber = None

        packingInfoList = device.find('packingList')
        # 用字典存储 key是bzcpbs
        packingDict = {}
        packageCountFirst = None
        packageUnitFirst = None
        packageCountSecond = None
        packageUnitSecond = None
        packageCountThird = None
        packageUnitThird = None
        traceableIdentificationCodeFirst = None
        traceableIdentificationCodeSecond = None
        traceableIdentificationCodeThird = None
        traceableIdentificationCode = device.find('zxxsdycpbs').text if device.find( 'zxxsdycpbs') is not None  and device.find('zxxsdycpbs').text is not None else None
        packingInfoListJson = None
        # 数组记录多个追溯码
        relatedTraceableCodeList = []
        if packingInfoList is not None:
            xml_str = ET.tostring(packingInfoList, encoding='unicode')
            xml_dict = xmltodict.parse(xml_str)
            packingInfoListJson = json.dumps(xml_dict)
            relatedTraceableCodeList.append(traceableIdentificationCode)
            for packingInfo in packingInfoList:
                parentCode = packingInfo.find('bzcpbs').text if  packingInfo.find('bzcpbs') is not None else None
                if parentCode is not None:
                    relatedTraceableCodeList.append(parentCode)
                childId = packingInfo.find('bznhxyjbzcpbs').text if  packingInfo.find('bznhxyjbzcpbs') is not None else None
                if childId is not None:
                    packingDict[childId] = packingInfo

            # 遍历
            findId = traceableIdentificationCode

            if  findId is not None and packingDict.get(findId) is not None:
                packingInfo = packingDict.get(findId)
                try:
                    packageCountFirst = int(packingInfo.find('bznhxyjcpbssl').text) if packingInfo.find('bznhxyjcpbssl').text is not None else None
                except (ValueError, TypeError):
                    packageCountFirst = None
                packageUnitFirst = packingInfo.find('cpbzjb').text if  packingInfo.find('cpbzjb').text is not None else None
                #  如果 packageUnitFirst 非空，且为数字，清空
                if packageUnitFirst is not None and packageUnitFirst.isdigit():
                    packageUnitFirst = None
                findId = packingInfo.find('bzcpbs').text if packingInfo.find('bzcpbs').text is not None else None
                traceableIdentificationCodeFirst = findId
            if  findId is not None and packingDict.get(findId) is not None:
                packingInfo = packingDict.get(findId)
                try:
                    packageCountSecond = int(packingInfo.find('bznhxyjcpbssl').text) if packingInfo.find('bznhxyjcpbssl').text is not None else None
                except (ValueError, TypeError):
                    packageCountSecond = None
                packageUnitSecond = packingInfo.find('cpbzjb').text if  packingInfo.find('cpbzjb').text is not None else None
                findId = packingInfo.find('bzcpbs').text if packingInfo.find('bzcpbs').text is not None else None
                traceableIdentificationCodeSecond = findId
            if  findId is not None and packingDict.get(findId) is not None:
                packingInfo = packingDict.get(findId)
                try:
                    packageCountThird = int(packingInfo.find('bznhxyjcpbssl').text) if packingInfo.find('bznhxyjcpbssl').text is not None else None
                except (ValueError, TypeError):
                    packageCountThird = None
                packageUnitThird = packingInfo.find('cpbzjb').text if  packingInfo.find('cpbzjb').text is not None else None
                findId = packingInfo.find('bzcpbs').text if packingInfo.find('bzcpbs').text is not None else None
                traceableIdentificationCodeThird = findId

        if len(relatedTraceableCodeList) <= 1:
            relatedTraceableCodeList = None
        else:
            relatedTraceableCodeList = json.dumps(relatedTraceableCodeList)


        companyContactList = device.find('contactList')
        companyContactListJson = None
        if companyContactList is not None:
            xml_str = ET.tostring(companyContactList, encoding='unicode')
            xml_dict = xmltodict.parse(xml_str)
            companyContactListJson = json.dumps(xml_dict)

        clinicalList = device.find('clinicalList')
        clinicalListJson = None
        if clinicalList is not None:
            xml_str = ET.tostring(clinicalList, encoding='unicode')
            xml_dict = xmltodict.parse(xml_str)
            clinicalListJson = json.dumps(xml_dict)


        storageList = device.find('storageList')
        storageListJson = None
        storage = None
        # 遍历device下的 "storageInfoList": [ { "cchcztj": "温度", "zdz": "0", "zgz": "40", "jldw": "℃" }, { "cchcztj": "湿度", "zdz": "0", "zgz": "80", "jldw": "%RH" } ]
        if storageList is not None:
            xml_str = ET.tostring(storageList, encoding='unicode')
            xml_dict = xmltodict.parse(xml_str)
            storageListJson = json.dumps(xml_dict)
            # 拼成 storage="温度:0-40℃,湿度:0-80%RH"
            storage = ''
            idx = 0
            for storageInfo in storageList:
                if idx > 0:
                    storage += ','
                storage += storageInfo.find('cchcztj').text if storageInfo.find('cchcztj') is not None and storageInfo.find('cchcztj').text is not None else '' + ':' + storageInfo.find('zdz').text if storageInfo.find('zdz') is not None and storageInfo.find( 'zdz').text is not None else ''   + '-' + storageInfo.find('zgz').text if storageInfo.find('zgz') is not None and storageInfo.find( 'zgz').text is not None else '' + storageInfo.find('jldw').text if storageInfo.find('jldw') is not None and storageInfo.find('jldw').text is not None else ''
                idx += 1
        data = {
            'deviceRecordKey': device.find('deviceRecordKey').text if device.find('deviceRecordKey') is not None and device.find('deviceRecordKey').text is not None else None,
            'name': device.find('cpmctymc').text if device.find('cpmctymc').text is not None and device.find('cpmctymc').text is not None else '',
            'traceableIdentificationCode': traceableIdentificationCode,
            'shebaoNationalCode': device.find('ybbm').text if device.find('ybbm').text is not None  and device.find('ybbm').text is not None else '',
            'materialSpec': materialSpec,
            'pieceUnit': pieceUnit,
            'packageUnit': packageUnit,
            'materialType': device.find('cplb').text if device.find('cplb') is not None  and device.find('cplb').text is not None else '',
            'materialSubType': device.find('qxlb').text if device.find('qxlb') is not None  and device.find('qxlb').text is not None else '',
            'udiCodeType' :  device.find('cpbsbmtxmc').text if device.find('cpbsbmtxmc') is not None  and device.find('cpbsbmtxmc').text is not None else None,
            'udiCodePublishDate': device.find('cpbsfbrq').text if device.find('cpbsfbrq') is not None  and device.find('cpbsfbrq').text is not None else None,
            'recordPublishDate' :  device.find('versionTime').text if device.find('versionTime') is not None  and device.find('versionTime').text is not None else None,
            'recordDelistingDate' :  device.find('tsrq').text if device.find('tsrq') is not None  and device.find('tsrq').text is not None else None,
            'manufacturerFull' :  device.find('ylqxzcrbarmc').text if device.find('ylqxzcrbarmc') is not None  and device.find('ylqxzcrbarmc').text is not None else None,
            'englishManufacturerFull' :  device.find('ylqxzcrbarywmc').text if device.find( 'ylqxzcrbarywmc') is not None and device.find('ylqxzcrbarywmc').text is not None else None,
            'unifiedSocialInformationCode' :  device.find('tyshxydm').text if device.find('tyshxydm') is not None  and device.find('tyshxydm').text is not None else None,
            'certificateName' :  device.find('zczbhhzbapzbh').text if device.find('zczbhhzbapzbh') is not None  and device.find('zczbhhzbapzbh').text is not None else  None,
            'relatedTraceableCodeList': relatedTraceableCodeList,
            'storage':storage,
            'traceableIdentificationCodeFirst': traceableIdentificationCodeFirst,
            'packageCountFirst' :  packageCountFirst,
            'packageUnitFirst' :  packageUnitFirst,
            'traceableIdentificationCodeSecond': traceableIdentificationCodeSecond,
            'packageCountSecond' :  packageCountSecond,
            'packageUnitSecond' :  packageUnitSecond,
            'traceableIdentificationCodeThird': traceableIdentificationCodeThird,
            'packageCountThird' :  packageCountThird,
            'packageUnitThird' :  packageUnitThird,
            'remark' :  device.find('cpms').text if device.find('cpms') is not None  and device.find('cpms').text is not None else None,
            'versionTime' :  device.find('versionTime').text if device.find('versionTime') is not None  and device.find('versionTime').text is not None else None,
            'versionStatus' :  device.find('versionStauts').text if device.find('versionStauts') is not None  and device.find('versionStauts').text is not None else None,
            'correctionRemark' :  device.find('correctionRemark').text if device.find('correctionRemark') is not None  and device.find('correctionRemark').text is not None else None,
            'correctionTime' :  device.find('correctionTime').text if device.find('correctionTime') is not None   and device.find('correctionTime').text is not None else None,
            'packageInfoList' :  packingInfoListJson if packingInfoListJson is not None  else None,
            'companyContactList' :  companyContactListJson if companyContactListJson is not None  else None,
            'clinicalInfoList' :  clinicalListJson if clinicalListJson is not None else None,
            'storageInfoList' :  storageListJson if storageListJson is not None else None,
            'useUnitProductIdentification' :  device.find('sydycpbs').text if device.find('sydycpbs') is not None  and device.find('sydycpbs').text is not None  else '',
            'ontologyDirectlyIdentified' :  device.find('btcpbs').text if device.find('btcpbs') is not None  and device.find('btcpbs').text is not None else '',
            'ontologyDirectlyIdentifiedXxx' :  device.find('btcpbsyzxxsdycpbssfyz').text if device.find( 'btcpbsyzxxsdycpbssfyz') is not None  and device.find('btcpbsyzxxsdycpbssfyz').text is not None else '',
            'productSerialNumber' :  device.find('cphhhbh').text if device.find('cphhhbh') is not None  and device.find('cphhhbh').text is not None else '',
            'materialCategoryTypeCode' :  device.find('flbm').text if device.find('flbm') is not None  and device.find('flbm').text is not None else '',
            'materialCategoryTypeCodeOld' :  device.find('yflbm').text if device.find('yflbm') is not None  and device.find('yflbm').text is not None else '',
            'isOneTimeUse' :  device.find('sfbjwycxsy').text if device.find('sfbjwycxsy') is not None  and device.find('sfbjwycxsy').text is not None else '',
            'maxTimeUse' :  device.find('zdcfsycs').text if device.find('zdcfsycs') is not None  and device.find('zdcfsycs').text is not None else '',
            'sterilizeWay' :  device.find('mjfs').text if device.find('mjfs') is not None  and device.find('mjfs').text is not None else '',
            'mrSafetyInfo' :  device.find('cgzmraqxgxx').text if device.find('cgzmraqxgxx') is not None  and device.find('cgzmraqxgxx').text is not None else '',
            'created' :  datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'lastModified' :  datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'pieceNum' : pieceNum,
            'isTraceableCodeHasBatchNo' : device.find('scbssfbhph').text if device.find('scbssfbhph') is not None else None,
            'isTraceableCodeHasSerialNo' : device.find('scbssfbhxlh').text if device.find('scbssfbhxlh') is not None else None,
            'isTraceableCodeHasProductionDate' : device.find('scbssfbhscrq').text if device.find('scbssfbhscrq') is not None else None,
            'isTraceableCodeHasExpiryDate' : device.find('scbssfbhsxrq').text if device.find('scbssfbhsxrq') is not None else None,
            'versionNumber' : versionNumber,
            'correctionNumber' : correctionNumber,
            'hasOntologyDirectlyIdentified' : device.find('sfybtzjbs').text if device.find('sfybtzjbs') is not None else None,
            'isCompositeProduct' : device.find('sfwblztlcp').text if device.find('sfwblztlcp') is not None else None,
            'isAsepticPackaging' : device.find('sfwwjbz').text if device.find('sfwwjbz') is not None else None,
            'isSterilizeBeforeUse' : device.find('syqsfxyjxmj').text if device.find('syqsfxyjxmj') is not None else None,
        }

        # 将数据添加到列表中
        data_list.append(data)
        # 当数据列表达到批量大小时，执行插入操作并清空列表
        if len(data_list) >= batch_size:
            insert_batch_data(data_list)
            data_list = []  # 清空列表以便下一批数据
    # 如果还有剩余的数据，执行最后一次插入操作
    if data_list:
        print("最后一次插入操作")
        insert_batch_data(data_list)
        #生成sql语句，把上面的字段插入到数据库中v2_domain_medical_material_traceable_code
    return count
def insert_batch_data(data_list):
    data = data_list[0]
    # 构建INSERT部分的字段名和占位符
    insert_fields =  ', '.join([f"{camel_to_snake(key)}" for key in data.keys()])
    insert_placeholders = ', '.join(['%s'] * len(data))

    # 构建ON DUPLICATE KEY UPDATE部分的更新语句
    # 把created从update_fields中排除
    update_fields = ', '.join( [f"{camel_to_snake(key)} = VALUES({camel_to_snake(key)})" for key in data.keys() if key != 'created'] )

    # 构建完整的SQL模板
    sql_template = f"""
        INSERT INTO v2_domain_medical_material_traceable_code({insert_fields}) VALUES ({insert_placeholders}) ON DUPLICATE KEY UPDATE {update_fields};
        """
    # 准备批量插入的数据
    batch_data = []
    for data in data_list:
        # 处理 None 值
        processed_data = [None if value is None else str(value) for value in data.values()]
        batch_data.append(tuple(processed_data))
    # 打印或执行生成的SQL语句
    # 打印SQL模板和批量插入的数据（用于调试）
    return db_client.executemanyParam(sql_template,batch_data)



def initData(unzipDir):
    count = 0
    #遍历 /Users/<USER>/Downloads/mat下的xml
    for root, dirs, files in os.walk(unzipDir):
        #遍历文件 files
        for file in files:
            if not file.endswith('.xml'):
                continue
            #直接解析本地xml文件
            #统计耗时
            # 记录开始时间
            start_time = time.time()
            tree = ET.parse(os.path.join(root, file))
            count+=insertOrUpdateData(tree,500)
            elapsed_time = time.time() - start_time
            print("处理文件完成:"+file+" ,用时:"+str(elapsed_time)+"s,数量:"+str(count))
    return count


def downloadDataAndParse(zip_file_name, unzip_dir,url):
    # 请求RSS下载页面
    response = requests.get(url)
    # 检查响应状态码是否为 200（表示成功）
    if response.status_code == 200:

        # 将文件内容写入本地文件
        with open(zip_file_name, 'wb') as f:
            f.write(response.content)

        # 创建一个与 ZIP 文件同名的目录，用于存放解压后的文件
        os.makedirs(unzip_dir, exist_ok=True)

        # 打开 ZIP 文件并解压到指定目录
        with zipfile.ZipFile(zip_file_name, 'r') as zip_ref:
            zip_ref.extractall(unzip_dir)

            # 遍历解压后的目录
        for root, dirs, files in os.walk(unzip_dir):
            for file in files:
                # 如果文件是 ZIP 文件，则递归解压
                if file.endswith('.zip'):
                    nested_zip_path = os.path.join(root, file)
                    # 打开 ZIP 文件并解压到指定目录
                    with zipfile.ZipFile(nested_zip_path, 'r') as zip_ref:
                        zip_ref.extractall(unzip_dir)

        # 定义要执行的 Linux 命令
        iOpt=" -i'' " #linux
        # iOpt=" -i '' " #mac
        commands = [
            f"sed {iOpt} 's/\\x02//g' {unzip_dir}/*.xml",
            f"sed {iOpt} 's/\\x03]//g' {unzip_dir}/*.xml",
            f"sed {iOpt} 's/&amp;//g' {unzip_dir}/*.xml",
            f"sed {iOpt} 's/&#1;//g' {unzip_dir}/*.xml",
            f"sed {iOpt} 's/灭.*埄༠//g' {unzip_dir}/*.xml"
        ]

        # 在解压后的目录中执行命令
        for command in commands:
            subprocess.run(command, shell=True, check=True)

    else:
        print("下载失败，状态码：", response.status_code)


def process_xml_files(directory):
    # 遍历目录下的所有文件
    for root_dir, dirs, files in os.walk(directory):
        for file in files:
            # 如果文件是 XML 文件，则处理它
            if file.endswith('.xml'):
                xml_file_path = os.path.join(root_dir, file)

                # 读取 XML 文件内容
                with open(xml_file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 解析 XML 内容
                root = ET.fromstring(content)

                # 查找所有的 <item> 标签
                items = root.findall('.//item')

                # 在这里执行你需要的操作，例如打印 <item> 标签的数量
                print(f"文件 {file} 中有 {len(items)} 个 <item> 标签")
def get_links(url):
    # 发送请求并获取响应
    response = requests.get(url)
    retBack = []

    # 检查响应状态码是否为200（成功）
    if response.status_code == 200:
        # 解析 XML 内容
        root = ET.fromstring(response.content)

        # 遍历所有的item元素
        for item in root.findall('channel/item'):
            # 提取description元素
            description = item.find('description').text
            link = item.find('link').text
            link_prefix = None
            # 提取title元素
            title = item.find('title').text

            # 使用正则表达式匹配日期
            date_pattern = r'\d{8}'
            match = re.search(date_pattern, title)

            # 查找链接前面的文本
            link_start_index = description.find('。链接：')
            if link_start_index != -1:
                link_prefix = description[:link_start_index].strip()
            retBack.append({
                'desc': link_prefix,
                'link': link,
                'date': match.group() if match else None
            })
        return retBack
    else:
        print(f'请求失败，状态码：{response.status_code}')
        return []

def notify(title,msg):
    requests.post(
        url='https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7057b18d-b9ad-4815-9977-4c9ebad7881f',
        json={
            "msgtype": "markdown",
            "markdown": {
                "content": f"""[{title}]\n{msg}
         """
            }
        })

# 获取当前年份和月份
current_date = datetime.date.today()
year_month = (current_date - datetime.timedelta(days=1)).strftime("%Y-%m-%d")
# 获取文件名
# baseDir = "/Users/<USER>/Downloads"
baseDir = "./national_material_update"
#检查目录，不存在建目录
if not os.path.exists(baseDir):
    os.makedirs(baseDir)
zip_file_name = f"{baseDir}/month_{year_month}.zip"
unzip_dir = zip_file_name.replace('.zip', '')
#月
# url = 'https://udid.nmpa.gov.cn/attachments/attachment/download.html?path=A2E0C4E371D356DC134BE4F49B806211AF5D12A4AF10DF797D0E420BB0668DF08CF732AC7E3CBABB22B4E3F855164A87A948D84488E01844E3EDB5CBF4CC0183'
#day
dayUrl ="https://udi.nmpa.gov.cn/rss/download.html?files=daily"
linkUrls = get_links(dayUrl)
#遍历 linkUrls

#通知字符串
notifyStr = ''
for itemDesc in linkUrls:
    # 下载数据并解析
    d = itemDesc["date"]
    zip_file_name = f"{baseDir}/day_{d}.zip"
    unzip_dir = zip_file_name.replace('.zip', '')
    downloadDataAndParse(zip_file_name, unzip_dir,itemDesc["link"])
    totalCount = initData(unzip_dir)
    notifyStr += itemDesc["desc"] +",ABC实际更新成功:<font color=\"warning\">"+str(totalCount) +"个</font>\n"
    # 删除下载的 ZIP 文件
    os.remove(zip_file_name)
    # 删除解压后的文件
notify(f"{year_month}国家耗材数据库定时更新",notifyStr)
#downloadDataAndParse(zip_file_name, unzip_dir,url)
# process_xml_files(unzip_dir)
# totalCount = initData(unzip_dir)


# initData("/Users/<USER>/Downloads/mat")