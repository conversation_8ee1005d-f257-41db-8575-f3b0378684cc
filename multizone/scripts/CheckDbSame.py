#! /usr/bin/env python3
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON>yon<PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""


"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
import logging
import oss2
import yagmail
import datetime


from multizone.db import DBClient
default_id = '00000000000000000000000000000000'
ANSI_RESET = "\033[0m"
ANSI_RED = "\033[31m"
ANSI_GREEN = "\033[32m"
ANSI_YELLOW = "\033[33m"
ANSI_BLUE = "\033[34m"
from jinja2 import Template


# 获取当前日期
current_date = datetime.date.today()

class ErrorItem:
    def __init__(self,regionName,dbName,tableName,errType,errMsg):
        self.regionName = regionName
        self.dbName = dbName
        self.tableName = tableName
        self.errType = errType
        self.errMsg = errMsg

# 表格信息
class TableInfo:
    def __init__(self, regionName,dbName,tableName, tableType, tableCollation):
        self.regionName = regionName
        self.dbName = dbName
        self.tableName = tableName
        self.tableType = tableType
        self.tableCollation = tableCollation
        self.columnInfoList = {}
        self.indexNameToIndexInfo = {}
        self.msg = []

    def addNotSameInfo(self,errType, info):
        errItem = ErrorItem(self.regionName,self.dbName,self.tableName,errType, info)
        self.msg.append(errItem)

    def compareTableSame(self, otherTableInfo):
        self.tableEqual = True
        if self.tableName != otherTableInfo.tableName:
            self.tableEqual = False
            self.addNotSameInfo("程序错误",f"表名不同,不能比较")
            return
        if self.tableType != otherTableInfo.tableType:
            self.tableEqual = False
            self.addNotSameInfo( f"表类型不同",f"{self.tableType}->{otherTableInfo.tableType}")
        if self.tableCollation != otherTableInfo.tableCollation:
            self.tableEqual = False
            self.addNotSameInfo( f"表字符集不同",f"{self.tableCollation}->{otherTableInfo.tableCollation}")
        if len(self.columnInfoList) != len(otherTableInfo.columnInfoList):
            self.tableEqual = False
            self.addNotSameInfo(
                f"列数量不同:",
                f"{len(self.columnInfoList)}->{len(otherTableInfo.columnInfoList)}"
                f"请立即检查建表语句")
        for columnName, columnInfo in self.columnInfoList.items():
            otherColumnInfo = otherTableInfo.columnInfoList.get(columnName)
            if otherColumnInfo is None:
                self.tableEqual = False
                self.addNotSameInfo( f":列不存在",f"{columnName}")
                continue
            if not columnInfo.compareColumnInfoSame(otherColumnInfo):
                self.tableEqual = False
                self.msg.extend(columnInfo.msg)
        for indexName, indexInfo in self.indexNameToIndexInfo.items():
            otherIndexInfo = otherTableInfo.indexNameToIndexInfo.get(indexName)
            if otherIndexInfo is None:
                self.tableEqual = False
                self.addNotSameInfo( f"索引不存在",f"{indexName}")
                continue
            if not indexInfo.compareIndexInfoSame(otherIndexInfo):
                self.tableEqual = False
                self.msg.extend(indexInfo.msg)

    def addColumnInfo(self, columnInfo):
        self.columnInfoList[columnInfo.columnName] = columnInfo

    def getAndAddIndexInfo(self, regionName,dbName,tableName, indexName):
        indexInfo = self.indexNameToIndexInfo.get(indexName)
        if indexInfo is None:
            indexInfo = IndexInfo(regionName,dbName,tableName, indexName)
            self.indexNameToIndexInfo[indexName] = indexInfo
        return indexInfo


# 索引信息
class IndexInfo:
    def __init__(self, regionName,dbName,tableName, indexName):
        self.regionName = regionName
        self.dbName = dbName
        self.tableName = tableName
        self.indexName = indexName
        self.maxSeqInIndex = 0
        self.seqToName = {}
        self.msg= []

    def addIndexColumnName(self, columnName, seqInIndex):
        if self.maxSeqInIndex < seqInIndex:
            self.maxSeqInIndex = seqInIndex
        self.seqToName[seqInIndex] = columnName

    def compareIndexInfoSame(self, otherColumnInfo):
        self.indexEqual = True
        if self.maxSeqInIndex != otherColumnInfo.maxSeqInIndex:
            self.indexEqual = False
            self.addNotSameInfo( f"索引列数量不同", f"{self.indexName}:{self.maxSeqInIndex}->{otherColumnInfo.maxSeqInIndex}")

        for seqInIndex, columnName in self.seqToName.items():
            otherColumnName = otherColumnInfo.seqToName.get(seqInIndex)
            if otherColumnName is None:
                self.indexEqual = False
                self.addNotSameInfo( f"索引列不存在", f"{self.indexName}:{columnName}")

            if columnName != otherColumnName:
                self.indexEqual = False
                self.addNotSameInfo(
                    f"索引列名不同",
                    f"{ANSI_YELLOW}{self.indexName}:{columnName}->{otherColumnName}")
        return self.indexEqual

    def addNotSameInfo(self,errType, info):
        errItem = ErrorItem(self.regionName,self.dbName,self.tableName,errType, info)
        self.msg.append(errItem)


# 列信息
class ColumnInfo:
    def __init__(self, regionName,dbName,tableName,
                 columnName,
                 columnDefault,
                 isNullable,
                 dataType,
                 numPrecision,
                 numScale,
                 datetimePrecision,
                 characterSetName,
                 collationName,
                 columnType):
        self.regionName = regionName
        self.dbName = dbName
        self.tableName = tableName
        self.columnName = columnName
        self.columnDefault = columnDefault
        self.isNullable = isNullable
        self.dataType = dataType
        self.numPrecision = numPrecision
        self.numScale = numScale
        self.datetimePrecision = datetimePrecision
        self.characterSetName = characterSetName
        self.collationName = collationName
        self.columnType = columnType
        self.msg = []

    #这个表和otherColumnInfo表的列信息是否相同
    def compareColumnInfoSame(self, otherColumnInfo):
        self.columnEqual = True
        if self.columnName != otherColumnInfo.columnName:
            self.columnEqual = False
            self.addNotSameInfo("程序错误","列名不同")
        if self.columnDefault != otherColumnInfo.columnDefault:
            self.columnEqual = False
            self.addNotSameInfo(
                f"默认值不同",f"{self.columnName}:{self.columnDefault}->{otherColumnInfo.columnDefault}")
        if self.isNullable != otherColumnInfo.isNullable:
            self.columnEqual = False
            self.addNotSameInfo( f"是否可空不同",f"{self.columnName}:{self.isNullable}->{otherColumnInfo.isNullable}")
        if self.dataType != otherColumnInfo.dataType:
            self.columnEqual = False
            self.addNotSameInfo( f"数据类型不同", f"{self.columnName}:{self.dataType}->{otherColumnInfo.dataType}")

        if self.numPrecision != otherColumnInfo.numPrecision:
            self.columnEqual = False
            self.addNotSameInfo( f"数字精度不同", f"{self.columnName}:{self.numPrecision}->{otherColumnInfo.numPrecision}")
        if self.numScale != otherColumnInfo.numScale:
            self.columnEqual = False
            self.addNotSameInfo( f"数字范围不同", f"{self.columnName}:{self.numScale}->{otherColumnInfo.numScale}")
        if self.datetimePrecision != otherColumnInfo.datetimePrecision:
            self.columnEqual = False
            self.addNotSameInfo( f"时间精度不同", f"{self.columnName}:{self.datetimePrecision}->{otherColumnInfo.datetimePrecision}")
        if self.characterSetName != otherColumnInfo.characterSetName:
            self.columnEqual = False
            self.addNotSameInfo( f"列字符集不同", f"{self.columnName}:{self.characterSetName}->{otherColumnInfo.characterSetName}")
        if self.collationName != otherColumnInfo.collationName:
            self.columnEqual = False
            self.addNotSameInfo( f"列字符集不同", f"{self.columnName}:{self.collationName}->{otherColumnInfo.collationName}")
        if self.columnType != otherColumnInfo.columnType:
            self.columnEqual = False
            self.addNotSameInfo( f"列类型不同", f"{self.columnName}:{self.columnType}->{otherColumnInfo.columnType}")
        return self.columnEqual

    def addNotSameInfo(self,errType, info):
        errItem = ErrorItem(self.regionName,self.dbName,self.tableName,errType, info)
        self.msg.append(errItem)


class CheckDbSame(object):
    def __init__(self, zoneFrom, clusterFrom, dbFrom, zoneTo, clusterTo, dbTo):
        self.zoneTo = zoneTo
        self.zoneFrom = zoneFrom
        self.clusterFrom = clusterFrom
        self.clusterTo = clusterTo
        self.dbFrom = dbFrom
        self.dbTo = dbTo
        self.fromTableNameToTableInfo = {}
        self.toTableNameToTableInfo = {}

    def checkDiff(self):
        logging.info('''Start  Table:schema_name:{schema_name}'''.format(schema_name=self.dbFrom))
        self.checkDbFrom = DBClient(self.zoneFrom, self.clusterFrom, self.dbFrom, 'prod', True)
        self.checkDbTo = DBClient(self.zoneTo, self.clusterTo, self.dbTo, 'prod', False)
        self.__getTableDefine__(self.zoneFrom,self.dbFrom,self.checkDbFrom, self.dbFrom, self.fromTableNameToTableInfo)
        self.__getColumnDefine__(self.zoneFrom,self.dbFrom,self.checkDbFrom, self.dbFrom, self.fromTableNameToTableInfo)
        self.__getIndexDefine__(self.zoneFrom,self.dbFrom,self.checkDbFrom, self.dbFrom, self.fromTableNameToTableInfo)

        self.__getTableDefine__(self.zoneTo,self.dbTo,self.checkDbTo, self.dbTo, self.toTableNameToTableInfo)
        self.__getColumnDefine__(self.zoneTo,self.dbTo,self.checkDbTo, self.dbTo, self.toTableNameToTableInfo)
        self.__getIndexDefine__(self.zoneTo,self.dbTo,self.checkDbTo, self.dbTo, self.toTableNameToTableInfo)
        self.msg = []
        for tableName, tableInfo in self.fromTableNameToTableInfo.items():
            otherTableInfo = self.toTableNameToTableInfo.get(tableName)
            if otherTableInfo is None:
                tableInfo.tableEqual = False
                tableInfo.addNotSameInfo(
                    f"表不存在",
                    """表{tableName}在{zoneCmp}不存在""".format(zoneCmp=self.zoneTo, tableName=tableName))
                continue
            tableInfo.compareTableSame(otherTableInfo)
            if not tableInfo.tableEqual:
                self.msg.extend(tableInfo.msg)
        self.checkDbFrom.close()
        self.checkDbTo.close()
        logging.info('''Finish  Table:schema_name:{schema_name}'''.format(schema_name=self.dbFrom))

    def __getTableDefine__(self,regionName,dbName,dbClient, schema_name, tableNameToTableInfo):
        results_db = dbClient.fetchall(
            """SELECT TABLE_NAME, TABLE_TYPE, TABLE_COLLATION
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_SCHEMA = '{schemaName}';""".format(schemaName=schema_name))

        if not results_db:
            return
        # 遍历处理
        for row in results_db:
            tableInfo = TableInfo(regionName,dbName,row["TABLE_NAME"], row["TABLE_TYPE"], row["TABLE_COLLATION"])
            tableNameToTableInfo[row["TABLE_NAME"]] =  tableInfo

    def __getIndexDefine__(self, regionName,dbName,dbClient, schema_name, tableNameToTableInfo):
        results_db = dbClient.fetchall("""
            SELECT TABLE_NAME, INDEX_NAME, COLUMN_NAME, SEQ_IN_INDEX
            FROM INFORMATION_SCHEMA.STATISTICS
            WHERE TABLE_SCHEMA = '{schemaName}'
            ORDER BY INDEX_NAME, SEQ_IN_INDEX
                """.format(schemaName=schema_name))

        if not results_db:
            return
        # 遍历处理
        for row in results_db:
            tableInfo = tableNameToTableInfo.get(row['TABLE_NAME'])
            if tableInfo is None:
                logging.error("""{0}:tableInfo is None""".format(row['TABLE_NAME']))
                continue
            indexInfo = tableInfo.getAndAddIndexInfo(regionName,dbName,row['TABLE_NAME'], row['INDEX_NAME'])
            indexInfo.addIndexColumnName(row['COLUMN_NAME'], row['SEQ_IN_INDEX'])

    def __getColumnDefine__(self, regionName,dbName,dbClient, schema_name, tableNameToTableInfo):
        results_db = dbClient.fetchall("""
            SELECT TABLE_NAME,
                COLUMN_NAME,
                COLUMN_DEFAULT,
                IS_NULLABLE,
                DATA_TYPE,
                NUMERIC_PRECISION,
                NUMERIC_SCALE,
                DATETIME_PRECISION,
                CHARACTER_SET_NAME,
                COLLATION_NAME,
                COLUMN_TYPE
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_SCHEMA = '{schemaName}'
            ORDER BY TABLE_NAME,ORDINAL_POSITION;
                """.format(schemaName=schema_name))
        if not results_db:
            return
        # 遍历处理
        for row in results_db:
            tableInfo = tableNameToTableInfo.get(row['TABLE_NAME'])
            if tableInfo is None:
                logging.error("""{0}tableInfo is None""".format(row['TABLE_NAME']))
                continue
            columnInfo = ColumnInfo(regionName,dbName,row['TABLE_NAME'],
                                    row['COLUMN_NAME'],
                                    row['COLUMN_DEFAULT'],
                                    row['IS_NULLABLE'],
                                    row['DATA_TYPE'],
                                    row['NUMERIC_PRECISION'],
                                    row['NUMERIC_SCALE'],
                                    row['DATETIME_PRECISION'],
                                    row['CHARACTER_SET_NAME'],
                                    row['COLLATION_NAME'],
                                    row['COLUMN_TYPE']
                                    )
            tableInfo.addColumnInfo(columnInfo)


def main():
    mainCheckDbList = [

        CheckDbSame('Master', 'abc_cis_account_base', 'abc_cis_message', 'HangZhou', 'abc_cis_account_base', 'abc_cis_message'),
        CheckDbSame('Master', 'abc_cis_account_base', 'abc_cis_im', 'HangZhou', 'abc_cis_account_base', 'abc_cis_im'),
        CheckDbSame('Master', 'abc_cis_account_base', 'abc_cis_patient', 'HangZhou', 'abc_cis_account_base', 'abc_cis_patient'),
        CheckDbSame('Master', 'abc_cis_account_base', 'abc_cis_mc', 'HangZhou', 'abc_cis_account_base', 'abc_cis_mc'),
        CheckDbSame('Master', 'abc_cis_account_base', 'abc_cis_report', 'HangZhou', 'abc_cis_account_base', 'abc_cis_report'),

        CheckDbSame('Master', 'scrm_hospital', 'abc_his_advice', 'HangZhou', 'scrm_hospital', 'abc_his_advice'),
        CheckDbSame('Master', 'scrm_hospital', 'abc_his_charge', 'HangZhou', 'scrm_hospital', 'abc_his_charge'),
        CheckDbSame('Master', 'scrm_hospital', 'abc_his_emr', 'HangZhou', 'scrm_hospital', 'abc_his_emr'),
        CheckDbSame('Master', 'scrm_hospital', 'abc_his_ward', 'HangZhou', 'scrm_hospital', 'abc_his_ward'),
        CheckDbSame('Master', 'scrm_hospital', 'abc_scrm_basic', 'HangZhou', 'scrm_hospital', 'abc_scrm_basic'),
        CheckDbSame('Master', 'scrm_hospital', 'abc_scrm_channel', 'HangZhou', 'scrm_hospital', 'abc_scrm_channel'),
        CheckDbSame('Master', 'scrm_hospital', 'abc_scrm_customer', 'HangZhou', 'scrm_hospital', 'abc_scrm_customer'),
        CheckDbSame('Master', 'scrm_hospital', 'abc_scrm_kf', 'HangZhou', 'scrm_hospital', 'abc_scrm_kf'),
        CheckDbSame('Master', 'scrm_hospital', 'abc_scrm_material', 'HangZhou', 'scrm_hospital', 'abc_scrm_material'),
        CheckDbSame('Master', 'scrm_hospital', 'abc_pe_order', 'HangZhou', 'scrm_hospital', 'abc_pe_order'),
        CheckDbSame('Master', 'scrm_hospital', 'abc_pe_charge', 'HangZhou', 'scrm_hospital', 'abc_pe_charge'),

        CheckDbSame('Master', 'abc_cis_stat', 'abc_cis_supervision', 'HangZhou', 'abc_cis_stat', 'abc_cis_supervision'),

        CheckDbSame('Master', 'abc_cis_charge', 'abc_cis_promotion', 'HangZhou', 'abc_cis_charge', 'abc_cis_promotion'),
        CheckDbSame('Master', 'abc_cis_charge', 'abc_cis_charge', 'HangZhou', 'abc_cis_charge', 'abc_cis_charge'),
        CheckDbSame('Master', 'abc_cis_charge_record', 'abc_cis_charge_record', 'HangZhou', 'abc_cis_charge_record', 'abc_cis_charge_record'),

        CheckDbSame('Master', 'abc_cis_stock', 'abc_cis_goods', 'HangZhou', 'abc_cis_stock', 'abc_cis_goods'),
        CheckDbSame('Master', 'abc_cis_stock_zip', 'abc_cis_goods_log', 'HangZhou', 'abc_cis_stock_zip', 'abc_cis_goods_log'),
        CheckDbSame('Master', 'abc_cis_stock_zip', 'abc_cis_dispensing', 'HangZhou', 'abc_cis_stock_zip', 'abc_cis_dispensing'),
        CheckDbSame('Master', 'abc_cis_stock_zip', 'abc_cis_oss', 'HangZhou', 'abc_cis_stock_zip', 'abc_cis_oss'),
        CheckDbSame('Master', 'abc_cis_stock_zip', 'abc_cis_processing', 'HangZhou', 'abc_cis_stock_zip', 'abc_cis_processing'),

        CheckDbSame('Master', 'abc_cis_bill', 'abc_cis_wallet', 'HangZhou', 'abc_cis_bill', 'abc_cis_wallet'),
        CheckDbSame('Master', 'abc_cis_bill', 'abc_cis_shebao', 'HangZhou', 'abc_cis_bill', 'abc_cis_shebao'),
        CheckDbSame('Master', 'abc_cis_bill', 'abc_cis_invoice', 'HangZhou', 'abc_cis_bill', 'abc_cis_invoice'),
        CheckDbSame('Master', 'abc_cis_bill', 'abc_cis_wechatpay', 'HangZhou', 'abc_cis_bill', 'abc_cis_wechatpay'),
        CheckDbSame('Master', 'abc_cis_bill', 'abc_mp_charge_center', 'HangZhou', 'abc_cis_bill', 'abc_mp_charge_center'),

        CheckDbSame('Master', 'abc_cis_outpatient', 'abc_cis_registration', 'HangZhou', 'abc_cis_outpatient', 'abc_cis_registration'),
        CheckDbSame('Master', 'abc_cis_outpatient', 'abc_cis_consultation', 'HangZhou', 'abc_cis_outpatient', 'abc_cis_consultation'),
        CheckDbSame('Master', 'abc_cis_outpatient', 'abc_cis_nurse', 'HangZhou', 'abc_cis_outpatient', 'abc_cis_nurse'),
        CheckDbSame('Master', 'abc_cis_outpatient', 'abc_cis_examination', 'HangZhou', 'abc_cis_outpatient', 'abc_cis_examination'),
        CheckDbSame('Master', 'abc_cis_outpatient', 'abc_cis_patientorder', 'HangZhou', 'abc_cis_outpatient', 'abc_cis_patientorder'),
        CheckDbSame('Master', 'abc_cis_outpatient', 'abc_cis_outpatient', 'HangZhou', 'abc_cis_outpatient', 'abc_cis_outpatient'),
        CheckDbSame('Master', 'abc_cis_outpatient', 'abc_cis_medical_plan', 'HangZhou', 'abc_cis_outpatient', 'abc_cis_medical_plan')
    ]
    errList = []
    for checkDb in mainCheckDbList:
        checkDb.checkDiff()
        if checkDb.msg:
            errList.extend(checkDb.msg)

    return errList

def uploadFileToOss(subDir,ossFileName,htmlContent):
    # 填写从STS服务获取的临时访问密钥（AccessKey ID和AccessKey Secret）。
    sts_access_key_id = 'LTAI4G6HRsiB2yKkuA2vpS5F'
    sts_access_key_secret = '******************************'
    # 使用代码嵌入的RAM用户的访问密钥配置访问凭证。
    auth = oss2.Auth(sts_access_key_id, sts_access_key_secret)
    # yourEndpoint填写Bucket所在地域对应的Endpoint。以华东1（杭州）为例，Endpoint填写为https://oss-cn-hangzhou.aliyuncs.com。
    # 填写Bucket名称。
    bucket = oss2.Bucket(auth, 'oss-cn-chengdu.aliyuncs.com', 'cd-cis-static-assets-dev')

    # 上传文件。
    # 如果需要在上传文件时设置文件存储类型（x-oss-storage-class）和访问权限（x-oss-object-acl），请在put_object中设置相关Header。
    # headers = dict()
    # headers["x-oss-storage-class"] = "Standard"
    # headers["x-oss-object-acl"] = oss2.OBJECT_ACL_PRIVATE
    # 填写Object完整路径和字符串。Object完整路径中不能包含Bucket名称。
    result = bucket.put_object('back-end-monitor/'+subDir+'/'+ossFileName, htmlContent)
    #result = bucket.put_object_from_file('.txt', 'Hello OSS')


def genMail(errMsgs):
    # 加载模板文件
    # 然后可以使用相对路径来引用其他文件
    file_path = os.path.join(CURRENT_DIR, "../../mail/zone/zone_db_compare_jinja.html")
    with open(file_path) as file:
        template = Template(file.read())
    # 渲染HTML文件
    # 格式化日期为字符串
    currentDate= current_date.strftime("%Y-%m-%d")
    html = template.render(compareDate=currentDate, errMsgs = errMsgs)
    uploadFileToOss('multi-zone',f"db-check-{currentDate}.html",html)
    print(f"[{html}]")
    # 配置邮箱信息
    email = '<EMAIL>'
    password = 'Abc@20250523'
    smtp_server = 'smtp.exmail.qq.com'
    smtp_port = 465  # 使用SSL，端口号465

    # 初始化 yagmail 客户端
    yag = yagmail.SMTP(email, password, smtp_ssl=True, host='smtp.exmail.qq.com', port=465)
    # 配置 SMTP 服务器信息

    # 接收邮件设置
    # 邮件内容
    # to_email = '<EMAIL>'
    #to_email = '<EMAIL>'
    currentDate= current_date.strftime("%Y-%m-%d")
    subject = f'【后台日报】多分区DB差异检查-{currentDate}'
    to_receivers = ['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>']
    cc_receivers = ['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>']
    try:
        # 发送邮件
        sendResult = yag.send(to=to_receivers,cc=cc_receivers, subject=subject, contents=html)
        print(sendResult)
    except Exception as e:
        print(e)

    # 关闭 yagmail 客户端
    yag.close()


if __name__ == '__main__':
    genMail(main())
