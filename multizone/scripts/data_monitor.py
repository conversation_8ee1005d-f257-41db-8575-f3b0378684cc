#! /usr/bin/env python3
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 Yang <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.


import argparse
import logging
import os
import sys
import json
from datetime import date, datetime, timedelta

import requests

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.db import DBClient


def run(sql, title, op_type, env, client='ob', show_result=False):
    region_list = ["ShangHai", "HangZhou"] if env == 'prod' else ["ShangHai"]
    for region in region_list:
        db_cli = DBClient(region, client, 'abc_cis_basic', env, False)
        try:
            rows = db_cli.fetchall(sql)
        except Exception as e:
            logging.info(f"ob查询失败，尝试ods查询。{e}")
            # 用ods
            ods_db_cli = DBClient(region, 'ods', 'abc_cis_basic', env, False)
            rows = ods_db_cli.fetchall(sql)
        if len(rows) > 0:
            if show_result:
                logging.info(f"{title} : {rows}")
            # 告警
            requests.post(
                url='https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=aa7224d8-ea45-4e7e-ac8e-426cbcdd0135',
                json={
                    "msgtype": "markdown",
                    "markdown": {
                        "content": f"""[{title}]<font color=\"warning\">{len(rows)} rows</font>，请相关同事注意。 region: <font color=\"comment\">{region}</font>
                 """
                    }
                })


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--op-type', type=int, help='操作类型 0-查询 1-清理')
    parser.add_argument('--env', help='环境 dev/test/prod')
    parser.add_argument('--today', help='时间 yyyy-mm-dd', required=False)
    args = parser.parse_args()
    if (not args.op_type and args.op_type != 0) or not args.env:
        parser.print_help()
        sys.exit(-1)

    today = datetime.strptime(args.today, '%Y-%m-%d').date() if args.today is not None and args.today != 'None' else date.today()
    requests.post(
        url='https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=aa7224d8-ea45-4e7e-ac8e-426cbcdd0135',
        json={
            "msgtype": "markdown",
            "markdown": {
                "content": f"""异常数据监控开始，时间：[<font color=\"warning\">{today - timedelta(days=1)}</font>]"""
            }
        })

    # 入库条目和进销存数据异常
    run(sql=f"""
                select /*+ max_execution_time(600000)*/ i.package_count,
                   l.package_count,
                   i.piece_count,
                   l.piece_count,
                   i.package_cost_price,
                   l.package_cost_price,
                   i.total_cost,
                   l.stock_change_cost
                from abc_cis_goods.v2_goods_stock_in as i
                         inner join abc_cis_goods_log.v2_goods_stock_log as l
                                    on i.order_id = l.order_id and i.id = l.order_detail_id and i.chain_id = l.chain_id
                                        and l.created_date >= '{today - timedelta(days=1)} 00:00:00' #前一天
                                        and l.created_date <= '{datetime.now() - timedelta(hours=2)}' #前一天
                                        and l.action = '采购入库' #入库
                                        and (i.package_count * i.piece_num + i.piece_count !=
                                             l.package_count * i.piece_num + l.piece_count or
                                             i.package_cost_price != l.package_cost_price or i.total_cost != l.stock_change_cost) and  l.last_modified is null ;
    """,
        title='入库数量和进销存数据异常', op_type=args.op_type, env=args.env)

    # 入库单和入库条目的汇总数据异常
    run(sql=f"""
                select /*+ max_execution_time(600000)*/ *
                from (
                         select o.id,
                                o.kind_count,                                                                     #单据上的goods数量
                                count(distinct i.goods_id)                                        as inKindCount, #单据内goods数量
                                o.amount,                                                                         #单据上金额
                                sum(i.total_cost)                                                 as inAmount,    #单据内金额
                                o.amount_excluding_tax,                                                           #单据上含税金额
                                sum(i.total_cost / (1 + IFNULL(JSON_EXTRACT(goods, '$.inTaxRat'),0) / 100)) as inAmountEx   #单据内含税金额
                         from abc_cis_goods.v2_goods_stock_in_order as o
                                  inner join abc_cis_goods.v2_goods_stock_in as i on i.order_id = o.id and i.chain_id = o.chain_id
                         where o.created_date >= '{today - timedelta(days=1)} 00:00:00'
                                        and o.created_date <= '{datetime.now() - timedelta(hours=2)}' #前一天
                           and o.status = 2
                           and i.status = 1
                         group by o.id) as t
                where kind_count != inKindCount
                   or abs(amount - inAmount) >= 1
                   or abs(inAmountEx - amount_excluding_tax) > 1;
    """,
        title='入库单和入库条目的汇总数据异常', op_type=args.op_type, env=args.env)
    run(sql=f"""
    select CONCAT('update v2_goods_stock_trans_order set amount=', i.total_cost, ' where id=', o.id, ';')
from abc_cis_goods.v2_goods_stock_trans_order as o
         inner join (select order_id, sum(total_cost) as total_cost
                     from abc_cis_goods.v2_goods_stock_trans
                     where status = 1
                       and created_date >=  '{today - timedelta(days=1)} 00:00:00'
                     group by order_id) as i on o.id = i.order_id
where o.created_date >= '{today - timedelta(days=1)} 00:00:00'
  and abs(o.amount - i.total_cost) >0.1
  and o.id = i.order_id
    """,
        title='调拨单和调拨条目的汇总数据异常', op_type=args.op_type, env=args.env)

    run(sql=f"""
    select  * from abc_cis_goods.v2_goods where compose_flag & 128 > 0 and status = 1 ;
    """,
        title='修改入库单导致失败导致无法入库的药品数量', op_type=args.op_type, env=args.env)

    run(sql=f"""
    select
       CONCAT('update v2_goods_stock set left_cost= ', abs((package_count + piece_count / piece_num) * package_cost_price),' where id =',id,';')
from abc_cis_goods.v2_goods_stock
where pharmacy_type = 0
  and package_cost_price > 0.1
  and abs((package_count + piece_count / piece_num) * package_cost_price -
          left_cost) > 1 and left_cost > 0;
    """,
        title='规格修改而导致的库存量增加导致的成本增加', op_type=args.op_type, env=args.env)
    run(sql=f"""
    select
       CONCAT('update v2_goods_stock set left_cost= ', abs((package_count + piece_count / piece_num) * package_cost_price),' where id =',id,';')
from abc_cis_goods.v2_goods_stock
where pharmacy_type = 0
  and package_cost_price > 0.1
  and abs((package_count + piece_count / piece_num) * package_cost_price -
          left_cost) > 1 and left_cost < 0;
    """,
        title='修改入库单并发导致修正两次成本变成负数', op_type=args.op_type, env=args.env)
    # 发药单和 item 上的药房号不一致
    # run(sql=f"""
    #                 select /*+ max_execution_time(600000)*/ * from abc_cis_dispensing.v2_dispensing_sheet s
    #                 join abc_cis_dispensing.v2_dispensing_form_item i on s.chain_id = i.chain_id and s.clinic_id = i.clinic_id and s.id = i.dispensing_sheet_id
    #                 where s.pharmacy_no != i.pharmacy_no and s.status = 0
    #                 and s.created >= '{today - timedelta(days=1)} 00:00:00' #前一天
    #                                     and s.created <= '{datetime.now() - timedelta(hours=2)}' #前一天
    #
    #     """,
    #     title='发药单和 item 上的药房号不一致', op_type=args.op_type, env=args.env)

    # 医嘱开药的发药未解锁数据异常
# #     run(sql=f"""
# #                 select /*+ max_execution_time(600000)*/ l.id, l.lock_id, l.goods_id, l.locking_piece_num, l.locking_package_count, l.locking_piece_count, l.lock_left_total_piece_count, l.locking_total_piece_count,
# #                    l.locking_order_id, f.advice_rule_id, f.advice_execute_id, l.status, s.id,s.status, s.created, s.last_modified,l.created, l.clinic_id
# #                 from abc_cis_goods.v2_goods_stock_locking l
# #                 join abc_cis_dispensing.v2_dispensing_form f on l.chain_id = f.chain_id and f.type = 10 and f.created >= '{today - timedelta(days=1)} 00:00:00' and l.clinic_id = f.clinic_id and l.locking_order_id =
# #                     case when f.advice_execute_id is not null then concat(f.advice_rule_id, '_', f.advice_execute_id)
# #                         else concat(f.advice_rule_id, '_NoRun') end
# #                 join abc_cis_dispensing.v2_dispensing_sheet s on f.clinic_id = s.clinic_id and f.dispensing_sheet_id = s.id and s.type = 10 and s.created >= '{today - timedelta(days=1)} 00:00:00'
# # #                 join abc_cis_basic.organ o on o.id = l.clinic_id and o.his_type = 100
# #                 where s.status in (1, 3)
# #                 and l.type = 20 and l.status in (0, 10)
# #                 and s.chain_id in ('ffffffff0000000034a0dfc65dc64002','ffffffff00000000348415b623ee0000')
# #                 and l.created >= '{today - timedelta(days=1)} 00:00:00' #前一天
# #     """,
#         title='医嘱开药的发药未解锁数据异常', op_type=args.op_type, env=args.env)
    run(sql=f"""
                select  distinct s.organ_id
                from abc_cis_goods.v2_goods_clinic_config as c
                         inner join abc_cis_goods.v2_goods_chain_config v2gcc
                                    on c.chain_id = v2gcc.chain_id and v2gcc.view_mode = 1 and c.view_mode = 0
                
                inner join  abc_cis_goods.v2_goods_stock as s on c.chain_id = s.chain_id and c.clinic_id = s.organ_id
                where s.package_count != 0 or s.piece_count != 0
    """,
        title='连锁降级单店有门店未清理库存', op_type=args.op_type, env=args.env)

    run(sql=f"""
                select g.id
                from abc_cis_goods.v2_goods as g
                    inner join abc_cis_goods.v2_goods_chain_config as c on g.organ_id = c.chain_id and c.view_mode = 0 and c.node_type =1
                    left join abc_cis_goods.v2_goods_compose_opt as o
                              on g.organ_id = o.chain_id and (o.clinic_id ='' or o.clinic_id is null) and g.id = o.parent_goods_id and o.compose_type = 10 and o.is_deleted = 0
                where  o.chain_id is null
                  and g.status = 1
                  and g.fee_compose_type = 20 and c.his_type != 100;
    """,
        title='总部goodsFeedComposeType20,总部无feeCompose', op_type=args.op_type, env=args.env)
    run(sql=f"""
                select g.id
                from abc_cis_goods.v2_goods as g
                         inner join abc_cis_goods.v2_goods_clinic_config as c on g.organ_id = c.chain_id and c.view_mode = 1 and c.node_type = 2
                         left join abc_cis_goods.v2_goods_compose_opt as o
                                   on g.organ_id = o.chain_id and o.clinic_id = c.clinic_id and g.id = o.parent_goods_id and
                                      o.compose_type = 10 and o.is_deleted = 0
                where o.chain_id is null
                  and g.status = 1
                  and g.fee_compose_type = 20 and c.his_type != 100;
    """,
        title='单店goodsFeedComposeType20,无feeCompose', op_type=args.op_type, env=args.env)
    run(sql=f"""
        select count(*) from abc_cis_goods.v2_goods_cowork_stock_check_task where status = 10 and type = 1
        and task_count != finish_count and created >= '{today - timedelta(days=1)} 00:00:00';
    """, title='盘点总任务数与完成任务数不等', op_type=args.op_type, env=args.env)

    run(sql=f"""
                select distinct b.task_id from abc_cis_goods.v2_goods_cowork_stock_check_task a
                join abc_cis_goods.v2_goods_cowork_stock_check_job b on a.task_id = b.task_id and a.type = 1 and a.status = 10
                where a.created >= '{today - timedelta(days=1)} 00:00:00'
                group by b.clinic_id, b.goods_id having count(*) > 1
    """, title='盘点主任务重复药品', op_type=args.op_type, env=args.env)

    run(sql=f"""
                select * from (
                    select a.task_id, sum(b.piece_count + (b.package_count * b.piece_num)) main_total_count from abc_cis_goods.v2_goods_cowork_stock_check_task a
                    join abc_cis_goods.v2_goods_cowork_stock_check_job b on a.clinic_id = b.clinic_id and  a.task_id = b.task_id
                    where a.type = 1 and a.status = 10 and a.created >= '{today - timedelta(days=1)} 00:00:00'
                    group by a.task_id) m
                    join (
                    select a.task_id, sum(b.piece_count + (b.package_count * b.piece_num)) sub_total_count from abc_cis_goods.v2_goods_cowork_stock_check_task a
                    join abc_cis_goods.v2_goods_cowork_stock_check_job b on a.clinic_id = b.clinic_id and  a.task_id = b.parent_task_id and a.task_id != b.task_id
                    where a.type = 1 and a.status = 10 and a.created >= '{today - timedelta(days=1)} 00:00:00'
                    group by a.task_id) s on m.task_id = s.task_id
                    where m.main_total_count != s.sub_total_count
    """, title='盘点主任务药品数量与子任务药品数量和不等', op_type=args.op_type, env=args.env)
    run(sql="""
                select order_id, goods_id, batch_id, count(*)
                from abc_cis_goods.v2_goods_stock_out_draft
                where is_deleted = 0
                group by order_id, goods_id, batch_id
                having count(*) > 1
    """, title="出库单草稿明细重复", op_type=args.op_type, env=args.env)
    run(sql="""
                select order_id, goods_id, stock_id, count(*)
                from abc_cis_goods.v2_goods_stock_trans_draft
                where is_deleted = 0
                group by order_id, goods_id, stock_id
                having count(*) > 1
        """, title="调拨单草稿明细重复", op_type=args.op_type, env=args.env)
    run(sql="""
                select order_id, goods_id, batch_id, count(*)
                from abc_cis_goods.v2_goods_stock_check_draft
                where is_deleted = 0
                group by order_id, goods_id, batch_id
                having count(*) > 1
        """, title="盘点单草稿明细重复", op_type=args.op_type, env=args.env)

    run(sql=f"""
                select *
                from abc_cis_dispensing.v2_dispensing_sheet b
                where b.status = 0
                  and b.chain_id in (select distinct parent_id from abc_cis_basic.organ where his_type = 10)
                  and b.created >= '{today - timedelta(days=1)} 00:00:00';
    """, title='药店发药单异常', op_type=args.op_type, env=args.env)

    run(sql=f"""
                select * from abc_cis_goods.v2_goods_stock_locking where type = 30 and status in (0, 10) and created < '{today - timedelta(days=90)} 00:00:00'
    """, title='锁库超90天未解', op_type=args.op_type, env=args.env)

    # run(sql=f"""
    #     select /*+ max_execution_time(600000)*/ t.*,
    #                                     pcie.value as examOnlyExecuteAfterPaid,
    #                                     pcii.value as inspectOnlyExecuteAfterPaid
    #     from (select e.charge_form_item_id,
    #                  e.clinic_id,
    #                  cfi.product_sub_type                   as examType,
    #                  cfi.status                             as chargeFormItemStatus,
    #                  cfi.unit_count,
    #                  cfi.refund_unit_count,
    #                  cfi.unit_count - cfi.refund_unit_count as remaining_count,
    #                  count(if(e.status = 2, 1, null))       as refund_count,
    #                  count(if(e.status != 2, 1, null))      as normal_count
    #           from abc_cis_examination.v2_examination_sheet e
    #                    inner join abc_cis_charge.v2_charge_form_item cfi
    #                               on e.charge_form_item_id = cfi.id and cfi.product_type = 3 and
    #                                  cfi.is_deleted = 0 and
    #                                  cfi.status != 2 and cfi.status != 3 and
    #                                  cfi.created >= '{today - timedelta(days=1)} 00:00:00' and
    #                                  cfi.created <= '{today - timedelta(days=1)} 23:59:59'
    #                    inner join abc_cis_charge.v2_charge_form cf
    #                               on cf.id = cfi.charge_form_id and cf.is_deleted = 0 and cf.status != 2 and
    #                                  cfi.created >= '{today - timedelta(days=1)} 00:00:00' and
    #                                  cfi.created <= '{today - timedelta(days=1)} 23:59:59'
    #                    inner join abc_cis_charge.v2_charge_sheet c
    #                               on c.id = cfi.charge_sheet_id and c.is_deleted = 0 and c.is_draft = 0 and c.is_closed = 0 and
    #                                  c.status != 4 and
    #                                  cfi.created >= '{today - timedelta(days=1)} 00:00:00' and
    #                                  cfi.created <= '{today - timedelta(days=1)} 23:59:59'
    #           group by e.charge_form_item_id) as t
    #              left join abc_cis_property.v2_property_config_item pcie on pcie.v2_scope_id = t.clinic_id and pcie.`key` = 'examination.settings.examine.onlyExecuteAfterPaid'
    #              left join abc_cis_property.v2_property_config_item pcii on pcii.v2_scope_id = t.clinic_id and pcii.`key` = 'examination.settings.inspect.onlyExecuteAfterPaid'
    #     where remaining_count > normal_count
    #       and not (remaining_count > 100 and normal_count = 100)
    #       and not (chargeFormItemStatus = 0 and examType = 1 and pcie.value = 1)
    #       and not (chargeFormItemStatus = 0 and examType = 2 and pcii.value = 1);
    # """, title="检查检验单数量与收费项数量不等", op_type=args.op_type, env=args.env, show_result=True)

    run(sql=f"""
            select *
            from abc_cis_goods.v2_goods_examination_device
            where goods_type = 3
              and goods_sub_type = 1
              and status = 10
              and device_parameters is not null
              and (json_extract(device_parameters, '$.mac') is null
                or json_extract(device_parameters, '$.mac') = '')
              and last_modified >= '{today - timedelta(days=1)} 00:00:00';
        """, title='检验设备mac地址被异常清空', op_type=args.op_type, env=args.env, client='ods')

    run(sql=f"""
    select g.chain_id, g.clinic_id, g.parent_goods_id, g.goods_id
    from abc_cis_goods.v2_goods_compose_opt g
             inner join abc_cis_basic.organ o on o.id = g.chain_id and o.node_type = 1 and o.view_mode = 0
    where compose_type = 0
      and is_deleted = 0
      and g.clinic_id is not null
      and g.clinic_id != '';
    """, title='goods_compose_opt[套餐/组合项]连锁 clinicId不为null', op_type=args.op_type, env=args.env)

    run(sql="""
    select g.chain_id, g.clinic_id, g.parent_goods_id, g.goods_id
    from abc_cis_goods.v2_goods_compose_opt g
             inner join abc_cis_basic.organ co on co.parent_id = g.chain_id and co.node_type = 2 and co.view_mode = 1
    where compose_type = 0
      and is_deleted = 0
      and (g.clinic_id is null or g.clinic_id = '' or g.clinic_id != co.id);
    """, title='goods_compose_opt[套餐/组合项]单店 clinicId异常', op_type=args.op_type, env=args.env)
