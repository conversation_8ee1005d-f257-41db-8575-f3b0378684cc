import math
import os
import sys
import requests
import json
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import oss2
from io import BytesIO
from base64 import b64encode
import hashlib
import numpy as np
import pandas as pd
from datetime import date, time, datetime, timedelta

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei', 'Songti SC', 'STFangsong']
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.db import DBClient
# from multizone.log import AliyunLogClient
import logging
from scripts.common.utils.attr_helper import AttrHelper as attr_helper
from scripts.common.utils.lists import ListUtils as list_utils

logger = logging#AliyunLogClient('Master', 'prod').logger

department_id_to_team = {
    '10': '后台研发中心',
    '234': '后台研发中心',
    '235': '后台研发中心',
    '11': '前端团队',
    '12': '终端团队|医保团队',
    '9': '统计团队',
    '49': '产品团队',
    '50': '产品团队',
    '52': '产品团队',
    '69': '产品团队',
    '116': '产品团队',
    '35': '技术支持一组',
    '36': '技术支持二组',
    '37': '工具及数据开发',
    '53': '测试团队'
}

team_bot_key = {
    "终端团队|医保团队": "c4b51a48-471a-4668-a5d8-b6f15f46f8b9",
    "前端团队": "2aaaf400-285d-4273-8cdf-2e4338124bb8",
    "后端团队": "31cf3d2c-b696-4f12-9aab-eebe57fda812",
    "统计团队": "b4125d1b-58ee-4400-b506-b9ddbd2b2967",
    "后台研发中心": "c11ea4eb-3ae7-4831-a55d-e9e57c81b7f4",
    "后台研发中心/数据分析组": "c11ea4eb-3ae7-4831-a55d-e9e57c81b7f4",
    "后台研发中心/业务后台组": "c11ea4eb-3ae7-4831-a55d-e9e57c81b7f4"
}


def findbugs(begin_date, end_date, filter: dict = None):
    bugs = []
    offset = 1
    limit = 200
    for i in range(20):
        rsp_body = json.loads(
            requests.get(url='https://api.tapd.cn/bugs',
                         auth=('EQqn1nGs', '3278700F-171A-EA3F-A796-06A84A5CF780'),
                         params={
                             'workspace_id': '43780818',
                             'page': offset,
                             'limit': limit,
                             'created': f'{begin_date}~{end_date}'
                         }).content.decode("utf-8"))
        if not rsp_body['data']:
            break
        bugs.extend(rsp_body['data'])
        offset += 1
    if len(bugs) == 0:
        return bugs
    ret_bugs = []
    for bug in bugs:
        if filter is not None and attr_helper.getattr(bug, filter['attrs']) != filter['value']:
            continue
        ret_bugs.append(bug)
    logger.info(f'offset: {offset}, limit: {limit}, bugs_total: {len(bugs)}, ret_bugs_total: {len(ret_bugs)}')
    return ret_bugs


def draw_bugs_stat():
    created_to_bugs = list_utils.group_by(team_bugs,
                                          lambda e: datetime.strptime(e['Bug']['created'], "%Y-%m-%d %H:%M:%S").date())
    x = list(created_to_bugs.keys())
    x.sort()
    y = [len(created_to_bugs[date]) for date in x]
    # 绘制折线图
    # plt.plot(x, y, color='red')
    # plt.scatter(x, y)
    plt.bar(x, y, alpha=0.8)
    # plt.pie(y, labels=x, autopct='%1.1f%%', shadow=True, startangle=90)
    # 添加标题和标签
    plt.title(f'【backend team】online bug statistics {begin_date} ~ {end_date}')
    plt.xlabel('date')
    plt.ylabel('bugs')
    # 显示平均值、最大值、最小值、标准差、方差
    # 找出最大值对应的下表
    max_value = max(y)
    min_value = min(y)
    avg_value = sum(y) / len(x)
    std_value = np.std(y)
    var_value = np.var(y)

    plt.axhline(y=avg_value, color='magenta', linestyle='--')
    plt.axhline(y=max_value, color='red', linestyle='--')
    plt.axhline(y=min_value, color='green', linestyle='--')
    plt.axhline(y=std_value, color='black', linestyle='--')
    plt.axhline(y=var_value, color='blue', linestyle='--')
    plt.text(x[0], y[y.index(round(avg_value))], f'avg: {avg_value: .2f}', color='magenta', ha='center', va='bottom')
    plt.text(x[5], y[y.index(round(max_value))], f'max: {max_value: .2f}', color='red', ha='center', va='bottom')
    plt.text(x[10], y[y.index(round(min_value))], f'min: {min_value: .2f}', color='green', ha='center', va='bottom')
    plt.text(x[15], y[y.index(round(std_value))], f'std: {std_value: .2f}', color='black', ha='center', va='bottom')
    plt.text(x[20], y[y.index(round(var_value))], f'var: {var_value: .2f}', color='blue', ha='center', va='bottom')

    # 设置日期格式
    date_format = mdates.DateFormatter('%m-%d')
    plt.gca().xaxis.set_major_formatter(date_format)
    # 调整刻度间隔
    plt.gca().xaxis.set_major_locator(mdates.DayLocator(interval=1))
    # 调整日期刻度尺寸
    # 显示图表
    # 旋转日期标签，并间隔显示
    plt.xticks(rotation=60)
    plt.tight_layout()  # 自动调整子图参数，防止标签重叠
    # plt.show()
    # 将图表保存为 BytesIO 对象
    buffer = BytesIO()
    plt.savefig(buffer, format='png')
    buffer.seek(0)
    buffer_value = buffer.read()
    # 获取buffer的md5值
    md5 = hashlib.md5(buffer_value).hexdigest()
    base64_str = b64encode(buffer_value).decode('utf-8')
    return {
        'md5': md5,
        'base64': base64_str
    }

    # 上传到oss
    # accessKeyId = 'LTAI4FgYhPMqQmb4uWe9CqdB'
    # accessKeySecret = '******************************'
    # # 使用代码嵌入的RAM用户的访问密钥配置访问凭证。
    # auth = oss2.Auth(accessKeyId, accessKeySecret)
    # # 填写Bucket所在地域对应的Endpoint。以华东1（杭州）为例，Endpoint填写为https://oss-cn-hangzhou.aliyuncs.com。
    # bucket = oss2.Bucket(auth, 'https://oss-cn-chengdu.aliyuncs.com', 'cd-cis-static-assets')
    # bucket.put_object(f'online_bugs/{team}/{begin_date}_{end_date}.png', buffer)
    # return f'https://cd-cis-static-assets.oss-cn-chengdu.aliyuncs.com/online_bugs/{team}/{begin_date}_{end_date}.png'


def run_online_bugs_stat():
    global begin_date, end_date, bug, team_bugs, content, image
    ods_client = DBClient('ShangHai', 'ods', 'bp', 'prod')
    today = date.today()  # - timedelta(days=2)
    employees = ods_client.fetchall("""select u.userid, u.name, group_concat(d.id separator '|') as department_ids
                            from qw_user u
                                     inner join qw_user_department ud on u.userid = ud.userid
                                     inner join qw_department d on d.id = ud.departmentid
                            group by u.userid;""")
    employee_name_to_employee = list_utils.to_map(employees, lambda e: e['name'])
    begin_date = today - timedelta(days=30)  # datetime.strptime('2024-06-01', '%Y-%m-%d')
    end_date = today
    bugs = findbugs(begin_date=begin_date, end_date=end_date)
    team_to_bugs = {}
    team_to_other_bugs = list_utils.group_by(bugs, lambda e: e['Bug']['custom_field_10'] if e['Bug'][
        'custom_field_10'] else '未分配')
    for bug in bugs:
        department_ids = employee_name_to_employee.get(
            bug['Bug']['fixer'] if bug['Bug']['fixer'] else bug['Bug']['current_owner'], {}).get('department_ids',
                                                                                                 '').split('|')
        teams = list_utils.dist_mapping(department_ids, lambda e: department_id_to_team.get(str(e),
                                                                                            bug['Bug']['fixer'] if
                                                                                            bug['Bug'][
                                                                                                'fixer'] else
                                                                                            bug['Bug'][
                                                                                                'current_owner'] if
                                                                                            bug['Bug'][
                                                                                                'current_owner'] else '未分配'))
        for team in teams:
            if team not in team_to_bugs:
                team_to_bugs[team] = []
            team_to_bugs[team].append(bug)
    for team, team_bugs in team_to_bugs.items():
        logger.info(f'【{team}】: {len(team_bugs)}')
        if team != '后台研发中心':
            continue
        fixer_bug_rows = generate_fixer_bugs(team_bugs)
        other_fixer_bug_rows = generate_fixer_bugs([bug for bug in team_to_other_bugs.get(team, []) if
                                                    bug['Bug']['fixer'] and bug['Bug']['id'] not in [e['Bug']['id'] for
                                                                                                     e in team_bugs]])
        title = f'【{team}】线上BUG统计{begin_date} ~ {end_date}'
        content = f"""
**<font color="warning">【{team}】线上BUG(<font color="red">total: {len(team_bugs)}, resolve: {len([b for b in team_bugs if b['Bug']['fixer']])}</font>)统计{begin_date} ~ {end_date}</font>**
{''.join([f'''> {fixer_row['name']}: <font color="red">{fixer_row['bug_count']}</font>
{os.linesep.join([f"- 【{module_row['name']}】: <font color='red'>{module_row['bug_count']}</font>" for module_row in fixer_row['module_bugs']])}
''' for fixer_row in fixer_bug_rows])}
        """
        logger.info(content)

        # 将数据存储到Excel文件中
        # 定义表头
        headers = ['时间', '修复人', '问题定性', '所属团队', '所属模块', '耗时', 'bug标题', 'tapd链接', 'bug原因', '是否存在脏数据', '脏数据是否处理',
                   '脏数据修复sql', '是否需要加入监控',
                   '监控sql']
        excel_rows = []
        for fixer_row in [*fixer_bug_rows, *other_fixer_bug_rows]:
            for module_row in fixer_row['module_bugs']:
                for bug in module_row['bugs']:
                    resolved = datetime.strptime(bug['Bug']['resolved'], "%Y-%m-%d %H:%M:%S").date() if bug['Bug'][
                        'resolved'] else None
                    if resolved == today or resolved == today - timedelta(days=1):
                        excel_rows.append([
                            date.strftime(resolved, "%Y-%m-%d") if resolved else '',
                            fixer_row['name'],
                            bug['Bug']['custom_field_two'],
                            bug['Bug']['custom_field_10'],
                            '',
                            '',
                            bug['Bug']['title'],
                            f"https://www.tapd.cn/43780818/bugtrace/bugs/view?bug_id={bug['Bug']['id']}",
                            '',
                            '',
                            '',
                            '',
                            '',
                            ''
                        ])

        # 告警
        team_bot_url = f'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key={team_bot_key[team]}'
        requests.post(
            url=team_bot_url,
            json={
                "msgtype": "markdown",
                "markdown": {
                    "content": content
                }
            })

        # 绘制图表
        image = draw_bugs_stat()
        requests.post(
            url=team_bot_url,
            json={
                "msgtype": "image",
                "image": image
            })
        if len(excel_rows) > 0:
            excel_rows.sort(
                key=lambda e: datetime.strptime(e[0], '%Y-%m-%d') if e[0] else datetime.strptime('1900-01-01',
                                                                                                 '%Y-%m-%d'))
            df = pd.DataFrame(excel_rows, columns=headers)
            # 设置固定列宽
            pd.set_option('display.max_colwidth', 100)
            doc_name = f"【{team}】线上BUG跟进{date.strftime(today, '%Y-%m-%d')}"
            filename = f"{doc_name}.xlsx"
            df.to_excel(filename, index=False)
            today_excel_rows = [row for row in excel_rows if (
                datetime.strptime(row[0], '%Y-%m-%d') if row[0] else datetime.strptime('1900-01-01',
                                                                                       '%Y-%m-%d')).date() == today]
            if len(today_excel_rows) > 0:
                pass
                # requests.post(
                #     url=team_bot_url,
                #     json={
                #         "msgtype": "text",
                #         "text": {
                #             "content": f"{filename}已生成，请上传到在线文档，今日需更新文档成员：{'、'.join(list(set([f'''<@{employee_name_to_employee.get(row[1])['userid']}>''' for row in today_excel_rows])))}"
                #         }
                #     })

            # # 上传文件
            # media_id = json.loads(
            #     requests.post(
            #         url=f'https://qyapi.weixin.qq.com/cgi-bin/webhook/upload_media?key={team_bot_key[team]}&type=file',
            #         files={
            #             'media': open(filename, 'rb')
            #         }
            #     ).content.decode('utf-8')
            # )['media_id']
            # # 推文件
            # requests.post(
            #     url=team_bot_url,
            #     json={
            #         "msgtype": "file",
            #         "file": {
            #             "media_id": media_id
            #         }
            #
            # 企业微信创建文档
            # qw_create_doc(doc_name, excel_rows, team_bot_url)


def generate_fixer_bugs(team_bugs):
    fixer_to_bugs = list_utils.group_by(team_bugs, lambda e: e['Bug']['fixer'] if e['Bug']['fixer'] else '未分配')
    fixer_bug_rows = []
    for fixer, fixer_bugs in fixer_to_bugs.items():
        module_to_bugs = list_utils.group_by(fixer_bugs,
                                             lambda e: e['Bug']['module'] if e['Bug']['module'] else '未分配')
        module_bug_rows = []
        for module, module_bugs in module_to_bugs.items():
            module_bug_rows.append({
                'name': module,
                'bug_count': len(module_bugs),
                'bugs': module_bugs
            })
        module_bug_rows.sort(key=lambda e: e['bug_count'], reverse=True)
        fixer_bug_rows.append({
            'name': fixer,
            'bug_count': len(fixer_bugs),
            'module_bugs': module_bug_rows
        })
    fixer_bug_rows.sort(key=lambda e: e['bug_count'], reverse=True)
    return fixer_bug_rows


def qw_create_doc(doc_name, excel_rows, team_bot_url):
    # corpid = 'wwc8c6faf2e6dd0142'
    # corpsecret = 'Ms4F_mh0nKvURRW0dHeJzFm288QJjuUvSOcrdywutxo'
    # 获取企业微信access_token
    corpid = '1970324942065258'
    corpsecret = 'QXgSKbS4Zf4qB-Fkq-JwA60aozLqq9ZDjVK-RFPkoIs'
    # access_token = json.loads(
    #     requests.get(
    #         url=f'https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={corpid}&corpsecret={corpsecret}'
    #     ).content.decode('utf-8')
    # )['access_token']
    access_token = 'g7MDUs7X1boBDWvz3WB7mlgtwfb7G2ZGiD7YFXLNf8CMo53l7pvCVoFLLGzVRuB5UqEjpWss_hcmFaZyse9k5ytTBDHntPWLWcsokpV8QlfIJo7g4qlkS34hcvODsHvlo0ucAW9TPy52z-ytpkBUPkZNz1VJBxMgJTxGBZl0MqmvWjLkMuym8xwuj1bDT2MjQdahEdkKCr-DJJFW1hmpTg'
    logger.info(f'access_token: {access_token}')
    # # 创建文档
    # doc = json.loads(
    #     requests.post(
    #         url=f'https://qyapi.weixin.qq.com/cgi-bin/wedoc/create_doc?access_token={access_token}',
    #         json={
    #             # "spaceid": "SPACEID",
    #             # "fatherid": "FATHERID",
    #             "doc_type": "4",
    #             "doc_name": doc_name,
    #             "admin_users": ['JiangXiaoFeng']
    #             # [employee['userid'] for employee in employees if list_utils.dist_mapping(employee['department_ids'].split('|'), lambda e: department_id_to_team.get(str(e), 'None')) == team],
    #         }
    #     ).content.decode('utf-8')
    # )
    # # doc = {"url": "https://doc.weixin.qq.com/sheet/e3_ADgAbBR8ABUOQPkKRZ1RRSDDnQpCV_a?scode=AMAAOAdDAHIK1hM1VwADgAbBR8ABU", "docid": "dcAdRAJ2B92mteG6rj3bj8cMpadpoUyWcVBWNMFMJ3ZYVH1Q3On3MESuNAtr9VabJAQElqde41-e9R0298BV5ieA", "errcode": 0, "errmsg": "ok"}
    doc = {"docid": "e3_AUkA7QZ7AEgcJgW1qbwSOi4ZOpv9i"}
    logger.info(f'doc: {json.dumps(doc, ensure_ascii=False)}')
    # 获取
    document = json.loads(
        requests.post(
            url=f'https://qyapi.weixin.qq.com/cgi-bin/wedoc/spreadsheet/get_sheet_properties?access_token={access_token}',
            json={
                "docid": doc['docid']
            }
        ).content.decode('utf-8')
    )
    logger.info(f'document: {json.dumps(document, ensure_ascii=False)}')
    # # 修改文档
    # update_response = json.loads(
    #     requests.post(
    #         url=f'https://qyapi.weixin.qq.com/cgi-bin/wedoc/spreadsheet/batch_update?access_token={access_token}',
    #         json={
    #             "docid": doc['docid'],
    #             "requests": [
    #                 {
    #                     "update_range_request": {
    #                         "sheet_id": document['properties'][0]['sheet_id'],
    #                         "grid_data": {
    #                             "start_row": 0,
    #                             "start_column": 0,
    #                             "rows": [
    #                                 {
    #                                     "values": [
    #                                         {
    #                                             "cell_value": {
    #                                                 "text": cell
    #                                             },
    #                                             "cell_format": {
    #                                                 "font": "Courier New",
    #                                                 "font_size": 14,
    #                                                 "bold": False if row_idx != 0 else True,
    #                                                 "italic": False,
    #                                                 "strikethrough": False,
    #                                                 "underline": False,
    #                                                 "color": {
    #                                                     "red": 0,
    #                                                     "green": 0,
    #                                                     "blue": 255,
    #                                                     "alpha": 255
    #                                                 }
    #                                             }
    #                                         } for cell in excel_rows[row_idx]
    #                                     ]
    #                                 } for row_idx in range(len(excel_rows))
    #                             ]
    #                         }
    #                     }
    #                 }
    #             ]
    #         }
    #     ).content.decode('utf-8')
    # )
    # logger.info(f'update_response: {json.dumps(update_response, ensure_ascii=False)}')
    # 分析链接
    # requests.post(
    #     url=team_bot_url,
    #     json={
    #         "msgtype": "text",
    #         "text": {
    #             "content": doc['url']
    #         }
    #     })


if __name__ == '__main__':
    run_online_bugs_stat()
    # qw_create_doc('doc_name', 'excel_rows', 'team_bot_url')
