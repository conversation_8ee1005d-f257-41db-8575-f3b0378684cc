# 备份 数据到另外一个中间表里面
# 病历表+处方表
import argparse
import os
import sys
from datetime import date, timedelta, datetime
import json
from decimal import Decimal

import requests

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))

from multizone.db import DBClient, escape_string

def escape_value(value):
    """
    安全地转义SQL值，根据值的类型进行适当处理
    
    Args:
        value: 任何类型的值
        
    Returns:
        格式化并转义后的SQL值字符串
    """
    if value is None:
        return 'NULL'
    elif isinstance(value, (int, float, Decimal)):
        return str(value)
    elif isinstance(value, bool):
        return '1' if value else '0'
    elif isinstance(value, str):
        # 使用现有的escape_string函数处理字符串
        escaped = escape_string(value)
        return f"'{escaped}'"
    else:
        # 对于其他类型，转换为字符串并转义
        escaped = escape_string(str(value))
        return f"'{escaped}'"

default_id = '00000000000000000000000000000000'

outpatient_sheet_sql = '''
select id, doctor_id, department_id, patient_id
from v2_outpatient_sheet
where last_modified >= '{beginDate}'
  and last_modified < '{endDate}'
  and chain_id = '{chainId}'
  and status = 1 and is_deleted = 0
'''

patient_sql = '''
select id, name, is_member, sex, sn, 
cast(AES_DECRYPT(FROM_BASE64(id_card_cipher), md5("d081bc67-b1a0-4d64-b58c-65e4dea69bfb")) as char) as id_card_cipher, 
cast(AES_DECRYPT(FROM_BASE64(mobile_cipher), md5("d081bc67-b1a0-4d64-b58c-65e4dea69bfb")) as char) as mobile_cipher, 
id_card_last6, mobile_last4, birthday
from v2_patient
where id in ({ids})
'''

employee_sql = '''
select id, name
from employee
where id in ({ids})
'''

department_sql = '''
select id, name
from department
where id in ({ids})
'''

goods_sql = '''
select a.id, a.name, a.type_id, b.name as type_id_name
from v2_goods a
         inner join v2_goods_sys_type b on a.type_id = b.id
where a.id in ({ids});
'''

medical_record_backup_sql = '''
select id,
       outpatient_sheet_id,
       patient_id,
       department_id,
       doctor_id,
       chief_complaint,
       past_history,
       allergic_history,
       family_history,
       personal_history,
       physical_examination,
       diagnosis,
       doctor_advice,
       present_history,
       syndrome,
       therapy,
       chinese_examination,
       is_deleted,
       created,
       created_by,
       last_modified_by,
       last_modified,
       type,
       birth_history,
       oral_examination,
       extend_data,
       epidemiological_history,
       obstetrical_history,
       dentistry_extend,
       extend_diagnosis_infos,
       auxiliary_examinations,
       wear_glasses_history,
       eye_examination,
       target,
       prognosis,
       symptom_time,
       syndrome_treatment,
       json_extract(extend_data, '$.chinesePrescription') as chinese_prescription,
       json_extract(dentistry_extend, '$.disposals')      as disposals
from v2_outpatient_medical_record where outpatient_sheet_id in ({ids}) and chain_id = '{chainId}' and is_deleted = 0
'''

product_form_item_backup_sql = '''
select id,
       patient_order_id,
       clinic_id,
       outpatient_sheet_id,
       product_form_id,
       product_id,
       name,
       unit_count,
       expected_unit_price,
       source_unit_price,
       unit,
       unit_price,
       fraction_price,
       cost_unit_price,
       is_dismounting,
       type,
       sub_type,
       compose_type,
       compose_parent_form_item_id,
       sort,
       is_deleted,
       created,
       created_by,
       last_modified_by,
       last_modified,
       days,
       daily_dosage,
       extend_data,
       tooth_nos,
       expected_total_price,
       form_flat_price,
       sheet_flat_price,
       is_unit_price_changed,
       is_total_price_changed,
       total_price,
       current_unit_price,
       pharmacy_no,
       pharmacy_type,
       fee_compose_type,
       fee_type_id,
       goods_fee_type,
       total_price_ratio,
       expected_total_price_ratio,
       unit_adjustment_fee,
       lock_id,
       source_total_price,
       json_extract(extend_data, '$.freq')   as freq,
       json_extract(extend_data, '$.remark') as remark,
       json_extract(extend_data, '$.nurse_id') as nurse_id,
       json_extract(extend_data, '$.department_id') as department_id
from v2_outpatient_product_form_item
where outpatient_sheet_id in ({ids}) and is_deleted = 0
'''

prescription_form_backup_sql = '''
select id,
       qrid,
       patient_order_id,
       outpatient_sheet_id,
       patient_id,
       chain_id,
       clinic_id,
       department_id,
       doctor_id,
       display_num,
       type,
       specification,
       dose_count,
       daily_dosage,
       `usage`,
       freq,
       requirement,
       usage_level,
       sort,
       is_deleted,
       created,
       created_by,
       last_modified_by,
       last_modified,
       contact_mobile,
       is_decoction,
       process_usage,
       process_bag_unit_count,
       usage_type,
       usage_sub_type,
       source,
       usage_scope_id,
       vendor_id,
       expected_total_price,
       medicine_state_scope_id,
       pharmacy_type,
       vendor_name,
       process_price,
       usage_days,
       ingredient_price,
       pharmacy_no,
       is_total_price_changed,
       total_price,
       finished_rate,
       finished_rate_min,
       psychotropic_narcotic_type,
       process_bag_unit_count_decimal,
       process_remark,
       total_process_count,
       vendor_usage_scope_id,
       process_bag_unit,
       optometrist_id,
       glasses_type,
       glasses_params
from v2_outpatient_prescription_form
where outpatient_sheet_id in ({ids}) and is_deleted = 0
'''

prescription_form_item_backup_sql = '''
select id,
       patient_order_id,
       clinic_id,
       outpatient_sheet_id,
       prescription_form_id,
       goods_id,
       domain_medicine_id,
       type,
       sub_type,
       compose_type,
       compose_parent_form_item_id,
       medicine_cadn,
       name,
       specification,
       manufacturer,
       group_id,
       ast,
       ast_result,
       `usage`,
       ivgtt,
       ivgtt_unit,
       freq,
       dosage,
       dosage_unit,
       days,
       special_requirement,
       dismounting,
       unit_count,
       expected_unit_price,
       unit,
       unit_price,
       cost_unit_price,
       sort,
       is_deleted,
       created,
       created_by,
       last_modified_by,
       last_modified,
       source_unit_price, 
       fraction_price,
       extend_data,
       expected_total_price,
       charge_type,
       form_flat_price,
       sheet_flat_price,
       is_unit_price_changed,
       is_total_price_changed,
       total_price,
       current_unit_price,
       executed_total_count,
       key_id,
       pharmacy_no,
       pharmacy_type,
       external_unit_count,
       total_price_ratio,
       expected_total_price_ratio,
       unit_adjustment_fee,
       lock_id,
       source_total_price
from v2_outpatient_prescription_form_item
where outpatient_sheet_id in ({ids})
  and is_deleted = 0;
'''

pharmacy_sql = '''
select id, name, no, clinic_id
from v2_goods_pharmacy
where type = 0 and chain_id = '{chainId}';
'''

organ_name_sql = '''
select name
from organ
where id = '{chain_id}';
'''

registration_sql = '''
select id,
       patient_id,
       concat(reserve_date, ' ', reserve_start, '~', reserve_end) as reserve_date,
       doctor_id,
       doctor_name,
       department_id,
       department_name,
       fee,
       status_v2  as status,
       created
from v2_registration_form_item
where last_modified >= '{beginDate}'
  and last_modified < '{endDate}'
  and chain_id = '{chainId}' and registration_type != 10
  and status_v2 != 10
  and status_v2 != 91
  and status_v2 != 92
  and is_deleted = 0;
'''

schedule_sql = '''
select a.id,
       a.employee_id,
       c.name as employee_name,
       a.department_id,
       d.name as department_name,
       a.working_date,
       b.name,
       b.start,
       b.end,
       a.service_num,
       f.name as consulting_room_name,
       if(a.status = 10, '停诊', '正常') as status,
       a.created
from abc_cis_registration.v2_registration_schedule a
         inner join abc_cis_registration.v2_organ_shift_config b on a.shift_id = b.id
         inner join abc_cis_basic.v2_clinic_chain_employee c on a.employee_id = c.employee_id
         inner join abc_cis_basic.department d on a.department_id = d.id
         inner join abc_cis_registration.consulting_room f on f.id = a.consulting_room_id
where a.registration_type = 0 
    and a.working_date >= '{beginDate}'
    and a.working_date < '{endDate}'
    and a.chain_id = '{chainId}'
    and a.is_deleted = 0
    group by a.id;
'''

class UpdateData:
    def __init__(self, region_name, chain_id, env, begin_date, end_date):
        self.chain_id = chain_id
        self.region_name = region_name
        self.ob_client = DBClient(self.region_name, 'ob', 'abc_cis_outpatient', env, True)
        self.outpatient_record_client = DBClient(self.region_name, 'ob', 'abc_cis_outpatient_record', env, True)
        self.goods_client = DBClient(self.region_name, 'ob', 'abc_cis_goods', env, True)
        self.patient_client = DBClient(self.region_name, 'ob', 'abc_cis_patient', env, True)
        self.basic_client = DBClient(self.region_name, 'ob', 'abc_cis_basic', env, True)
        self.registration_client = DBClient(self.region_name, 'ob', 'abc_cis_registration', env, True)

        # 备份client
        self.outpatient_backup_client = DBClient(self.region_name, 'outpatient_external_backup', 'abc_cis_outpatient_external_backup', env,True)
        today = date.today() - timedelta(days=2)
        if begin_date and end_date:
            self.beginDate = begin_date
            self.endDate = end_date
        else:
            self.beginDate = today
            self.endDate = today + timedelta(days=2)
            
        # 统计每个表的插入或更新行数
        self.stats = {
            'v2_registration': 0,
            'v2_outpatient_medical_record': 0,
            'v2_outpatient_product_form_item': 0,
            'v2_outpatient_prescription_form': 0,
            'v2_outpatient_prescription_form_item': 0
        }
        # 记录SQL执行失败的统计
        self.failed_stats = {
            'v2_registration': 0,
            'v2_outpatient_medical_record': 0,
            'v2_outpatient_product_form_item': 0,
            'v2_outpatient_prescription_form': 0,
            'v2_outpatient_prescription_form_item': 0,
        }

    def process_sheets(self, sheets, pharmacies_map):
        outpatientSheetIds = [sheet['id'] for sheet in sheets]

        # Format outpatientSheetIds for SQL query
        formatted_outpatientSheetIds = ','.join(f"'{id}'" for id in outpatientSheetIds)

        # 根据门诊单id查询病历数据
        medicalRecords = self.outpatient_record_client.fetchall(
            medical_record_backup_sql.format(ids=formatted_outpatientSheetIds, chainId=self.chain_id))

        # Fetch and map patient information
        patients_map = {}
        patientIds = list(set(sheet['patient_id'] for sheet in sheets if sheet.get('patient_id')))
        if len(patientIds) > 0:
            formatted_patientIds = ','.join(f"'{id}'" for id in patientIds)
            patients = self.patient_client.fetchall(patient_sql.format(ids=formatted_patientIds))
            patients_map = {patient['id']: patient for patient in patients}

        # Remove duplicates from doctorIds and departmentIds
        employee_map = {}
        doctorIds = list(set(sheet['doctor_id'] for sheet in sheets if sheet.get('doctor_id')))
        if len(doctorIds) >= 1:
            formatted_doctorIds = ','.join(f"'{id}'" for id in doctorIds)
            employees = self.basic_client.fetchall(employee_sql.format(ids=formatted_doctorIds))
            employee_map = {employee['id']: employee for employee in employees}

        # Fetch and map department information
        department_map = {}
        departmentIds = list(set(sheet['department_id'] for sheet in sheets))
        if len(departmentIds) >= 1:
            formatted_departmentIds = ','.join(f"'{id}'" for id in departmentIds)
            departments = self.basic_client.fetchall(department_sql.format(ids=formatted_departmentIds))
            department_map = {department['id']: department for department in departments}

        # 写病历
        self.insertOrUpdateMedicalData(medicalRecords, patients_map, employee_map, department_map)
        print("medicalRecords: " + str(len(medicalRecords)))

        # 根据门诊单id查询诊疗项目
        productFormItems = self.outpatient_record_client.fetchall(product_form_item_backup_sql.format(ids=formatted_outpatientSheetIds))
        self.insertOrUpdateProductFormItems(productFormItems)
        print("productFormItems: " + str(len(productFormItems)))

        # fetch prescription_from info
        prescriptionForms = self.ob_client.fetchall(prescription_form_backup_sql.format(ids=formatted_outpatientSheetIds))
        self.insertOrUpdatePrescriptionForms(prescriptionForms, pharmacies_map)
        print("prescriptionForms: " + str(len(prescriptionForms)))

        # 根据门诊单id查询处方明细数据
        prescriptionFormItems = self.outpatient_record_client.fetchall(prescription_form_item_backup_sql.format(ids=formatted_outpatientSheetIds))
        self.insertOrUpdatePrescriptionFormItems(prescriptionFormItems, pharmacies_map)
        print("prescriptionFormItems: " + str(len(prescriptionFormItems)))

    def run(self):
        # 通过sql查出来，在写入备份数据
        # 每3天同步一次数据

        # 生成pharmaciesMap key= clinic_id + pharmay_no value pharmacy
        pharmacies = self.goods_client.fetchall(pharmacy_sql.format(chainId=self.chain_id))
        pharmacies_map = {pharmacy['clinic_id'] + str(pharmacy['no']): pharmacy for pharmacy in pharmacies}
        
        current = self.beginDate
        while current < self.endDate:
            chunk_end = min(current + timedelta(days=1), self.endDate)
            
            # 更新处理日期范围
            start_str = current.strftime('%Y-%m-%d')
            end_str = chunk_end.strftime('%Y-%m-%d')

            # 设置解码算法
            self.patient_client.execute("SET block_encryption_mode = 'aes-256-ecb'")
            
            print(f"\n处理时间段: {start_str} 到 {end_str}")
            # 备份挂号
            self.process_registration_sheets(start_str, end_str)
            # 备份门诊
            self.process_outpatient_sheets(start_str, end_str, pharmacies_map)
            # 备份schedule
            self.process_schedule(start_str, end_str)
            
            current = chunk_end

    def process_schedule(self, start_str, end_str):
        schedules = self.registration_client.fetchall(schedule_sql.format(chainId=self.chain_id, beginDate=start_str, endDate=end_str))
        self.insertOrUpdateScheduleData(schedules)
        print("schedule: " + str(len(schedules)))

    def insertOrUpdateScheduleData(self, schedules):
        if not schedules or len(schedules) == 0:
            return
        self.execute_sql_update('v2_registration_schedule', schedules)      

    def execute_sql(self, sql, table_name=None):
        """执行SQL并打印错误信息"""
        try:
            result = self.outpatient_backup_client.execute(sql)
            # 如果指定了table_name，更新统计信息
            if table_name and table_name in self.stats:
                # 数据库客户端返回的是受影响的行数（整数），直接使用这个值
                affected_rows = result if isinstance(result, int) else getattr(result, 'rowcount', 0)
                self.stats[table_name] += affected_rows
            return result
        except Exception as e:
            print(f"\nSQL执行出错:")
            print(f"SQL: {sql}")
            print(f"错误信息: {str(e)}")
            if table_name and table_name in self.failed_stats:
                self.failed_stats[table_name] += 1
            # 仍然抛出异常，让调用者决定如何处理
            # raise

    def insertOrUpdateProductFormItems(self, productFormItems):
        if not productFormItems or len(productFormItems) == 0:
            return 0

        # Fetch and map employee information
        nurseIds = list(set(formItem['nurse_id'] for formItem in productFormItems if formItem.get('nurse_id')))
        employee_map = {}
        if len(nurseIds) >= 1:
            formatted_doctorIds = ','.join(f"'{id}'" for id in nurseIds)
            employees = self.basic_client.fetchall(employee_sql.format(ids=formatted_doctorIds))
            employee_map = {employee['id']: employee for employee in employees}

        goods_map = {}
        goodsIds = list(set(formItem['product_id'] for formItem in productFormItems if formItem.get('product_id')))
        if len(goodsIds) >= 1:
            formatted_goodsIds = ','.join(f"'{id}'" for id in goodsIds)
            # fetch goods info
            goods = self.goods_client.fetchall(goods_sql.format(ids=formatted_goodsIds))
            goods_map = {good['id']: good for good in goods}

        # Fetch and map department information
        department_map = {}
        departmentIds = list(
            set(formItem['department_id'] for formItem in productFormItems if formItem.get('department_id')))
        if len(departmentIds) >= 1:
            formatted_departmentIds = ','.join(f"'{id}'" for id in departmentIds)
            departments = self.basic_client.fetchall(department_sql.format(ids=formatted_departmentIds))
            department_map = {department['id']: department for department in departments}

        batch_data = []
        # Iterate over each product form item
        for item in productFormItems:
            # Extract nurse and department information using the maps
            nurse_info = employee_map.get(item['nurse_id'])
            department_info = department_map.get(item['department_id'])

            # Debug logging for goods data
            product_id = item.get('product_id')
            if not product_id or product_id not in goods_map:
                print(f"No goods data found for product_id {product_id}")

            values = {
                'id': escape_value(item['id']),
                'outpatient_sheet_id': escape_value(item['outpatient_sheet_id']),
                'name': escape_value(item['name']),
                'unit_count': escape_value(item['unit_count']),
                'unit': escape_value(item['unit']),
                'unit_price': escape_value(item['unit_price']),
                'type_id': escape_value(goods_map[item['product_id']]['type_id']) if item.get('product_id') and goods_map.get(
                    item['product_id']) is not None else 'NULL',
                'type_name': escape_value(goods_map[item['product_id']]['type_id_name']) if item.get(
                    'product_id') and goods_map.get(item['product_id']) is not None else 'NULL',
                'days': escape_value(item['days']),
                'daily_dosage': escape_value(item['daily_dosage']),
                'doctor_name': escape_value(nurse_info['name']) if nurse_info is not None else 'NULL',
                'department_name': escape_value(department_info['name']) if department_info is not None else 'NULL',
                'freq': escape_value(item['freq']),
                'created': escape_value(item['created'])
            }
            # Add to batch
            batch_data.append(values)

        # Execute the SQL query
        self.execute_sql_update('v2_outpatient_product_form_item', batch_data)

    def insertOrUpdatePrescriptionForms(self, prescriptionForms, pharmacies_map):
        if not prescriptionForms or len(prescriptionForms) == 0:
            return 0

        batch_data = []
        
        # Iterate over each prescription form
        for item in prescriptionForms:
            pharmacy_key = item.get('clinic_id', '') + str(item.get('pharmacy_no', ''))
            pharmacy = pharmacies_map.get(pharmacy_key, {})
            pharmacy_name = pharmacy.get('name', None)

            # Handle NULL values and escape strings
            values = {
                'id': escape_value(item['id']),
                'outpatient_sheet_id': escape_value(item['outpatient_sheet_id']),
                'type': escape_value(item['type']),
                'specification': escape_value(item['specification']),
                'dose_count': escape_value(item['dose_count']),
                'daily_dosage': escape_value(item['daily_dosage']),
                'usage': escape_value(item['usage']),
                'freq': escape_value(item['freq']),
                'requirement': escape_value(item['requirement']),
                'usage_level': escape_value(item['usage_level']),
                'usage_days': escape_value(item['usage_days']),
                'sort': escape_value(item['sort']),
                'pharmacy_name': escape_value(pharmacy_name),
                'created': escape_value(item['created'])
            }
            
            # Add to batch
            batch_data.append(values)

        # Execute the SQL query
        self.execute_sql_update('v2_outpatient_prescription_form', batch_data)

    def execute_sql_update(self, table_name, batch_data):
        # Process data in batches
        if not batch_data:
            return

        # Construct column names for the SQL statement
        columns = list(batch_data[0].keys())
        column_str = ", ".join([f"`{col}`" for col in columns])

        # Construct the VALUES part with multiple value sets
        values_list = []
        for data_item in batch_data:
            value_set = f"({', '.join([escape_value(data_item[col]) for col in columns])})"
            values_list.append(value_set)

        values_str = ",\n            ".join(values_list)

        # Construct the UPDATE part that updates all fields
        update_clauses = [f"`{col}` = VALUES(`{col}`)" for col in columns]
        update_str = ", ".join(update_clauses)

        # Construct the SQL query for batch operation as a single statement
        sql = f"""
        INSERT INTO {table_name} (
            {column_str}
        ) VALUES 
            {values_str}
        ON DUPLICATE KEY UPDATE 
            {update_str}
        """

        # Execute the SQL query
        self.execute_sql(sql, table_name)


    def insertOrUpdatePrescriptionFormItems(self, prescriptionFormItems, pharmacies_map):
        if not prescriptionFormItems or len(prescriptionFormItems) == 0:
            return 0

        batch_data = []
        
        goods_map = {}
        goodsIds = list(set(formItem['product_id'] for formItem in prescriptionFormItems if formItem.get('product_id')))
        if len(goodsIds) >= 1:
            formatted_goodsIds = ','.join(f"'{id}'" for id in goodsIds)
            # fetch goods info
            goods = self.goods_client.fetchall(goods_sql.format(ids=formatted_goodsIds))
            goods_map = {good['id']: good for good in goods}
        
        # Iterate over each prescription form item
        for item in prescriptionFormItems:
            # Get pharmacy name from map using clinic_id and pharmacy_no
            pharmacy_key = item.get('clinic_id', '') + str(item.get('pharmacy_no', ''))
            pharmacy = pharmacies_map.get(pharmacy_key, {})
            pharmacy_name = pharmacy.get('name', None)

            goods_key = item.get('goods_id', '')
            goods = goods_map.get(goods_key, {})
            if goods is not None:
                type_name = goods.get('type_name', None)
                type_id = goods.get('type_id', None)
            else:
                type_name = None
                type_id = None

            # Handle NULL values and escape strings
            values = {
                'id': escape_value(item['id']),
                'outpatient_sheet_id': escape_value(item['outpatient_sheet_id']),
                'prescription_form_id': escape_value(item['prescription_form_id']),
                'type_id': escape_value(type_id),
                'type_name': escape_value(type_name),
                'medicine_cadn': escape_value(item['medicine_cadn']),
                'name': escape_value(item['name']),
                'specification': escape_value(item['specification']),
                'manufacturer': escape_value(item['manufacturer']),
                'group_id': escape_value(item['group_id']),
                'usage': escape_value(item['usage']),
                'ivgtt': escape_value(item['ivgtt']),
                'ivgtt_unit': escape_value(item['ivgtt_unit']),
                'freq': escape_value(item['freq']),
                'dosage': escape_value(item['dosage']),
                'dosage_unit': escape_value(item['dosage_unit']),
                'days': escape_value(item['days']),
                'special_requirement': escape_value(item['special_requirement']),
                'unit_count': escape_value(item['unit_count']),
                'unit': escape_value(item['unit']),
                'unit_price': escape_value(item['unit_price']),
                'sort': escape_value(item['sort']),
                'pharmacy_name': escape_value(pharmacy_name),
                'created': escape_value(item['created'])
            }
            
            batch_data.append(values)

        self.execute_sql_update('v2_outpatient_prescription_form_item', batch_data)

    def insertOrUpdateRegistrationData(self, registration_sheets):
        """
        Insert or update registration data into v2_registration_backup table
        :param registration_sheets: List of registration data to backup
        """
        if not registration_sheets:
            return

        patients_map = {}
        patientIds = list(set(sheet['patient_id'] for sheet in registration_sheets if sheet.get('patient_id')))
        if len(patientIds) > 0:
            formatted_patientIds = ','.join(f"'{id}'" for id in patientIds)
            patients = self.patient_client.fetchall(patient_sql.format(ids=formatted_patientIds))
            patients_map = {patient['id']: patient for patient in patients}

        # Prepare values for batch insert/update
        values = []
        for sheet in registration_sheets:
            patient = patients_map.get(sheet.get('patient_id'), {})
            # Use patient info if available, otherwise fallback to sheet data
            patient_name = patient.get('name') if patient else sheet.get('patient_name')
            patient_mobile = patient.get('mobile_cipher') if patient else sheet.get('patient_mobile')
            patient_sex = patient.get('sex') if patient else sheet.get('patient_sex')

            def format_value(val, escape=False):
                if val is None or val == '':
                    return 'NULL'
                return f"'{escape_string(val)}'" if escape else f"'{val}'"

            def format_numeric(val, default=0):
                if val is None:
                    return 'NULL'
                return str(val)

            value = (
                f"({format_value(sheet.get('id'))}, "
                f"{format_value(sheet.get('patient_id'))}, "
                f"{format_value(patient_name, True)}, "
                f"{format_value(patient_mobile)}, "
                f"{format_value(patient_sex)}, "
                f"{format_value(sheet.get('reserve_date'))}, "
                f"{format_value(sheet.get('doctor_id'))}, "
                f"{format_value(sheet.get('doctor_name'), True)}, "
                f"{format_value(sheet.get('department_id'))}, "
                f"{format_value(sheet.get('department_name'), True)}, "
                f"{format_numeric(sheet.get('fee'))}, "
                f"{format_numeric(sheet.get('status'))}, "
                f"{format_value(sheet.get('created'))})"
            )
            values.append(value)

        # Construct the insert/update SQL
        sql = f"""
        INSERT INTO v2_registration (
            `id`, `patient_id`, `patient_name`, `patient_mobile`, `patient_sex`,
            `reserve_date`, `doctor_id`, `doctor_name`, `department_id`, `department_name`,
            `fee`, `status`, `created`
        ) VALUES {','.join(values)}
        ON DUPLICATE KEY UPDATE
            `patient_id` = VALUES(`patient_id`),
            `patient_name` = VALUES(`patient_name`),
            `patient_mobile` = VALUES(`patient_mobile`),
            `patient_sex` = VALUES(`patient_sex`),
            `reserve_date` = VALUES(`reserve_date`),
            `doctor_id` = VALUES(`doctor_id`),
            `doctor_name` = VALUES(`doctor_name`),
            `department_id` = VALUES(`department_id`),
            `department_name` = VALUES(`department_name`),
            `fee` = VALUES(`fee`),
            `status` = VALUES(`status`),
            `created` = VALUES(`created`)
        """

        # print("execute sql: " + sql)

        # Execute the SQL
        self.execute_sql(sql, 'v2_registration')

    def insertOrUpdateMedicalData(self, medicalRecords, patients_map, employee_map, department_map):
        if not medicalRecords:
            return

        # Create a mapping of outpatient_sheet_id to the corresponding sheet
        outpatient_sheets_map = {}
        
        # Find all outpatient sheet IDs from medical records
        outpatient_sheet_ids = set(record['outpatient_sheet_id'] for record in medicalRecords if record.get('outpatient_sheet_id'))
        
        # For each outpatient sheet ID, fetch the corresponding sheet
        if outpatient_sheet_ids:
            formatted_outpatient_sheet_ids = ','.join(f"'{id}'" for id in outpatient_sheet_ids)
            sheets = self.ob_client.fetchall(
                f"SELECT id, doctor_id, department_id FROM v2_outpatient_sheet WHERE id IN ({formatted_outpatient_sheet_ids})"
            )
            outpatient_sheets_map = {sheet['id']: sheet for sheet in sheets}

        # Prepare batch data for insertion
        batch_data = []
        
        # Iterate over each medical record
        for record in medicalRecords:
            # First look for the associated outpatient sheet
            outpatient_sheet = outpatient_sheets_map.get(record.get('outpatient_sheet_id'))
            
            # Use doctor_id and department_id from the sheet if available, otherwise try the record
            doctor_id = (outpatient_sheet or {}).get('doctor_id') or record.get('doctor_id')
            department_id = (outpatient_sheet or {}).get('department_id') or record.get('department_id')
            
            # Extract patient and doctor information using the maps
            patient_info = patients_map.get(record['patient_id'])
            doctor_info = employee_map.get(doctor_id)
            department_info = department_map.get(department_id)
            
            diagnosis = convert_extend_diagnosis_infos(record["extend_diagnosis_infos"]) if record[
                "extend_diagnosis_infos"] else ""

            data = {
                'id': escape_value(record['id']),
                'outpatient_sheet_id': escape_value(record['outpatient_sheet_id']),
                'patient_id': escape_value(record['patient_id']),
                'patient_name': escape_value(patient_info['name']) if patient_info and patient_info.get(
                    'name') is not None else 'NULL',
                'patient_mobile': escape_value(patient_info['mobile_cipher']) if patient_info and patient_info.get(
                    'mobile_cipher') is not None else 'NULL',
                'patient_sex': escape_value(patient_info['sex']) if patient_info and patient_info.get(
                    'sex') is not None else 'NULL',
                'patient_birthday': escape_value(patient_info['birthday']) if patient_info and patient_info.get(
                    'birthday') is not None else 'NULL',
                'patient_id_card': escape_value(patient_info['id_card_cipher']) if patient_info and patient_info.get(
                    'id_card_cipher') is not None else 'NULL',
                'department_name': escape_value(department_info['name']) if department_info and department_info.get(
                    'name') is not None else 'NULL',
                'doctor_name': escape_value(doctor_info['name']) if doctor_info and doctor_info.get(
                    'name') is not None else 'NULL',
                'chief_complaint': escape_value(record['chief_complaint']),
                'past_history': escape_value(record['past_history']),
                'allergic_history': escape_value(record['allergic_history']),
                'family_history': escape_value(record['family_history']),
                'personal_history': escape_value(record['personal_history']),
                'physical_examination': escape_value(record['physical_examination']),
                'diagnosis': escape_value(diagnosis),
                'doctor_advice': escape_value(record['doctor_advice']),
                'present_history': escape_value(record['present_history']),
                'syndrome': escape_value(record['syndrome']),
                'therapy': escape_value(record['therapy']),
                'chinese_examination': escape_value(record['chinese_examination']),
                'birth_history': escape_value(record['birth_history']),
                'epidemiological_history': escape_value(record['epidemiological_history']),
                'obstetrical_history': escape_value(record['obstetrical_history']),
                'auxiliary_examinations': escape_value(record['auxiliary_examinations']),
                'target': escape_value(record['target']),
                'prognosis': escape_value(record['prognosis']),
                'symptom_time': escape_value(record['symptom_time']),
                'syndrome_treatment': escape_value(record['syndrome_treatment']),
                'chinese_prescription': escape_value(record['chinese_prescription']),
                'disposals': escape_value(record['disposals']),
                'created': escape_value(record['created'])
            }
            
            batch_data.append(data)

        self.execute_sql_update('v2_outpatient_medical_record', batch_data)
        # Execute the single batch SQL
        print("medicalRecords: " + str(len(batch_data)))


    def process_registration_sheets(self, start, end):
        registration_sheets = self.registration_client.fetchall(
            registration_sql.format(beginDate=start, endDate=end, chainId=self.chain_id))
        self.insertOrUpdateRegistrationData(registration_sheets)
        print("registration_sheets: " + str(len(registration_sheets)))


    def process_outpatient_sheets(self, start, end, pharmacies_map):
        sheets = self.ob_client.fetchall(
            outpatient_sheet_sql.format(beginDate=start, endDate=end, chainId=self.chain_id))
    
        print("outpatient_sheets: " + str(len(sheets)))

        # Process in batches of 100
        batch_size = 100
        for i in range(0, len(sheets), batch_size):
            batch = sheets[i:i + batch_size]
            print(str(i) + " process_outpatient_sheets: " + str(len(batch)))
            self.process_sheets(batch, pharmacies_map)


def convert_extend_diagnosis_infos(extend_diagnosis_infos):
    if not extend_diagnosis_infos:
        return ""

    try:
        # Parse the JSON string
        diagnosis_list = json.loads(extend_diagnosis_infos)

        # Extract names from the value field
        diagnosis_info = '；'.join(
            filter(None, (
                '，'.join(
                    filter(None, (info.get('name') for info in diagnosis.get('value', []) if info.get('name')))
                )
                for diagnosis in diagnosis_list
                if diagnosis.get('value')
            ))
        )
        return diagnosis_info
    except json.JSONDecodeError:
        return ""


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--chain-id', help='连锁id')
    parser.add_argument('--region-name', help='分区名字可直接查配置')
    parser.add_argument('--env', help='环境')
    parser.add_argument('--begin-date', help='开始时间', default='')
    parser.add_argument('--end-date', help='结束时间', default='')
    args = parser.parse_args()
    if not args.chain_id:
        parser.print_help()
        sys.exit(-1)

    begin_date_format = None
    end_date_format = None
    if args.begin_date and args.end_date:
        begin_date_format = datetime.strptime(args.begin_date, "%Y-%m-%d")
        end_date_format = datetime.strptime(args.end_date, "%Y-%m-%d") + timedelta(days=1)

        if begin_date_format >= end_date_format:
            raise TypeError("备份日期错误")

        # if end_date_format - begin_date_format > timedelta(days=3):
        #     print("备份日期大于3天")
        #     raise TypeError("备份日期大于3天")

    updateData = UpdateData(args.region_name, args.chain_id, args.env, begin_date_format, end_date_format)
    
    # 获取机构名称
    organ_name = "未知机构"
    try:
        # 使用basic_client查询organ表
        basic_client = DBClient(args.region_name, 'ob', 'abc_cis_basic', args.env, True)
        organ_result = basic_client.fetchall(organ_name_sql.format(chain_id=args.chain_id))
        if organ_result and len(organ_result) > 0:
            organ_name = organ_result[0]['name']
    except Exception as e:
        print(f"获取机构名称出错: {str(e)}")
    
    updateData.run()
    
    # 构建统计信息字符串
    stats_message = "\n\n备份数据：\n"
    for key, value in updateData.stats.items():
        failed_count = updateData.failed_stats.get(key, 0)
        if failed_count > 0:
            stats_message += f"- {key}: 成功 {value} 行, 失败 {failed_count} 行\n"
        else:
            stats_message += f"- {key}: {value} 行\n"

    total_rows = sum(updateData.stats.values())
    total_failed = sum(updateData.failed_stats.values())
    stats_message += f"\n总计影响: {total_rows} 行"
    if total_failed > 0:
        stats_message += f", 失败: {total_failed} 行"
    
    # 获取时间范围信息
    if begin_date_format and end_date_format:
        date_range = f"{begin_date_format.strftime('%Y-%m-%d')} 至 {(end_date_format - timedelta(days=1)).strftime('%Y-%m-%d')}"
    else:
        yesterday = date.today() - timedelta(days=2)
        date_range = f"{yesterday.strftime('%Y-%m-%d')} 至 {(yesterday + timedelta(days=1)).strftime('%Y-%m-%d')}"
    
    if args.env == 'test' or args.env == 'dev':
        url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c8dbd27a-2522-49cc-b5b7-714ab5ced403'
    else:
        url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=be9dbb00-0d2e-4cba-b16f-fe2ceb849c46'
        
    requests.post(
        url=url,
        json={
            "msgtype": "markdown",
            "markdown": {
                "content": f"""{organ_name}执行门诊数据备份任务完成

**时间范围**: {date_range}{stats_message}"""
            }
        }
    )


if __name__ == '__main__':
    main()
