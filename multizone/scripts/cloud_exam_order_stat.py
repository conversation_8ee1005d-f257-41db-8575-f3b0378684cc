import argparse
import logging
import os
import sys
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import yagmail
import pandas as pd
from datetime import date, time, datetime, timedelta
from prettytable import PrettyTable
from io import BytesIO
from tempfile import NamedTemporaryFile
from base64 import b64encode

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.db import DBClient
from multizone.rpc import AbcCisMonitorClient as monitor


def daily_cloud_exam_order_stat(env):
    """
    每日云检订单统计
    :param env:
    :return:
    """
    today = date.today()
    title = "ABC云检订单"
    sql = f"""
            select count(*)                                                                                                  '订单数量',
                   ifnull(sum(receivable_fee), 0)                                                                            '订单金额',
                   ifnull(sum(deposit_deduction_fee), 0)                                                                     '退还押金',
                   ifnull(sum(received_fee), 0)                                                                              '实付金额',
                   ifnull(sum(package_cost_price), 0)                                                                        '试剂成本',
                   ifnull(sum(sales_commission), 0)                                                                          '销售提成',
                   ifnull(sum(if(status != 20, 0,
                                 ifnull(received_fee, 0) - ifnull(package_cost_price, 0) - ifnull(sales_commission, 0))), 0) '毛利'
            from abc_cis_examination.v2_examination_order
            where chain_id not in ('22ca4183b86147b8ad77b72928e236f2', 'ffffffff00000000349171418e58c000')
              and is_deleted = 0
              and pharmacy_type = 0
              and pharmacy_no = 0
              and created >= '{today} 00:00:00'
              and created <= '{today} 23:59:59';
    """
    region_list = ["ShangHai", "HangZhou"] if env == 'prod' else ["ShangHai"]
    res = {}
    for region in region_list:
        db_cli = DBClient(region, 'ob', 'abc_cis_basic', env, False)
        row = db_cli.fetchone(sql)
        for k in row:
            res[k] = res.get(k, 0) + row[k]

    content = os.linesep.join([f"""[{k}] <font color=\"red\">{res[k]}</font>""" for k in res])
    monitor.sendServiceAlertMessage(f'{title}', os.linesep + content)



def last_7_day_cloud_exam_order_stat_notify(env):
    """
    7天内云检订单统计
    :param env:
    :return:
    """
    today = date.today()
    sql = f"""
    select   c.name                                                      as c_name,
             date(d.created)                                             as created,
             e.name                                                      as e_name,
             SUM(IF(DATE(o.created) = CURDATE() - INTERVAL 7 DAY, 1, 0)) AS '7',
             SUM(IF(DATE(o.created) = CURDATE() - INTERVAL 6 DAY, 1, 0)) AS '6',
             SUM(IF(DATE(o.created) = CURDATE() - INTERVAL 5 DAY, 1, 0)) AS '5',
             SUM(IF(DATE(o.created) = CURDATE() - INTERVAL 4 DAY, 1, 0)) AS '4',
             SUM(IF(DATE(o.created) = CURDATE() - INTERVAL 3 DAY, 1, 0)) AS '3',
             SUM(IF(DATE(o.created) = CURDATE() - INTERVAL 2 DAY, 1, 0)) AS '2',
             SUM(IF(DATE(o.created) = CURDATE() - INTERVAL 1 DAY, 1, 0)) AS '1',
             count(distinct o.id)                                        AS total
      from abc_cis_basic.organ c
               inner join abc_cis_property.v2_property_config_item p on c.id = p.v2_scope_id and p.`key` =
                                                                                                 'examination.settings.examine.cloudSupplierSettings.isEnable' and
                                                                        value = 1
               inner join abc_cis_goods.v2_goods_examination_device d on d.clinic_id = c.id and d.status = 10
               inner join abc_cis_goods.v2_goods_examination_device_model dm
                          on dm.id = d.device_model_id and dm.supplier_id = 1
               inner join abc_bis.v1_vendor_employee e on e.id = d.seller_id
               left join abc_cis_examination.v2_examination_order o
                         on o.clinic_id = c.id and o.is_deleted = 0 and o.status = 20 and o.pharmacy_type = 0 and
                            o.pharmacy_no = 0 and o.created >= '{today - timedelta(days=7)} 00:00:00' and o.created <= '{today - timedelta(days=1)} 23:59:59'
      group by c.id
    """
    region_list = ["ShangHai", "HangZhou"] if env == 'prod' else ["ShangHai"]
    rows = []
    abnormal_pay_rules = PrettyTable()
    abnormal_pay_rules.field_names = ['诊所名称', '项目编码', '项目名称', '订单收费', '退还押金', '试剂成本', '销售提成']
    for region in region_list:
        db_cli = DBClient(region, 'ob', 'abc_cis_basic', env, False)
        rows.extend(db_cli.fetchall(sql))
        abnormal_pay_rule_rows = db_cli.fetchall("""
                        select o.name, g.short_id, g.name as g_name, r.receivable_fee, r.deposit_deduction_fee, r.package_cost_price, r.sales_commission
                        from abc_cis_examination.v2_examination_pay_rule r
                                 inner join abc_cis_goods.v2_goods g on r.goods_id = g.id and r.is_deleted = 0
                        inner join abc_cis_basic.organ o on o.id = r.clinic_id
                        where (case g.short_id
                                   when 'HAJYABC001' then receivable_fee >= 20.5 || receivable_fee <= 8.5 # 14.5
                                   when 'HAJYABC002' then receivable_fee >= 30.5 || receivable_fee <= 18.5 # 24.5
                                   when 'HAJYABC003' then receivable_fee >= 40.5 || receivable_fee <= 28.5 # 34.5
                                   when 'HAJYABC004' then receivable_fee >= 20.9 || receivable_fee <= 8.9 # 14.9
                                   when 'HAJYABC005' then receivable_fee >= 30.9 || receivable_fee <= 18.9 # 24.9
                                   when 'HAJYABC006' then receivable_fee >= 40.9 || receivable_fee <= 28.9 # 34.9
                                   when 'HAJYABC007' then receivable_fee >= 36.5 || receivable_fee <= 24.5 # 30.5
                                   when 'HAJYABC009' then receivable_fee >= 45 || receivable_fee <= 33 # 39
                                   when 'HAJYABC0010' then receivable_fee >= 36.5 || receivable_fee <= 24.5 # 30.5
                                   when 'HAJYABC0012' then receivable_fee >= 45 || receivable_fee <= 33 # 39
                                   else 0 end )
                        order by g.short_id;
                        """)
        for a in abnormal_pay_rule_rows:
            abnormal_pay_rules.add_rows([a.values()])
    if len(rows) > 0:
        rows.sort(key=lambda x: (-x['total'], [-x[f'{i}'] for i in range(1, 8)]))
        headers = ['诊所名称', '创建日期', '销售', '图表统计']
        excel_rows = []
        data = PrettyTable()
        data.field_names = headers
        for row in rows:
            # data.append([row['c_name'], row['created'], row['e_name']] +[row[f'{i}'] for i in range(7, 0, -1)])
            x = [today - timedelta(days=i) for i in range(7, 0, -1)]
            y = [row[f'{i}'] for i in range(7, 0, -1)]
            plt.clf()
            bars = plt.bar(x, y, alpha=0.8)
            # 在每根柱子上方添加数字标签
            for bar in bars:
                height = bar.get_height()  # 获取柱子的高度
                plt.text(bar.get_x() + bar.get_width() / 2, height,  # x 坐标是柱子的中心
                         str(height),  # 显示的文本
                         ha='center',  # 水平对齐方式
                         va='bottom',  # 垂直对齐方式，'bottom' 会让数字显示在柱子顶部
                         fontsize=10,  # 字体大小
                         color='red')  # 文字颜色
            plt.xlabel('date')
            plt.ylabel('order')
            date_format = mdates.DateFormatter('%m-%d')
            plt.gca().xaxis.set_major_formatter(date_format)
            # 调整刻度间隔
            plt.gca().xaxis.set_major_locator(mdates.DayLocator(interval=1))
            # 调整日期刻度尺寸
            # 显示图表
            # 旋转日期标签，并间隔显示
            plt.xticks(rotation=60)
            plt.tight_layout()  # 自动调整子图参数，防止标签重
            # plt.show()
            # 将图表保存为 BytesIO 对象
            buffer = BytesIO()
            plt.savefig(buffer, format='png')
            buffer.seek(0)
            buffer_value = buffer.read()
            base64_str = b64encode(buffer_value).decode('utf-8')
            data.add_row([row['c_name'], row['created'], row['e_name'], f'<img src="data:image/png;base64,{base64_str}" alt=""/>'])
            excel_rows.append([row['c_name'], row['created'], row['e_name']] + [row[f'{i}'] for i in range(7, 0, -1)])

        # excel
        df = pd.DataFrame(excel_rows, columns=['诊所名称', '创建日期', '销售'] + [f'{date.strftime(today - timedelta(days=i), "%m-%d")}' for i in range(7, 0, -1)])
        # 设置固定列宽
        pd.set_option('display.max_colwidth', 100)
        with NamedTemporaryFile(delete=False, suffix=".xlsx") as temp_excel_file:
            df.to_excel(temp_excel_file.name, index=False)
            temp_excel_file_path = temp_excel_file.name

        # table = tabulate(data, headers=headers, tablefmt="html")
        table = data.get_html_string(attributes={"border": "1", "style": "position: absolute; top: 500px; left: 0; width: 100%; height: 100%;"})
        html = f"""
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <title>Title</title>
            </head>
            <body style="position: relative;">
                <h2>支付规则数据异常</h2>
                {abnormal_pay_rules.get_html_string(attributes={"border": "1", "style": "position: absolute; top: 250px; left: 0; width: 100%;"}).replace('&lt;', '<').replace('&gt;', '>').replace('&quot;', '"').replace('<tr>', '<tr style="height: 50px">')}
                <h2 style="position: absolute;top: 450px;">最近7天云检订单统计</h2>
                {table.replace('&lt;', '<').replace('&gt;', '>').replace('&quot;', '"')}
            </body>
            </html>
            """
        logging.info(f'\n{html}')
        # f = open('test.html', 'w')
        # f.write(html)
        # qw_create_doc('最近7天云检订单统计', rows)
        # rsp = json.loads(
        #     requests.post(url=f'http://oa.rpc.abczs.cn/rpc/management/corp/send-wechat-message/card',
        #                   json={
        #                       'type': 0,
        #                       'cardType': 1,
        #                       'content': markdown,
        #                       'userIds': ['10199']
        #                   }).content.decode('utf-8')
        # )
        # logging.info(rsp)
        send_email('最近7天云检订单统计', html, temp_excel_file_path)


def send_email(title, content, attachments):
    email = '<EMAIL>'
    password = '3j2x5tSGBwiEKBft'

    # 初始化 yagmail 客户端
    yag = yagmail.SMTP(user=email, password=password, smtp_ssl=True, host='smtp.exmail.qq.com', port=465)
    sendResult = yag.send(to=['<EMAIL>', '<EMAIL>', '<EMAIL>'], subject=title, contents=content, attachments=attachments)
    print(sendResult)

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--env', help='环境 dev/test/prod')
    parser.add_argument('--method', help='method', required=False)
    args = parser.parse_args()
    if not args.env:
        parser.print_help()
        sys.exit(-1)
    if args.method == 'daily_cloud_exam_order_stat':
        daily_cloud_exam_order_stat(env=args.env)
    elif args.method == 'last_7_day_cloud_exam_order_stat_notify':
        last_7_day_cloud_exam_order_stat_notify(env=args.env)
