#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2022 <PERSON> <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""


"""
import os

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
import sys

sys.path.append(os.path.dirname(os.path.dirname(CURRENT_DIR)))
from multizone.rediscli import RedisClient
from multizone.log import AliyunLogClient

import argparse
from region_data_sync import execute_data_sync

default_id = '00000000000000000000000000000000'
logger = AliyunLogClient('Master', 'prod').logger

goods_redis_version = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
                       'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j',
                       'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't',
                       'u', 'v', 'w', 'x', 'y', 'z'
                       ]

def run(env, server_instance, full_sync):
    region_list = ["ShangHai", "HangZhou"] if env == 'prod' else ["ShangHai"]
    execute_data_sync(master_region='Master',
                      master_cluster='internal_system',
                      master_db='abc_oa',
                      master_table='examination_device_model',
                      slave_region_list=region_list,
                      slave_cluster='abc_cis_stock',
                      jumper_user=server_instance,
                      slave_db='abc_cis_goods',
                      slave_table='v2_goods_examination_device_model',
                      env=env,
                      whereSql=None,
                      custom_update_dict=None,
                      last_modified_column=None if full_sync else 'last_modified')

    # 清除缓存
    for region in region_list:
        redis = RedisClient(zone=region, cluster='abc-goods-redis', db=0, env=env, no_tunnel=False, jumper_user=server_instance)
        redis.client.delete(*[f'd:assay:{version}:model' for version in goods_redis_version])
        redis.client.delete(*[f'd:inspect:{version}:model' for version in goods_redis_version])

        logger.info(f'清除缓存成功 {region}')

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--env', help='环境 dev/test/prod')
    parser.add_argument('--server-instance', help='服务实例 schedulerx/jenkins')
    parser.add_argument('--full-sync', type=int, help='是否全量同步')
    args = parser.parse_args()
    if not args.env:
        parser.print_help()
        sys.exit(-1)

    run(args.env, args.server_instance, args.full_sync == 1)
    # run('dev')


if __name__ == '__main__':
    main()
