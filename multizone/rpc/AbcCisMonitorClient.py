#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2023 jiangxf
#
# Distributed under terms of the MIT license/private/var/folders/pz/kwzyd6zd0s98pg_w3d0bd7gm0000gn/T/pip-uninstall-Tr_3rX/easy_install.py.


import inspect
import os
import logging
import requests

from .RpcClient import regionRpcHost

# 获取当前脚本的文件路径
current_script_path = inspect.getframeinfo(inspect.currentframe()).filename
# 获取当前脚本所在的目录路径
current_script_directory = os.path.dirname(os.path.abspath(os.path.join(current_script_path, "..")))


# 分区配置字典表
def sendServiceAlertMessage(title, content, region='ShangHai', env='prod'):
    try:
        logging.info(f"sendServiceAlertMessage title:{title}, content:{content}")
        rsp_body = requests.post(url=f"http://{regionRpcHost(region, env)}/rpc/monitor/service-alert", json={
            "serviceName": "PreGrayDataMigrate",
            "title": title,
            "content": content
        }).content
        logging.info(f"sendServiceAlertMessage rsp_body:{rsp_body}")
    except Exception as e:
        logging.error(f"sendServiceAlertMessage error:{e}")
