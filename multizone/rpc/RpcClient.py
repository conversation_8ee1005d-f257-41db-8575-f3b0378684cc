#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2023 robins
#
# Distributed under terms of the MIT license/private/var/folders/pz/kwzyd6zd0s98pg_w3d0bd7gm0000gn/T/pip-uninstall-Tr_3rX/easy_install.py.


import inspect
import os


# 获取当前脚本的文件路径
current_script_path = inspect.getframeinfo(inspect.currentframe()).filename
# 获取当前脚本所在的目录路径
current_script_directory = os.path.dirname(os.path.abspath(os.path.join(current_script_path, "..")))

# 分区配置字典表
ZONE_CONFIG_MAP = {
    # 上海分区
    'ShangHai': {
        'dev': 'dev.rpc.abczs.cn',
        'test': 'test.rpc.abczs.cn',
        'pre': 'pre.rpc.abczs.cn',
        'gray': 'gray.rpc.abczs.cn',
        'prod': 'prod.rpc.abczs.cn'
    },
    # 杭州分区
    'HangZhou': {
        'dev': 'region2-dev.rpc.abczs.cn',
        'test': 'region2-test.rpc.abczs.cn',
        'pre': 'region2-pre.rpc.abczs.cn',
        'gray': 'region2-pre.rpc.abczs.cn',
        'prod': 'region2-pre.rpc.abczs.cn'
    }
}


def regionRpcHost(abcRegion, env = 'prod'):
    return ZONE_CONFIG_MAP[abcRegion][env]
