apiVersion: v1
clusters:
- cluster:
    server: https://172.19.119.79:6443
    certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURHakNDQWdLZ0F3SUJBZ0lCQURBTkJna3Foa2lHOXcwQkFRc0ZBREErTVNjd0ZBWURWUVFLRXcxaGJHbGkKWVdKaElHTnNiM1ZrTUE4R0ExVUVDaE1JYUdGdVozcG9iM1V4RXpBUkJnTlZCQU1UQ210MVltVnlibVYwWlhNdwpIaGNOTVRrd05USTRNVE0xTnpNM1doY05Namt3TlRJMU1UTTFOek0zV2pBK01TY3dGQVlEVlFRS0V3MWhiR2xpCllXSmhJR05zYjNWa01BOEdBMVVFQ2hNSWFHRnVaM3BvYjNVeEV6QVJCZ05WQkFNVENtdDFZbVZ5Ym1WMFpYTXcKZ2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLQW9JQkFRQzBSV2pNVFZFVjhYd2FyR3J3ekh1aAprb0R6YUZ0bEVMS1paMUdWRHdCK3RDaXlobUp3eENLYnU1RW15enh3ZzBNQVJqLzFoWVUwcHkvUjEwL2EzUm9rCmZVajZIVkd3SkxEQXRWR01KS3pRTEY0clFUWVEydXc5TkprVDV5eCtuTmVIaWpiSUhCZVJNVjc1U0tFUWRUVncKWXJHZnUxb3BZMGg4eXhONFRGV2I5TFdORTZvdEhOREtlSWlQck1acGhXSmNDTWdiWlR2YndyeHlJUXVJU0NuQwpiNDBMWkZwRzNyczk1SUdVM1N4dWloeXlhdk9NY01aNFFBOG9MbFY4cXNPZE5Ta1J1MXpzUlVNUHkzS1RBWGprCktiTlBJY3JyTlZyeWNPakI1REFwNEhBbkFCSWI5TmpnYTF0YTZkK2Z3SWpGNlBXWFdUS0FSOGhON0tNV05qd0QKQWdNQkFBR2pJekFoTUE0R0ExVWREd0VCL3dRRUF3SUNwREFQQmdOVkhSTUJBZjhFQlRBREFRSC9NQTBHQ1NxRwpTSWIzRFFFQkN3VUFBNElCQVFCQ1IwM1VvY3pxbEhKc2pwbTluU1hOaGRybjFLbTR6eGZuMDVyNWZmR3d4d0RxCmNTNytkbnh4OGltbWZzVStXTlBwYU1BdTFpNWpDejd4alhnbnhWMkRCZ2tSOXcwWTV2Wlp5b01oS2VWcjZKd3QKZnVOVXcrRzkyU0xueDA0ZFY3UXlwcEhJRGwyUGFBd1IxZkg3cU4ybWFGbnZKbStlRkdmQmM3YnZobEVtWG5Xdwo5eGc1ejk5clR6TVRaemFMaEpXODdENkV0M21kUUdUUjdNaVp0RWw4QWJ4MDI3a0cvQXIwNlk4OUdkZktqTE1ZCk5JUkJzUmc1QkF4dDlWUEwyUUFhejFPSWR1ZDVyb3N2bHoxbHk0N0xMUFg5ZUVYdkpFbnR4dEhjSGdpZUk2NmoKZXhhditQK2VoV0pSeVFWaXhzcHNLY0xuNHU4czBaL0c3M3ZEMlFObAotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
  name: kubernetes
contexts:
- context:
    cluster: kubernetes
    user: "251012352961641950"
  name: 251012352961641950-cfc6e1f69273c4797a6cb13faf1572a82
current-context: 251012352961641950-cfc6e1f69273c4797a6cb13faf1572a82
kind: Config
preferences: {}
users:
- name: "251012352961641950"
  user:
    client-certificate-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURVVENDQXJxZ0F3SUJBZ0lEV3ZKSk1BMEdDU3FHU0liM0RRRUJDd1VBTUdveEtqQW9CZ05WQkFvVElXTm0KWXpabE1XWTJPVEkzTTJNME56azNZVFpqWWpFelptRm1NVFUzTW1FNE1qRVFNQTRHQTFVRUN4TUhaR1ZtWVhWcwpkREVxTUNnR0ExVUVBeE1oWTJaak5tVXhaalk1TWpjell6UTNPVGRoTm1OaU1UTm1ZV1l4TlRjeVlUZ3lNQjRYCkRUSXlNRE15T1RBNE5Ua3dNRm9YRFRJMU1ETXlPREE1TURReE0xb3dQekVWTUJNR0ExVUVDaE1NYzNsemRHVnQKT25WelpYSnpNUWt3QndZRFZRUUxFd0F4R3pBWkJnTlZCQU1URWpJMU1UQXhNak0xTWprMk1UWTBNVGsxTURDQwpBU0l3RFFZSktvWklodmNOQVFFQkJRQURnZ0VQQURDQ0FRb0NnZ0VCQUwrL1QrWVNYcFJ5eEJ2blVRRCtCVWVPCm1oNTFOSHIweDJzVjAxOFVxaG5VVW4vRjZxQmM2eTJGLzluYUhXek01UjFBbkNHb3ZKM0QwWStVMlluODZPU1MKcy9sd3JRdTB5NmR0SkF1UmtPY2RFYzdzNFd3d05KMUoxL0E3TDFZbE1FZU9TQU9pUXhqQ2VBMWxDNDVrN3VNbApaOGJHcnAvdHdtVzNHWElhWUFxSlJ5TEI5a2w0dGRqY2JJa1V2ZHJGSm1WVEdnRm56YVFYNXVnNjJkZlg1ZXM5ClJGVVVaWmdzMEd4bXkzZ1ZNVlVrek55RXVHZWZvb1lLN0pFdTJMZ0dpcTlFQ0VFeFBmYjF1MEh4dkx1N1lKZWUKbG5VallMVjlRbVhFTTl1eSszMTBVaFBhSXpiQ20wSUVZd0l6Rml4ZnRqVFR2M1JzNTJxU1VOekFhVWF2dENVQwpBd0VBQWFPQnF6Q0JxREFPQmdOVkhROEJBZjhFQkFNQ0I0QXdFd1lEVlIwbEJBd3dDZ1lJS3dZQkJRVUhBd0l3CkRBWURWUjBUQVFIL0JBSXdBREE4QmdnckJnRUZCUWNCQVFRd01DNHdMQVlJS3dZQkJRVUhNQUdHSUdoMGRIQTYKTHk5alpYSjBjeTVoWTNNdVlXeHBlWFZ1TG1OdmJTOXZZM053TURVR0ExVWRId1F1TUN3d0txQW9vQ2FHSkdoMApkSEE2THk5alpYSjBjeTVoWTNNdVlXeHBlWFZ1TG1OdmJTOXliMjkwTG1OeWJEQU5CZ2txaGtpRzl3MEJBUXNGCkFBT0JnUUEyakd4bldxejdoc0k0ZitYb3JuSTlsQ0pGMmtzdjZQTW9HUzFnbGJmdlUvNkxRak1idmV1RTVrbkoKdE5HSGpUQ1pqd0c2YXRQNUZGellnTkUybGdTNWdkRE5LRjdodjBXTFVhYlpKSkVPL3d4S3J1OVZqUDhNdnBRSApWdE9IT0habFp1QktIdm9ZM0xnaGlWTzJuVW5obWRhamlGVjZ2VFlpeVluRzBYNDd4UT09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0KLS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUMvekNDQW1pZ0F3SUJBZ0lERDVuU01BMEdDU3FHU0liM0RRRUJDd1VBTUdJeEN6QUpCZ05WQkFZVEFrTk8KTVJFd0R3WURWUVFJREFoYWFHVkthV0Z1WnpFUk1BOEdBMVVFQnd3SVNHRnVaMXBvYjNVeEVEQU9CZ05WQkFvTQpCMEZzYVdKaFltRXhEREFLQmdOVkJBc01BMEZEVXpFTk1Bc0dBMVVFQXd3RWNtOXZkREFlRncweE9UQTFNamd4Ck16VXlNREJhRncwek9UQTFNak14TXpVM01qTmFNR294S2pBb0JnTlZCQW9USVdObVl6WmxNV1kyT1RJM00yTTAKTnprM1lUWmpZakV6Wm1GbU1UVTNNbUU0TWpFUU1BNEdBMVVFQ3hNSFpHVm1ZWFZzZERFcU1DZ0dBMVVFQXhNaApZMlpqTm1VeFpqWTVNamN6WXpRM09UZGhObU5pTVRObVlXWXhOVGN5WVRneU1JR2ZNQTBHQ1NxR1NJYjNEUUVCCkFRVUFBNEdOQURDQmlRS0JnUURTQTl2U3MrSTNsQUwzeUZOQ25jMndkbDZyZ1lQRnNIWko0TW9ZQnNEYU9rNmoKZWU4bDVTbC9lWVVtZlc2TFQwdzFFMlV6RDkrNURIMGdvbjBlcUJKVk93VEJaQ2F4YmxxQUJUZUpjWE5VN0ZyMwpYTzJZcUVLaitQN2ZzNGJjdHFoSDdUR05oZ0NHSU1lRlBUTXA1YVB2L2hvOTlkdm54V0ZDUTZJdGFaVS9wd0lECkFRQUJvNEc2TUlHM01BNEdBMVVkRHdFQi93UUVBd0lDckRBUEJnTlZIUk1CQWY4RUJUQURBUUgvTUI4R0ExVWQKSXdRWU1CYUFGSVZhLzkwanpTVnZXRUZ2bm0xRk9adFlmWFgvTUR3R0NDc0dBUVVGQndFQkJEQXdMakFzQmdncgpCZ0VGQlFjd0FZWWdhSFIwY0RvdkwyTmxjblJ6TG1GamN5NWhiR2w1ZFc0dVkyOXRMMjlqYzNBd05RWURWUjBmCkJDNHdMREFxb0NpZ0pvWWthSFIwY0RvdkwyTmxjblJ6TG1GamN5NWhiR2w1ZFc0dVkyOXRMM0p2YjNRdVkzSnMKTUEwR0NTcUdTSWIzRFFFQkN3VUFBNEdCQUVlc3gyMUFZS2Q3M0RIWTU1VmxOeUY5QlNRU1dvekNiUHBGTmVWdApYMmlIR0UzL3RoS3ZoSHpSZ0VPMHpvMDZScm4zSFZ6UzZmSlRQYzVhc1lYQkN3dkx6RHVXYmcwUnlmbVNjdFBuCkxPRWUweXFnTXJCUVliaDR2dlUvcEJnVEptN21Yc0JRaGQ2K2NJT2JLODkvZVVCanowYUtxK1dWWEhBM1BSeFgKZ3FkNwotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
    client-key-data: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************