#集群名字
name: "杭州"
type: "master"

#配置执行这个集群的登录跳板机的登录方式。如果不配置直联
useJumper: 1
jumperHost: '**************'
jumperPort: '22'
#robins local computer
#jumperSshUser: 'robinsli'
#jumperSshKey: '/Users/<USER>/.ssh/id_rsa'
jumperSshUser: 'schedulerx'
jumperSshKey: '/home/<USER>/.ssh/id_rsa'
#jumperSshUser: 'jiangxf'
#jumperSshKey: '/Users/<USER>/.ssh/id_rsa'

jumperList:
  # 本地配置 方便调试ob
  - sshUser: 'jiangxf'
    sshKey: '/Users/<USER>/.ssh/id_rsa'
    sshPassword: 'jiangxf'
    useJumper: 1
  - sshUser: 'robinsli'
    sshKey: '/Users/<USER>/.ssh/id_rsa'
    useJumper: 1
  - sshUser: 'schedulerx'
    sshKey: '/home/<USER>/.ssh/id_rsa'
    useJumper: 1
  - sshUser: 'jenkins'
    sshKey: '/home/<USER>/.ssh/id_rsa'
    useJumper: 1
  - sshUser: 'yinxiaoyang_mini'
    sshKey: '/Users/<USER>/.ssh/id_rsa'
    useJumper: 1
  - sshUser: 'yinxiaoyang'
    sshKey: '/Users/<USER>/.ssh/id_rsa'
    useJumper: 1

#DB配置
dbList:
  # 测试DB
  - cluster: "test"
    user: "h_test_backend"
    password: "03ca22bbb74eb001864fa517dab203F8"
    host: "pc-uf6p03ez7eoy4n103.mysql.polardb.rds.aliyuncs.com"
    port: 3306
    mode: "readwrite"
    database:
      - 'abc_cis_smart_dispensing'
      - 'abc_cis_smart_dispensing_dev'
      - 'abc_cis_smart_dispensing_test'
      - 'abc_cis_basic_dev'
      - 'abc_bis_dev'
      - 'abc_cis_wallet_dev'
      - 'abc_cis_shebao_dev'
      - 'abc_cis_invoice_dev'
      - 'abc_cis_wechatpay_dev'
      - 'abc_cis_promotion_dev'
      - 'abc_cis_dispensing_dev'
      - 'abc_cis_search_dev'
      - 'abc_cis_message_dev'
      - 'abc_cis_im_dev'
      - 'abc_cis_registration_dev'
      - 'abc_cis_patient_dev'
      - 'abc_cis_examination_dev'
      - 'abc_cis_nurse_dev'
      - 'abc_cis_goods_dev'
      - 'abc_cis_outpatient_dev'
      - 'abc_cis_patientorder_dev'
      - 'abc_cis_supervision_dev'
      - 'abc_cis_basic_test'
      - 'abc_cis_ai_test'
      - 'abc_bis_test'
      - 'abc_cis_wallet_test'
      - 'abc_cis_shebao_test'
      - 'abc_cis_invoice_test'
      - 'abc_cis_wechatpay_test'
      - 'abc_cis_promotion_test'
      - 'abc_cis_dispensing_test'
      - 'abc_cis_search_test'
      - 'abc_cis_message_test'
      - 'abc_cis_im_test'
      - 'abc_cis_patient_test'
      - 'abc_cis_registration_test'
      - 'abc_cis_examination_test'
      - 'abc_cis_nurse_test'
      - 'abc_cis_goods_test'
      - 'abc_cis_outpatient_test'
      - 'abc_cis_patientorder_test'
      - 'abc_cis_supervision_test'

  # 开发DB
  - cluster: "dev"
    user: "h_dev_backend"
    password: "d6f48652d7ca9510cf90673D4342e017"
    host: "pc-uf6p03ez7eoy4n103.mysql.polardb.rds.aliyuncs.com"
    port: 3306
    mode: "readwrite"
    database:
      - 'abc_cis_smart_dispensing'
      - 'abc_cis_smart_dispensing_dev'
      - 'abc_cis_smart_dispensing_test'
      - 'abc_cis_basic_dev'
      - 'abc_bis_dev'
      - 'abc_cis_wallet_dev'
      - 'abc_cis_shebao_dev'
      - 'abc_cis_invoice_dev'
      - 'abc_cis_wechatpay_dev'
      - 'abc_cis_promotion_dev'
      - 'abc_cis_dispensing_dev'
      - 'abc_cis_search_dev'
      - 'abc_cis_message_dev'
      - 'abc_cis_im_dev'
      - 'abc_cis_registration_dev'
      - 'abc_cis_patient_dev'
      - 'abc_cis_examination_dev'
      - 'abc_cis_nurse_dev'
      - 'abc_cis_goods_dev'
      - 'abc_cis_outpatient_dev'
      - 'abc_cis_patientorder_dev'
      - 'abc_cis_supervision_dev'
      - 'abc_cis_basic_test'
      - 'abc_bis_test'
      - 'abc_cis_wallet_test'
      - 'abc_cis_shebao_test'
      - 'abc_cis_invoice_test'
      - 'abc_cis_wechatpay_test'
      - 'abc_cis_promotion_test'
      - 'abc_cis_dispensing_test'
      - 'abc_cis_search_test'
      - 'abc_cis_message_test'
      - 'abc_cis_im_test'
      - 'abc_cis_patient_test'
      - 'abc_cis_registration_test'
      - 'abc_cis_examination_test'
      - 'abc_cis_nurse_test'
      - 'abc_cis_goods_test'
      - 'abc_cis_goods_dev'
      - 'abc_oa_dev'
      - 'abc_oa_test'
      - 'abc_cis_outpatient_test'
      - 'abc_cis_patientorder_test'
      - 'abc_cis_supervision_test'
  #ADB
  - cluster: "adb"
    user: "m_schedulex"
    password: "df3d26e55a972e6534385c7cB0185343"
    host: "t5sja2kp58yow-ro0.cn-hangzhou.oceanbase.aliyuncs.com"
    port: 3306
    mode: "readonly"
    database:
      - "abc_cis_basic"
      - "abc_bis"
      - "abc_ops_stat"
      - "abc_cis_wallet"
      - "abc_cis_shebao"
      - "abc_cis_invoice"
      - "abc_cis_wechatpay"
      - "abc_cis_promotion"
      - "abc_cis_dispensing"
      - "abc_cis_search"
      - "abc_cis_message"
      - "abc_cis_im"
      - "abc_cis_registration"
      - "abc_cis_examination"
      - "abc_cis_nurse"
      - "abc_cis_patient"
      - "abc_cis_goods"
      - "abc_cis_goods_log"
      - "abc_cis_outpatient"
      - "abc_cis_patientorder"
      - "abc_cis_charge"
      - "abc_scrm_customer"
      - "abc_pe_order"
      - "abc_pe_charge"
      - "abc_his_charge"
      - "abc_his_advice"
  # "ODS写"
  #  - cluster: "ods-write"
  #    user: "abc_sa"
  #    password: "aud806cuAwo!wML5BceoWop$mh4ao^DL"
  #    host: "pc-bp1l7g5n898o561m5.rwlb.rds.aliyuncs.com"
  #    port: 3306
  #    mode: "readwrite"
  #    database:
  #      - "abc_cis_goods_log"
  #ODS读
  - cluster: "ods"
    user: "m_schedulex"
    password: "df3d26e55a972e6534385c7cB0185343"
    host: "t5sja2kp58yow-ro0.cn-hangzhou.oceanbase.aliyuncs.com"
    port: 3306
    mode: "readonly"
    database:
      - "abc_cis_basic"
      - "abc_bis"
      - "abc_ops_stat"
      - "abc_cis_wallet"
      - "abc_cis_shebao"
      - "abc_cis_invoice"
      - "abc_cis_wechatpay"
      - "abc_cis_promotion"
      - "abc_cis_dispensing"
      - "abc_cis_search"
      - "abc_cis_message"
      - "abc_cis_im"
      - "abc_cis_registration"
      - "abc_cis_examination"
      - "abc_cis_nurse"
      - "abc_cis_patient"
      - "abc_cis_goods"
      - "abc_cis_outpatient"
      - "abc_cis_patientorder"
      - "abc_cis_charge"
      - "abc_scrm_customer"
      - "abc_cis_property"
  # OceanBase
  - cluster: "ob"
    user: "m_schedulex"
    password: "df3d26e55a972e6534385c7cB0185343"
    host: "t5sja2kp58yow-ro0.cn-hangzhou.oceanbase.aliyuncs.com"
    port: 3306
    mode: "readonly"
    database:
      - "abc_cis_basic"
      - "abc_bis"
      - "abc_ops_stat"
      - "abc_cis_wallet"
      - "abc_cis_shebao"
      - "abc_cis_invoice"
      - "abc_cis_wechatpay"
      - "abc_cis_promotion"
      - "abc_cis_dispensing"
      - "abc_cis_search"
      - "abc_cis_message"
      - "abc_cis_im"
      - "abc_cis_registration"
      - "abc_cis_examination"
      - "abc_cis_nurse"
      - "abc_cis_patient"
      - "abc_cis_goods"
      - "abc_cis_outpatient"
      - "abc_cis_patientorder"
      - "abc_cis_charge"
      - "abc_scrm_customer"
      - "abc_cis_property"
      - "abc_cis_dz_data_sync"
      - "abc_cis_goods_log"
      - "abc_cis_ai"
      - "abc_cis_outpatient_record"
      - "abc_cis_monitor"
  # base实例
  - cluster: "abc_cis_mixed"
    user: "abc_sa"
    password: "aud806cuAwo!wML5BceoWop$mh4ao^DL"
    host: "pc-bp1x2j80plou91zwb.rwlb.rds.aliyuncs.com"
    port: 3306
    mode: "readwrite"
    database:
      - "abc_cis_property"
      - "abc_cis_basic"
      - "abc_ops_stat"
      - "abc_cis_shorturl"
      - "abc_cis_form"
      - "abc_cis_domain"
  #MC-IM-PATIENT实例
  - cluster: "abc_cis_account_base"
    user: "abc_sa"
    password: "aud806cuAwo!wML5BceoWop$mh4ao^DL"
    host: "pc-bp19pxm9b5a781knm.mysql.polardb.rds.aliyuncs.com"
    port: 3306
    mode: "readwrite"
    database:
      - "abc_cis_message"
      - "abc_cis_im"
      - "abc_cis_patient"
      - "abc_cis_mc"
      - "abc_cis_report"
  #SCRM-HOSPITAL实例
  - cluster: "scrm_hospital"
    user: "abc_sa"
    password: "aud806cuAwo!wML5BceoWop$mh4ao^DL"
    host: "pc-bp159nq6h26krbx58.mysql.polardb.rds.aliyuncs.com"
    port: 3306
    mode: "readwrite"
    database:
      - "abc_his_advice"
      - "abc_his_charge"
      - "abc_his_emr"
      - "abc_his_ward"
      - "abc_scrm_basic"
      - "abc_scrm_channel"
      - "abc_scrm_customer"
      - "abc_scrm_kf"
      - "abc_scrm_material"
      - "abc_pe_order"
      - "abc_pe_charge"
      - "abc_pha_gsp"
  #统计&搜索实例
  - cluster: "abc_cis_stat"
    user: "abc_sa"
    password: "aud806cuAwo!wML5BceoWop$mh4ao^DL"
    host: "pc-bp1zk3ur4fqa08zjk.mysql.polardb.rds.aliyuncs.com"
    port: 3306
    mode: "readwrite"
    database:
      - "abc_cis_search"
      - "abc_cis_supervision"
  #库存
  - cluster: "abc_cis_stock"
    user: "abc_sa"
    password: "aud806cuAwo!wML5BceoWop$mh4ao^DL"
    host: "pc-bp12rnyad1lj7w4bd.mysql.polardb.rds.aliyuncs.com"
    port: 3306
    mode: "readwrite"
    database:
      - "abc_cis_goods"
  #库存&发药日志库
  - cluster: "abc_cis_stock_zip"
    user: "abc_sa"
    password: "aud806cuAwo!wML5BceoWop$mh4ao^DL"
    host: "pc-bp1c5jb1bi4bihd65.mysql.polardb.rds.aliyuncs.com"
    port: 3306
    mode: "readwrite"
    database:
      - "abc_cis_goods_log"
      - "abc_cis_dispensing"
      - "abc_cis_oss"
      - "abc_oa_kf"
      - "abc_cis_processing"
      - "abc_cis_monitor"

  #收费&优惠卷
  - cluster: "abc_cis_charge"
    user: "abc_sa"
    password: "aud806cuAwo!wML5BceoWop$mh4ao^DL"
    host: "pc-bp1h2w8upnhi8x1ol.mysql.polardb.rds.aliyuncs.com"
    port: 3306
    mode: "readwrite"
    database:
      - "abc_cis_promotion"
      - "abc_cis_charge"
  # 收费交易流水
  - cluster: "abc_cis_charge_record"
    user: "abc_sa"
    password: "aud806cuAwo!wML5BceoWop$mh4ao^DL"
    host: "pc-bp16lzdt19fk6moio.mysql.polardb.rds.aliyuncs.com"
    port: 3306
    mode: "readwrite"
    database:
      - "abc_cis_charge_record"
      - "abc_cis_outpatient_record"
  #社保
  - cluster: "abc_cis_bill"
    user: "abc_sa"
    password: "aud806cuAwo!wML5BceoWop$mh4ao^DL"
    host: "pc-bp1r666txtlzt247t.mysql.polardb.rds.aliyuncs.com"
    port: 3306
    mode: "readwrite"
    database:
      - "abc_cis_wallet"
      - "abc_cis_shebao"
      - "abc_cis_invoice"
      - "abc_cis_wechatpay"
      - "abc_mp_charge_center"
  #门诊 & 检查检验 & patientOrder
  - cluster: "abc_cis_outpatient"
    user: "abc_sa"
    password: "aud806cuAwo!wML5BceoWop$mh4ao^DL"
    host: "pc-bp1y10rf5v7ls9l5j.mysql.polardb.rds.aliyuncs.com"
    port: 3306
    mode: "readwrite"
    database:
      - "abc_cis_registration"
      - "abc_cis_nurse"
      - "abc_cis_examination"
      - "abc_cis_patientorder"
      - "abc_cis_consultation"
      - "abc_cis_outpatient"
      - "abc_cis_medical_plan"
  # OceanBase
  - cluster: "outpatient_external_backup"
    user: "m_outpatient_external_backup"
    password: "bP6o52ELR1QUXJT8jpZO1"
    host: "abc-dev-test.rwlb.rds.aliyuncs.com"
    port: 3306
    mode: "readwrite"
    database:
      - "abc_cis_outpatient_external_backup"
      - "abc_cis_outpatient_external_backup_test"
#ES配置
esList:
  - esBase: "abc-search-prod"
    host: "es-cn-x0r37vpl3000npcp0.elasticsearch.aliyuncs.com"
    port: 9200
    username: "elastic"
    password: "379f2d6ce1a1a52bc6804ac65d77D"
  - esBase: "abc-search-prod-normal"
    host: "es-cn-zpr37vlcw000iufxf.elasticsearch.aliyuncs.com"
    port: 9200
    username: "elastic"
    password: "379f2d6ce1a1a52bc6804ac65d77D"
#redis配置
redisList:
  - name: "abc-redis"
    host: "r-bp17i2r0mkcbs9jp5t.redis.rds.aliyuncs.com"
    port: 6379
  - name: "abc-ha-redis"
    host: "r-bp1jgbw6mnhlvrnc4r.redis.rds.aliyuncs.com"
    port: 6379
  - name: "abc-goods-redis"
    host: "r-bp18hhxjcf21bqy657.redis.rds.aliyuncs.com"
    port: 6379
rocketmq:
  - env: "pre"
    host: ep-bp1i6adb828da4960c96.epsrv-bp1g2k84c7kid2igyiun.cn-hangzhou.privatelink.aliyuncs.com
    port: 8080
    producer:
      access-key: tZkUMM35yi09Xb4V
      secret-key: OYZWw8sWlQiOe5dP
    consumer:
      access-key: tZkUMM35yi09Xb4V
      secret-key: OYZWw8sWlQiOe5dP
  - env: "gray"
    host: ep-bp1i4045d7ee08c64564.epsrv-bp1g2k84c7kid2igyiun.cn-hangzhou.privatelink.aliyuncs.com
    port: 8080
    producer:
      access-key: eHxj0N5CP14DOuD0
      secret-key: lO36e8qn472a5SFP
    consumer:
      access-key: eHxj0N5CP14DOuD0
      secret-key: lO36e8qn472a5SFP
  - env: "prod"
    host: ep-bp1i1c18203133d65e69.epsrv-bp1g2k84c7kid2igyiun.cn-hangzhou.privatelink.aliyuncs.com
    port: 8080
    producer:
      access-key: zrxH8y208GvTLjhS
      secret-key: 1zlC3H62iFH520r9
    consumer:
      access-key: zrxH8y208GvTLjhS
      secret-key: 1zlC3H62iFH520r9
rabbitmq:
  - env: "pre"
    host: rabbitmq-cn-em941remu03.cn-hangzhou.amqp-9.vpc.mq.amqp.aliyuncs.com
    username: MjpyYWJiaXRtcS1jbi1lbTk0MXJlbXUwMzpMVEFJNXQ3cUpRanVBYjFxdlRwbUg4eWU=
    password: ODc3Q0RGMDFFNzg2QUU3M0Q0RTE4Q0E1RkZGNzI1RTYyNEE0N0NFMzoxNzM0MzM1Nzk2MTc4
    port: 5672
    vhost: /ha
  - env: "gray"
    host: rabbitmq-cn-k96429k1o02.cn-hangzhou.amqp-7.vpc.mq.amqp.aliyuncs.com
    username: MjpyYWJiaXRtcS1jbi1rOTY0MjlrMW8wMjpMVEFJNXQ3cUpRanVBYjFxdlRwbUg4eWU=
    password: QThEQTVEQzcyNkVBNkRBMTZEN0ZGMUU1NTlGNDVCQkYxMDc1QUY2NDoxNzM1MTc3MjQwNTU2
    port: 5672
    vhost: /ha
  - env: "prod"
    host: rabbitmq-cn-3ic41rix305.cn-hangzhou.amqp-8.vpc.mq.amqp.aliyuncs.com
    username: MjpyYWJiaXRtcS1jbi0zaWM0MXJpeDMwNTpMVEFJNXQ3cUpRanVBYjFxdlRwbUg4eWU=
    password: QUZFNzZBNUI5RkMyQTAyOUZBNEI5MDRFMEM4QTE2N0Q0QjQwMjcwQjoxNzM0MzM1ODE5MDU3
    port: 5672
    vhost: /
#MQ配置
mqList:

aliyun:
  log:
    endpoint: "cn-hangzhou.log.aliyuncs.com"
    access_key_id: "LTAI4FuQ6NgxxQETz8pjXFb5"
    access_key_secret: "******************************"
    project: "abc-cis-log-hangzhou"

rpcList:
  rpcHost: 'region2-pre.rpc.abczs.cn'
  grayRpcHost: 'region2-gray.rpc.abczs.cn'
  prodRpcHost: 'region2-prod.rpc.abczs.cn'

