#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2023 robins
#
# Distributed under terms of the MIT license/private/var/folders/pz/kwzyd6zd0s98pg_w3d0bd7gm0000gn/T/pip-uninstall-Tr_3rX/easy_install.py.


from aliyun.log import LogClient, PutLogsRequest, LogItem
from aliyun.log.logexception import LogException
import yaml
from sshtunnel import SSHTunnelForwarder
import logging
import os
import inspect
import time

logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)

# 获取当前脚本的文件路径
current_script_path = inspect.getframeinfo(inspect.currentframe()).filename
# 获取当前脚本所在的目录路径
current_script_directory = os.path.dirname(os.path.abspath(os.path.join(current_script_path, "..")))

# 分区配置字典表
ZONE_CONFIG_MAP = {
    # 上海分区
    'ShangHai': '/zone_shanghai.yaml',
    # 杭州分区
    'HangZhou': '/zone_hangzhou.yaml',
    # 主分区
    'Master': '/zone_shanghai.yaml',
    # 从分区
    'Slave': ['/zone_hangzhou.yaml']
}


class AliyunLogClient(object):
    def __init__(self, zone, logstore='prod'):
        # 通过分区的简写 找到分区配置文件的路径
        if zone not in ZONE_CONFIG_MAP.keys():
            raise Exception('请检查分区配置，传入的分区:{zone}不存在'.format(zone=zone))
        zone_config_path = ZONE_CONFIG_MAP[zone]
        with open(current_script_directory + zone_config_path, 'r') as yamlConfigFile:
            yamlZoneConfig = yaml.safe_load(yamlConfigFile)

        # 通过集群的简写 找到集群配置
        log_config = yamlZoneConfig.get('aliyun', {}).get('log')
        if log_config is None:
            raise Exception('aliyun.log还未配置')

        self.zone = zone
        self.logstore = logstore
        self.log_config = log_config
        self.client = LogClient(endpoint=log_config['endpoint'], accessKeyId=log_config['access_key_id'],
                                accessKey=log_config['access_key_secret'])
        self.logger = logging.getLogger()

    def bindHandler(self):
        self.logger.addHandler(AliyunLogHandler(self.zone, self.logstore, self.log_config, self.client))


class AliyunLogHandler(logging.Handler):
    def __init__(self, zone, logstore, log_config, client):
        super().__init__()
        self.logstore = logstore
        self.log_config = log_config
        self.client = client

    def emit(self, record):
        contents = [
            ('message', self.format(record)),
            ('level', record.levelname),
            ('X-B3-TraceId', ''),
            ('X-B3-SpanId', ''),
            ('trace_id', ''),
            ('span_id', '')
        ]
        log_item = LogItem(timestamp=int(time.time()), contents=contents)
        try:
            request = PutLogsRequest(project=self.log_config['project'], logstore=self.logstore, topic='PreGrayDataMigrate', source='', logitems=[log_item])
            self.client.put_logs(request)
        except LogException as ex:
            print("日志写入失败：", ex)
