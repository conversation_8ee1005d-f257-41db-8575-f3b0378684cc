#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2023 robins
#
# Distributed under terms of the MIT license/private/var/folders/pz/kwzyd6zd0s98pg_w3d0bd7gm0000gn/T/pip-uninstall-Tr_3rX/easy_install.py.


import inspect
import json
import logging
import os
import uuid

import yaml
from rocketmq.client import Producer, Message
from sshtunnel import SSHTunnelForwarder

logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)

# 获取当前脚本的文件路径
current_script_path = inspect.getframeinfo(inspect.currentframe()).filename
# 获取当前脚本所在的目录路径
current_script_directory = os.path.dirname(os.path.abspath(os.path.join(current_script_path, "..")))

# 分区配置字典表
ZONE_CONFIG_MAP = {
    # 上海分区
    'ShangHai': '/zone_shanghai.yaml',
    # 杭州分区
    'HangZhou': '/zone_hangzhou.yaml',
    # 主分区
    'Master': '/zone_shanghai.yaml',
    # 从分区
    'Slave': ['/zone_hangzhou.yaml']
}


class RocketMqClient(object):
    def __init__(self, zone, env='prod', no_tunnel=False, jumper_user='schedulerx'):
        # 通过分区的简写 找到分区配置文件的路径
        if zone not in ZONE_CONFIG_MAP.keys():
            raise Exception('请检查分区配置，传入的分区:{zone}不存在'.format(zone=zone))
        zone_config_path = ZONE_CONFIG_MAP[zone]
        with open(current_script_directory + zone_config_path, 'r') as yamlConfigFile:
            yamlZoneConfig = yaml.safe_load(yamlConfigFile)

        # 通过集群的简写 找到集群配置
        rocketmq_config_map = {item['env']: item for item in yamlZoneConfig['rocketmq']}
        rocketmq_config = rocketmq_config_map.get(env)
        if rocketmq_config is None:
            raise Exception('rocketmq集群:{env} 还未配置'.format(env=env))

        self.rocketmq_config = rocketmq_config
        jumper_config_map = {item['sshUser']: item for item in yamlZoneConfig['jumperList']}
        jumper_config = jumper_config_map.get(jumper_user)
        logging.info(f'jumper: {json.dumps(jumper_config)}')
        use_jumper = jumper_config.get('useJumper') if jumper_config else yamlZoneConfig.get('useJumper')
        jumper_ssh_user = jumper_config.get('sshUser') if jumper_config else yamlZoneConfig.get('jumperSshUser')
        jumper_ssh_key = jumper_config.get('sshKey') if jumper_config else yamlZoneConfig.get('jumperSshKey')
        jumper_ssh_password = jumper_config.get('sshPassword') if jumper_config else yamlZoneConfig.get('jumperSshPassword')
        if no_tunnel == False and use_jumper and env in ['pre', 'gray', 'prod']:
            # 创建ssh隧道，用于连接到远程Mysql数据库
            self.tunnel = SSHTunnelForwarder((yamlZoneConfig.get('jumperHost'), int(yamlZoneConfig.get('jumperPort'))),
                                             ssh_username=jumper_ssh_user,
                                             ssh_pkey=jumper_ssh_key,
                                             ssh_password=jumper_ssh_password,
                                             remote_bind_address=(
                                                 self.rocketmq_config.get('host'), self.rocketmq_config.get('port'))
                                             )
            self.tunnel.start()
            # 通过跳板机连接数据库
            self._connect_producer_with_jumper(self.rocketmq_config, self.tunnel)
        else:
            # 直接连接数据库
            self._connect_producer(self.rocketmq_config)

    # 如果跳板机断开，重新连接
    def __reconnect_if_turnel_break_down__(self):
        if hasattr(self, 'tunnel') and (not self.tunnel.is_active or not self.tunnel.is_alive):
            self.tunnel.start()
            self._connect_producer_with_jumper(self.rocketmq_config, self.tunnel)
            logging.info('__reconnect_if_turnel_break_down__ [%s]', str(self.tunnel))

    def _connect_producer_with_jumper(self, rocketmq_config, tunnel):
        # 跳板机连接数据库
        producer = Producer('PID-PreGrayDataMigrate')
        producer.set_namesrv_addr(f"{tunnel.local_bind_host}:{tunnel.local_bind_port}")
        producer.set_session_credentials(access_key=rocketmq_config['producer']['access-key'], access_secret=rocketmq_config['producer']['secret-key'], channel='')
        self.producer = producer

    # 直接连接数据库
    def _connect_producer(self, rocketmq_config):
        producer = Producer('PID-PreGrayDataMigrate')
        producer.set_namesrv_addr(f"{rocketmq_config['host']}:{rocketmq_config['port']}")
        producer.set_session_credentials(access_key=rocketmq_config['producer']['access-key'], access_secret=rocketmq_config['producer']['secret-key'], channel='')
        self.producer = producer

    def producer_start(self):
        self.producer.start()

    def producer_shutdown(self):
        self.producer.shutdown()

    def send_message(self, topic, tag, message):
        msg = Message(topic)  # topic名称
        # msg.set_keys(uuid.uuid4().bytes)  # 每个消息在业务层面的唯一标识码，要设置到keys字段，方便将来定位消息丢失问题。服务器会为每个消息创建索引（哈希索引），应用可以通过topic，key来查询这条消息内容，以及消息被谁消费。由于是哈希索引，请务必保证key尽可能唯一，这样可以避免潜在的哈希冲突。
        msg.set_tags(tag)  # 一个应用尽可能用一个Topic，消息子类型用tags来标识，tags可以由应用自由设置。只有发送消息设置了tags，消费方在订阅消息时，才可以利用tags在broker做消息过滤。
        msg.set_body(json.dumps(message).encode('utf-8'))  # 消息内容
        ret = self.producer.send_sync(msg)
        logging.info(ret)
        return ret
