#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2023 robins
#
# Distributed under terms of the MIT license/private/var/folders/pz/kwzyd6zd0s98pg_w3d0bd7gm0000gn/T/pip-uninstall-Tr_3rX/easy_install.py.


import inspect
import json
import logging
import os
import uuid
import pika

import yaml
from sshtunnel import SSHTunnelForwarder

logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)

# 获取当前脚本的文件路径
current_script_path = inspect.getframeinfo(inspect.currentframe()).filename
# 获取当前脚本所在的目录路径
current_script_directory = os.path.dirname(os.path.abspath(os.path.join(current_script_path, "..")))

# 分区配置字典表
ZONE_CONFIG_MAP = {
    # 上海分区
    'ShangHai': '/zone_shanghai.yaml',
    # 杭州分区
    'HangZhou': '/zone_hangzhou.yaml',
    # 主分区
    'Master': '/zone_shanghai.yaml',
    # 从分区
    'Slave': ['/zone_hangzhou.yaml']
}


class RabbitMqClient(object):
    def __init__(self, zone, env='prod', no_tunnel=False, jumper_user='schedulerx'):
        # 通过分区的简写 找到分区配置文件的路径
        if zone not in ZONE_CONFIG_MAP.keys():
            raise Exception('请检查分区配置，传入的分区:{zone}不存在'.format(zone=zone))
        zone_config_path = ZONE_CONFIG_MAP[zone]
        with open(current_script_directory + zone_config_path, 'r') as yamlConfigFile:
            yamlZoneConfig = yaml.safe_load(yamlConfigFile)

        # 通过集群的简写 找到集群配置
        rabbitmq_config_map = {item['env']: item for item in yamlZoneConfig['rabbitmq']}
        rabbitmq_config = rabbitmq_config_map.get(env)
        if rabbitmq_config is None:
            raise Exception('rabbitmq集群:{env} 还未配置'.format(env=env))

        self.rabbitmq_config = rabbitmq_config
        jumper_config_map = {item['sshUser']: item for item in yamlZoneConfig['jumperList']}
        jumper_config = jumper_config_map.get(jumper_user)
        logging.info(f'jumper: {json.dumps(jumper_config)}')
        use_jumper = jumper_config.get('useJumper') if jumper_config else yamlZoneConfig.get('useJumper')
        jumper_ssh_user = jumper_config.get('sshUser') if jumper_config else yamlZoneConfig.get('jumperSshUser')
        jumper_ssh_key = jumper_config.get('sshKey') if jumper_config else yamlZoneConfig.get('jumperSshKey')
        jumper_ssh_password = jumper_config.get('sshPassword') if jumper_config else yamlZoneConfig.get('jumperSshPassword')
        if no_tunnel == False and use_jumper and env in ['pre', 'gray', 'prod']:
            # 创建ssh隧道，用于连接到远程Mysql数据库
            self.tunnel = SSHTunnelForwarder((yamlZoneConfig.get('jumperHost'), int(yamlZoneConfig.get('jumperPort'))),
                                             ssh_username=jumper_ssh_user,
                                             ssh_pkey=jumper_ssh_key,
                                             ssh_password=jumper_ssh_password,
                                             remote_bind_address=(
                                                 self.rabbitmq_config.get('host'), self.rabbitmq_config.get('port'))
                                             )
            self.tunnel.start()
            # 通过跳板机连接数据库
            self._connect_channel_with_jumper(self.rabbitmq_config, self.tunnel)
        else:
            # 直接连接数据库
            self._connect_channel(self.rabbitmq_config)

    # 如果跳板机断开，重新连接
    def __reconnect_if_turnel_break_down__(self):
        if hasattr(self, 'tunnel') and (not self.tunnel.is_active or not self.tunnel.is_alive):
            self.tunnel.start()
            self._connect_channel_with_jumper(self.rabbitmq_config, self.tunnel)
            logging.info('__reconnect_if_turnel_break_down__ [%s]', str(self.tunnel))

    def _connect_channel_with_jumper(self, rabbitmq_config, tunnel):
        # 跳板机连接数据库
        self.connection = pika.BlockingConnection(
            pika.ConnectionParameters(host=tunnel.local_bind_host, port=tunnel.local_bind_port,
                                      credentials=pika.PlainCredentials(rabbitmq_config['username'],
                                                                        rabbitmq_config['password']),
                                      virtual_host=rabbitmq_config['vhost']))
        self.channel = self.connection.channel()

    # 直接连接数据库
    def _connect_channel(self, rabbitmq_config):
        self.connection = pika.BlockingConnection(
            pika.ConnectionParameters(host=rabbitmq_config['host'], port=rabbitmq_config['port'],
                                      credentials=pika.PlainCredentials(rabbitmq_config['username'],
                                                                        rabbitmq_config['password']),
                                      virtual_host=rabbitmq_config['vhost']))
        self.channel = self.connection.channel()

    def close(self):
        self.connection.close()

    def send_message(self, exchange, routing_key, queue, message):
        # self.channel.exchange_declare(exchange=exchange, passive=True)
        self.channel.queue_declare(queue=queue, passive=True)
        # self.channel.queue_bind(queue=queue, exchange=exchange, routing_key=routing_key)
        ret = self.channel.basic_publish(exchange=exchange, routing_key=routing_key if routing_key is not None and routing_key != '' else None, body=message)
        logging.info('send_message 「%s」', ret)
        return ret
