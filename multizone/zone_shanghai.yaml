#集群名字
name: "上海"
type: "master"

#配置执行这个集群的登录跳板机的登录方式。如果不配置直联
useJumper: 0
jumperHost: *************
jumperPort: 22
#robins local computer
#jumperSshUser: 'root'
#jumperSshKey: '/Users/<USER>/.ssh/id_rsa'
jumperSshUser: 'schedulerx'
jumperSshKey: '/home/<USER>/.ssh/id_rsa'

jumperList:
  # 本地配置 方便调试ob
  - sshUser: 'robinsli'
    sshKey: '/Users/<USER>/.ssh/id_rsa'
    useJumper: 1
  - sshUser: 'jiangxiaofeng'
    sshKey: '/Users/<USER>/.ssh/id_rsa'
    sshPassword: 'jiangxf'
    useJumper: 1
  - sshUser: 'schedulerx'
    sshKey: '/home/<USER>/.ssh/id_rsa'
    useJumper: 0
  - sshUser: 'jenkins'
    sshKey: '/home/<USER>/.ssh/id_rsa'
    useJumper: 0

#DB配置
dbList:
  # 测试DB
  - cluster: "test"
    user: "h_test_backend"
    password: "03ca22bbb74eb001864fa517dab203F8"
    host: "pc-uf6p03ez7eoy4n103.mysql.polardb.rds.aliyuncs.com"
    port: 3306
    mode: "readwrite"
    database:
      - 'abc_cis_smart_dispensing'
      - 'abc_cis_smart_dispensing_dev'
      - 'abc_cis_smart_dispensing_test'
      - 'abc_cis_basic_dev'
      - 'abc_bis_dev'
      - 'abc_cis_wallet_dev'
      - 'abc_cis_shebao_dev'
      - 'abc_cis_invoice_dev'
      - 'abc_cis_wechatpay_dev'
      - 'abc_cis_promotion_dev'
      - 'abc_cis_dispensing_dev'
      - 'abc_cis_search_dev'
      - 'abc_cis_message_dev'
      - 'abc_cis_im_dev'
      - 'abc_cis_registration_dev'
      - 'abc_cis_patient_dev'
      - 'abc_cis_examination_dev'
      - 'abc_cis_nurse_test'
      - 'abc_cis_goods_test'
      - 'abc_cis_outpatient_test'
      - 'abc_cis_charge_test'
      - 'abc_cis_charge_record_test'
      - 'abc_cis_patientorder_test'
      - 'abc_cis_supervision_test'
      - 'abc_cis_basic_test'
      - 'abc_bis_test'
      - 'abc_cis_ai_test'
      - 'abc_cis_wallet_test'
      - 'abc_cis_shebao_test'
      - 'abc_cis_invoice_test'
      - 'abc_cis_wechatpay_test'
      - 'abc_cis_promotion_test'
      - 'abc_cis_dispensing_test'
      - 'abc_cis_search_test'
      - 'abc_cis_message_test'
      - 'abc_cis_im_test'
      - 'abc_cis_patient_test'
      - 'abc_cis_registration_test'
      - 'abc_cis_examination_test'
      - 'abc_cis_nurse_test'
      - 'abc_cis_goods_test'
      - 'abc_cis_outpatient_test'
      - 'abc_cis_patientorder_test'
      - 'abc_cis_supervision_test'
      - 'abc_oa_test'
      - 'abc_his_emr_test'
      - 'abc_cis_property_test'
      - 'abc_cis_goods_log_test'
      - 'abc_his_advice_test'
      - "abc_pe_order_test"
      - "abc_his_charge_test"
      - 'abc_cis_medical_plan_test'
      - 'abc_pe_charge_test'
      - "abc_cis_gsp_test"
      - 'abc_cis_data_analysis_test'

# 开发DB
  - cluster: "dev"
    user: "h_dev_backend"
    password: "d6f48652d7ca9510cf90673D4342e017"
    host: "pc-uf6p03ez7eoy4n103.mysql.polardb.rds.aliyuncs.com"
    port: 3306
    mode: "readwrite"
    database:
      - 'abc_cis_smart_dispensing'
      - 'abc_cis_smart_dispensing_dev'
      - 'abc_cis_smart_dispensing_test'
      - 'abc_cis_basic_dev'
      - 'abc_bis_dev'
      - 'abc_cis_wallet_dev'
      - 'abc_cis_domain_dev'
      - 'abc_cis_shebao_dev'
      - 'abc_cis_invoice_dev'
      - 'abc_cis_wechatpay_dev'
      - 'abc_cis_promotion_dev'
      - 'abc_cis_dispensing_dev'
      - 'abc_cis_search_dev'
      - 'abc_cis_message_dev'
      - 'abc_cis_im_dev'
      - 'abc_cis_registration_dev'
      - 'abc_cis_patient_dev'
      - 'abc_cis_examination_dev'
      - 'abc_cis_nurse_dev'
      - 'abc_cis_goods_dev'
      - 'abc_cis_outpatient_dev'
      - 'abc_cis_charge_dev'
      - 'abc_cis_charge_record_dev'
      - 'abc_cis_patientorder_dev'
      - 'abc_cis_supervision_dev'
      - 'abc_cis_basic_test'
      - 'abc_bis_test'
      - 'abc_cis_wallet_test'
      - 'abc_cis_shebao_test'
      - 'abc_cis_invoice_test'
      - 'abc_cis_wechatpay_test'
      - 'abc_cis_promotion_test'
      - 'abc_cis_dispensing_test'
      - 'abc_cis_search_test'
      - 'abc_cis_message_test'
      - 'abc_cis_im_test'
      - 'abc_cis_patient_test'
      - 'abc_cis_registration_test'
      - 'abc_cis_examination_test'
      - 'abc_cis_nurse_test'
      - 'abc_cis_goods_test'
      - 'abc_cis_medical_plan_test'
      - 'abc_pe_charge_test'
      - 'abc_cis_goods_dev'
      - 'abc_oa_dev'
      - 'abc_oa_test'
      - 'abc_cis_outpatient_test'
      - 'abc_cis_patientorder_test'
      - 'abc_cis_supervision_test'
      - 'abc_cis_shorturl_dev'
      - 'abc_his_emr_dev'
      - 'abc_his_ward_dev'
      - 'abc_cis_form_dev'
      - 'abc_cis_property_dev'
      - 'abc_his_advice_dev'
      - "abc_pe_order_dev"
      - "abc_his_charge_dev"
      - "abc_cis_medical_plan_dev"
      - 'abc_pe_charge_dev'
      - 'abc_scrm_customer_dev'
      - 'abc_cis_data_analysis_dev'
      - 'abc_cis_data_analysis_test'
      - 'abc_cis_outpatient_external_backup_dev'
      - 'abc_cis_outpatient_record_dev'
      - 'abc_cis_gsp_dev'
  #ADB
  - cluster: "adb"
    user: "m_schedulex"
    password: "df3d26e55a972e6534385c7cB0185343"
    host: "t5pqnkyzalom8-ro0.cn-shanghai.oceanbase.aliyuncs.com"
    port: 3306
    mode: "readonly"
    database:
      - "abc_cis_basic"
      - "abc_bis"
      - "abc_ops_stat"
      - "abc_cis_wallet"
      - "abc_cis_shebao"
      - "abc_cis_invoice"
      - "abc_cis_wechatpay"
      - "abc_cis_promotion"
      - "abc_cis_dispensing"
      - "abc_cis_search"
      - "abc_cis_message"
      - "abc_cis_im"
      - "abc_cis_registration"
      - "abc_cis_examination"
      - "abc_cis_nurse"
      - "abc_cis_patient"
      - "abc_cis_goods"
      - "abc_cis_goods_log"
      - "abc_cis_outpatient"
      - "abc_cis_patientorder"
      - "abc_cis_charge"
      - "abc_cis_charge_record"
      - "abc_scrm_customer"
      - "abc_cis_property"
      - "abc_pe_order"
      - "abc_pe_charge"
      - "abc_his_charge"
      - "abc_his_advice"
  # "ODS写"
  - cluster: "ods-write"
    user: "m_schedulex"
    password: "df3d26e55a972e6534385c7cB0185343"
    host: "t5pqnkyzalom8-ro0.cn-shanghai.oceanbase.aliyuncs.com"
    port: 3306
    mode: "readonly"
    database:
      - "abc_cis_goods_log"
  #ODS读
  - cluster: "ods"
    user: "m_schedulex"
    password: "df3d26e55a972e6534385c7cB0185343"
    host: "t5pqnkyzalom8-ro0.cn-shanghai.oceanbase.aliyuncs.com"
    port: 3306
    mode: "readonly"
    database:
      - "abc_cis_basic"
      - "abc_bis"
      - "abc_ops_stat"
      - "abc_cis_wallet"
      - "abc_cis_shebao"
      - "abc_cis_invoice"
      - "abc_cis_wechatpay"
      - "abc_cis_promotion"
      - "abc_cis_dispensing"
      - "abc_cis_search"
      - "abc_cis_message"
      - "abc_cis_im"
      - "abc_cis_registration"
      - "abc_cis_examination"
      - "abc_cis_nurse"
      - "abc_cis_patient"
      - "abc_cis_goods"
      - "abc_cis_outpatient"
      - "abc_cis_patientorder"
      - "abc_cis_charge"
      - "abc_cis_charge_record"
      - "abc_scrm_customer"
      - "abc_cis_property"
      - "bp"
  # base实例
  - cluster: "abc_cis_mixed"
    user: "abc_sa"
    password: "aud806cuAwo!wML5BceoWop$mh4ao^DL"
    host: "pc-uf6801s1t1q5gh1vl.mysql.polardb.rds.aliyuncs.com"
    port: 3306
    mode: "readwrite"
    database:
      - "abc_cis_property"
      - "abc_cis_basic"
      - "abc_ops_stat"
      - "abc_cis_shorturl"
      - "abc_cis_form"
      - "abc_cis_domain"
  - cluster: "internal_system"
    user: "abc_sa"
    password: "aud806cuAwo!wML5BceoWop$mh4ao^DL"
    host: "pc-uf6zr7h146n2lf357.rwlb.rds.aliyuncs.com"
    port: 3306
    mode: "readwrite"
    database:
      - "abc_oa"
      - "abc_oa_data_transfer"
      - "abc_cis_help"
      - "abc_bis"
  #MC-IM-PATIENT实例
  - cluster: "abc_cis_account_base"
    user: "abc_sa"
    password: "aud806cuAwo!wML5BceoWop$mh4ao^DL"
    host: "pc-uf68y9b4kx13waz2f.mysql.polardb.rds.aliyuncs.com"
    port: 3306
    mode: "readwrite"
    database:
      - "abc_cis_message"
      - "abc_cis_im"
      - "abc_cis_patient"
      - "abc_cis_mc"
      - "abc_cis_report"
  #SCRM-HOSPITAL实例
  - cluster: "scrm_hospital"
    user: "abc_sa"
    password: "aud806cuAwo!wML5BceoWop$mh4ao^DL"
    host: "pc-uf62ehi56inw9xi4o.mysql.polardb.rds.aliyuncs.com"
    port: 3306
    mode: "readwrite"
    database:
      - "abc_his_advice"
      - "abc_his_charge"
      - "abc_his_emr"
      - "abc_his_ward"
      - "abc_scrm_basic"
      - "abc_scrm_channel"
      - "abc_scrm_customer"
      - "abc_scrm_kf"
      - "abc_scrm_material"
      - "abc_pe_order"
      - "abc_pe_charge"
      - "abc_pha_gsp"
  #统计&搜索实例
  - cluster: "abc_cis_stat"
    user: "abc_sa"
    password: "aud806cuAwo!wML5BceoWop$mh4ao^DL"
    host: "pc-uf64q0091s06g91a2.mysql.polardb.rds.aliyuncs.com"
    port: 3306
    mode: "readwrite"
    database:
      - "abc_cis_search"
      - "abc_cis_supervision"
  #库存
  - cluster: "abc_cis_stock"
    user: "abc_sa"
    password: "aud806cuAwo!wML5BceoWop$mh4ao^DL"
    host: "pc-uf694xp877zzax27o.mysql.polardb.rds.aliyuncs.com"
    port: 3306
    mode: "readwrite"
    database:
      - "abc_cis_goods"
  #库存&发药日志库
  - cluster: "abc_cis_stock_zip"
    user: "abc_sa"
    password: "aud806cuAwo!wML5BceoWop$mh4ao^DL"
    host: "pc-uf6n7apxmi7v16oy7.mysql.polardb.rds.aliyuncs.com"
    port: 3306
    mode: "readwrite"
    database:
      - "abc_cis_goods_log"
      - "abc_cis_dispensing"
      - "abc_cis_oss"
      - "abc_oa_kf"
      - "abc_cis_processing"
      - "abc_cis_monitor"
  #收费&优惠卷
  - cluster: "abc_cis_charge"
    user: "abc_sa"
    password: "aud806cuAwo!wML5BceoWop$mh4ao^DL"
    host: "pc-uf6mi072uovlh2jn1.mysql.polardb.rds.aliyuncs.com"
    port: 3306
    mode: "readwrite"
    database:
      - "abc_cis_promotion"
      - "abc_cis_charge"
  # 收费交易流水
  - cluster: "abc_cis_charge_record"
    user: "abc_sa"
    password: "aud806cuAwo!wML5BceoWop$mh4ao^DL"
    host: "pc-uf6i64trrh5lba22f.mysql.polardb.rds.aliyuncs.com"
    port: 3306
    mode: "readwrite"
    database:
      - "abc_cis_charge_record"
      - "abc_cis_outpatient_record"
  #社保
  - cluster: "abc_cis_bill"
    user: "abc_sa"
    password: "aud806cuAwo!wML5BceoWop$mh4ao^DL"
    host: "pc-uf67hy1519j3j8r80.mysql.polardb.rds.aliyuncs.com"
    port: 3306
    mode: "readwrite"
    database:
      - "abc_cis_wallet"
      - "abc_cis_shebao"
      - "abc_cis_invoice"
      - "abc_cis_wechatpay"
      - "abc_mp_charge_center"
  #门诊 & 检查检验 & patientOrder
  - cluster: "abc_cis_outpatient"
    user: "abc_sa"
    password: "aud806cuAwo!wML5BceoWop$mh4ao^DL"
    host: "pc-uf634ccb06g16t26q.mysql.polardb.rds.aliyuncs.com"
    port: 3306
    mode: "readwrite"
    database:
      - "abc_cis_registration"
      - "abc_cis_nurse"
      - "abc_cis_examination"
      - "abc_cis_patientorder"
      - "abc_cis_consultation"
      - "abc_cis_outpatient"
      - "abc_cis_medical_plan"
  # OceanBase
  - cluster: "ob"
    user: "m_schedulex"
    password: "df3d26e55a972e6534385c7cB0185343"
    host: "t5pqnkyzalom8-ro0.cn-shanghai.oceanbase.aliyuncs.com"
    port: 3306
    mode: "readonly"
    database:
      - "abc_cis_basic"
      - "abc_oa"
      - "abc_bis"
      - "abc_ops_stat"
      - "abc_cis_wallet"
      - "abc_cis_shebao"
      - "abc_cis_invoice"
      - "abc_cis_wechatpay"
      - "abc_cis_promotion"
      - "abc_cis_dispensing"
      - "abc_cis_search"
      - "abc_cis_message"
      - "abc_cis_im"
      - "abc_cis_registration"
      - "abc_cis_examination"
      - "abc_cis_nurse"
      - "abc_cis_patient"
      - "abc_cis_goods"
      - "abc_cis_outpatient"
      - "abc_cis_patientorder"
      - "abc_cis_charge"
      - "abc_scrm_customer"
      - "abc_cis_property"
      - "bp"
      - "abc_cis_dz_data_sync"
      - "abc_cis_goods_log"
      - "abc_cis_ai"
      - "abc_cis_outpatient_record"
      - "abc_cis_monitor"
  - cluster: "outpatient_external_backup"
    user: "m_outpatient_external_backup"
    password: "bP6o52ELR1QUXJT8jpZO1"
    host: "pc-uf6eudnn7246anub8.mysql.polardb.rds.aliyuncs.com"
    port: 3306
    mode: "readwrite"
    database:
      - "abc_cis_outpatient_external_backup"
      - "abc_cis_outpatient_external_backup_test"
#ES配置
esList:
  - esBase: "abc-search-dev"
    host: 'es-cn-tl32aueks0028dg1m.elasticsearch.aliyuncs.com'
    port: 9200
    username: 'elastic'
    password: '379f2d6ce1a1a52bc6804ac65d77D'
  - esBase: "abc-search-test"
    host: 'es-cn-tl32aueks0028dg1m.elasticsearch.aliyuncs.com'
    port: 9200
    username: 'elastic'
    password: '379f2d6ce1a1a52bc6804ac65d77D'
  - esBase: "abc-search-prod"
    host: 'es-cn-tl32b5c9e00335sgr.elasticsearch.aliyuncs.com'
    port: 9200
    username: "elastic"
    password: "379f2d6ce1a1a52bc6804ac65d77D"
  - esBase: "abc-search-prod-normal"
    host: 'es-cn-tl32ccbqp000x98zi.elasticsearch.aliyuncs.com'
    port: 9200
    username: "elastic"
    password: "379f2d6ce1a1a52bc6804ac65d77E"
#redis配置
redisList:
  - name: "dev"
    host: "dev.redis.abczs.cn"
    port: 6379
  - name: "test"
    host: "**************"
    port: 6379
  - name: "abc-redis"
    host: "r-uf6nwm1ls7omou5kht.redis.rds.aliyuncs.com"
    port: 6379
  - name: "abc-ha-redis"
    host: "r-uf6v7bqm58u2pzrc4v.redis.rds.aliyuncs.com"
    port: 6379
  - name: "abc-goods-redis"
    host: "r-uf6cx731ddew2rqe88.redis.rds.aliyuncs.com"
    port: 6379
rocketmq:
  - env: "dev"
    host: rmq-cn-7pp2tx9qo03-vpc.cn-shanghai.rmq.aliyuncs.com
    port: 8080
    producer:
      access-key: i4tV317J78Gr3OY3
      secret-key: 1rB48PiAUO2oBS7r
  - env: "test"
    host: rmq-cn-i7m2u74lf0b-vpc.cn-shanghai.rmq.aliyuncs.com
    port: 8080
    producer:
      access-key: g32ycMR2QE6atVH4
      secret-key: S6Bt8FGz8aR78u12
  - env: "pre"
    host: ep-uf6id4aa091e63375d8d.epsrv-uf6lsm3b2e4jyv0gnsrc.cn-shanghai.privatelink.aliyuncs.com
    port: 8080
    producer:
      access-key: vcPDIEFydb924qhs
      secret-key: cFq8Gy1uiBRyNVVh
    consumer:
      access-key: vcPDIEFydb924qhs
      secret-key: cFq8Gy1uiBRyNVVh
  - env: "gray"
    host: ep-uf6i3342aa21105908e4.epsrv-uf6lsm3b2e4jyv0gnsrc.cn-shanghai.privatelink.aliyuncs.com
    port: 8080
    producer:
      access-key: 0azxrYJaK3WpTK9k
      secret-key: COFVk6sU27a0H0Id
    consumer:
      access-key: 0azxrYJaK3WpTK9k
      secret-key: COFVk6sU27a0H0Id
  - env: "prod"
    host: ep-uf6i23a8a732c669e66b.epsrv-uf6lsm3b2e4jyv0gnsrc.cn-shanghai.privatelink.aliyuncs.com
    port: 8080
    producer:
      access-key: qXiVAT6BsoB5f16x
      secret-key: mL9g51H40K1j03J9
    consumer:
      access-key: qXiVAT6BsoB5f16x
      secret-key: mL9g51H40K1j03J9
rabbitmq:
  - env: "dev"
    host: dev.rabbitmq.abczs.cn
    username: 'guest'
    password: 'guest'
    port: 5672
    vhost: /ha
  - env: "test"
    host: test.rabbitmq.abczs.cn
    username: 'guest'
    password: 'guest'
    port: 5672
    vhost: /ha
  - env: "pre"
    host: rabbitmq-serverless-cn-rno3yzdr209.cn-shanghai.amqp-21.vpc.mq.amqp.aliyuncs.com
    username: MjpyYWJiaXRtcS1zZXJ2ZXJsZXNzLWNuLXJubzN5emRyMjA5OkxUQUk1dDdxSlFqdUFiMXF2VHBtSDh5ZQ==
    password: Mjk0NkYxMTlDRDczQTNCQkE0QTY0QkQ4MkFBQzhEOTI1NTQ0OEQzRToxNzI5Njc0NDE0NDYw
    port: 5672
    vhost: /ha
  - env: "gray"
    host: rabbitmq-cn-0dw429jzk05.cn-shanghai.amqp-10.vpc.mq.amqp.aliyuncs.com
    username: MjpyYWJiaXRtcS1jbi0wZHc0MjlqemswNTpMVEFJNXQ3cUpRanVBYjFxdlRwbUg4eWU=
    password: N0MwNDJCRDI4RjhCRDZEMjVCMzQ1OUNERDkxMzdGNkQ3NDY5NzczRToxNzM1MTc5NTg0ODgz
    port: 5672
    vhost: /ha
  - env: "prod"
    host: rabbitmq-cn-vc240ug8l01.cn-shanghai.amqp-3.vpc.mq.amqp.aliyuncs.com
    username: MjpyYWJiaXRtcS1jbi12YzI0MHVnOGwwMTpMVEFJNXQ3cUpRanVBYjFxdlRwbUg4eWU=
    password: MjgyRTNBNzcyMEU0RTUwOTE1MkQwRUE5Rjk0NjRDMTE1OTg0RENBMzoxNzMyNzk0NzkyMDMw
    port: 5672
    vhost: /ha
#MQ配置
mqList:

aliyun:
  log:
    endpoint: "cn-shanghai.log.aliyuncs.com"
    access_key_id: "LTAI4FuQ6NgxxQETz8pjXFb5"
    access_key_secret: "******************************"
    project: "abc-cis-log"

rpcList:
  rpcHost: 'pre.rpc.abczs.cn'
  grayRpcHost: 'gray.rpc.abczs.cn'
  prodRpcHost: 'prod.rpc.abczs.cn'
