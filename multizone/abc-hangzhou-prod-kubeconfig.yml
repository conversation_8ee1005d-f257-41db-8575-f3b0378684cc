apiVersion: v1
clusters:
- cluster:
    server: https://120.55.71.234:6443
    certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURUakNDQWphZ0F3SUJBZ0lVS1dVb2xSZ3FKdERvS2JrYmJkbjROaHVwaVZZd0RRWUpLb1pJaHZjTkFRRUwKQlFBd1BqRW5NQThHQTFVRUNoTUlhR0Z1WjNwb2IzVXdGQVlEVlFRS0V3MWhiR2xpWVdKaElHTnNiM1ZrTVJNdwpFUVlEVlFRREV3cHJkV0psY201bGRHVnpNQ0FYRFRJek1EVXhNVEEyTXpRd01Gb1lEekl3TlRNd05UQXpNRFl6Ck5EQXdXakErTVNjd0R3WURWUVFLRXdob1lXNW5lbWh2ZFRBVUJnTlZCQW9URFdGc2FXSmhZbUVnWTJ4dmRXUXgKRXpBUkJnTlZCQU1UQ210MVltVnlibVYwWlhNd2dnRWlNQTBHQ1NxR1NJYjNEUUVCQVFVQUE0SUJEd0F3Z2dFSwpBb0lCQVFDaE9nZW8weXZRWVFZcUN4aDljcmVzTVI2SnpoUFVlN3BmbVNIaDJBa3FZK2J1L2NLQVJjK1RnOWdGCmlxMVpIVkwweHB2ZUozVjlyd0UyNlREQURnZDNoY0ZvUkoyNWlCMjdXNnZBckZVOW0yejFpWTJhRlhvd0JMTDYKL3JoYTYxZXZFdHJPRG1XdkJSS0VrdjBYTnNJVnBGQVRkUXZPb3RjejhpYnR2QlBxcURKMThZMHRnUlpRRjl6SApxUFA2T3BPUW9obndJbHdQNS8rc09NMW9uTkUzR1hkMWxGSnBOMlY2QkEwbmZzbEtHU2hlWEdQa0N0Ti90cGY5CllPdGJUZ2NsYWo5c3E5UXdUN0tSMkdsMU5ieHBHdFRaUGVmTGlqT2RkUmVBem8vUi9LUThZMitrS09lVUYraE8KeWY2Wmlabk1sNm1KSmIxcHlLaDJKUnUweTkxTkFnTUJBQUdqUWpCQU1BNEdBMVVkRHdFQi93UUVBd0lDckRBUApCZ05WSFJNQkFmOEVCVEFEQVFIL01CMEdBMVVkRGdRV0JCVHRqRFNmMU1PTmxwMmRDL2hTQURxM09VVkxIekFOCkJna3Foa2lHOXcwQkFRc0ZBQU9DQVFFQWcyY2pzb1BSbUU4YWp0b3Z2YUFXcnhUUHVwbTVTdmxQMHZwZ2YyemsKYmxPVXN1aWx3TDAxaG12am1rcnVwUGlaUlBTSXNVMWZyYmt4YlZaVUpxcHNob3FBY3R3R1Z1YnJxTllVeC9lbApuSURpenh6RmdlWWFDTDNYRU9hdGFVKzdhMFE1cVliVTZQc282dU90THdYRzJIZERVRFNoeXVmbW1UUjNVZUZOCnlobU5nTW9aUzdxUEdMaWN5NDJUOU5QZWlldGxQUlF4eTFOMTZqSWhWWEF5MTB1VklzckVzNmt5TlV1WjVkTDEKb2U1bWd0VDlPWVQ3a0NlMFY1UlhEaTRaUFlja2E1Ujd2RWlFeGdESGZoeXVuVC9iTEt2bC9IWXliU1BrdXN6Kwp3WnV4NStrQlhldmkrOVA0WDJTa0pTZEROaFpKelV6OXQrelVoaU94blQxdXB3PT0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=
  name: kubernetes
contexts:
- context:
    cluster: kubernetes
    user: "257763494863521822"
  name: 257763494863521822-c27cd45e5ade1408bbf03f4a8a72ca792
current-context: 257763494863521822-c27cd45e5ade1408bbf03f4a8a72ca792
kind: Config
preferences: {}
users:
- name: "257763494863521822"
  user:
    client-certificate-data: 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
    client-key-data: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************