#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2023 robins
#
# Distributed under terms of the MIT license/private/var/folders/pz/kwzyd6zd0s98pg_w3d0bd7gm0000gn/T/pip-uninstall-Tr_3rX/easy_install.py.


import inspect
import logging
import os
import redis
import yaml
from sshtunnel import SSHTunnelForwarder
import json

logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)

# 获取当前脚本的文件路径
current_script_path = inspect.getframeinfo(inspect.currentframe()).filename
# 获取当前脚本所在的目录路径
current_script_directory = os.path.dirname(os.path.abspath(os.path.join(current_script_path, "..")))

# 分区配置字典表
ZONE_CONFIG_MAP = {
    # 上海分区
    'ShangHai': '/zone_shanghai.yaml',
    # 杭州分区
    'HangZhou': '/zone_hangzhou.yaml',
    # 主分区
    'Master': '/zone_shanghai.yaml',
    # 从分区
    'Slave': ['/zone_hangzhou.yaml']
}
# 分区配置字典表
REDIS_INFO_MAP = {
    # dev&test
    'ShangHai_abcRedis_dev': 'dev.redis.abczs.cn',
    'ShangHai_abcRedis_test': '**************',
    # 上海分区
    'ShangHai_abcHaRedis_prod': 'r-uf6v7bqm58u2pzrc4v.redis.rds.aliyuncs.com',
    'ShangHai_abcRedis_prod': 'r-uf6nwm1ls7omou5kht.redis.rds.aliyuncs.com',
    # 杭州分区
    'HangZhou_abcHaRedis_prod': 'r-bp1jgbw6mnhlvrnc4r.redis.rds.aliyuncs.com',
    'HangZhou_abcRedis_prod': 'r-bp17i2r0mkcbs9jp5t.redis.rds.aliyuncs.com'
}


def redisHost(abcRegion, redisName, env='prod'):
    return REDIS_INFO_MAP[abcRegion + '_' + redisName + '_' + env]


class RedisClient(object):
    def __init__(self, zone, cluster, db, env='prod', no_tunnel=False, jumper_user='schedulerx'):
        if env is not None and env in ['pre', 'gray']:
            env = 'prod'
        # 通过分区的简写 找到分区配置文件的路径
        if zone not in ZONE_CONFIG_MAP.keys():
            raise Exception('请检查分区配置，传入的分区:{zone}不存在'.format(zone=zone))
        zone_config_path = ZONE_CONFIG_MAP[zone]
        with open(current_script_directory + zone_config_path, 'r') as yamlConfigFile:
            yamlZoneConfig = yaml.safe_load(yamlConfigFile)

        # 通过集群的简写 找到集群配置
        cluster = env if env != 'prod' else cluster
        redis_config_map = {item['name']: item for item in yamlZoneConfig['redisList']}
        redis_config = redis_config_map.get(cluster)
        if redis_config is None:
            raise Exception('redis集群:{cluster} 还未配置'.format(cluster=cluster))

        self.redis_config = redis_config
        self.db = db
        jumper_config_map = {item['sshUser']: item for item in yamlZoneConfig['jumperList']}
        jumper_config = jumper_config_map.get(jumper_user)
        logging.info(f'jumper: {json.dumps(jumper_config)}')
        use_jumper = jumper_config.get('useJumper') if jumper_config else yamlZoneConfig.get('useJumper')
        jumper_ssh_user = jumper_config.get('sshUser') if jumper_config else yamlZoneConfig.get('jumperSshUser')
        jumper_ssh_key = jumper_config.get('sshKey') if jumper_config else yamlZoneConfig.get('jumperSshKey')
        jumper_ssh_password = jumper_config.get('sshPassword') if jumper_config else yamlZoneConfig.get('jumperSshPassword')
        if no_tunnel == False and use_jumper and env == 'prod':
            # 创建ssh隧道，用于连接到远程Mysql数据库
            self.tunnel = SSHTunnelForwarder((yamlZoneConfig.get('jumperHost'), int(yamlZoneConfig.get('jumperPort'))),
                                             ssh_username=jumper_ssh_user,
                                             ssh_pkey=jumper_ssh_key,
                                             ssh_password=jumper_ssh_password,
                                             remote_bind_address=(
                                                 self.redis_config.get('host'), self.redis_config.get('port'))
                                             )
            self.tunnel.start()
            # 通过跳板机连接数据库
            self.client = self._connect_db_with_jumper(self.redis_config, db, self.tunnel)
        else:
            # 直接连接数据库
            self.client = self._connect_db(self.redis_config, db)

    # 如果跳板机断开，重新连接
    def __reconnect_if_turnel_break_down__(self):
        if hasattr(self, 'tunnel') and (not self.tunnel.is_active or not self.tunnel.is_alive):
            self.tunnel.start()
            self.client = self._connect_db_with_jumper(self.redis_config, self.db, self.tunnel)
            logging.info('__reconnect_if_turnel_break_down__ [%s]', str(self.tunnel))

    # 跳板机连接数据库
    def _connect_db_with_jumper(self, redis_config, db, tunnel):
        return redis.Redis(host=tunnel.local_bind_host, port=tunnel.local_bind_port, db=db,
                           username=redis_config.get('user'), password=redis_config.get('password'))

    # 直接连接数据库
    def _connect_db(self, redis_config, db):
        return redis.Redis(host=redis_config['host'], port=redis_config['port'], db=db,
                           username=redis_config.get('user'), password=redis_config.get('password'))
