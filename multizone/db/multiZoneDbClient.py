#! /usr/bin/env python2
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright © 2023 robins
#
# Distributed under terms of the MIT license/private/var/folders/pz/kwzyd6zd0s98pg_w3d0bd7gm0000gn/T/pip-uninstall-Tr_3rX/easy_install.py.


import pymysql
import yaml
from sshtunnel import SSHTunnelForwarder
import logging
import os
import inspect
import json

logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)

# 获取当前脚本的文件路径
current_script_path = inspect.getframeinfo(inspect.currentframe()).filename
# 获取当前脚本所在的目录路径
current_script_directory = os.path.dirname(os.path.abspath(os.path.join(current_script_path, "..")))

# 分区配置字典表
ZONE_COFNIG_MAP = {
    # 上海分区
    'ShangHai': '/zone_shanghai.yaml',
    # 杭州分区
    'HangZhou': '/zone_hangzhou.yaml',
    # 主分区
    'Master': '/zone_shanghai.yaml',
    # 从分区
    'Slave': ['/zone_hangzhou.yaml']
}


def escape_string(value):
    return pymysql.converters.escape_string(value)


class DBClient(object):
    def __init__(self, zone, cluster, database, env='prod', no_tunnel=False, jumper_user='schedulerx'):
        if env is not None and env in ['pre', 'gray']:
            env = 'prod'
        # 通过分区的简写 找到分区配置文件的路径
        if zone not in ZONE_COFNIG_MAP.keys():
            raise Exception('请检查分区配置，传入的分区:{zone}不存在'.format(zone=zone))
        zone_config_path = ZONE_COFNIG_MAP[zone]
        with open(current_script_directory + zone_config_path, 'r') as yamlConfigFile:
            yamlZoneConfig = yaml.safe_load(yamlConfigFile)

        # 通过集群的简写 找到集群配置
        dbYamlConfig = None
        cluster = env if env != 'prod' else cluster
        for db_config in yamlZoneConfig['dbList']:
            if db_config['cluster'] == cluster:
                dbYamlConfig = db_config
                break
        if dbYamlConfig is None:
            raise Exception('数据库集群:{cluster} 还未配置'.format(cluster=cluster))

        database = '{database}{env}'.format(database=database, env='_{}'.format(env) if env != 'prod' else '')
        if database not in dbYamlConfig['database']:
            raise Exception(
                '数据库:{database} 还未配置{dbYamlConfig}'.format(database=database, dbYamlConfig=dbYamlConfig))

        self.db_config = dbYamlConfig
        self.database = database
        jumper_config_map = {item['sshUser']: item for item in yamlZoneConfig['jumperList']}
        jumper_config = jumper_config_map.get(jumper_user)
        logging.info(f'jumper: {json.dumps(jumper_config)}')
        use_jumper = jumper_config.get('useJumper') if jumper_config else yamlZoneConfig.get('useJumper')
        jumper_ssh_user = jumper_config.get('sshUser') if jumper_config else yamlZoneConfig.get('jumperSshUser')
        jumper_ssh_key = jumper_config.get('sshKey') if jumper_config else yamlZoneConfig.get('jumperSshKey')
        jumper_ssh_password = jumper_config.get('sshPassword') if jumper_config else yamlZoneConfig.get('jumperSshPassword')
        if no_tunnel == False and use_jumper and env == 'prod':
            # 创建ssh隧道，用于连接到远程Mysql数据库
            self.tunnel = SSHTunnelForwarder((yamlZoneConfig.get('jumperHost'), int(yamlZoneConfig.get('jumperPort'))),
                                             ssh_username=jumper_ssh_user,
                                             ssh_pkey=jumper_ssh_key,
                                             ssh_password=jumper_ssh_password,
                                             remote_bind_address=(self.db_config.get('host'), 3306)
                                             )
            self.tunnel.start()
            # 通过跳板机连接数据库
            self.client = self._connect_db_with_jumper(self.db_config, database, self.tunnel)
            logging.info('''隧道链接  Zone:{zone},Cluster:{cluster},DataBase:{database},Env:{env},NoTunnel:{no_tunnel} connectToTunnel Success client:{client}'''.format(zone=zone, cluster=cluster, database=database, env=env, no_tunnel=no_tunnel,client = self.client))
        else:
            # 直接连接数据库
            self.client = self._connect_db(self.db_config, database)
            logging.info('''直接链接 Zone:{zone},Cluster:{cluster},DataBase:{database},Env:{env},NoTunnel:{no_tunnel} connectDirectToDataBase Success client:{client}'''.format(zone=zone, cluster=cluster, database=database, env=env, no_tunnel=no_tunnel,client = self.client))

    # 如果跳板机断开，重新连接
    def __reconnect_if_turnel_break_down__(self):
        if hasattr(self, 'tunnel') and (not self.tunnel.is_active or not self.tunnel.is_alive):
            self.tunnel.start()
            self.client = self._connect_db_with_jumper(self.db_config, self.database, self.tunnel)
            logging.info('__reconnect_if_turnel_break_down__ [%s]', str(self.tunnel))

    # 跳板机连接数据库
    def _connect_db_with_jumper(self, db_config, database, tunnel):
        return pymysql.connect(host=tunnel.local_bind_host, port=tunnel.local_bind_port, user=db_config['user'],
                               password=db_config['password'], database=database, charset='utf8')

    # 直接连接数据库
    def _connect_db(self, db_config, database):
        return pymysql.connect(host=db_config['host'], port=db_config['port'], user=db_config['user'],
                               password=db_config['password'], database=database, charset='utf8')

    # 获取最后一行的id
    def lastrowid(self, sql):
        self.__reconnect_if_turnel_break_down__()
        cursor = self.client.cursor(pymysql.cursors.DictCursor)
        cursor.execute(sql)
        return cursor.lastrowid

    # 获取一行数据
    def fetchone(self, sql):
        self.__reconnect_if_turnel_break_down__()
        cursor = self.client.cursor(pymysql.cursors.DictCursor)
        cursor.execute(sql)
        return cursor.fetchone()

    # 获取所有数据
    def fetchall(self, sql):
        # logging.info('execute sql:\n%s', sql)
        self.__reconnect_if_turnel_break_down__()
        cursor = self.client.cursor(pymysql.cursors.DictCursor)
        cursor.execute(sql)
        return cursor.fetchall()

    # 执行sql
    def execute(self, sql):
        # 统计下sql执行的耗时

        # logging.info('execute sql:\n%s', sql)
        self.__reconnect_if_turnel_break_down__()
        cursor = self.client.cursor(pymysql.cursors.DictCursor)
        cursor.execute(sql)
        self.client.commit()
        return cursor.rowcount

    def executemanyParam(self, sql ,args):
        # 统计下sql执行的耗时

        # logging.info('execute sql:\n%s', sql)
        self.__reconnect_if_turnel_break_down__()
        cursor = self.client.cursor(pymysql.cursors.DictCursor)
        cursor.executemany(sql,args)
        self.client.commit()
        return cursor.rowcount
    def executemany(self, sqls: list):
        if len(sqls) == 0:
            return 0
        # 统计下sql执行的耗时

        # logging.info('execute sql:\n%s', sql)
        self.__reconnect_if_turnel_break_down__()
        cursor = self.client.cursor(pymysql.cursors.DictCursor)
        for sql in sqls:
            cursor.execute(sql)
        self.client.commit()
        return cursor.rowcount

    def show_tables(self):
        sql = 'show tables'
        cursor = self.client.cursor(pymysql.cursors.DictCursor)
        cursor.execute(sql)
        results = cursor.fetchall()
        return [result[0] for result in results]

    def show_columns(self, table):
        sql = 'show columns from {0}'.format(table)
        columns = self.fetchall(sql)
        return columns

    # 关闭连接
    def close(self):
        self.client.close()
        if hasattr(self, 'tunnel'):
            self.tunnel.close()

    def insert_item(self, table, item):
        if not item:
            return
        fields = item.keys()
        values = []
        for field in fields:
            value = item[field]
            if value is None:
                values.append('null')
            elif isinstance(value, str):
                values.append('\'{0}\''.format(escape_string(value)))
            else:
                value = u'{0}'.format(value).encode('utf-8')
                values.append('\'{0}\''.format(escape_string(value)))
        insert_sql = 'insert into {0} ({1}) values ({2})'.format(table, ','.join(fields), ','.join(values))
        self.execute(insert_sql)
