# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
#
# Distributed under terms of the MIT license.

"""
ESClient
"""

import requests
import logging
import json
import inspect
import os
import yaml
from base64 import b64encode
from elasticsearch import Elasticsearch
from sshtunnel import SSHTunnelForwarder

logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)
# 获取当前脚本的文件路径
current_script_path = inspect.getframeinfo(inspect.currentframe()).filename
# 获取当前脚本所在的目录路径
current_script_directory = os.path.dirname(os.path.abspath(os.path.join(current_script_path, "..")))

# 分区配置字典表
ZONE_CONFIG_MAP = {
    # 上海分区
    'ShangHai': '/zone_shanghai.yaml',
    # 杭州分区
    'HangZhou': '/zone_hangzhou.yaml',
    # 主分区
    'Master': '/zone_shanghai.yaml',
    # 从分区
    'Slave': ['/zone_hangzhou.yaml']
}
LOGSTASH_TEMPLATE = '''
        input {{
            jdbc {{
                jdbc_driver_library => "/opt/logstash-6.8.9/bin/mysql-connector-java-5.1.47.jar"
                jdbc_driver_class => "com.mysql.jdbc.Driver"
                jdbc_connection_string => "jdbc:mysql://{dbHost}:3306/abc_cis_charge?serverTimezone=Asia/Shanghai&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&tinyInt1isBit=false&useSSL=false"
                jdbc_default_timezone => "Asia/Shanghai"
                jdbc_user => "{dbUser}"
                jdbc_password => "{dbPwd}"
                jdbc_paging_enabled => "true"
                jdbc_page_size => "{pageSize}"
                lowercase_column_names => false
                statement => "{sql}"
                }}
        }}
     

        filter {{
            mutate {{
                {convert}
                remove_field => ["@version", "@timestamp"]
            }}
        }}


        output {{
            elasticsearch {{
                hosts => ["{esUrl}"]
                index => "{indexName}"
                document_id => "%{{{docIdName}}}"
                user => "{esUser}"
                {action}
                password => "{esPwd}"
            }}
        }}'''


# robinsli
# 通过logstash刷数据
class LogStashInfo(object):

    def __init__(self, dbConfig, action, esName, pageSize, convert, indexName, docIdName, sql):
        self.dbConfig = dbConfig
        self.pageSize = pageSize
        self.convert = convert
        self.indexName = indexName
        self.docIdName = docIdName
        self.action = action
        self.esName = esName
        self.sql = sql

    def getLogStashSystemCommand(self):
        logStashString = LOGSTASH_TEMPLATE.format(
            dbHost=self.dbConfig['host'],
            dbUser=self.dbConfig['user'],
            dbPwd=self.dbConfig['password'],
            pageSize=self.pageSize,
            convert=self.convert,
            indexName=self.indexName,
            docIdName=self.docIdName,
            action=self.action,
            esUrl=self.es_info["url"],
            esUser=self.es_info["username"],
            esPwd=self.es_info["password"],
            sql=self.sql
        )
        logStashCmd = '''{logStashString}'''.format(logStashString=logStashString)
        return logStashCmd


# 通过es api直接操作索引
class ESClient(object):

    def __init__(self, zone, env='prod', normal=True, no_tunnel=False, jumper_user='schedulerx'):
        if zone not in ZONE_CONFIG_MAP.keys():
            raise Exception('请检查分区配置，传入的分区:{zone}不存在'.format(zone=zone))
        zone_config_path = ZONE_CONFIG_MAP[zone]
        with open(current_script_directory + zone_config_path, 'r') as yamlConfigFile:
            yamlZoneConfig = yaml.safe_load(yamlConfigFile)

        # 通过集群的简写 找到集群配置
        es_config_map = {item['esBase']: item for item in yamlZoneConfig['esList']}
        es_name = f'abc-search-{env}{"-normal" if env == "prod" and normal else ""}'
        es_info = es_config_map.get(es_name)
        if es_info is None:
            raise Exception('es:{0} not existed'.format(es_name))
        self.es_info = es_info
        self.es_url = f"http://{es_info['host']}:{es_info['port']}"
        self.headers = {}
        if 'username' in es_info.keys() and 'password' in es_info.keys():
            authorization = f"""Basic {b64encode(f"{es_info['username']}:{es_info['password']}".encode('utf-8')).decode('utf-8')}"""
            self.headers['Authorization'] = authorization
        jumper_config_map = {item['sshUser']: item for item in yamlZoneConfig['jumperList']}
        jumper_config = jumper_config_map.get(jumper_user)
        logging.info(f'jumper: {json.dumps(jumper_config)}')
        use_jumper = jumper_config.get('useJumper') if jumper_config else yamlZoneConfig.get('useJumper')
        jumper_ssh_user = jumper_config.get('sshUser') if jumper_config else yamlZoneConfig.get('jumperSshUser')
        jumper_ssh_key = jumper_config.get('sshKey') if jumper_config else yamlZoneConfig.get('jumperSshKey')
        jumper_ssh_password = jumper_config.get('sshPassword') if jumper_config else yamlZoneConfig.get('jumperSshPassword')
        if no_tunnel == False and use_jumper and env == 'prod':
            # 创建ssh隧道，用于连接到远程Mysql数据库
            self.tunnel = SSHTunnelForwarder((yamlZoneConfig.get('jumperHost'), int(yamlZoneConfig.get('jumperPort'))),
                                             ssh_username=jumper_ssh_user,
                                             ssh_pkey=jumper_ssh_key,
                                             ssh_password=jumper_ssh_password,
                                             remote_bind_address=(
                                                 self.es_info.get('host'), self.es_info.get('port'))
                                             )
            self.tunnel.start()
            # 通过跳板机连接数据库
            self.elasticsearch = self._connect_db_with_jumper()
        else:
            # 直接连接数据库
            self.elasticsearch = self._connect_db()
        print(self.elasticsearch)
        logging.info("success construt")

    def _es_get(self, path):
        url = '{0}{1}'.format(self.es_url, path)
        requests.get(url)
        rsp = requests.get(url, headers=self.headers)
        return rsp

    def _es_post(self, path, data=None):
        url = '{0}{1}'.format(self.es_url, path)
        rsp = requests.post(url, json=data, headers=self.headers)
        return rsp

    def _es_put(self, path, data=None):
        url = '{0}{1}'.format(self.es_url, path)
        rsp = requests.put(url, json=data, headers=self.headers)
        return rsp

    def _es_delete(self, path):
        url = '{0}{1}'.format(self.es_url, path)
        rsp = requests.delete(url, headers=self.headers)
        return rsp

    def get_index_info(self, index_name):
        path = '/{0}'.format(index_name)
        rsp = self._es_get(path)
        index_info = None
        if rsp.status_code == 200:
            index_info = rsp.json()
        elif rsp.status_code == 404:
            raise Exception('get_index_info not found index:{0}'.format(index_name))
        else:
            raise Exception('get_index_info error, name:{0}, rsp:{1}'.format(index_name, rsp.content))
        return index_info

    def create_index(self, index_name, index_info):
        logging.info('create index:{0}, body:{1}'.format(index_name, json.dumps(index_info, indent=4)))
        path = '/{0}'.format(index_name)
        rsp = self._es_put(path, index_info)
        if rsp.status_code != 200:
            raise Exception('create_index error, name:{0}, rsp:{1}'.format(index_name, rsp.content))
        return True

    def add_alias_for_index(self, index_name, alias):
        path = '/{0}/_alias/{1}'.format(index_name, alias)
        rsp = self._es_put(path)
        if rsp.status_code != 200:
            raise Exception(
                'add_alias_for_index error, name:{0}, alias:{1}, rsp:{2}'.format(index_name, alias, rsp.content))
        return True

    def delete_alias_for_index(self, index_name, alias):
        path = '/{0}/_alias/{1}'.format(index_name, alias)
        rsp = self._es_delete(path)
        if rsp.status_code != 200:
            raise Exception(
                'delete_alias_for_index error, name:{0}, alias:{1}, rsp:{2}'.format(index_name, alias, rsp.content))
        return True

    def reindex(self, data):
        path = '/_reindex?wait_for_completion=false'
        rsp = self._es_post(path, data)
        if rsp.status_code != 200:
            raise Exception('reindex error, data:{0}, rsp:{1}'.format(data, rsp.content))
        return rsp.json()

    def bulkInsert(self, index_name, write_bulk):
        rsp = self.elasticsearch.bulk(index=index_name, body=write_bulk, timeout='120s', request_timeout=120)
        if rsp['errors']:
            raise Exception('bulkInsert errors, rsp:{}'.format(rsp))
        return rsp

    def search(self, index_name, dsl):
        path = '/{0}/_search'.format(index_name)
        rsp = self._es_post(path, data=dsl)
        if rsp.status_code != 200:
            raise Exception('search error, data:{0}, rsp:{1}'.format(dsl, rsp.content))
        return rsp.json()

    def count(self, index_name, dsl):
        rsp = self.elasticsearch.count(index=[index_name], body=dsl)
        logging.info("Req:{0}\n,Rsp:{1}\n".format(dsl, rsp))
        return rsp

    def update_by_query(self, index_name, queryDsl, runScript):
        logging.info("DSL:{0}".format(queryDsl))
        rsp = self.elasticsearch.update_by_query(index=index_name, query=queryDsl, script=runScript)
        if rsp.status_code != 200:
            raise Exception('update_by_query error, data:{0}, rsp:{1}'.format(queryDsl, rsp.content))
        svr_rsp = rsp.json()
        logging.info("Req:{0}\n,Rsp:{1}\n".format(queryDsl, svr_rsp))
        return svr_rsp

    def __reconnect_if_turnel_break_down__(self):
        if hasattr(self, 'tunnel') and (not self.tunnel.is_active or not self.tunnel.is_alive):
            self.tunnel.start()
            self.elasticsearch = self._connect_db_with_jumper()
            logging.info('__reconnect_if_turnel_break_down__ [%s]', str(self.tunnel))

    # 跳板机连接数据库
    def _connect_db_with_jumper(self):
        self.es_url = f"http://127.0.0.1:{self.tunnel.local_bind_port}"
        return Elasticsearch([self.es_url], http_auth=(self.es_info['username'], self.es_info['password']))

    def _connect_db(self):
        return Elasticsearch([self.es_url], http_auth=(self.es_info['username'], self.es_info['password']))
