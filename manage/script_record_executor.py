import db


class ScriptRecordExecutor(object):
    def __init__(self):
        self.db_client = db.DBClient('abc_cis_mixed', 'abc_cis_basic')

    def execute(self, script_id, chain_id):
        script_chain = {
            'chain_id': chain_id,
            'script_id': script_id,
            'status': 1
        }
        self.db_client.insert_item('v2_data_migrate_script_organ_execute', script_chain)
