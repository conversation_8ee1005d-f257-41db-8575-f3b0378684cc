#!/usr/bin/python
# -*- coding: utf-8 -*-

import logging
import time
import datetime
import socket
import random

logging.basicConfig(format='%(asctime)s [%(levelname)s]: %(message)s', level=logging.INFO)

# 常量
TOTAL_BITS = 1 << 6
ABC_ID_PREFIX = "ffffffff00000000"
CONTAINER = 1
ACTUAL = 2


class BitsAllocator(object):
    def __init__(self, timestampBits, workerIdBits, sequenceBits):
        signBits = 1
        # make sure allocated 64 bits
        allocateTotalBits = signBits + timestampBits + workerIdBits + sequenceBits
        if allocateTotalBits > TOTAL_BITS:
            raise Exception('allocate not enough 64 bits')

        # initialize bits
        self.timestampBits = timestampBits
        self.workerIdBits = workerIdBits
        self.sequenceBits = sequenceBits

        # initialize max value
        self.maxDeltaSeconds = ~(-1 << timestampBits)
        self.maxWorkerId = ~(-1 << workerIdBits)
        self.maxSequence = ~(-1 << sequenceBits)

        # initialize shift
        self.timestampShift = workerIdBits + sequenceBits
        self.workerIdShift = sequenceBits

    def allocate(self, deltaSeconds, workerId, sequence):
        return (deltaSeconds << self.timestampShift) | (workerId << self.workerIdShift) | sequence

    def getMaxDeltaSeconds(self):
        return self.maxDeltaSeconds

    def getMaxWorkerId(self):
        return self.maxWorkerId

    def getMaxSequence(self):
        return self.maxSequence


class IdWork(object):
    def __init__(self, db_client, is_docker):
        # 变量
        self.db_client = db_client
        self.is_docker = is_docker
        self.workerId = None
        self.epochStr = '1800-01-01'  # 2020-01-01 -> 1800-01-01
        self.epochSeconds = -5364691200
        self.timeBits = 34  # 28->34
        self.workerBits = 15  # 22->15
        self.seqBits = 14  # 13->14
        self.sequence = 0
        self.lastSecond = -1
        self.bitsAllocator = None  # new BitsAllocator(timeBits, workerBits, seqBits)

    def getIpAddr(self):
        sc = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        try:
            sc.connect(('**************', 1))
            ip = sc.getsockname()[0]
        except Exception as e:
            logging.error(e)
            ip = "127.0.0.1"
        finally:
            sc.close()
        return ip

    def _getWorkerId(self):
        hostName = self.getIpAddr()
        type = CONTAINER if self.is_docker else ACTUAL
        port = """{0}-{1}""".format(int(time.time() * 1000), random.randint(0, 100000))
        lunch_date = datetime.date.today()
        created = datetime.datetime.now()

        abc_uid_worker_node_sql = """insert into abc_uid_worker_node(HOST_NAME, PORT, TYPE, LAUNCH_DATE, MODIFIED, CREATED)
                                     VALUES (
                                        '{hostName}', 
                                        '{port}', 
                                        '{type}', 
                                        '{lunch_date}', 
                                        '{created}', 
                                        '{created}');""".format(hostName=hostName,
                                                                port=port,
                                                                type=type,
                                                                lunch_date=lunch_date,
                                                                created=created)
        return self.db_client.lastrowid(abc_uid_worker_node_sql)

    def getWorkerId(self):
        if self.workerId is None:
            self.workerId = self._getWorkerId()
        return self.workerId

    def config(self):
        self.bitsAllocator = BitsAllocator(self.timeBits, self.workerBits, self.seqBits)
        self.getWorkerId()
        if self.workerId > self.bitsAllocator.getMaxWorkerId():
            self.workerId = self.workerId & self.bitsAllocator.getMaxWorkerId()
        logging.info(
            "Initialized bits(1, {0}, {1}, {2}) for workerID:{3}".format(self.timeBits, self.workerBits, self.seqBits,
                                                                         self.workerId))

    def getNextSecond(self, last_timestamp):
        currentSecond = self.getCurrentSecond()
        while currentSecond <= last_timestamp:
            currentSecond = self.getCurrentSecond()
        return currentSecond

    def getCurrentSecond(self):
        currentSecond = int(time.time())
        if currentSecond - self.epochSeconds > self.bitsAllocator.getMaxDeltaSeconds():
            raise Exception("Timestamp bits is exhausted. Refusing UID generate. Now: {}".format(currentSecond))

        return currentSecond

    def nextId(self):
        currentSecond = self.getCurrentSecond()

        # Clock moved backwards, refuse to generateuid
        if currentSecond < self.lastSecond:
            refusedSeconds = self.lastSecond - currentSecond
            raise Exception("""Clock moved backwards.Refusing for {0} seconds""".format(refusedSeconds))

        # At the same second, increase sequence
        if currentSecond == self.lastSecond:
            self.sequence = (self.sequence + 1) & self.bitsAllocator.getMaxSequence()
            # Exceed the max sequence, we wait the next second to generate uid
            if self.sequence == 0:
                currentSecond = self.getNextSecond(self.lastSecond)

        # At the different second, sequence restart from zero
        else:
            self.sequence = 0

        self.lastSecond = currentSecond

        # Allocate bits for UID
        return self.bitsAllocator.allocate(currentSecond - self.epochSeconds, self.workerId, self.sequence)

    def _genId(self):
        try:
            return self.nextId()
        except Exception as e:
            logging.error("Generate unique id exception. ", e)
            raise e

    def getUIDLong(self):
        return self._genId()

    def getUID(self):
        return """'{}'""""".format(self.getUIDLong())

    def getUUID(self):
        uid = self.getUIDLong()
        return """{0}{1:x}""".format(ABC_ID_PREFIX, uid)
