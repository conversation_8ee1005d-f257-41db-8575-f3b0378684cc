import pynetdicom.sop_class
from pydicom.dataset import Dataset

from pynetdicom import AE, debug_logger
from pynetdicom.sop_class import ModalityWorklistInformationFind

if __name__ == '__main__':
    debug_logger()

    # Initialise the Application Entity
    ae = AE()

    # Add a requested presentation context
    ae.add_requested_context(ModalityWorklistInformationFind)

    # Create our Identifier (query) dataset
    ds = Dataset()
    ds.PatientName = '*'
    ds.PatientID = '000931'
    ds.SpecificCharacterSet = 'ISO_IR 2022'
    ds.ScheduledProcedureStepSequence = [Dataset()]
    item = ds.ScheduledProcedureStepSequence[0]
    # item.ScheduledStationAETitle = ''
    # item.ScheduledProcedureStepStartDate = ''
    # item.Modality = 'CT'

    # Associate with peer AE at IP 127.0.0.1 and port 11112
    assoc = ae.associate(addr="**************", port=11112, ae_title='DCM4CHEE')

    if assoc.is_established:
        # Use the C-FIND service to send the identifier
        responses = assoc.send_c_find(ds, ModalityWorklistInformationFind)
        for (status, identifier) in responses:
            if status:
                pass
                # print('C-FIND query status: 0x{0:04x}, identifier:\n{1}'.format(status.Status, identifier))
            else:
                print('Connection timed out, was aborted or received invalid response')

        # Release the association
        assoc.release()
    else:
        print('Association rejected, aborted or never connected')
